<template>
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<div class="tab-nav-tabs my-3">
				<ul class="nav nav-tabs nav-justified">
					<li class="nav-item">
						<a class="nav-link active" href="#SectionD" data-bs-toggle="tab">依資產類別</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" href="#SectionE" data-bs-toggle="tab">依投資標的</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" href="#SectionF" data-bs-toggle="tab">依投資區域</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" href="#SectionG" data-bs-toggle="tab">依風險屬性</a>
					</li>
				</ul>
				<div class="tab-content">
					<div class="tab-pane fade show active" id="SectionD">
						<div class="row g-3 align-items-center">
							<div class="col-lg-4 text-center">
								<div id="chart-container1" style="min-width: 310px; min-height: 310px"></div>
							</div>
							<div class="col-lg-8">
								<div class="tx-13 text-end">(新臺幣:元)</div>
								<table class="table table-RWD table-bordered table-hover">
									<thead>
										<tr style="display: table-row">
											<th class="text-start">資產類別</th>
											<th>投資金額</th>
											<th>投資比例</th>
											<th>實現損益</th>
											<th>報酬率</th>
											<th>報酬率貢獻度</th>
										</tr>
									</thead>
									<tbody>
										<template v-for="(proTypeAssetSumAmtm, index) in assetSumAmtProTypes">
											<tr class="accordion-header">
												<td class="text-start" data-th="資產類別">
													<span class="accordion-toggle collapsed" data-bs-toggle="collapse" :href="'.collapse' + index"
														>{{ proTypeAssetSumAmtm.assetcatName || '--' }}
													</span>
												</td>
												<td data-th="投資本金（含手續費）" class="text-end">
													{{ $filters.formatCurAmt(proTypeAssetSumAmtm.invAmtLc, getCurDecimal()) }}
												</td>
												<td data-th="投資比例(%)" class="text-end">
													{{
														$filters.formatPct(
															proTypeAssetSumAmtm.invAmtLc / $filters.sumTotal(assetSumAmtProTypes, 'invAmtLc')
														)
													}}%
												</td>
												<td data-th="未實現損益(含息)" class="text-end" :class="{ 'tx-red': proTypeAssetSumAmtm.plLc < 0 }">
													{{ $filters.formatCurAmt(proTypeAssetSumAmtm.plLc, getCurDecimal()) }}
												</td>
												<td data-th="含息報酬率(%)" class="text-end" :class="{ 'tx-red': proTypeAssetSumAmtm.plLc < 0 }">
													{{ $filters.formatPct(proTypeAssetSumAmtm.plLc / proTypeAssetSumAmtm.invAmtLc) }}%
												</td>
												<td data-th="報酬率貢獻度(%)" class="text-end" :class="{ 'tx-red': proTypeAssetSumAmtm.plLc < 0 }">
													{{
														$filters.formatPct(proTypeAssetSumAmtm.plLc / $filters.sumTotal(assetSumAmtProTypes, 'plLc'))
													}}%
												</td>
											</tr>

											<tr
												class="collapse collapse1"
												:class="'collapse' + index"
												v-if="proTypeAssetSumAmtm.assetSumAmtProTypes"
												v-for="subProTypeAssetSumAmt in proTypeAssetSumAmtm.assetSumAmtProTypes"
											>
												<td data-th="資產類別" class="td-info">{{ subProTypeAssetSumAmt.pfcatName || '--' }}</td>
												<td data-th="投資本金（含手續費）" class="text-end">
													{{ $filters.formatCurAmt(subProTypeAssetSumAmt.invAmtLc, getCurDecimal()) }}
												</td>
												<td data-th="投資比例(%)" class="text-end">
													{{
														$filters.formatPct(
															subProTypeAssetSumAmt.invAmtLc / $filters.sumTotal(assetSumAmtProTypes, 'invAmtLc')
														)
													}}%
												</td>
												<td data-th="未實現損益(含息)" class="text-end" :class="{ 'tx-red': subProTypeAssetSumAmt.plLc < 0 }">
													{{ $filters.formatCurAmt(subProTypeAssetSumAmt.plLc, getCurDecimal()) }}
												</td>
												<td data-th="含息報酬率(%)" class="text-end" :class="{ 'tx-red': subProTypeAssetSumAmt.plLc < 0 }">
													{{ $filters.formatPct(subProTypeAssetSumAmt.plLc / subProTypeAssetSumAmt.invAmtLc) }}%
												</td>
												<td data-th="報酬率貢獻度(%)" class="text-end" :class="{ 'tx-red': subProTypeAssetSumAmt.plLc < 0 }">
													{{
														$filters.formatPct(
															subProTypeAssetSumAmt.plLc / $filters.sumTotal(assetSumAmtProTypes, 'plLc')
														)
													}}%
												</td>
											</tr>
										</template>
									</tbody>
									<tfoot>
										<tr class="tx-sum bg-total">
											<td class="text-start" data-th="資產類別">
												<label class="text-start">小計</label>
											</td>
											<td data-th="投資金額">
												<label class="JQ-valuecolor text-end">{{
													$filters.formatCurAmt($filters.sumTotal(assetSumAmtProTypes, 'invAmtLc'), getCurDecimal())
												}}</label>
											</td>
											<td data-th="投資比例">
												<label class="JQ-valuecolor text-end">100%</label>
											</td>
											<td data-th="未實現損益">
												<label class="JQ-valuecolor text-end">{{
													$filters.formatCurAmt($filters.sumTotal(assetSumAmtProTypes, 'plLc'), getCurDecimal())
												}}</label>
											</td>
											<td data-th="報酬率">
												<label class="JQ-valuecolor text-end"
													>{{
														$filters.formatPct(
															$filters.sumTotal(assetSumAmtProTypes, 'plLc') /
																$filters.sumTotal(assetSumAmtProTypes, 'invAmtLc')
														)
													}}%</label
												>
											</td>
											<td data-th="報酬率貢獻度">
												<label class="JQ-valuecolor text-end">100%</label>
											</td>
										</tr>
									</tfoot>
								</table>
							</div>
						</div>
					</div>
					<div class="tab-pane fade show" id="SectionE">
						<div class="row g-3 align-items-center">
							<div class="col-lg-5 text-center">
								<div id="chart-container2" style="min-width: 310px; min-height: 310px"></div>
							</div>
							<div class="col-lg-7">
								<div class="tx-13 text-end">(新臺幣:元)</div>
								<table class="table table-RWD table-bordered table-hover">
									<thead>
										<tr style="display: table-row">
											<th class="text-start">投資標的</th>
											<th>投資本金</th>
											<th>投資比例</th>
											<th>未實現損益</th>
											<th>報酬率</th>
											<th>報酬率貢獻度</th>
										</tr>
									</thead>
									<tbody>
										<template v-for="(item, index) in assetSumAmtInvTargets">
											<tr class="accordion-header">
												<td data-th="資產類別" class="text-start">{{ item.label || '--' }}</td>
												<td data-th="投資本金（含手續費）" class="text-end">
													{{ $filters.formatCurAmt(item.invAmtLc, getCurDecimal()) }}
												</td>
												<td data-th="投資比例(%)" class="text-end">
													{{ $filters.formatPct(item.invAmtLc / $filters.sumTotal(assetSumAmtInvTargets, 'invAmtLc')) }}%
												</td>
												<td data-th="未實現損益(含息)" class="text-end" :class="{ 'tx-red': item.plLc < 0 }">
													{{ $filters.formatCurAmt(item.plLc, getCurDecimal()) }}
												</td>
												<td data-th="含息報酬率(%)" class="text-end" :class="{ 'tx-red': item.plLc < 0 }">
													{{ $filters.formatPct(item.plLc / item.invAmtLc) }}%
												</td>
												<td data-th="報酬率貢獻度(%)" class="text-end" :class="{ 'tx-red': item.plLc < 0 }">
													{{ $filters.formatPct(item.plLc / $filters.sumTotal(assetSumAmtInvTargets, 'plLc')) }}%
												</td>
											</tr>
										</template>
									</tbody>
									<tfoot>
										<tr class="tx-sum bg-total">
											<td class="text-start" data-th="資產類別">
												<label class="text-start">小計</label>
											</td>
											<td data-th="投資金額">
												<label class="JQ-valuecolor text-end">{{
													$filters.formatCurAmt($filters.sumTotal(assetSumAmtInvTargets, 'invAmtLc'), getCurDecimal())
												}}</label>
											</td>
											<td data-th="投資比例">
												<label class="JQ-valuecolor text-end">100%</label>
											</td>
											<td data-th="未實現損益">
												<label class="JQ-valuecolor text-end">{{
													$filters.formatCurAmt($filters.sumTotal(assetSumAmtInvTargets, 'plLc'), getCurDecimal())
												}}</label>
											</td>
											<td data-th="報酬率">
												<label class="JQ-valuecolor text-end"
													>{{
														$filters.formatPct(
															$filters.sumTotal(assetSumAmtInvTargets, 'plLc') /
																$filters.sumTotal(assetSumAmtInvTargets, 'invAmtLc')
														)
													}}%</label
												>
											</td>
											<td data-th="報酬率貢獻度">
												<label class="JQ-valuecolor text-end">100%</label>
											</td>
										</tr>
									</tfoot>
								</table>
							</div>
						</div>
					</div>
					<div class="tab-pane fade show" id="SectionF">
						<div class="row g-3 align-items-center">
							<div class="col-lg-4 text-center">
								<div id="chart-container3" style="min-width: 310px; min-height: 310px"></div>
							</div>
							<div class="col-lg-8">
								<div class="tx-13 text-end">(新臺幣:元)</div>
								<table class="table table-RWD table-bordered table-hover">
									<thead>
										<tr style="display: table-row">
											<th class="text-start">投資標的</th>
											<th>投資本金</th>
											<th>投資比例</th>
											<th>實現損益</th>
											<th>報酬率</th>
											<th>報酬率貢獻度</th>
										</tr>
									</thead>
									<tbody>
										<template v-for="(item, index) in assetSumAmtGeoFocus">
											<tr class="accordion-header">
												<td data-th="資產類別" class="text-start">{{ item.label || '--' }}</td>
												<td data-th="投資本金（含手續費）" class="text-end">
													{{ $filters.formatCurAmt(item.invAmtLc, getCurDecimal()) }}
												</td>
												<td data-th="投資比例(%)" class="text-end">
													{{ $filters.formatPct(item.invAmtLc / $filters.sumTotal(assetSumAmtGeoFocus, 'invAmtLc')) }}%
												</td>
												<td data-th="未實現損益(含息)" class="text-end" :class="{ 'tx-red': item.plLc < 0 }">
													{{ $filters.formatCurAmt(item.plLc, getCurDecimal()) }}
												</td>
												<td data-th="含息報酬率(%)" class="text-end" :class="{ 'tx-red': item.plLc < 0 }">
													{{ $filters.formatPct(item.plLc / item.invAmtLc) }}%
												</td>
												<td data-th="報酬率貢獻度(%)" class="text-end" :class="{ 'tx-red': item.plLc < 0 }">
													{{ $filters.formatPct(item.plLc / $filters.sumTotal(assetSumAmtGeoFocus, 'plLc')) }}%
												</td>
											</tr>
										</template>
									</tbody>
									<tfoot>
										<tr class="tx-sum bg-total">
											<td class="text-start" data-th="資產類別">
												<label class="text-start">小計</label>
											</td>
											<td data-th="投資金額">
												<label class="JQ-valuecolor text-end">{{
													$filters.formatCurAmt($filters.sumTotal(assetSumAmtGeoFocus, 'invAmtLc'), getCurDecimal())
												}}</label>
											</td>
											<td data-th="投資比例">
												<label class="JQ-valuecolor text-end">100%</label>
											</td>
											<td data-th="未實現損益">
												<label class="JQ-valuecolor text-end">{{
													$filters.formatCurAmt($filters.sumTotal(assetSumAmtGeoFocus, 'plLc'), getCurDecimal())
												}}</label>
											</td>
											<td data-th="報酬率">
												<label class="JQ-valuecolor text-end"
													>{{
														$filters.formatPct(
															$filters.sumTotal(assetSumAmtGeoFocus, 'plLc') /
																$filters.sumTotal(assetSumAmtGeoFocus, 'invAmtLc')
														)
													}}%</label
												>
											</td>
											<td data-th="報酬率貢獻度">
												<label class="JQ-valuecolor text-end">100%</label>
											</td>
										</tr>
									</tfoot>
								</table>
							</div>
						</div>
					</div>
					<div class="tab-pane fade show" id="SectionG">
						<div class="row g-3 align-items-center">
							<div class="col-lg-4 text-center">
								<div id="chart-container4" style="min-width: 310px; min-height: 310px"></div>
							</div>
							<div class="col-lg-8">
								<div class="tx-13 text-end">(新臺幣:元)</div>
								<table id="sel4" class="table table-RWD table-bordered table-hover">
									<thead>
										<tr style="display: table-row">
											<th class="text-start">風險評級</th>
											<th>投資本金</th>
											<th>投資比例</th>
											<th>實現損益</th>
											<th>報酬率</th>
											<th>報酬率貢獻度</th>
										</tr>
									</thead>
									<tbody>
										<template v-for="(item, index) in assetSumAmtRisk">
											<tr class="accordion-header">
												<td data-th="資產類別" class="text-start">{{ item.label || '--' }}</td>
												<td data-th="投資本金（含手續費）" class="text-end">
													{{ $filters.formatCurAmt(item.invAmtLc, getCurDecimal()) }}
												</td>
												<td data-th="投資比例(%)" class="text-end">
													{{ $filters.formatPct(item.invAmtLc / $filters.sumTotal(assetSumAmtRisk, 'invAmtLc')) }}%
												</td>
												<td data-th="未實現損益(含息)" class="text-end" :class="{ 'tx-red': item.plLc < 0 }">
													{{ $filters.formatCurAmt(item.plLc, getCurDecimal()) }}
												</td>
												<td data-th="含息報酬率(%)" class="text-end" :class="{ 'tx-red': item.plLc < 0 }">
													{{ $filters.formatPct(item.plLc / item.invAmtLc) }}%
												</td>
												<td data-th="報酬率貢獻度(%)" class="text-end" :class="{ 'tx-red': item.plLc < 0 }">
													{{ $filters.formatPct(item.plLc / $filters.sumTotal(assetSumAmtRisk, 'plLc')) }}%
												</td>
											</tr>
										</template>
									</tbody>
									<tfoot>
										<tr class="tx-sum bg-total">
											<td class="text-start" data-th="資產類別">
												<label class="text-start">小計</label>
											</td>
											<td data-th="投資金額">
												<label class="JQ-valuecolor text-end">{{
													$filters.formatCurAmt($filters.sumTotal(assetSumAmtRisk, 'invAmtLc'), getCurDecimal())
												}}</label>
											</td>
											<td data-th="投資比例">
												<label class="JQ-valuecolor text-end">100%</label>
											</td>
											<td data-th="未實現損益">
												<label class="JQ-valuecolor text-end">{{
													$filters.formatCurAmt($filters.sumTotal(assetSumAmtRisk, 'plLc'), getCurDecimal())
												}}</label>
											</td>
											<td data-th="報酬率">
												<label class="JQ-valuecolor text-end"
													>{{
														$filters.formatPct(
															$filters.sumTotal(assetSumAmtRisk, 'plLc') /
																$filters.sumTotal(assetSumAmtRisk, 'invAmtLc')
														)
													}}%</label
												>
											</td>
											<td data-th="報酬率貢獻度">
												<label class="JQ-valuecolor text-end">100%</label>
											</td>
										</tr>
									</tfoot>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		cusCode: null,
		hasAuth: Boolean,
		customer: Object,
		userName: String,
		userRoleName: String,
		queryDt: null,
		dataDt: null
	},
	data: function () {
		return {
			assetSumAmtProTypes: null,
			assetSumAmtInvTargets: null,
			assetSumAmtGeoFocus: null,
			assetSumAmtRisk: null
		};
	},
	mounted: function () {
		var self = this;
		self.getProTypes();
		self.getInvTargets();
		self.getGeoFocus();
		self.getRisk();
	},
	filters: {
		defaultValue: function (value) {
			if (value || value == 0) {
				return '-';
			}
			return value;
		},
		formatPct: function (value) {
			if (_.isNil(value)) {
				value = 0;
			}
			if (!_.isNumber(value)) {
				return 'NaN';
			}
			return numeral(value).multiply(100).format('0.00');
		}
	},
	methods: {
		getProTypes: async function () {
			var self = this;
			const ret = await self.$api.getInvTargetProTypes({
				cusCodes: self.cusCode
			});
			self.assetSumAmtProTypes = ret.data;
			self.renderProTypeChart();
		},
		renderProTypeChart: function () {
			var self = this;
			var chartDatas = [];

			self.assetSumAmtProTypes.forEach(function (layer1) {
				// 第一層
				var chartData = {
					name: layer1.assetcatName,
					value: layer1.invAmtLc + 10
				};
				chartDatas.push(chartData);
			});

			am5.ready(() => {
				var root = am5.Root.new('chart-container1');
				root._logo.dispose();

				root.setThemes([am5themes_Animated.new(root)]);
				var chart = root.container.children.push(
					am5percent.PieChart.new(root, {
						layout: root.verticalLayout,
						innerRadius: am5.percent(50)
					})
				);
				var series = chart.series.push(
					am5percent.PieSeries.new(root, {
						valueField: 'value',
						categoryField: 'name',
						alignLabels: false
					})
				);

				series.labels.template.set('visible', false);
				series.data.setAll(chartDatas);

				var legend = chart.children.push(
					am5.Legend.new(root, {
						centerX: am5.percent(50),
						x: am5.percent(50),
						marginTop: 15,
						marginBottom: 15
					})
				);

				legend.data.setAll(series.dataItems);
				series.appear(1000, 100);
			});
		},
		getInvTargets: async function () {
			var self = this;
			const ret = await self.$api.getInvTargetApi({
				cusCode: self.cusCode
			});
			self.assetSumAmtInvTargets = ret.data;
			self.renderInvTargetChart();
		},
		renderInvTargetChart: function () {
			var self = this;
			var chartDatas = [];

			self.assetSumAmtInvTargets.forEach(function (item) {
				var chartData = {
					name: item.label,
					value: item.invAmtLc
				};
				chartDatas.push(chartData);
			});
			am5.ready(function () {
				var root = am5.Root.new('chart-container2');
				root._logo.dispose();

				root.setThemes([am5themes_Animated.new(root)]);
				var chart = root.container.children.push(
					am5percent.PieChart.new(root, {
						radius: am5.percent(80),
						innerRadius: am5.percent(50),
						layout: root.horizontalLayout
					})
				);
				var series = chart.series.push(
					am5percent.PieSeries.new(root, {
						valueField: 'value',
						categoryField: 'name',
						alignLabels: false,
						legendValueText: ''
					})
				);

				series.labels.template.set('visible', false);
				series.ticks.template.set('visible', false);
				series.data.setAll(chartDatas);

				series.slices.template.set('strokeOpacity', 0);
				var legend = chart.children.push(
					am5.Legend.new(root, {
						centerY: am5.percent(50),
						y: am5.percent(50),
						x: am5.percent(70),
						layout: root.verticalLayout
					})
				);
				legend.valueLabels.template.disabled = true;
				legend.labels.template.setAll({
					maxWidth: 70,
					oversizedBehavior: 'wrap'
				});
				legend.data.setAll(series.dataItems);
				series.appear(1000, 100);
			});
		},
		getGeoFocus: async function () {
			var self = this;
			const ret = await self.$api.getInvTargetGeoFocusApi({
				cusCodes: self.cusCode
			});
			self.assetSumAmtGeoFocus = ret.data;
			self.renderGeoFocusChart();
		},
		renderGeoFocusChart: function () {
			var self = this;
			var chartDatas = [];

			self.assetSumAmtGeoFocus.forEach(function (item) {
				var chartData = {
					name: item.label,
					value: item.invAmtLc
				};
				chartDatas.push(chartData);
			});

			am5.ready(function () {
				var root = am5.Root.new('chart-container3');
				root._logo.dispose();

				root.setThemes([am5themes_Animated.new(root)]);
				var chart = root.container.children.push(
					am5percent.PieChart.new(root, {
						radius: am5.percent(90),
						innerRadius: am5.percent(50),
						layout: root.horizontalLayout
					})
				);
				var series = chart.series.push(
					am5percent.PieSeries.new(root, {
						valueField: 'value',
						categoryField: 'name',
						alignLabels: false,
						legendValueText: ''
					})
				);

				series.labels.template.set('visible', false);
				series.ticks.template.set('visible', false);
				series.data.setAll(chartDatas);

				series.slices.template.set('strokeOpacity', 0);
				var legend = chart.children.push(
					am5.Legend.new(root, {
						centerY: am5.percent(50),
						y: am5.percent(50),
						x: am5.percent(70),
						layout: root.verticalLayout
					})
				);
				legend.valueLabels.template.disabled = true;
				legend.labels.template.setAll({
					maxWidth: 70,
					oversizedBehavior: 'wrap'
				});
				legend.data.setAll(series.dataItems);
				series.appear(1000, 100);
			});
		},
		getRisk: async function () {
			var self = this;
			const ret = await self.$api.getInvTargetRiskApi({
				cusCodes: self.cusCode
			});
			self.assetSumAmtRisk = ret.data;
			self.renderRiskChart();
		},
		renderRiskChart: function () {
			var self = this;
			var chartDatas = [];

			self.assetSumAmtRisk.forEach(function (item) {
				var chartData = {
					name: item.label,
					value: item.invAmtLc
				};
				chartDatas.push(chartData);
			});

			am5.ready(function () {
				var root = am5.Root.new('chart-container4');
				root._logo.dispose();

				root.setThemes([am5themes_Animated.new(root)]);
				var chart = root.container.children.push(
					am5percent.PieChart.new(root, {
						radius: am5.percent(90),
						innerRadius: am5.percent(50),
						layout: root.horizontalLayout
					})
				);
				var series = chart.series.push(
					am5percent.PieSeries.new(root, {
						valueField: 'value',
						categoryField: 'name',
						alignLabels: false,
						legendValueText: ''
					})
				);

				series.labels.template.set('visible', false);
				series.ticks.template.set('visible', false);
				series.data.setAll(chartDatas);

				series.slices.template.set('strokeOpacity', 0);
				var legend = chart.children.push(
					am5.Legend.new(root, {
						centerY: am5.percent(50),
						y: am5.percent(50),
						x: am5.percent(70),
						layout: root.verticalLayout
					})
				);
				legend.valueLabels.template.disabled = true;
				legend.labels.template.setAll({
					maxWidth: 70,
					oversizedBehavior: 'wrap'
				});
				legend.data.setAll(series.dataItems);
				series.appear(1000, 100);
			});
		},
		// 取得會員基準幣小數位數
		getCurDecimal: function () {
			var self = this;
			if (self.customer.baseCurCode == 'TWD') {
				return 0; // 台幣顯示至整數位
			}
			return 2; // 其他顯示至小數後兩位
		},
		formatValue(value, type) {
			switch (type) {
				case 'I': // 千分位整數格式
					return this.formatInteger(value);
				case 'D': // 日期格式
					return this.formatDate(value);
				case 'F': // 浮點數轉為千分位整數格式
					return this.formatFloatAsInteger(value);
				case 'S': // 字串格式
					return value;
				// 其他類型的格式化處理可以在這裡繼續擴展
				default:
					return value; // 預設返回原始值
			}
		},
		formatInteger(value) {
			const numericValue = Number(value);
			return isNaN(numericValue) ? 0 : numeral(numericValue).format('0,0');
		},
		formatDate(value) {
			const date = new Date(value);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`; // 手動格式化為 'YYYY-MM-DD'
		},
		formatFloatAsInteger(value) {
			const numericValue = Number(value);
			return isNaN(numericValue) ? 0 : parseFloat(numericValue).toFixed(0); // 或者其他浮點數處理方式
		}
	}
};
</script>
