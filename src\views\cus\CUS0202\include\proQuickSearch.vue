<template>
	<vue-modal :is-open="isOpenModal" :before-close="closeModal">
		<template v-slot:content="props">
			<div class="modal-dialog modal-xl modal-dialog-centered">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">商品快速搜尋</h4>
						<button type="button" class="btn-expand"><i class="bi bi-arrows-fullscreen"></i></button>
						<button type="button" class="btn-close" @click.prevent="props.close()" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="card card-form shadow-none">
							<div class="card-body">
								<form>
									<div class="row g-3 align-items-end">
										<div class="col-lg-4 col-xl-4">
											<label class="form-label">商品名稱</label>
											<div class="input-group">
												<input name="proName" class="form-control" id="proName" type="text" size="15"
													v-model="proSearch.proName" />
												<button class="btn btn-primary JQ-query" type="button" @click.prevent="proSearchByName">
													<i class="bi bi-search"></i>
												</button>
											</div>
										</div>
										<div class="col-lg-4 col-xl-4">
											<label class="form-label">商品代號</label>
											<div class="input-group">
												<input name="bankProCode" class="form-control" id="bankProCode" type="text" size="15"
													v-model="proSearch.proCode" />
												<button class="btn btn-primary JQ-query" type="button" @click.prevent="proSearchByCode">
													<i class="bi bi-search"></i>
												</button>
											</div>
										</div>
										<div class="col-lg-4 col-xl-4">
											<label class="form-label">商品主類</label>
											<div class="input-group">
												<select name="pfcatCode" class="form-select" v-model="proSearch.proType">
													<option :value="null">全部</option>
													<option v-for="item in selProCat" :value="item.pfcatCode">
														{{ item.pfcatName }}
													</option>
												</select>
												<button class="btn btn-primary JQ-query" type="button" @click.prevent="proSearchByType">
													<i class="bi bi-search"></i>
												</button>
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
						<div id="ProResult">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>查詢結果</h4>
									<vue-pagination :pageable="proSearch.pageData" :goto-page="getProQuickSearch"></vue-pagination>
								</div>
								<div class="table-responsive">
									<table class="table table-RWD table-bordered text-center" id="tbl">
										<thead>
											<tr>
												<th width="5%">選取</th>
												<th width="10%">商品代號</th>
												<th width="14%">中文名稱</th>
												<th width="14%">英文名稱</th>
												<th width="12%">商品次分類</th>
												<th width="10%">風險屬性</th>
												<th width="10%">商品幣別</th>
												<th width="12%">是否可申購</th>
												<th width="13%">商品到期日</th>
											</tr>
										</thead>
										<tbody id="wrapperList">
											<tr v-for="(item, index) in proSearch.pageData.content" :key="index">
												<td data-th="">
													<input name="proSearch.radioProCode" type="radio" class="form-check-input"
														:value="item.bankProCode" v-model="proSearch.radioProCode" />
												</td>
												<td data-th="商品代號">{{ item.bankProCode }}</td>
												<td data-th="中文名稱">{{ item.proName }}</td>
												<td data-th="英文名稱">{{ item.engProName }}</td>
												<td data-th="商品次分類">{{ item.proSubType }}</td>
												<td data-th="風險屬性">{{ item.riskName }}</td>
												<td data-th="商品幣別">{{ item.curName }}</td>
												<td data-th="是否可申購">
													<img v-if="item.buyYn === 'Y'" :src="getImgURL('icon', 'ico-yes.png')" />
													<img v-else :src="getImgURL('icon', 'ico-no.png')" />
												</td>
												<td data-th="商品到期日">{{ $filters.formatDate(item.expireDt) }}</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" @click="closeModal" class="btn btn-white">關閉</button>
						<button type="button" class="btn btn-primary" @click="
							setProCode(proSearch.radioProCode);
						closeModal();
						">
							確認
						</button>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
</template>
<script>
import vuePagination from '@/views/components/pagination.vue';
import vueModal from '@/views/components/model.vue';
import { getImgURL } from '@/utils/imgURL.js';
export default {
	components: {
		vueModal,
		vuePagination
	},
	props: {
		setProCode: Function
	},
	data: function () {
		return {
			proSearch: {
				proName: null,
				proCode: null,
				proType: null,
				req: {
					proName: null,
					proCode: null,
					proType: null
				},
				//分頁元件
				pageable: {
					page: 0,
					size: 10,
					sort: 'PRO_CODE',
					direction: 'ASC'
				},
				//查詢結果
				pageData: {
					content: {}
				},
				radioProCode: null
			},
			isOpenModal: false
		};
	},
	watch: {},
	beforeMount: function () { },
	created: function () { },
	mounted: function () { },
	methods: {
		getImgURL,
		show: function () {
			this.isOpenModal = true;
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		proSearchByName: function () {
			if (_.isBlank(this.proSearch.proName)) {
				this.$bi.alert('請輸入商品名稱');
			} else {
				this.proSearch.req.proName = this.proSearch.proName;
				this.proSearch.req.proCode = null;
				this.proSearch.req.proType = null;
				this.getProQuickSearch(0);
			}
		},
		proSearchByCode: function () {
			if (_.isBlank(this.proSearch.proCode)) {
				this.$bi.alert('請輸入商品代號');
			} else {
				this.proSearch.req.proName = null;
				this.proSearch.req.proCode = this.proSearch.proCode;
				this.proSearch.req.proType = null;
				this.getProQuickSearch(0);
			}
		},
		proSearchByType: function () {
			this.proSearch.req.proName = null;
			this.proSearch.req.proCode = null;
			this.proSearch.req.proType = this.proSearch.proType;
			this.getProQuickSearch(0);
		},
		getProQuickSearch: async function (page) {
			var self = this;
			self.proSearch.pageable.page = page;
			self.proSearch.radioProCode = null;
			var url = _.toPageUrl('', self.proSearch.pageable.page, self.proSearch.pageable);
			const resp = await self.$api.getDocProPageData(self.proSearch.req, url);
			resp.data.totalPages = 10;
		}
	}
};
</script>
