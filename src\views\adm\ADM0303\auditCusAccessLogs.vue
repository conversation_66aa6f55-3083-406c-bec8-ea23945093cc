<template>
	<dynamic-title></dynamic-title>
	<div>
		<div class="row">
			<div class="col-12">
				<div class="card card-form">
					<div class="card-body">
						<div class="row g-3 align-items-end">
							<div class="col-md-3">
								<label class="form-label">查詢分行(單位)</label>
								<select
									name="branCodes"
									id="sltBran"
									class="form-select"
									data-inline="true"
									v-model="branCode"
									@change="getUserMenu()"
								>
									<option value="">全部</option>
									<option v-for="branInfo in branMenu" :value="branInfo.branCode">
										{{ branInfo.branCode }} {{ branInfo.branName }}
									</option>
								</select>
							</div>
							<div class="col-md-6">
								<label class="form-label">查詢日期區間</label>
								<div class="input-group">
									<vue-field
										type="date"
										id="beginDate"
										name="beginDate"
										v-model="logStartDt"
										size="13"
										label="查詢日期起"
										class="form-control"
										maxlength="10"
										:class="{ 'is-invalid': showErrors && errors.logStartDt }"
									>
									</vue-field>

									<span class="input-group-text">~</span>

									<vue-field
										type="date"
										id="endDate"
										name="endDate"
										v-model="logEndDt"
										size="13"
										label="查詢日期迄"
										class="form-control"
										maxlength="10"
										:min="minValidEndDt"
										:class="{ 'is-invalid': showErrors && errors.logEndDt }"
									>
									</vue-field>
								</div>
							</div>
							<div class="col-md-3">
								<label class="form-label">查詢使用者</label>
								<select name="userCode" id="userCode" class="form-select" v-model="userCode">
									<option value="">全部</option>
									<option v-for="user in userMenu" :value="user.userCode">{{ user.userCode }} {{ user.userName }}</option>
								</select>
							</div>
							<div class="col-md-3">
								<label class="form-label">功能項目</label>
								<select name="menuCode" id="progListId" class="form-select" v-model="functionMenuCode">
									<option value="">全部</option>
									<option v-for="item in cusFunctionMenu" :value="item.menuCode">{{ item.menuName }}</option>
								</select>
							</div>
							<div class="col-md-3">
								<label class="form-label">客戶ID/統編</label>
								<input id="idn" name="idn" class="form-control" v-model="idn" />
							</div>
							<div class="col-md-2">
								<button role="button" class="btn btn-primary btn-glow btn-searc=h" @click.prevent="gotoPage(0)">查詢</button>
							</div>
						</div>
					</div>
				</div>

				<div id="searchResult">
					<div class="card card-table">
						<div class="card-header">
							<h4>使用者存取客戶紀錄列表</h4>
							<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
						</div>
						<div class="table-responsive">
							<table class="table table-RWD table-hover text-center">
								<thead>
									<tr>
										<th>日期時間<a class="icon-sort" data-rel="LOG_DT" @click="sort('logDt')"></a></th>
										<th>分行(單位)代號<a class="icon-sort" data-rel="BRAN_CODE" @click="sort('branCode')"></a></th>
										<th>分行(單位)名稱<a class="icon-sort" data-rel="BRAN_NAME" @click="sort('branName')"></a></th>
										<th>行員編號<a class="icon-sort" data-rel="USER_CODE" @click="sort('userCode')"></a></th>
										<th>行員姓名<a class="icon-sort" data-rel="USER_NAME" @click="sort('userName')"></a></th>
										<th class="text-start">功能模組<a class="icon-sort" data-rel="MENU_NAME" @click="sort('menuName')"></a></th>
										<th>IP<a class="icon-sort" data-rel="REMOTE_IP" @click="sort('remoteIp')"></a></th>
										<th>客戶名稱<a class="icon-sort" data-rel="CUS_NAME" @click="sort('cusName')"></a></th>
										<th>客戶ID/統編<a class="icon-sort" data-rel="CUS_CODE" @click="sort('cusCode')"></a></th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="item in pageData.content">
										<td data-th="日期時間">{{ item.logDt }}</td>
										<td data-th="分行(單位)代號">{{ item.branCode }}</td>
										<td data-th="分行(單位)名稱">{{ item.branName }}</td>
										<td data-th="行員編號">{{ item.userCode }}</td>
										<td data-th="行員姓名">{{ item.userName }}</td>
										<td data-th="功能模組" class="text-start">{{ item.menuName }}</td>
										<td data-th="IP">{{ item.remoteIp }}</td>
										<td data-th="客戶名稱">{{ item.cusName }}</td>
										<td data-th="客戶ID/統編">{{ item.cusCode }}</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!--頁面內容 end-->
	</div>
</template>
<script>
import _ from 'lodash';
import moment from 'moment';
import { Field } from 'vee-validate';
import dynamicTitle from '@/views/components/dynamicTitle.vue';
import pagination from '@/views/components/pagination.vue';

export default {
	components: {
		'vue-pagination': pagination,
		'vue-field': Field,
		dynamicTitle
	},
	data: function () {
		return {
			//API 用參數
			buCode: '',
			branCode: '',
			idn: null,
			functionMenuCode: '',
			userCode: '',
			logStartDt: null,
			logEndDt: null,
			//下拉選單
			branMenu: [],
			moduleMenu: [],
			cusFunctionMenu: [],
			userMenu: [],
			//主要顯示資料
			columnDef: {
				logDt: { sortRef: 'log_dt' },
				branCode: { sortRef: 'bran_code' },
				branName: { sortRef: 'bran_name' },
				userCode: { sortRef: 'user_code' },
				userName: { sortRef: 'user_name' },
				remoteId: { sortRef: 'remote_ip' },
				menuName: { sortRef: 'menu_name' },
				cusName: { sortRef: 'cus_name' },
				idnOriginal: { sortRef: 'idn_original' }
			},
			pageData: {},
			pageable: {
				page: 0,
				size: 20,
				sort: 'log_dt',
				direction: 'ASC'
			}
		};
	},
	computed: {
		minValidEndDt() {
			return this.logStartDt ? this.logStartDt : null;
		}
	},
	watch: {
		logStartDt: function (newVal, oldVal) {
			this.showErrors = false;
			if (newVal && this.logEndDt && newVal > this.logEndDt) {
				this.logEndDt = null;
			}
		}
	},
	mounted: function () {
		var self = this;
		self.getBranMenu();
		self.getCusFunctionMenu();
	},
	methods: {
		getBranMenu: function () {
			var self = this;
			self.$api.getAllBranchesMenuApi().then(function (ret) {
				self.branMenu = ret.data;
			});
		},
		getUserMenu: function () {
			var self = this;
			if (_.isBlank(self.branMenu)) {
				return;
			}

			self.$api
				.getUserMenuApi({
					branCode: self.branCode
				})
				.then(function (ret) {
					self.userMenu = ret.data;
				});
		},
		getCusFunctionMenu: function () {
			var self = this;
			self.$api.getCusFunctionMenuApi().then(function (ret) {
				self.cusFunctionMenu = ret.data;
			});
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: function (page) {
			var self = this;
			var url = _.toPageUrl('', page, self.pageable);
			// 查詢迄日+1, 若起迄日為同一天, 才能查到當日資料
			var logEndDt = !_.isEmpty(self.logEndDt) ? moment(self.logEndDt).add(1, 'days') : self.logEndDt;

			self.$api
				.getUserAccessCusLogsApi(
					{
						branCode: self.branCode,
						userCode: self.userCode,
						cusCode: self.idn,
						menuCode: self.functionMenuCode,
						logStartDt: _.formatDate(self.logStartDt),
						logEndDt: _.formatDate(logEndDt)
					},
					url
				)
				.then(function (ret) {
					self.pageData = ret.data;
				});
		},
		sort: function (columnName) {
			if (this.pageable.sort !== this.columnDef[columnName].sortRef) {
				this.pageable.sort = this.columnDef[columnName].sortRef;
				this.pageable.direction = 'DESC';
			} else {
				this.pageable.direction = this.pageable.direction == 'ASC' ? 'DESC' : 'ASC';
			}
			this.gotoPage(0);
		}
	}
};
</script>
