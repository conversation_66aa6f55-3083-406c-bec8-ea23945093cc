<template>
	<div>
		<h4>其他對應代碼</h4>
		<table class="table table-bordered">
			<tbody>
				<tr v-for="(item, i) in fundCodes" v-if="i < 1">
					<th width="45%">{{ item.name }}</th>
					<td width="55%">{{ item.code }}</td>
				</tr>
			</tbody>
			<tfoot v-if="fundCodes && fundCodes.length > 1">
				<td colspan="2">
					<a href="#fundCode" @click.prevent="openModal" class="pull-right">...更多</a>
				</td>
			</tfoot>
		</table>
		<vue-modal :is-open="isOpenModal" :before-close="closeModal">
			<template v-slot:content="props">
				<div class="modal-dialog">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" @click.prevent="props.close()" aria-hidden="true"></button>
							<h4 class="modal-title">其他對應代碼</h4>
						</div>
						<div class="modal-body dataTables_wrapper">
							<table class="table table-bordered">
								<tr v-for="(item, i) in fundCodes">
									<th width="50%">{{ item.name }}</th>
									<td width="50%">{{ item.code }}</td>
								</tr>
							</table>
						</div>
						<div class="modal-footer">
							<button @click.prevent="props.close()" type="button" class="btn btn-default">關閉視窗</button>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
	</div>
</template>
<script>
export default {
	props: {
		fundCode: String
	},
	data: function () {
		return {
			isOpenModal: null,
			fundCodes: []
		};
	},
	watch: {
		fundCode: {
			handler: function (newVal, oldVal) {
				this.getFundCodes();
			}
		}
	},
	computed: {},
	mounted: function () {},
	methods: {
		openModal: function () {
			this.isOpenModal = true;
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		getFundCodes: async function () {
			var self = this;
			const ret = await this.$api.getFundCodeApi({
				fundCode
			});
			self.fundCodes = ret.data;
		}
	}
};
</script>
