<template>
	<vue-form v-slot="{ errors }" ref="userConditionForm">
		<div class="form-group gap-1 align-items-start" :class="'col-lg-' + colWidth">
			<div :class="[{ 'form-group': isInline }, `col-lg-${fieldWidth}`]" v-if="isAreaShow">
				<label class="form-label" :class="{ 'tx-require': isAreaRequired }">{{ areaLabel }}</label>
				<div class="d-flex flex-column wd-100p">
					<vue-field
						name="area"
						as="select"
						:rules="isAreaRequired ? 'required' : ''"
						:class="{ 'is-invalid': errors['area'] }"
						:label="areaLabel"
						id="area"
						class="form-select"
						v-model="selectedArea"
					>
						<option value="">{{ isAreaRequired ? '請選擇' : '全部' }}</option>
						<option :value="area.branCode + '_' + area.branName" v-for="area in areaMenu" :key="area.branCode">
							{{ area.branName }}
						</option>
					</vue-field>
					<div class="text-danger" style="height: 0">{{ errors['area'] }}</div>
				</div>
			</div>
			<div :class="[{ 'form-group': isInline }, `col-lg-${fieldWidth}`]" v-if="isBranShow">
				<label class="form-label" :class="{ 'tx-require': isBranRequired }">{{ branLabel }}</label>
				<div class="d-flex flex-column wd-100p">
					<vue-field
						as="select"
						id="bran"
						name="bran"
						:class="{ 'is-invalid': errors['bran'] }"
						:label="branLabel"
						:rules="isBranRequired ? 'required' : ''"
						class="form-select"
						v-model="selectedBran"
					>
						<option value="">{{ isBranRequired ? '請選擇' : '全部' }}</option>
						<option :value="bran.branCode + '_' + bran.branName" v-for="bran in branMenu" :key="bran.branCode">
							{{ bran.branCode }} {{ bran.branName }}
						</option>
					</vue-field>
					<div class="text-danger" style="height: 0">{{ errors['bran'] }}</div>
				</div>
			</div>
			<div :class="[{ 'form-group': isInline }, `col-lg-${fieldWidth}`]" v-if="isUserShow">
				<label class="form-label" :class="{ 'tx-require': isUserRequired }">{{ userLabel }}</label>
				<div class="d-flex flex-column wd-100p">
					<vue-field
						as="select"
						id="user"
						name="user"
						:class="{ 'is-invalid': errors['user'] }"
						:label="userLabel"
						:rules="isUserRequired ? 'required' : ''"
						class="form-select"
						v-model="selectedUser"
					>
						<option value="">{{ isUserRequired ? '請選擇' : '全部' }}</option>
						<option :value="user.userCode + '_' + user.userName" v-for="user in userMenu" :key="user.userCode">
							{{ user.userName }}
						</option>
					</vue-field>
					<div class="text-danger" style="height: 0">{{ errors['user'] }}</div>
				</div>
			</div>
		</div>
	</vue-form>
</template>
<script>
import { Field, Form } from 'vee-validate';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	props: {
		colWidth: {
			type: Number,
			default: 9
		},
		isRequired: {
			type: [Boolean, String],
			default: false
		},
		areaLabel: {
			type: String,
			default: '區'
		},
		branLabel: {
			type: String,
			default: '分行'
		},
		userLabel: {
			type: String,
			default: '理專'
		},
		isInline: {
			type: Boolean,
			default: false
		},
		customCondition: {
			type: Object,
			default: {
				areaCode: '',
				branCode: '',
				userCode: ''
			}
		},
		show: {
			type: String,
			default: 'user'
		},
		errors: {
			type: Object,
			default: {}
		}
	},
	computed: {
		fieldWidth: function () {
			const fieldCnt = [this.isAreaShow, this.isBranShow, this.isUserShow].filter((isShow) => isShow).length;
			return Math.floor(this.colWidth / fieldCnt);
		}
	},
	data: function () {
		return {
			selectedArea: '',
			selectedBran: '',
			selectedUser: '',
			areaMenu: [],
			branMenu: [],
			userMenu: [],

			isAreaRequired: false,
			isBranRequired: false,
			isUserRequired: false,

			firstLoad: true,
			firstCondition: {
				branMenu: [],
				userMenu: [],
				selectedArea: '',
				selectedBran: '',
				selectedUser: ''
			},

			isLoadCustom: false,
			watchEnabled: true,

			isAreaShow: true,
			isBranShow: true,
			isUserShow: true
		};
	},
	watch: {
		customCondition: {
			handler: function (newVal, oldVal) {
				var self = this;
				if (newVal && !self.isLoadCustom && !self.firstLoad) {
					self.loadCustomCondition();
				}
			}
		},
		isRequired: function (newVal) {
			this.isAreaRequired = false;
			this.isBranRequired = false;
			this.isUserRequired = false;
			if ([true, 'user'].includes(newVal)) {
				this.isAreaRequired = true;
				this.isBranRequired = true;
				this.isUserRequired = true;
			} else if (newVal === 'bran') {
				this.isAreaRequired = true;
				this.isBranRequired = true;
			} else if (newVal === 'area') {
				this.isAreaRequired = true;
			}
		},
		show: function (newVal, oldVal) {
			if (newVal === 'user') {
				this.isAreaShow = true;
				this.isBranShow = true;
				this.isUserShow = true;
			} else if (newVal === 'bran') {
				this.isAreaShow = true;
				this.isBranShow = true;
				this.isUserShow = false;
			} else if (newVal === 'area') {
				this.isAreaShow = true;
				this.isBranShow = false;
				this.isUserShow = false;
			}
		},
		firstLoad: function (newVal, oldVal) {
			if (!newVal && !this.isLoadCustom) {
				this.loadCustomCondition();
			}
		},
		selectedArea: function (newVal) {
			var self = this;
			const [branCode, branName] = newVal.split('_');
			if (self.watchEnabled) {
				self.branMenu = [];
				self.userMenu = [];
				self.selectedBran = '';
				self.selectedUser = '';
				if (newVal) {
					self.getBranMenu(branCode);
				}
			}
			self.$emit('change-area', branCode);
			self.$emit('change-area-name', branName || '');
		},
		selectedBran: function (newVal) {
			var self = this;
			const [branCode, branName] = newVal.split('_');
			if (self.watchEnabled) {
				self.userMenu = [];
				self.selectedUser = '';
				if (newVal) {
					self.getUserMenu(branCode);
				}
			}
			self.$emit('change-bran', branCode);
			self.$emit('change-bran-name', branName || '');
		},
		selectedUser: function (newVal) {
			var self = this;
			const [userCode, userName] = newVal.split('_');
			self.$emit('change-user', userCode);
			self.$emit('change-user-name', userName || '');
		}
	},
	mounted: function () {
		var self = this;
		self.getAreaMenu();
		if ([true, 'user'].includes(self.isRequired)) {
			self.isAreaRequired = true;
			self.isBranRequired = true;
			self.isUserRequired = true;
		} else if (self.isRequired === 'bran') {
			self.isAreaRequired = true;
			self.isBranRequired = true;
		} else if (self.isRequired === 'area') {
			self.isAreaRequired = true;
		}

		if (self.show === 'user') {
			self.isAreaShow = true;
			self.isBranShow = true;
			self.isUserShow = true;
		} else if (self.show === 'bran') {
			self.isAreaShow = true;
			self.isBranShow = true;
			self.isUserShow = false;
		} else if (self.show === 'area') {
			self.isAreaShow = true;
			self.isBranShow = false;
			self.isUserShow = false;
		}
	},
	methods: {
		//下拉選單
		getAreaMenu: async function () {
			var self = this;
			const ret = await self.$api.getAreaMenu();
			self.areaMenu = ret.data;
			if (self.areaMenu.length == 1) {
				self.selectedArea = self.areaMenu[0].branCode + '_' + self.areaMenu[0].branName;
			} else {
				if (self.firstLoad) {
					self.$emit('loaded');
					self.firstLoad = false;
				}
			}
		},
		getBranMenu: async function (branCode) {
			var self = this;
			const ret = await self.$api.getBranchesApi({
				minorCode: branCode
			});
			self.branMenu = ret.data;
			if (self.branMenu.length == 1) {
				self.selectedBran = self.branMenu[0].branCode + '_' + self.branMenu[0].branName;
			} else {
				if (self.firstLoad) {
					self.$nextTick(() => {
						self.$emit('loaded');
						self.firstLoad = false;
						self.firstCondition.branMenu = self.branMenu;
						self.firstCondition.selectedArea = self.selectedArea;
					});
				}
			}
		},
		getUserMenu: async function (branCode) {
			var self = this;
			const ret = await self.$api.getBranEmployeeApi({
				branCode: branCode
			});
			self.userMenu = ret.data;
			if (self.userMenu.length == 1) {
				self.selectedUser = self.userMenu[0].userCode + '_' + self.userMenu[0].userName;
			}
			if (self.firstLoad) {
				self.$nextTick(() => {
					self.$emit('loaded');
					self.firstLoad = false;
					self.firstCondition.branMenu = self.branMenu;
					self.firstCondition.userMenu = self.userMenu;
					self.firstCondition.selectedArea = self.selectedArea;
					self.firstCondition.selectedBran = self.selectedBran;
					self.firstCondition.selectedUser = self.selectedUser;
				});
			}
		},
		validate: async function () {
			return await this.$refs.userConditionForm.validate();
		},
		reset: function () {
			this.watchEnabled = false;
			this.selectedArea = this.firstCondition.selectedArea;
			this.branMenu = this.firstCondition.branMenu;
			this.selectedBran = this.firstCondition.selectedBran;
			this.userMenu = this.firstCondition.userMenu;
			this.selectedUser = this.firstCondition.selectedUser;
			this.$nextTick(() => {
				this.watchEnabled = true;
			});
		},
		loadCustomCondition: async function () {
			if (this.customCondition.areaCode) {
				this.watchEnabled = false;
				const targetArea = this.areaMenu.find((item) => item.branCode == this.customCondition.areaCode);
				if (targetArea) {
					const targetAreaFormat = targetArea.branCode + '_' + targetArea.branName;
					if (targetAreaFormat != this.selectedArea) {
						this.selectedArea = targetArea.branCode + '_' + targetArea.branName;
						const branMenuRes = await this.$api.getBranchesApi({
							minorCode: this.customCondition.areaCode
						});
						this.branMenu = branMenuRes.data;
					}
					if (this.customCondition.branCode) {
						const targetBran = this.branMenu.find((item) => item.branCode == this.customCondition.branCode);
						if (targetBran) {
							const targetBranFormat = targetBran.branCode + '_' + targetBran.branName;
							if (targetBranFormat != this.selectedBran) {
								this.selectedBran = targetBran.branCode + '_' + targetBran.branName;
								const userMenuRes = await this.$bi.getUserMenu({
									branCode: this.customCondition.branCode
								});
								this.userMenu = userMenuRes.data;
							}
							if (this.customCondition.userCode) {
								const targetUser = this.userMenu.find((item) => item.userCode == this.customCondition.userCode);
								if (targetUser) {
									this.selectedUser = targetUser.userCode + '_' + targetUser.userName;
								}
							}
						}
					}
				}
			} else {
				return;
			}
			this.$nextTick(() => {
				this.$emit('custom-loaded');
				this.watchEnabled = true;
			});
		}
	}
};
</script>
