<template>
	<div class="modal fade show" ref="componentListModal" aria-modal="true" tabindex="-1">
		<div class="modal-dialog modal-dialog-centered">
			<div class="modal-content">
				<div class="modal-header">
					<h4 class="modal-title">元件選擇</h4>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<el-checkbox-group v-model="checkList">
						<el-checkbox v-for="(item, index) in COMPONENT_LIST" :key="index" :label="item.Name" :value="item.Name"></el-checkbox>
					</el-checkbox-group>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
					<button type="button" class="btn btn-primary" @click="handleConfirm">確認</button>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { Modal } from 'bootstrap';
import { COMPONENT_LIST } from '@/views/layoutComponent/layoutData.js';

export default {
	props: {
		components: {
			type: Array,
			default: () => []
		}
	},
	data: function () {
		return {
			componentListModal: {},
			selectedLayout: null,
			checkList: [],
			COMPONENT_LIST
		};
	},
	created() {
		this.checkList = [...this.$props.components];
	},
	emits: ['components'],
	mounted: function () {
		// 確保在 Vue 完成渲染後再初始化 Modal
		this.$nextTick(() => {
			this.componentListModal = new Modal(this.$refs.componentListModal); // 在 Vue 渲染完成後初始化
		});
	},
	methods: {
		show: function () {
			this.componentListModal.show();
		},
		select: function (layout) {
			this.selectedLayout = layout;
		},
		handleConfirm: function () {
			this.$emit('components', this.checkList);
			this.checkList = [];
			this.componentListModal.hide();
		}
	},
	watch: {
		components: {
			immediate: true,
			handler(newVal) {
				this.checkList = [...newVal];
			}
		}
	}
};
</script>
