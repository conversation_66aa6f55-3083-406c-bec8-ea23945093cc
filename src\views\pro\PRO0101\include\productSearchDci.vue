<template>
	<!--頁面內容 組合式商品dci start-->
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<div class="tab-nav-tabs my-3">
				<ul class="nav nav-tabs nav-justified">
					<li class="nav-item">
						<a class="nav-link" :class="{ active: activeTab == 'common' }" data-bs-toggle="tab" @click="changeTab('common')">一般篩選</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" :class="{ active: activeTab == 'fast' }" data-bs-toggle="tab" @click="changeTab('fast')">快速篩選</a>
					</li>
				</ul>

				<div class="tab-content">
					<div class="tab-pane fade show" id="SectionA" :class="{ 'active show': activeTab == 'common' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
								<h4>查詢條件</h4>
								<span class="tx-square-bracket">為必填欄位</span>
							</div>
							<div class="collapse show" id="collapseListGroup1">
								<div class="card-body">
									<form>
										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 商品代號 </label>
												<input
													class="form-control"
													id="prod_bank_pro_code"
													maxlength="20"
													size="25"
													type="text"
													v-model="bankProCode"
												/>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 商品名稱</label>
												<input
													class="form-control"
													id="prod_pro_name"
													maxlength="20"
													v-model="proName"
													size="45"
													type="text"
												/>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 計價幣別</label>
												<select
													class="selectpicker form-control"
													id="curMenuDci"
													multiple
													title="請選擇幣別"
													v-model="curObjs"
													data-style="btn-white"
												>
													<option value="">全部</option>
													<option v-for="(item, index) in curOption" :key="index" :value="item.value">
														{{ $filters.defaultValue(item.name, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 風險等級 </label>
												<select class="form-select" id="riskCode" name="riskCode" v-model="riskCode">
													<option value="">全部</option>
													<option v-for="item in riskMenu" :value="item.riskCode">
														{{ $filters.defaultValue(item.riskName, '--') }}
													</option>
												</select>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">投資標的</label>
												<select class="form-select" id="linkTarget" name="linkTarget" v-model="linkTarget">
													<option value="">全部</option>
													<option v-for="item in linkTargetMenu" :value="item.codeValue">
														{{ $filters.defaultValue(item.codeName, '--') }}
													</option>
												</select>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">商品類型</label>
												<div class="form-check-group">
													<div class="form-check form-check-inline">
														<input
															class="form-check-input"
															checked
															id="imi"
															name="guaranteeStatus"
															type="radio"
															value=""
															v-model="guaranteeStatus"
														/>
														<label class="form-check-label" for="imi">全部</label>
													</div>
													<div class="form-check form-check-inline" v-for="item in dcdGuaranteeTypeMenu">
														<input
															class="form-check-input"
															:id="'id-' + item.codeValue"
															name="guaranteeStatus"
															v-model="guaranteeStatus"
															type="radio"
															:value="item.codeValue"
														/>
														<label class="form-check-label" :for="'id-' + item.codeValue">{{
															$filters.defaultValue(item.codeName, '--')
														}}</label>
													</div>
												</div>
											</div>

											<div class="form-group col-12 col-lg-6">
												<label class="form-label">募集起日</label>
												<div class="input-group">
													<input
														class="form-control"
														id="prod_chinese_name"
														maxlength="20"
														name="prod_chinese_name"
														v-model="intStartDtBegin"
														size="10"
														type="date"
														value=""
													/>
													<div class="input-group-text">~</div>
													<input
														class="form-control"
														id="prod_chinese_name"
														maxlength="20"
														name="prod_chinese_name"
														v-model="intStartDtEnd"
														size="10"
														type="date"
														value=""
													/>
												</div>
											</div>
											<div class="form-group col-12 col-lg-6">
												<label class="form-label">募集迄日</label>
												<div class="input-group">
													<input
														class="form-control"
														id="prod_chinese_name"
														maxlength="20"
														name="prod_chinese_name"
														v-model="intEndDtBegin"
														size="10"
														type="date"
														value=""
													/>
													<div class="input-group-text">~</div>
													<input
														class="form-control"
														id="prod_chinese_name"
														maxlength="20"
														name="prod_chinese_name"
														v-model="intEndDtEnd"
														size="10"
														type="date"
														value=""
													/>
												</div>
											</div>
										</div>
									</form>
								</div>
								<div class="form-footer">
									<button class="btn btn-primary" @click.prevent="gotoPage(0)">查詢</button>
								</div>
							</div>
						</div>
					</div>

					<div class="tab-pane fade" id="SectionB" :class="{ 'active show': activeTab == 'fast' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2">
								<h4>查詢條件</h4>
								<span class="tx-square-bracket">為必填欄位</span>
							</div>
							<div class="collapse show" id="collapseListGroup2">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-12">
											<label class="form-label tx-require"> 篩選條件 </label>

											<div class="form-check-group">
												<div
													class="form-check form-check-inline"
													v-for="item in dciFastMenu"
													@change="fastChange(item.codeValue)"
												>
													<input
														class="form-check-input"
														:id="'fast' + item.codeValue"
														name="fastCode"
														v-model="fastCode"
														:value="item.codeValue"
														type="radio"
													/>
													<label class="form-check-label" :for="'fast' + item.codeValue">{{
														$filters.defaultValue(item.codeName, '--')
													}}</label>
												</div>
											</div>
										</div>
										<div class="form-group col-12 col-lg-6" id="rangeFixedTr" style="display: none">
											<label class="form-label tx-require"> 顯示區間</label>

											<select class="form-select" id="prod_protype_code" v-model="timeRange">
												<option v-for="item in timeRangeMenu" :value="item">
													{{ $filters.defaultValue(item.termName, '--') }}
												</option>
											</select>
										</div>

										<div class="form-group col-12 col-lg-6" id="maxRowIdTr" style="display: none">
											<label class="form-label tx-require">顯示資料筆數</label>
											<select class="form-select" id="maxRowId" v-model="rowNumber">
												<option value="">全部</option>
												<option v-for="item in rowNumerMenu" :value="item.rangeFixed">
													{{ $filters.defaultValue(item.termName, '--') }}
												</option>
											</select>
										</div>
									</div>

									<div class="form-footer">
										<button class="btn btn-primary" @click.prevent="gotoFastPage(0)">查詢</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div id="searchResult" v-if="pageData.content.length > 0">
				<div class="card card-table">
					<div class="card-header">
						<h4>查詢結果</h4>
						<div style="display: flex">
							<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
							<button type="button" class="btn btn-info ms-2" @click="performancesCompareModelHandler()">績效比較圖</button>
							<vue-modal :is-open="isOpenCompareModal" :before-close="isOpenCompareModal = false">
								<template v-slot:content="props">
									<vue-performances-compare-modal
										:close="props.close"
										ref="performancesCompareModalRef"
										id="performancesCompareModal"
									></vue-performances-compare-modal>
								</template>
							</vue-modal>
						</div>
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-bordered text-center">
							<thead>
								<tr>
									<th class="wd-100 text-start">加入比較</th>
									<th>商品代號</th>
									<th class="10% text-start">商品中文名稱</th>
									<th>計價幣別</th>
									<th>風險等級</th>
									<th>投資標的</th>
									<th>商品類型</th>
									<th>評估日期<br />評估損益</th>
									<th>募集起日</th>
									<th>募集迄日</th>
									<th class="text-center" width="120">執行</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="(item, index) in pageData.content">
									<td data-th="加入比較" class="text-start text-center">
										<input
											class="form-check-input text-center"
											v-model="selectedItems[item.proCode]"
											:id="'id-' + item.bankProCode"
											type="checkbox"
										/>
										<label class="form-check-label" :for="'id-' + item.bankProCode"></label>
									</td>
									<td data-th="商品代號">
										<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
									</td>
									<td class="text-start" data-th="商品中文名稱">
										<span>
											<a class="tx-link" @click="dciModalHandler(item.proCode, item.pfcatCode)">{{
												$filters.defaultValue(item.proName, '--')
											}}</a>
										</span>
									</td>
									<td class="text-end" data-th="計價幣別">
										<span>{{ $filters.defaultValue(item.curCode, '--') }}</span>
									</td>
									<td class="text-end" data-th="風險等級">
										<span>{{ $filters.defaultValue(item.riskName, '--') }}</span>
									</td>
									<td class="text-end" data-th="投資標的">
										<span>{{ $filters.defaultValue(item.linkTargetName, '--') }}</span>
									</td>
									<td class="text-end" data-th="商品類型">
										<span>{{ $filters.defaultValue(item.guaranteeStatusName, '--') }}</span>
									</td>
									<td class="text-end" data-th="評估日期<br>評估損益">
										<span
											>{{ $filters.defaultValue(item.priceDt ? item.priceDt : '--', '--') }}<br />{{
												$filters.formatNumber(item.aprice, '--')
											}}</span
										>
									</td>
									<td data-th="募集起日">
										<span>{{ $filters.defaultValue(item.intStartDt, '--') }}</span>
									</td>
									<td data-th="募集迄日">
										<span>{{ $filters.defaultValue(item.intEndDt, '--') }}</span>
									</td>
									<td class="text-center" data-th="執行">
										<button
											v-if="activeTab === 'fast' && fastCode === '06'"
											type="button"
											class="btn btn-primary"
											title="移除我的最愛"
											@click="remove(item.proCode)"
										>
											移除最愛
										</button>
										<button
											v-else
											type="button"
											class="btn btn-dark btn-icon"
											data-bs-toggle="tooltip"
											title="加入我的最愛"
											@click="favoritesHandler(item.proCode, item.pfcatCode)"
										>
											<i class="bi bi-heart text-danger"></i>
										</button>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="tx-note">
					<ol>
						<li><span>資料日期：</span></li>
						<li><span>商品是否可申購以交易系統為主</span></li>
					</ol>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import pagination from '@/views/components/pagination.vue';
import vuePerformancesCompareModal from './performancesCompareModal.vue';
import vueModal from '@/views/components/model.vue';
import _ from 'lodash';

export default {
	components: {
		'vue-pagination': pagination,
		vuePerformancesCompareModal,
		vueModal
	},
	props: {
		dciModalHandler: Function,
		favoritesHandler: Function,
		curOption: Array,
		riskMenu: Array
	},
	data: function () {
		return {
			activeTab: 'common',

			bankProCode: null,
			proName: null,
			curObjs: [], // 計價幣別 陣列物件
			riskCode: '', //風險等級
			linkTarget: '', // 投資標的
			guaranteeStatus: '', // 商品類型
			intStartDtBegin: null, // 募集起日start
			intStartDtEnd: null, // 募集起日end
			intEndDtBegin: null, // 募集迄日start
			intEndDtEnd: null, //募集迄日end

			fastCode: '05', // 快速 篩選條件
			timeRange: null, // 快速 顯示區間
			rowNumber: null, // 快速 顯示資料筆數

			linkTargetMenu: [], // 投資標的選項
			dcdGuaranteeTypeMenu: [], // 商品類型選項

			dciFastMenu: [], // 快速篩選條件
			timeRangeMenu: [], // 顯示區間
			rowNumerMenu: [], // 顯示資料筆數
			proCodes: [], // 商品代碼
			selectedItems: {}, // 加入比較選項

			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: ['PRO_CODE'],
				direction: 'ASC'
			},
			isOpenCompareModal: false
		};
	},
	watch: {
		selectedItems: {
			handler(newValues) {
				this.proCodes = Object.keys(newValues).filter((proCode) => newValues[proCode]);
			},
			deep: true
		},
		activeTab(newVal, oldVal) {
			var self = this;
			self.fastCode = '05';
			self.fastChange(self.fastCode);
		},
		curObjs(newVal, oldVal) {
			var self = this;
			if (newVal[0] !== oldVal[0]) {
				if (newVal[0] === '') {
					$('#curMenuDci').selectpicker('selectAll');
				} else if (oldVal[0] === '' && newVal[0] !== '') {
					$('#curMenuDci').selectpicker('deselectAll');
				}
			}
		}
	},
	mounted: function () {
		var self = this;
		$('#curMenuDci').selectpicker('refresh');
		self.getDcdLinkTargetMenu(); // 取得投資標的選項
		self.getDcdGuaranteeTypeMenu(); // 取得商品類型選項
		self.getdciFastMenu(); // 取得快速篩選條件選項
		self.getTimeRangeMenu(); // 取得顯示區間選項
		self.getRowNumerMenu(); // 取得顯示資料筆數選項
		self.fastChange(self.fastCode);
	},
	methods: {
		// 條件Tab切換
		changeTab: function (tabName) {
			var self = this;
			self.activeTab = tabName;
			self.pageData = {
				// 清空查詢資料
				content: []
			};
		},
		// 取得投資標的選項
		async getDcdLinkTargetMenu() {
			const ret = await this.$api.getAdmCodeDetail({
				codeType: 'DCD_LINK_TARGET'
			});
			this.linkTargetMenu = ret.data;
		},
		// 取得商品類型選項
		async getDcdGuaranteeTypeMenu() {
			const ret = await this.$api.getAdmCodeDetail({
				codeType: 'DCD_GUARANTEE_TYPE'
			});
			this.dcdGuaranteeTypeMenu = ret.data;
		},

		// 取得快速篩選條件選項
		async getdciFastMenu() {
			const ret = await this.$api.getDciFastFilterMenuApi();
			this.dciFastMenu = ret.data;
		},

		// 取得顯示區間
		async getTimeRangeMenu() {
			const ret = await this.$api.getTimeRangeMenuApi();
			this.timeRangeMenu = ret.data;
		},
		// 取得顯示資料筆數
		async getRowNumerMenu() {
			const ret = await this.$api.getRowNumerMenuApi();
			this.rowNumerMenu = ret.data;
		},
		fastChange(fastCode) {
			var self = this;
			self.timeRange = {}; // 快速 顯示區間
			self.rowNumber = ''; // 快速 顯示資料筆數
			self.pageData = {
				// 清空查詢資料
				content: []
			};
			// 快速查詢切換
			if (fastCode === '05') {
				// 超人氣
				$('#rangeFixedTr').show();
				$('#maxRowIdTr').show();
				self.timeRange = self.timeRangeMenu[0];
			} else {
				$('#maxRowIdTr').hide();
				$('#rangeFixedTr').hide();
			}
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		async getPageData(page) {
			const queryString = `?page=${page || this.pageable.page}&size=${this.pageable.size}&sort=${this.pageable.sort},${this.pageable.direction}`;
			const ret = await this.$api.getDciProductsApi(
				{
					bankProCode: this.bankProCode, // 商品代號
					proName: this.proName, //商品名稱
					curCodes: this.curObjs, // 計價幣別 陣列物件
					riskCode: this.riskCode, //風險等級
					linkTarget: this.linkTarget, //投資標的
					guaranteeStatus: this.guaranteeStatus, //商品類型
					buyMin: this.mininvAmt, // 最低申購面額
					invAccAmt: this.mininvAccAmt, //累加面額
					intStartDtBegin: this.intStartDtBegin, // 募集起日start
					intStartDtEnd: this.intStartDtEnd, // 募集起日end
					intEndDtBegin: this.intEndDtBegin, // 募集迄日start
					intEndDtEnd: this.intEndDtEnd //募集迄日end
				},
				queryString
			);
			this.pageData = ret.data;
			this.pageData.content.forEach(() => {
				this.checkboxs.push(false);
			});
		},
		//執行績效比較圖
		performancesCompareModelHandler: function () {
			var self = this;
			if (self.proCodes.length > 0) {
				if (self.proCodes.length > 6) {
					thi.$bi.alert('最多加入6筆');
				} else {
					this.$refs.performancesCompareModalRef.comparePropItem(self.proCodes, 'dci');
					this.isOpenCompareModal = true;
				}
			} else {
				thi.$bi.alert('至少要勾選一項商品');
			}
		},
		gotoFastPage: function (page) {
			this.pageable.page = page;
			this.getFastPageData(page);
		},
		async getFastPageData(page) {
			const queryString = `?page=${page || this.pageable.page}&size=${this.pageable.size}&sort=${this.pageable.sort},${this.pageable.direction}`;

			const ret = await this.$api.getDciProductsFilterQueryApi(
				{
					filterCodeValue: this.fastCode,
					timeRangeType: this.timeRange.rangeType,
					timeRangeFixed: this.timeRange.rangeFixed,
					rowNumberFixed: this.rowNumber
				},
				queryString
			);

			this.pageData = ret.data;
			this.pageData.content.forEach(() => {
				this.checkboxs.push(false);
			});
		},
		// 刪除我的最愛
		async remove(proCode) {
			await this.$api.deleteFavoriteApi({ proCode });
			this.$bi.alert('刪除成功');
			this.checkboxs = [];
			this.proCodes = [];
			this.gotoFastPage(0);
		}
	} // methods end
};
</script>
<style scoped>
.dropdown.bootstrap-select {
	min-width: 0;
}
</style>
