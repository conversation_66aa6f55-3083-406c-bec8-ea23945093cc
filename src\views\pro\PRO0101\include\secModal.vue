<template>
	<!-- Modal 9 兼營證券自營商品-->
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">兼營證券自營商品</h4>
				<button type="button" class="btn-close" @click.prevent="close()" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="card card-prointro shadow-none mb-3">
					<div class="pro_left">
						<div class="pro_name">
							<div class="avatar avatar-md avatar-structure"></div>
							<h4>
								<span>商品名稱</span> <br />{{ $filters.defaultValue(proInfo.proName, '--') }} <br /><span class="tx-black">{{
									$filters.defaultValue(proInfo.proEName, '--')
								}}</span>
							</h4>
						</div>
						<h4 class="pro_value">
							<span>參考贖回價格/參考申購價格</span>
							<br /><span>{{ $filters.defaultValue(proInfo.sprice, '--') }}</span> <br /><span>{{
								$filters.defaultValue(proInfo.bprice, '--')
							}}</span>
						</h4>
					</div>
					<div class="pro_class">
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品代碼</span> <br />{{ $filters.defaultValue(proInfo.bankProCode, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6>
								<span>資產類別 <br /></span>{{ $filters.defaultValue(proInfo.assetcatName, '--') }}
							</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品主類</span> <br />{{ $filters.defaultValue(proInfo.pfcatName, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品次類</span><br />{{ $filters.defaultValue(proInfo.proTypeName, '--') }}</h6>
						</div>
					</div>
				</div>
				<div class="tab-nav-pills">
					<ul class="nav nav-pills justify-content-center" role="tablist">
						<li class="nav-item"><a class="nav-link active" href="#Sectionovers01" data-bs-toggle="pill">商品基本資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionovers02" data-bs-toggle="pill">商品共同資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionovers03" data-bs-toggle="pill">商品附加資料</a></li>
						<li class="nav-item"><a class="nav-link datainfo" href="#Sectionovers04" data-bs-toggle="pill">價格分析</a></li>
						<li class="nav-item"><a class="nav-link datainfo" href="#Sectionovers05" data-bs-toggle="pill">績效表現</a></li>
					</ul>

					<div class="tab-content">
						<div class="tab-pane show active" id="Sectionovers01">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>商品資訊</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>風險等級</th>
											<td class="wd-30p">{{ $filters.defaultValue(proInfo.secInfo.riskName, '--') }}</td>
											<th>計價幣別</th>
											<td class="wd-30p">{{ $filters.defaultValue(proInfo.secInfo.curCode, '--') }}</td>
										</tr>
										<tr>
											<th>到期日</th>
											<td>{{ $filters.defaultValue(proInfo.secInfo.expireDt, '--') }}</td>
											<th>配息類別</th>
											<td>{{ $filters.defaultValue(proInfo.secInfo.intFreqUnittypeName, '--') }}</td>
										</tr>
										<tr>
											<th>票面利率(%)</th>
											<td>{{ $filters.defaultValue(proInfo.secInfo.stockRate, '--') }}</td>
											<th>國際代碼</th>
											<td>{{ $filters.defaultValue(proInfo.secInfo.isinCode, '--') }}</td>
										</tr>
										<tr>
											<th>發行機構名稱/發行人名稱</th>
											<td>{{ $filters.defaultValue(proInfo.secInfo.issuerName, '--') }}</td>
											<th>商品評等(穆迪)</th>
											<td>{{ $filters.defaultValue(proInfo.secInfo.mlongRatingLvl, '--') }}</td>
										</tr>
										<tr>
											<th>商品評等(標準普爾)</th>
											<td>{{ $filters.defaultValue(proInfo.secInfo.slongRatingLvl, '--') }}</td>
											<th>商品評等(惠譽)</th>
											<td>{{ $filters.defaultValue(proInfo.secInfo.flongRatingLvl, '--') }}</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>投資金額限制</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>外幣最低投資金額</th>
											<td class="wd-30p">{{ $filters.defaultValue(proInfo.secInfo.mininvFcAmt, '--') }}</td>
											<th>外幣最低累加金額</th>
											<td class="wd-30p">{{ $filters.defaultValue(proInfo.secInfo.mininvFcAccAmt, '--') }}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionovers02">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>銷售相關資料</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>客戶類別</span></th>
											<td class="wd-30p">
												<span>{{ $filters.defaultValue(proInfo.allYn, '--') }}</span>
											</td>
											<th><span>銷售對象</span></th>
											<td class="wd-30p">{{ $filters.defaultValue(proInfo.targetCusBu, '--') }}</td>
										</tr>
										<tr>
											<th>是否開放申購</th>
											<td>{{ $filters.defaultValue(proInfo.buyYn, '--') }}</td>
											<th>是否開放贖回</th>
											<td>{{ $filters.defaultValue(proInfo.sellYn, '--') }}</td>
										</tr>
										<tr>
											<th><span>波動類型</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.volatilityType, '--') }}</span>
											</td>
											<th><span>配息頻率</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.intFreqUnitype, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>理財需求</span></th>
											<td colspan="3">
												<div class="form-check form-check-inline" v-for="item in finReqCodeMenu">
													<input
														class="form-check-input"
														name="finReqCodes"
														disabled
														id="c1"
														v-model="finReqCodes"
														:value="item.codeValue"
														type="checkbox"
													/>
													<label class="form-check-label" for="c1">{{ $filters.defaultValue(item.codeName, '--') }}</label>
												</div>
											</td>
										</tr>
										<tr>
											<th>商品標籤</th>
											<td disabled="disabled" colspan="3">
												{{ $filters.defaultValue(proInfo.selprocatNames, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionovers03">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>其他設定</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>備註</span></th>
											<td class="wd-80p">{{ $filters.defaultValue(proInfo.memo, '--') }}</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>商品說明書</th>
											<td class="wd-80p">
												<a v-if="proFileA && proFileA.url" :href="proFileA.url" target="_blank">{{
													$filters.defaultValue(proFileA.url, '--')
												}}</a
												><br v-if="proFileA && proFileA.url" />
												<a v-if="proFileA" class="tx-link" href="#" @click="downloadFile(proFileA)">{{
													$filters.defaultValue(proFileA.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>投資人須知</th>
											<td class="wd-80p">
												<a v-if="proFileD && proFileD.url" :href="proFileD.url" target="_blank">{{
													$filters.defaultValue(proFileD.url, '--')
												}}</a
												><br v-if="proFileD && proFileD.url" />
												<a v-if="proFileD" class="tx-link" href="#" @click="downloadFile(proFileD)">{{
													$filters.defaultValue(proFileD.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>DM</th>
											<td class="wd-80p">
												<a v-if="proFileF && proFileF.url" :href="proFileF.url" target="_blank">{{
													$filters.defaultValue(proFileF.url, '--')
												}}</a
												><br v-if="proFileF && proFileF.url" />
												<a v-if="proFileF" class="tx-link" href="#" @click="downloadFile(proFileF)">{{
													$filters.defaultValue(proFileF.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>其他</th>
											<td class="wd-80p">
												<a v-if="proFileG && proFileG.url" :href="proFileG.url" target="_blank">{{
													$filters.defaultValue(proFileG.url, '--')
												}}</a
												><br v-if="proFileG && proFileG.url" />
												<a v-if="proFileG" class="tx-link" href="#" @click="downloadFile(proFileG)">{{
													$filters.defaultValue(proFileG.showName, '--')
												}}</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>其他相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<td>
												<span v-for="(item, index) in otherFileList">
													<a
														v-if="index === otherFileList.length - 1"
														v-show="item.show"
														href="#"
														class="tx-link"
														@click="downloadOtherFile(item.docFileId)"
														>{{ $filters.defaultValue(item.showName, '--') }}</a
													>
													<a v-else href="#" class="tx-link" v-show="item.show" @click="downloadOtherFile(item.docFileId)"
														>{{ $filters.defaultValue(item.showName, '--') }}、</a
													>
												</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="tx-note">其他相關附件為文件管理內所上傳與本商品相關的文件。</div>
						</div>

						<div class="tab-pane fade" id="Sectionovers04">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>價格分析</h4>
								</div>
								<table class="table table-RWD table-bordered text-end">
									<thead>
										<tr>
											<th class="wd-10p text-start">項目</th>
											<th class="wd-30p">價格</th>
											<th class="wd-30p">最高價格(年)</th>
											<th class="wd-30p">最低價格(年)</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td class="text-start" data-th="項目">價格</td>
											<td class="text-end" data-th="價格">
												<span
													>{{ $filters.defaultValue(secPriceAna.aprice, '--') }}({{
														$filters.defaultValue(secPriceAna.priceDt, '--')
													}})</span
												>
											</td>
											<td class="text-end" data-th="最高價格(年)">
												<span
													>{{ $filters.defaultValue(secPriceAna.maxAprice, '--') }}({{
														$filters.defaultValue(secPriceAna.maxPriceDt, '--')
													}})</span
												>
											</td>
											<td class="text-end" data-th="最低價格(年)">
												<span
													>{{ $filters.defaultValue(secPriceAna.minAprice, '--') }}({{
														$filters.defaultValue(secPriceAna.minPriceDt, '--')
													}})</span
												>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>歷史價格走勢圖</h4>
								</div>
								<div class="text-center">
									<vue-net-chart
										ref="secNetChartRef"
										:chart-id="chartId"
										:pro-code="proInfo.proCode"
										:pro-price-range-menu="proPriceRangeMenu"
									></vue-net-chart>
									<div class="btn-group btn-group-sm mb-4" role="group">
										<input
											v-for="item in proPriceRangeMenu"
											type="radio"
											class="btn-check"
											name="time"
											:id="'secNetPeriod' + item.termValue"
											:checked="item.termValue == '4'"
											@click="getSecNets(proInfo.proCode, item.rangeType, item.rangeFixed)"
										/>
										<label
											v-for="item in proPriceRangeMenu"
											class="btn btn-outline-secondary"
											:for="'secNetPeriod' + item.termValue"
											>{{ $filters.defaultValue(item.termName, '--') }}</label
										>
									</div>
								</div>

								<div class="caption">近30日價格</div>
								<table class="table table-RWD table-bordered text-center">
									<thead>
										<tr>
											<th>日期</th>
											<th class="text-end">參考價格</th>
											<th>日期</th>
											<th class="text-end">參考價格</th>
											<th>日期</th>
											<th class="text-end">參考價格</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="(item, index) in secPriceHist">
											<td data-th="日期">{{ $filters.defaultValue(item.priceDt1, '--') }}</td>
											<td class="text-end" data-th="參考價格">
												<span>{{ $filters.defaultValue(item.aprice1, '--') }}</span>
											</td>
											<td data-th="日期">{{ $filters.defaultValue(item.priceDt2, '--') }}</td>
											<td class="text-end" data-th="參考價格">
												<span>{{ $filters.defaultValue(item.aprice2, '--') }}</span>
											</td>
											<td data-th="日期">{{ $filters.defaultValue(item.priceDt3, '--') }}</td>
											<td class="text-end" data-th="參考價格">
												<span>{{ $filters.defaultValue(item.aprice3, '--') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionovers05">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>績效分析</h4>
								</div>
								<div class="card-body">
									<div class="row">
										<div class="col-sm-2">
											<label class="tc-blue">選擇商品：</label>
										</div>
										<div class="col-sm-8">
											<div class="input-group">
												<select class="form-select" v-model="issuerCode">
													<option selected value>--</option>
													<option v-for="item in productList" :value="item.proCode">
														{{ $filters.defaultValue(item.proName, '--') }}
														{{ $filters.defaultValue(item.proCode, '--') }}
													</option>
												</select>
											</div>
										</div>
										<div class="col-sm-2">
											<p><input class="btn btn-primary text-alignRight" type="button" value="加入" @click="addPro()" /></p>
										</div>
									</div>

									<div class="caption">已加入商品</div>
									<div class="table-responsive mb-3">
										<table class="table table-bordered">
											<thead>
												<tr>
													<th>商品名稱</th>
													<th class="text-end">一年報酬率</th>
													<th class="text-center">動作</th>
												</tr>
											</thead>
											<tbody>
												<tr v-for="item in observedProList">
													<td>
														{{ $filters.defaultValue(item.proName, '--') }}{{ $filters.defaultValue(item.proCode, '--') }}
													</td>
													<td class="text-end">{{ $filters.defaultValue($filters.formatPct(item.fcTdReturn), '--') }}%</td>
													<td class="text-center">
														<button
															type="button"
															class="btn btn-danger btn-icon"
															data-bs-toggle="tooltip"
															title="刪除"
															@click="deletePro(item.proCode)"
														>
															<i class="fa-solid fa-trash"></i>
														</button>
													</td>
												</tr>
											</tbody>
										</table>
									</div>

									<div class="text-center">
										<!-- 績效分析圖表  -->
										<vue-performances-chart ref="secPerformancesChartRef" :chart-id="performancesId"></vue-performances-chart>
										<div class="btn-group btn-group-sm mb-4" role="group">
											<template v-for="item in proPriceRangeMenu">
												<input
													type="radio"
													class="btn-check"
													name="time"
													:id="'performancesPeriod' + item.termValue"
													:checked="item.termValue == '4' ? true : false"
												/>
												<label
													class="btn btn-outline-secondary"
													:for="'performancesPeriod' + item.termValue"
													@click.prevent="getSecPerformances(proCodes, item.rangeType, item.rangeFixed)"
													>{{ $filters.defaultValue(item.termName, '--') }}</label
												>
											</template>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button @click.prevent="close()" type="button" class="btn btn-white">關閉視窗</button>
			</div>
		</div>
	</div>
	<!-- Modal 9 End -->
</template>
<script>
import moment from 'moment';
import _ from 'lodash';
import vuePerformancesChart from './performancesChart.vue';
import vueNetChart from './netChart.vue';
export default {
	components: {
		vuePerformancesChart,
		vueNetChart
	},
	props: {
		downloadFile: Function,
		downloadOtherFile: Function,
		finReqCodeMenu: Array,
		proPriceRangeMenu: Array,
		closeModal: Function
	},
	data: function () {
		const now = moment();
		return {
			proInfo: {
				fundInfo: {},
				etfInfo: {},
				bondInfo: {},
				spInfo: {},
				insInfo: {},
				dciInfo: {},
				secInfo: {}
			},

			chartsData: [], // 績效分析  績效所得圖表資料
			proCodes: [], // 績效分析 已加入商品
			productList: [], // 績效分析 加入商品清單
			observedProList: [], // 績效分析 已加入商品顯示報酬率清單
			issuerCode: null, // 績效分析 選擇商品

			finReqCodes: [],
			proFileA: {},
			proFileD: {},
			proFileF: {},
			proFileG: {},
			otherFileList: [],
			secPriceAna: {},
			secPriceHist: [],
			chartId: 'secNetChartId',
			performancesId: 'secPerformancesChartId'
		};
	},
	watch: {},
	mounted: function () {},
	methods: {
		getProInfo: async function (proCode, pfcatCode) {
			const self = this;

			self.proCodes = [];
			self.proCodes.push(proCode); // 預設加入的商品

			// 取得商品基本資料
			const ret = await this.$api.getProductInfoApi({ proCode, pfcatCode });
			if (_.isNil(ret.data)) {
				ret.data = {};
				this.$bi.alert('資料不存在');
				return;
			}
			if (_.isNil(ret.data.secInfo)) {
				ret.data.secInfo = {};
			}

			self.proInfo = ret.data;

			// 這邊處理商品基本資料

			// 商品共用資料
			if (!_.isUndefined(self.proInfo.allYn)) {
				const allYnList = await this.$api.getAdmCodeDetail({ codeType: 'CUS_BU' });
				const allYnObjs = _.filter(allYnList.data, {
					codeValue: self.proInfo.allYn
				});
				if (!_.isEmpty(allYnObjs)) {
					self.proInfo.allYn = allYnObjs[0].codeName;
				}
			}

			if (!_.isUndefined(self.proInfo.targetCusBu)) {
				const targetCusBuList = await this.$api.getAdmCodeDetail({ codeType: 'PROF_INVESTOR' });
				const targetCusBuObjs = _.filter(targetCusBuList.data, {
					codeValue: self.proInfo.targetCusBu
				});
				if (!_.isEmpty(targetCusBuObjs)) {
					self.proInfo.targetCusBu = targetCusBuObjs[0].codeName;
				}
			}

			if (!_.isUndefined(self.proInfo.buyYn) && !_.isUndefined(self.proInfo.sellYn)) {
				const selectYnList = await this.$api.getAdmCodeDetail({ codeType: 'SELECT_YN' });

				if (!_.isUndefined(self.proInfo.buyYn)) {
					const buyYnObjs = _.filter(selectYnList.data, {
						codeValue: self.proInfo.buyYn
					});
					self.proInfo.buyYn = buyYnObjs[0].codeName;
				}

				if (!_.isUndefined(self.proInfo.sellYn)) {
					const sellYnObjs = _.filter(selectYnList.data, {
						codeValue: self.proInfo.sellYn
					});
					if (!_.isEmpty(sellYnObjs)) {
						self.proInfo.sellYn = sellYnObjs[0].codeName;
					}
				}
			}

			if (!_.isUndefined(self.proInfo.volatilityType)) {
				const volatilityTypeList = await this.$api.getAdmCodeDetail({ codeType: 'VOLATILITY_TYPE' });
				const volatilityTypeObjs = _.filter(volatilityTypeList.data, {
					codeValue: self.proInfo.volatilityType
				});
				self.proInfo.volatilityType = volatilityTypeObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.intFreqUnitype)) {
				const intFreqUnitypeList = await this.$api.getAdmCodeDetail({ codeType: 'INT_FREQ_UNITTYPE' });
				const intFreqUnitypeObjs = _.filter(intFreqUnitypeList.data, {
					codeValue: self.proInfo.intFreqUnitype
				});
				self.proInfo.intFreqUnitype = intFreqUnitypeObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.selprocatNames)) {
				const selprocatNames = self.proInfo.selprocatNames.replaceAll(',', '、');
				self.proInfo.selprocatNames = selprocatNames;
			}

			if (!_.isUndefined(self.proInfo.finReqCode)) {
				self.finReqCodes = self.proInfo.finReqCode.split(',');
			}

			// 商品附加資料
			const commInfoRet = await this.$api.getProductsCommInfo({ proCode, pfcatCode });
			if (!_.isEmpty(commInfoRet.data)) {
				if (commInfoRet.data.proDocs) {
					self.otherFileList = commInfoRet.data.proDocs; // 其他相關附件
					self.otherFileList.forEach(function (item) {
						// 其他相關附件 檔案顯示時間範圍
						item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
					});
				}

				const proFileList = commInfoRet.data.proFiles;
				if (!_.isEmpty(proFileList)) {
					self.proFileA = proFileList.filter((proFile) => proFile.fileType === 'A')[0];
					self.proFileD = proFileList.filter((proFile) => proFile.fileType === 'D')[0];
					self.proFileF = proFileList.filter((proFile) => proFile.fileType === 'F')[0];
					self.proFileG = proFileList.filter((proFile) => proFile.fileType === 'G')[0];
				}
			}
			self.observedPro(); // 績效分析 取得 已加入商品清單
			self.productMenu();
		},
		// 商品資訊/價格分析及近30日價格
		// 商品資訊/價格分析及近30日價格
		getSecPriceAna: async function (proCode) {
			const self = this;
			const proCodeArray = { 0: proCode };

			const ret = await this.$api.getPriceAnaApi({ proCodes: [proCode] });
			if (!_.isEmpty(ret.data)) {
				self.secPriceAna = ret.data;
				if (!_.isNil(ret.data.priceHist)) {
					const orgPriceHis = ret.data.priceHist;
					const newPriceHis = [];
					orgPriceHis.forEach(function (item, index) {
						if (index % 3 == 0) {
							if (index + 2 < orgPriceHis.length) {
								const pricHisObj = {
									priceDt1: orgPriceHis[index].priceDt,
									aprice1: Number.parseFloat(orgPriceHis[index].aprice),
									priceDt2: orgPriceHis[index + 1].priceDt,
									aprice2: Number.parseFloat(orgPriceHis[index + 1].aprice),
									priceDt3: orgPriceHis[index + 2].priceDt,
									aprice3: Number.parseFloat(orgPriceHis[index + 2].aprice)
								};
								newPriceHis.push(pricHisObj);
							} else if (index + 1 < orgPriceHis.length) {
								const pricHisObj = {
									priceDt1: orgPriceHis[index].priceDt,
									aprice1: Number.parseFloat(orgPriceHis[index].aprice),
									priceDt2: orgPriceHis[index + 1].priceDt,
									aprice2: Number.parseFloat(orgPriceHis[index + 1].aprice),
									priceDt3: null,
									aprice3: null
								};
								newPriceHis.push(pricHisObj);
							} else {
								const pricHisObj = {
									priceDt1: orgPriceHis[index].priceDt,
									aprice1: Number.parseFloat(orgPriceHis[index].aprice),
									priceDt2: null,
									aprice2: null,
									priceDt3: null,
									aprice3: null
								};
								newPriceHis.push(pricHisObj);
							}
						}
					});
					self.secPriceHist = newPriceHis;
				}
			}
		},
		// 淨值歷史圖
		getSecNets: function (proCode, rangeType, rangeFixed) {
			var self = this;
			self.$refs.secNetChartRef.getNets(proCode, rangeType, rangeFixed);
		},
		//績效分析 圖表
		//績效分析 圖表
		getSecPerformances: async function (proCodes, rangeType, rangeFixed) {
			const self = this;
			const ret = await this.$api.getPerformanceRunChartApi({ proCodes, freqType: rangeType, freqFixed: rangeFixed });
			if (!_.isEmpty(ret.data.datas)) {
				for (let i = 0; i < ret.data.length; i++) {
					ret.data[i].datas.forEach((e) => {
						e.value = e.returnFc;
						e.date = new Date(e.returnDt).getTime();
					});
				}
				self.chartsData = ret.data;
				self.$refs.secPerformancesChartRef.initChart(self.chartsData);
			}
		},
		// 績效分析 已加入商品清單
		observedPro: async function () {
			const self = this;
			const ret = await this.$api.getObservedProductsApi({ proCodes: self.proCodes });
			self.observedProList = ret.data;
		},
		// 績效分析 選擇商品下拉
		productMenu: async function () {
			const self = this;
			const ret = await this.$api.getProductByPfcatCodeApi({ pfcatCode: 'SEC' });
			self.productList = ret.data;
		},
		// 績效分析 選擇商品 加入按鈕
		addPro() {
			var self = this;
			let pk = null;
			pk = _.find(self.proCodes, function (item) {
				return item == self.issuerCode;
			});
			if (self.issuerCode != null && pk == null) {
				self.proCodes.push(self.issuerCode); // 加入選擇商品
				self.observedPro(); // 績效分析 取得 已加入商品清單
				self.getSecPerformances(self.proCodes, 'Y', -1.0); // 商品資訊/績效分析圖表
			} else if (self.issuerCode != null && pk != null) {
				thi.$bi.alert('此商品已加入');
			} else {
				thi.$bi.alert('請選擇商品');
			}
		},
		// 績效分析 已加入商品 刪除按鈕
		deletePro(proCode) {
			var self = this;
			if (self.proCodes.length > 1) {
				let index = self.proCodes.indexOf(proCode); // 找出要移除的index
				self.proCodes.splice(index, 1); // 移除加入的商品(要插入或刪除的索引位置, 要刪除的元素數量)
				self.observedPro(); // 績效分析 取得 已加入商品清單
				self.geSectPerformances(self.proCodes, 'Y', -1.0); // 商品資訊/績效分析圖表
			} else {
				thi.$bi.alert('至少要有一項商品');
			}
		}
	} // methods end
};
</script>
