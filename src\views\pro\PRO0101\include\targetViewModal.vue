<template>
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">連結標的檢視</h4>
				<button type="button" class="btn-close" @click="close()" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="card card-form mb-3">
					<div class="card-header">
						<h4>連結標的</h4>
					</div>

					<table class="table table-RWD table-bordered table-horizontal-RWD">
						<thead>
							<tr>
								<th>連結標的<br />期初價格</th>
								<th>最新收盤價</th>
								<th>轉換價</th>
								<th>下限價</th>
								<th>提前出場價</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="item in targetDataList">
								<td class="">{{ item.isinCode }}<br />{{ item.entryPrice }}</td>
								<td class="text-end">{{ item.closePrice }}</td>
								<td class="text-end">{{ item.convPrice }} (70%)</td>
								<td class="text-end">{{ item.kiPrice }} (55%)</td>
								<td class="text-end">{{ item.koPrice }} (105%)</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			<div class="modal-footer">
				<input type="button" @click.prevent="close()" class="btn btn-primary" value="關閉" />
			</div>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		proCode: String,
		close: Function
	},
	data: function () {
		return {
			targetDataList: []
		};
	},
	watch: {},
	mounted: function () {},
	methods: {
		getTargetDataList: async function (proCode) {
			var self = this;
			const ret = this.$api.getTargetViewDataListApi({
				proCode: proCode
			});
			self.targetDataList = ret.data;
		}
	} // methods end
};
</script>
