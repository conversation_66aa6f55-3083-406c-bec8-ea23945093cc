export default {
	data() {
		return {
			userCodeRequiredLength: 0
		};
	},
	created: function () {
		this.fetchUserCodeRequiredLength();
	},
	methods: {
		fetchUserCodeRequiredLength() {
			var self = this;
			self.$api
				.getUserCodeLengthApi()

				.then(function (ret) {
					self.userCodeRequiredLength = ret.data || 0;
				});
		},
		complementUserCode(userCode) {
			if (!userCode) return;
			userCode = String(userCode);
			if (userCode.length < this.userCodeRequiredLength) {
				return '0'.repeat(this.userCodeRequiredLength - userCode.length) + userCode;
			}

			return userCode;
		}
	}
};
