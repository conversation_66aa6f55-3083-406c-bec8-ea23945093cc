<template>
	<!--filemgr-sidebar start-->
	<div class="filemgr-wrapper filemgr-wrapper-two">
		<span id="filemgrMenuclose"><i class="bi bi-arrow-left-square-fill" @click="hideMenu()"></i></span>
		<vue-cus-info-sidebar
			v-if="customer"
			:cus-code="cusCode"
			:page-code="pageCode"
			:has-auth="hasAuth"
			:customer="customer"
		></vue-cus-info-sidebar>
		<div class="filemgr-content">
			<vue-cus-client-overview v-if="customer" :cus-code="cusCode" :has-auth="hasAuth" :customer="customer"></vue-cus-client-overview>
		</div>
	</div>
	<!-- filemgr-content -->
</template>
<script>
import vueCusClientOverview from './include/clientOverview.vue';
import vueCusInfoSidebar from '../include/cusInfoSidebar.vue';
import _ from 'lodash';
export default {
	components: {
		vueCusClientOverview,
		vueCusInfoSidebar
	},
	data: function () {
		return {
			pageCode: 2,
			cusCode: null,
			customer: null,
			hasAuth: false,
			pbStatusName: null
		};
	},
	mounted: function () {
		var self = this;
		if (self.$route.params.cusCode) {
			self.cusCode = self.$route.params.cusCode;
		}
		self.checkAuthAndGetCustomer();
	},
	methods: {
		checkAuthAndGetCustomer: async function () {
			var self = this;
			const retAuth = await self.$api.checkCusAuthApi({
				cusCode: self.cusCode
			});
			if (!_.isNil(retAuth.data) && retAuth.data.authYn != 'N') {
				const ret = await self.$api.getCusInfoApi({
					cusCode: self.cusCode
				});
				if (!ret.data || ret.data.length == 0) {
					self.$bi.confirm('此客戶不存在。', {
						event: {
							confirmOk: function () {
								location.href = self.config.contextPath + '/';
							},
							confirmCancel: function () {
								location.href = self.config.contextPath + '/';
							}
						},
						button: {
							confirmOk: '確定'
						}
					});
				} else {
					if (retAuth.data.authYn == 'Y') {
						self.hasAuth = true;
					} else if (retAuth.data.authYn == 'E') {
						self.hasAuth = false;
					}
					self.customer = ret.data;
					self.customer.pbStatusName = retAuth.data.pbStatusName;
					self.customer.pbStatus = retAuth.data.authYn;
				}
			} else {
				self.hasAuth = false;
				self.$bi.confirm('此客戶未被授權檢視。', {
					event: {
						confirmOk: function () {
							location.href = self.config.contextPath + '/';
						},
						confirmCancel: function () {
							location.href = self.config.contextPath + '/';
						}
					},
					button: {
						confirmOk: '確定'
					}
				});
			}
		}
	}
};
</script>
