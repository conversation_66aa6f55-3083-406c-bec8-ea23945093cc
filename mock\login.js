import LoginMockData from './mockData/login/LoginMockData.json';
import RolesMockData from './mockData/login/RolesMockData.json';
import TokenLoginMockData from './mockData/login/TokenLoginMockData.json';
import request from '@/utils/request';

// Login.vue
export function loginApi() {
	return request({
		url: 'https://192.168.0.181:8081/bi-pbs/login',
		method: 'post',
		params: {
			userCode: '112790',
			pwd: 'a',
			deputyUserCode: '',
			posCode: '891_98'
		}
	});
}
export function postLoginApi() {
	return LoginMockData;
}

export function recaptchaVerifiedApi() {
	return { success: true };
}

// SelectPos.vue
export function getUserRolesApi() {
	return RolesMockData;
}

export function postTokenLoginApi() {
	return TokenLoginMockData;
}
