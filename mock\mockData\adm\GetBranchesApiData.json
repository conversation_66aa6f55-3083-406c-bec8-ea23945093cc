{"status": 200, "data": [{"order": 0, "leaf": true, "branCode": "6101", "branName": "台北分行", "parentBranCode": "6100", "depths": 4}, {"order": 0, "leaf": true, "branCode": "6102", "branName": "信義分行", "parentBranCode": "6100", "depths": 4}, {"order": 0, "leaf": true, "branCode": "6103", "branName": "松山分行", "parentBranCode": "6100", "depths": 4}, {"order": 0, "leaf": true, "branCode": "6201", "branName": "板橋分行", "parentBranCode": "6200", "depths": 4}, {"order": 0, "leaf": true, "branCode": "6202", "branName": "新莊分行", "parentBranCode": "6200", "depths": 4}, {"order": 0, "leaf": true, "branCode": "6301", "branName": "中山分行", "parentBranCode": "6300", "depths": 4}, {"order": 0, "leaf": true, "branCode": "6302", "branName": "大同分行", "parentBranCode": "6300", "depths": 4}, {"order": 0, "leaf": true, "branCode": "6401", "branName": "桃園分行", "parentBranCode": "6400", "depths": 4}, {"order": 0, "leaf": true, "branCode": "6402", "branName": "新竹分行", "parentBranCode": "6400", "depths": 4}, {"order": 0, "leaf": true, "branCode": "6501", "branName": "台中分行", "parentBranCode": "6500", "depths": 4}, {"order": 0, "leaf": true, "branCode": "6502", "branName": "彰化分行", "parentBranCode": "6500", "depths": 4}], "timestamp": "2025/07/16", "sqlTracer": [{"data": [], "sqlInfo": "SELECT DISTINCT B.<PERSON><PERSON>_CODE, B.<PERSON><PERSON>_NAME, B.BRAN_ENAME, B.BRAN_ADDR, B.BRAN_EADDR, B.PARENT_BRAN_CODE, B.STRSET, B.DEPTHS, B.BRAN_LVL_CODE FROM ADM_POSITIONS P JOIN ADM_BRANCHES B ON P.BRAN_CODE = B.BRAN_CODE AND P.BU_CODE = B.BU_CODE WHERE B.PARENT_BRAN_CODE = :parentBranCode AND B.BU_CODE = :buCode ORDER BY B.BRAN_CODE"}]}