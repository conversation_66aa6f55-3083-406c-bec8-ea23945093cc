<template>
	<header>
		<h2>
			{{ pageTitle }}
		</h2>
		<nav>
			<ol class="breadcrumb breadcrumb-style2">
				<li v-if="menuPath?.length > 0" class="breadcrumb-item">
					<a href="#" @click="goHome()"><i class="bi bi-house"></i></a>
				</li>
				<template v-for="(item, index) in menuPath">
					<li class="breadcrumb-item" :class="{ active: index == menuPath.length - 1 }">
						{{ index == menuPath.length - 1 ? pageTitle : item }}
					</li>
				</template>
			</ol>
		</nav>
	</header>
</template>

<script>
export default {
	data: function () {
		return {
			path: this.$route.path,
			menuPath: []
		};
	},
	props: {
		customTitle: {
			type: String,
			default: null
		},
		isShowPageTitle: {
			type: Boolean,
			default: true
		}
	},
	watch: {
		menus: {
			handler: function (newVal) {
				if (!newVal || !Array.isArray(newVal)) {
					return;
				}
				newVal?.find((menu) => this.getMenuPath(menu));
			},
			deep: true,
			immediate: true
		}
	},
	computed: {
		menus: function () {
			return this.$store.getters['menus/menus'] || [];
		},
		pageTitle: function () {
			return this.customTitle || this.menuPath.at(-1) || '';
		}
	},
	methods: {
		getMenuPath: function (menu) {
			if (this.isCurrentPath(menu)) {
				this.menuPath = [menu?.name];
				return true;
			}
			if (menu.nodes) {
				for (let subMenu of menu.nodes) {
					if (this.getMenuPath(subMenu)) {
						this.menuPath = [menu?.name, ...this.menuPath];
						return true;
					}
				}
			}
			return false;
		},
		isCurrentPath: function (menu) {
			return menu.url && this.path === menu.url;
		},
		goHome: function () {
			this.$router.push('/');
		}
	}
};
</script>
