{"status": 200, "data": {"cusName": "臺00", "age": 60, "addC": " ", "addH": "台北市北投區永欣里14鄰模00", "idnEntityType": "L", "idnEntityTypeName": "法人", "rejectPhoneYn": "N", "graCode": "F", "graName": "一般戶", "piDueDt": "1911-01-01T00:00:00", "branName": "石牌分行      "}, "timestamp": "2025/07/09", "sqlTracer": [{"data": {"cusName": "臺00", "age": 60, "addC": " ", "addH": "台北市北投區永欣里14鄰模00", "idnEntityType": "L", "idnEntityTypeName": "法人", "rejectPhoneYn": "N", "graCode": "F", "graName": "一般戶", "piDueDt": "1911-01-01T00:00:00", "branName": "石牌分行      "}, "sqlInfo": " SELECT C.CUS_NAME, C.AGE , C.IDN_ENTITY_TYPE, IETACD.CODE_NAME IDN_ENTITY_TYPE_NAME , CS.REJECT_PHONE_YN , CS.MKT_AMT_AUA, CS.MKT_AMT_AUM , CFI.IPS_CODE, CFI.PB_EXPIRE_DT, PSACD.CODE_NAME PB_STATUS_NAME , (SELECT MAX(CREATE_DT) FROM WOB_TD_REC WHERE CUS_CODE = :cusCode) CONTACT_DATE , (SELECT MAX(TRAN_DT) FROM CUS_TRANS WHERE CUS_CODE = :cusCode) TRANS_DATE , C.GRA_CODE, CG.GRA_NAME , CKC.RANK_CODE, CR.RANK_NAME, CKC.NEXT_RANK_QUE_DT , C.PI_YN, C.PI_DUE_DT , AB.BRAN_NAME, AU.USER_NAME , (SELECT PHONE_1 FROM CUS_CONTACT_INFO WHERE CUS_CODE = :cusCode AND PRIORITY = '3' AND CONTACT_TYPE = 'H') PHONE_H , (SELECT PHONE_2 FROM CUS_CONTACT_INFO WHERE CUS_CODE = :cusCode AND PRIORITY = '4' AND CONTACT_TYPE = 'O') PHONE_O , (SELECT CELLPHONE FROM CUS_CONTACT_INFO WHERE CUS_CODE = :cusCode AND PRIORITY = '1' AND CONTACT_TYPE = 'M') PHONE_M , (SELECT ADDR_FULL FROM CUS_ADDRESS WHERE CUS_CODE = :cusCode AND PRIORITY = '1' AND ADDR_TYPE = 'C') ADD_C , (SELECT ADDR_FULL FROM CUS_ADDRESS WHERE CUS_CODE = :cusCode AND PRIORITY = '2' AND ADDR_TYPE = 'H') ADD_H FROM CUSTOMERS C LEFT JOIN CUS_AO_INFO CAI ON C.CUS_CODE = CAI.CUS_CODE LEFT JOIN ADM_BRANCHES AB ON CAI.BRAN_CODE = AB.BRAN_CODE LEFT JOIN ADM_USERS AU ON CAI.USER_CODE = AU.USER_CODE LEFT JOIN CUS_SUMMARIES CS ON C.CUS_CODE = CS.CUS_CODE LEFT JOIN CUS_KYC_CURRENT CKC ON C.CUS_CODE = CKC.CUS_CODE LEFT JOIN CUS_RANKS CR ON CKC.RANK_CODE = CR.RANK_CODE LEFT JOIN CUS_FORM_INFO CFI ON C.CUS_CODE = CFI.CUS_CODE LEFT JOIN ADM_CODE_DETAIL PSACD ON CFI.PB_STATUS = PSACD.CODE_VALUE AND PSACD.CODE_TYPE = 'PB_STATUS' LEFT JOIN ADM_CODE_DETAIL IETACD ON C.IDN_ENTITY_TYPE = IETACD.CODE_VALUE AND IETACD.CODE_TYPE = 'IDN_ENTITY_TYPE'  LEFT JOIN CUS_GRADES CG ON C.GRA_CODE = CG.GRA_CODE WHERE C.CUS_CODE =:cusCode;,class com.bi.pbs.cus.web.model.ClientOverviewCusInfoResp,{cusCode=00956829}"}]}