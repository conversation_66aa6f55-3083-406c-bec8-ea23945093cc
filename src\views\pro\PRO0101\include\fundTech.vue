<template>
	<div class="row m-t-10">
		<div class="col-3">
			<select class="select" v-model="selectedTech">
				<option value="0">累積報酬率</option>
				<option value="1">夏普值</option>
				<option value="2">Alpha值</option>
				<option value="3">Beta值</option>
				<option value="4">標準差</option>
			</select>
		</div>
		<div class="col-12">
			<vue-fund-column-chart
				:chart-id="techsChartId"
				:prop-chart-data="techsChartData"
				:prop-selected-tech="techsMenu[selectedTech]"
			></vue-fund-column-chart>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import vueFundColumnChart from './fundColumnChart.vue';
export default {
	components: {
		vueFundColumnChart
	},
	props: {
		techs: Array
	},
	data: function () {
		return {
			selectedTech: '0',
			techsMenu: [
				{
					name: '累積報酬率',
					valueList: [{ name: '累積報酬率', value: 'pct' }],
					tooltipText: '{categoryX}:{valueY}'
				},
				{
					name: '夏普值',
					valueList: [{ name: '夏普值', value: 'shp' }],
					tooltipText: '{categoryX}:{valueY}'
				},
				{
					name: 'Alpha值',
					valueList: [{ name: 'Alpha值', value: 'alp' }],
					tooltipText: '{categoryX}:{valueY}'
				},
				{
					name: 'Beta值',
					valueList: [{ name: 'Beta值', value: 'bet' }],
					tooltipText: '{categoryX}:{valueY}'
				},
				{
					name: '標準差',
					valueList: [{ name: '標準差', value: 'std' }],
					tooltipText: '{categoryX}:{valueY}'
				}
			],
			techsChartId: 'techsChartId'
		};
	},
	watch: {},
	computed: {
		techsChartData: function () {
			return [
				{
					year: '6個月',
					pct: this.getTech('PCT6M'),
					shp: this.getTech('SHP6M'),
					alp: this.getTech('ALP6M'),
					bet: this.getTech('BET6M'),
					std: this.getTech('STD6M')
				},
				{
					year: '1年',
					pct: this.getTech('PCT1Y'),
					shp: this.getTech('SHP1Y'),
					alp: this.getTech('ALP1Y'),
					bet: this.getTech('BET1Y'),
					std: this.getTech('STD1Y')
				},
				{
					year: '3年',
					pct: this.getTech('PCT3Y'),
					shp: this.getTech('SHP3Y'),
					alp: this.getTech('ALP3Y'),
					bet: this.getTech('BET3Y'),
					std: this.getTech('STD3Y')
				},
				{
					year: '5年',
					pct: this.getTech('PCT5Y'),
					shp: this.getTech('SHP5Y'),
					alp: this.getTech('ALP5Y'),
					bet: this.getTech('BET5Y'),
					std: this.getTech('STD5Y')
				}
			];
		}
	},
	mounted: function () {},
	methods: {
		getTech: function (statCode) {
			var tech = _.find(this.techs, { statCode: statCode });
			return tech ? tech.dvalue : null;
		}
	}
};
</script>
