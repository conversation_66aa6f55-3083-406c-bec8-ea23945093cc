<template>
	<dynamicTitle />
	<div>
		<div class="row">
			<div class="col-12">
				<vue-bi-tabs :menu-code="'M40-02'" :default-tab-code="defaultTabCode">
					<template #default="{ id, tabName }">
						<component :is="id" :title="tabName"></component>
					</template>
				</vue-bi-tabs>
			</div>
		</div>
		<!--頁面內容 end-->
	</div>
</template>
<script>
import dynamicTitle from '@/views/components/dynamicTitle.vue';
import vueBiTabs from '@/views/components/biTabs.vue';
import vueBbsHead from './include/bbsHead.vue';
import vueBbsHeadAll from './include/bbsHeadAll.vue';
export default {
	components: {
		vueBiTabs,
		vueBbsHead,
		vueBbsHeadAll,
		dynamicTitle
	},
	data: function () {
		return {
			msgCodeMenuMap: {
				MSG01: 'M40-021',
				MSG02: 'M40-022',
				MSG021: 'M40-023',
				MSG026: 'M40-024'
			},
			defaultTabCode: ''
		};
	},
	computed: {
		msgCode: function () {
			return this.prop?.msgCode;
		}
	},
	mounted: function () {
		var self = this;
		self.defaultTabCode = self.msgCodeMenuMap[self.msgCode];
	},
	methods: {}
};
</script>
