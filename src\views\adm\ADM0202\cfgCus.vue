<template>
	<dynamic-title></dynamic-title>
	<div>
		<div class="row">
			<div class="col-12">
				<vue-bi-tabs :menu-code="'M01-01'" @change-tab="changeTab">
					<template #default="{ id }">
						<component :is="id"></component>
					</template>
				</vue-bi-tabs>
			</div>
		</div>
		<!--頁面內容 end-->
	</div>
</template>
<script>
import vueBiTabs from '@/views/components/biTabs.vue';
import vueCfgPotCusConfig from './include/cfgPotCusConfig.vue';
import vueCfgCusRegularView from './include/cfgCusRegularView.vue';
import dynamicTitle from '@/views/components/dynamicTitle.vue';

export default {
	components: {
		vueBiTabs,
		vueCfgPotCusConfig,
		vueCfgCusRegularView,
		dynamicTitle
	},
	data: function () {
		return {
			//畫面顯示用參數
			customTitle: null,
			//畫面邏輯判斷用參數
			tabCodeTitleMap: {
				// 'M01-010': '客戶其他補充資料',
				'M01-011': '客戶資產等級',
				'M01-012': '客戶定期檢視'
				// 'M01-013': '體驗戶參數'
			}
		};
	},
	methods: {
		changeTab: function (tabCode) {
			var self = this;
			self.customTitle = self.tabCodeTitleMap[tabCode];
		}
	}
};
</script>
