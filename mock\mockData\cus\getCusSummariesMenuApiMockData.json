{"status": 200, "data": [{"menuCode": "M20-051", "menuName": "客戶總覽"}, {"menuCode": "M20-052", "menuName": "基本資料"}, {"menuCode": "M20-054", "menuName": "帳戶綜合概要"}, {"menuCode": "M20-055", "menuName": "帳戶總覽"}, {"menuCode": "M20-058", "menuName": "投資績效分析"}, {"menuCode": "M20-056", "menuName": "服務紀錄"}, {"menuCode": "M20-059", "menuName": "客戶投資屬性問卷紀錄"}, {"menuCode": "M20-057", "menuName": "客戶報告書"}, {"menuCode": "M20-053", "menuName": "價格警示設定"}], "timestamp": "2025/07/09", "sqlTracer": [{"data": [{"menuCode": "M20-051", "menuName": "客戶總覽"}, {"menuCode": "M20-052", "menuName": "基本資料"}, {"menuCode": "M20-054", "menuName": "帳戶綜合概要"}, {"menuCode": "M20-055", "menuName": "帳戶總覽"}, {"menuCode": "M20-058", "menuName": "投資績效分析"}, {"menuCode": "M20-056", "menuName": "服務紀錄"}, {"menuCode": "M20-059", "menuName": "客戶投資屬性問卷紀錄"}, {"menuCode": "M20-057", "menuName": "客戶報告書"}, {"menuCode": "M20-053", "menuName": "價格警示設定"}], "sqlInfo": "SELECT DISTINCT M<PERSON><PERSON>, <PERSON><PERSON>_<PERSON>AME, <PERSON><PERSON>OW_ORDER, AP.PROG_CLASSNAME FROM ADM_MENUS M LEFT JOIN ADM_PROGS AP ON AP.PROG_CODE = M.PROG_CODE WHERE M.ACTIVE_YN = 'Y' AND M.PARENT_MENU_CODE='M20-05' ORDER BY M.SHOW_ORDER ASC, M.MENU_CODE ASC ,class com.bi.pbs.cus.web.model.MenusResp,{}"}]}