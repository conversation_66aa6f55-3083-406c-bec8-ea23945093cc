<template>
	<!-- Modal group InsCmp start -->
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">選擇保險公司</h4>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="row m-2">
					<div class="col-3 form-check" v-for="item in inscmpsMenu">
						<input
							class="form-check-input"
							name="inscmpCodes"
							:id="item.inscmpCode"
							v-model="inscmpCode"
							:value="item.inscmpCode"
							type="checkbox"
						/>
						<label class="form-check-label" :for="item.inscmpCode">{{ item.inscmpCode }}{{ item.inscmpName }}</label>
					</div>
				</div>
				<!-- 展開按鈕 -->
				<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup" v-if="insOthercmpsMenu.length > 0">
					<h4>展開</h4>
				</div>

				<!-- 第二組選項 -->
				<div class="collapse" id="collapseListGroup" v-if="insOthercmpsMenu.length > 0">
					<div class="modal-body">
						<div class="row">
							<div class="col-3" v-for="(item, index) in insOthercmpsMenu" :key="item.compCode">
								<div class="form-check">
									<input
										class="form-check-input"
										name="insOthercmpsMenu"
										:id="item.inscmpCode"
										v-model="inscmpCode"
										:value="item.inscmpCode"
										type="checkbox"
									/>
									<label class="form-check-label" :for="item.inscmpCode">{{ item.inscmpCode }}{{ item.inscmpName }}</label>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<input id="modaladdButton" type="button" class="btn btn-primary" value="加入" @click="addInsCmp()" />
			</div>
		</div>
	</div>
	<!-- Modal group InsCmp End -->
</template>
<script>
export default {
	props: {
		inscmpProp: Array, // 已選擇項目
		close: Function
	},
	data: function () {
		return {
			inscmpsMenu: [], // 保險公司 選項
			insOthercmpsMenu: [], // 其他保險公司 選項
			inscmpCode: [], // 保險公司 選擇項目
			inscmpItem: [] // 保險公司 選擇項目代碼與中文名稱
		};
	},
	watch: {},
	mounted: function () {
		var self = this;
		self.groupInscmpsMenu();
		self.groupInsOthercmpsMenu();
		self.inscmpItem = [];
	},
	methods: {
		groupInscmpsMenu: async function () {
			// 保險公司來源資料
			var self = this;
			const ret = await this.$api.getGroupInsCompanies();
			self.inscmpsMenu = ret.data;
		},

		groupInsOthercmpsMenu: async function () {
			// 其餘保險公司來源資料
			var self = this;
			const ret = await this.$api.getOtherInsCompanies();
			self.insOthercmpsMenu = ret.data;
		},
		addInsCmp() {
			// 增加保險公司
			var self = this;
			self.inscmpItem = [];
			if (self.inscmpCode.length === 0) {
				this.$bi.alert('請至少選擇一個');
			} else {
				self.inscmpCode.forEach((code) => {
					if (_.find(self.inscmpsMenu, { inscmpCode: code })) {
						let item = _.find(self.inscmpsMenu, { inscmpCode: code });
						self.inscmpItem.push(item);
					} else if (_.find(self.insOthercmpsMenu, { inscmpCode: code })) {
						let item = _.find(self.insOthercmpsMenu, { inscmpCode: code });
						self.inscmpItem.push(item);
					}
				});
				self.$emit('selected', self.inscmpItem);
				self.close();
			}
		},
		inscmpPropItem(v) {
			var self = this;
			if (v.length > 0) {
				self.inscmpItem = v;
				self.inscmpCode = [];
				v.forEach((item) => {
					self.inscmpCode.push(item.inscmpCode);
				});
			} else {
				self.inscmpItem = [];
				self.inscmpCode = [];
			}
		},
		// 全形轉半形
		toHalfWidth: function (str) {
			return str
				.replace(/[\uff01-\uff5e]/g, function (char) {
					return String.fromCharCode(char.charCodeAt(0) - 0xfee0);
				})
				.replace(/\u3000/g, ' ');
		},
		getStringType: function (str) {
			const convertedStr = this.toHalfWidth(str);
			const firstChar = convertedStr.charAt(0);

			if (/[a-zA-Z]/.test(firstChar)) return 'english';
			if (/[0-9]/.test(firstChar)) return 'number';
			return 'chinese';
		}
	}
	// methods end
};
</script>
