<template>
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<div class="card card-form" v-if="auth">
				<div class="card-header">
					<h4>親友資料設定</h4>
					<span class="tx-square-bracket">為必填欄位</span>
				</div>
				<div class="card-body">
					<vue-form v-slot="{ errors, validate }" ref="homeFriend">
						<div class="row g-3 align-items-end">
							<div class="col-md-3">
								<label class="form-label tx-require">關係</label>
								<vue-field
									id="select1"
									name="reltypeCode"
									class="form-select JQdata-hide"
									v-model="reltypeCode"
									:class="{ 'is-invalid': errors.reltypeCode }"
									rules="required"
									label="關係"
									as="select"
								>
									<option value="">請選擇</option>
									<option v-for="relativeType in relativeTypeMenu" :value="relativeType.codeValue">
										{{ relativeType.codeName }}
									</option>
								</vue-field>
								<div class="text-danger" style="height: 3px" v-show="errors.reltypeCode">{{ errors.reltypeCode }}</div>
							</div>
							<div class="col-md-3">
								<label class="form-label">客戶ID</label>
								<input name="text" type="text" class="form-control JQdata-hide" size="15" v-model="idn" />
								<div class="text-danger" style="height: 3px"></div>
							</div>
							<div class="col-md-3">
								<label class="form-label tx-require">姓名</label>
								<vue-field
									name="relName"
									type="text"
									class="form-control"
									size="15"
									v-model="relName"
									label="姓名"
									rules="required"
								></vue-field>
								<div class="text-danger" style="height: 3px" v-show="errors.relName">{{ errors.relName }}</div>
							</div>
							<div class="col-md-3">
								<label class="form-label tx-require">性別</label>
								<div class="JQdata-hide">
									<div v-for="(item, i) in genderType" class="form-check form-check-inline">
										<vue-field
											type="radio"
											name="gender"
											class="form-check-input"
											:value="item.codeValue"
											:id="'genderType-' + i"
											v-model="gender"
											rules="required"
											label="性別"
										></vue-field>
										<label :for="'genderType-' + i" class="form-label">{{ item.codeName }}</label>
									</div>
									<div class="text-danger" style="height: 3px" v-show="errors.gender">{{ errors.gender }}</div>
								</div>
							</div>
							<div class="col-md-3">
								<label class="form-label">生日</label>
								<input type="date" size="13" class="textfield form-control" v-model="birthday" />
							</div>
							<div class="col-md-3">
								<label class="form-label">聯繫電話</label>
								<vue-field
									name="cphone"
									type="text"
									class="form-control JQdata-hide"
									size="15"
									v-model="cphone"
									label="聯繫電話"
									rules="max:20"
								></vue-field>
								<div class="text-danger" style="height: 3px" v-show="errors.cphone">{{ errors.cphone }}</div>
							</div>
							<div class="col-md-3">
								<label class="form-label">學歷</label>
								<vue-field
									name="education"
									type="text"
									class="form-control JQdata-hide"
									size="15"
									v-model="education"
									label="學歷"
									rules="max:20"
								></vue-field>
								<div class="text-danger" style="height: 3px" v-show="errors.education">{{ errors.education }}</div>
							</div>
							<div class="col-md-3">
								<label class="form-label">工作單位</label>
								<vue-field
									name="organization"
									type="text"
									class="form-control JQdata-hide"
									size="15"
									v-model="organization"
									label="工作單位"
									rules="max:50"
								></vue-field>
								<div class="text-danger" style="height: 3px" v-show="errors.organization">{{ errors.organization }}</div>
							</div>
							<div class="col-md-3">
								<label class="form-label">職務</label>
								<vue-field
									name="post"
									type="text"
									class="form-control JQdata-hide"
									size="15"
									v-model="post"
									label="職務"
									rules="max:20"
								></vue-field>
								<div class="text-danger" style="height: 3px" v-show="errors.post">{{ errors.post }}</div>
							</div>
							<div class="col-md-6">
								<label class="form-label">備註</label>
								<vue-field
									name="note"
									type="text"
									class="form-control JQdata-hide"
									size="50"
									v-model="note"
									label="備註"
									rules="max:100"
								></vue-field>
								<div class="text-danger" style="height: 3px" v-show="errors.note">{{ errors.note }}</div>
							</div>
						</div>
						<div class="form-footer">
							<div id="button1" v-if="id == null">
								<button class="btn btn-primary btn-glow" type="button" @click="insertRelativeFriends()">儲存</button>
							</div>
							<div id="button2" v-if="id">
								<input class="btn btn-primary" type="button" value="修改" name="cancel" @click="updateRelativeFriends()" />
								<input class="btn btn-primary" type="button" value="取消修改" name="cancel" @click="cancel()" />
							</div>
						</div>
					</vue-form>
				</div>
			</div>
		</div>

		<div class="card card-table">
			<div class="card-header">
				<h4>親友資料列表</h4>
			</div>
			<div class="table-responsive">
				<table class="table table-RWD text-center">
					<thead>
						<tr>
							<th>關係</th>
							<th>客戶ID</th>
							<th>姓名</th>
							<th>性別</th>
							<th>生日</th>
							<th>聯繫電話</th>
							<th>學歷</th>
							<th>工作單位</th>
							<th>職務</th>
							<th class="text-start">備註</th>
							<th class="text-end" v-if="auth">執行</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="relativeFriend in relativeFriends">
							<td>{{ relativeFriend.reltypeName }}</td>
							<td>{{ relativeFriend.idn }}</td>
							<td>
								<a
									href="#"
									class="JQ-cusDetail tx-link"
									v-if="relativeFriend.relCusCode"
									@click.prevent="setCusCode(relativeFriend.relCusCode)"
									>{{ relativeFriend.relName }}</a
								>
								<div v-else>{{ relativeFriend.relName }}</div>
							</td>
							<td>{{ relativeFriend.genderName }}</td>
							<td>{{ $filters.formatDate(relativeFriend.birthday) }}</td>
							<td>{{ relativeFriend.cphone }}</td>
							<td>{{ relativeFriend.education }}</td>
							<td>{{ relativeFriend.organization }}</td>
							<td>{{ relativeFriend.post }}</td>
							<td class="text-start">{{ relativeFriend.note }}</td>
							<td class="text-end" v-if="auth">
								<button
									type="button"
									class="btn btn-info btn-glow btn-icon"
									data-bs-toggle="tooltip"
									title="編輯"
									id="edit"
									@click="doUpdate(relativeFriend)"
								>
									<i class="bi bi-pen"></i>
								</button>
								<button
									type="button"
									class="btn btn-danger btn-glow btn-icon"
									data-bs-toggle="tooltip"
									title="刪除"
									@click="deleteRelativeFriends(relativeFriend.id)"
								>
									<i class="bi bi-trash"></i>
								</button>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</template>
<script>
import { Field, Form } from 'vee-validate';
import _ from 'lodash';
export default {
	props: {
		cusCode: null,
		setCusCode: Function
	},
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	data: function () {
		return {
			relativeFriends: [],
			authYn: null,
			id: null,
			reltypeCode: '',
			idn: null,
			relName: null,
			gender: null,
			birthday: null,
			cphone: null,
			education: null,
			organization: null,
			post: null,
			note: null,
			relativeTypeMenu: [],
			genderType: []
		};
	},
	computed: {
		auth: function () {
			return this.authYn === 'Y';
		}
	},
	mounted: function () {
		var self = this;
		self.getRelativeFriends();
		self.getRelativeType();
		self.getGenderType();
		self.chkCustomerAuth();
	},
	methods: {
		getRelativeFriends: async function () {
			var self = this;
			const ret = await self.$api.getRelativeFriendsApi({
				cusCode: self.cusCode
			});
			self.relativeFriends = ret.data;
		},
		doUpdate: function (relativeFriend) {
			var self = this;
			self.id = relativeFriend.id;
			self.reltypeCode = relativeFriend.reltypeCode;
			self.idn = relativeFriend.idn;
			self.relName = relativeFriend.relName;
			self.gender = relativeFriend.gender;
			self.birthday = relativeFriend.birthday;
			self.cphone = relativeFriend.cphone;
			self.education = relativeFriend.education;
			self.organization = relativeFriend.organization;
			self.post = relativeFriend.post;
			self.note = relativeFriend.note;
		},
		insertRelativeFriends: async function () {
			var self = this;
			var url = self.config.apiPath + '/cus/relativeFriends';

			self.$refs.homeFriend.validate().then(async function (pass) {
				if (pass.valid) {
					const ret = await self.$api.insertRelativeFriendsApi({
						cusCode: self.cusCode,
						idn: self.idn,
						relName: self.relName,
						reltypeCode: self.reltypeCode,
						gender: self.gender,
						birthday: _.formatDate(self.birthday),
						cphone: self.cphone,
						education: self.education,
						organization: self.organization,
						post: self.post,
						note: self.note
					});
					if (ret.data > 0) {
						self.$bi.alert('新增成功');
						self.getRelativeFriends();
					} else {
						self.$bi.alert('系統無此客戶，無法儲存');
					}
				}
			});
		},
		updateRelativeFriends: async function () {
			var self = this;
			var url = self.config.apiPath + '/cus/relativeFriends';
			self.$refs.homeFriend.validate().then(async function (pass) {
				if (pass.valid) {
					const ret = await self.$api.updateRelativeFriendsApi({
						id: self.id,
						cusCode: self.cusCode,
						idn: self.idn,
						relName: self.relName,
						reltypeCode: self.reltypeCode,
						gender: self.gender,
						birthday: _.formatDate(self.birthday),
						cphone: self.cphone,
						education: self.education,
						organization: self.organization,
						post: self.post,
						note: self.note
					});
					self.$bi.alert('更新成功');
					self.getRelativeFriends();
				}
			});
		},
		deleteRelativeFriends: async function (id) {
			var self = this;
			var url = self.config.apiPath + '/cus/relativeFriends';
			const ret = await self.$api.deleteRelativeFriendsApi({
				id: id,
				cusCode: self.cusCode
			});

			self.$bi.alert('刪除成功');
			self.getRelativeFriends();
			if ((self.id = id)) {
				self.id = null;
				self.cancel();
			}
		},
		getRelativeType: async function () {
			var self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'RELTYPE_CODE'
			});
			self.relativeTypeMenu = ret.data;
		},
		getGenderType: async function () {
			var self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'GENDER'
			});
			self.genderType = ret.data;
		},
		cancel: function () {
			var self = this;
			self.id = null;
			self.reltypeCode = null;
			self.idn = null;
			self.relName = null;
			self.gender = null;
			self.birthday = null;
			self.cphone = null;
			self.education = null;
			self.organization = null;
			self.post = null;
			self.note = null;
		},
		chkCustomerAuth: async function () {
			var self = this;
			const resp = await self.$api.chkCustomerAuthApi({
				cusCode: self.cusCode,
				progCode: 'ACUS_002'
			});
			self.authYn = resp.data.authYn;
		}
	}
};
</script>
