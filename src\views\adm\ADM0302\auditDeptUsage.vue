<template>
	<dynamic-title></dynamic-title>
	<div>
		<div class="row">
			<div class="col-12">
				<div class="card card-form">
					<div class="card-body">
						<div class="row g-3 align-items-end">
							<div class="col-md-3">
								<label class="form-label">查詢分行(單位)</label>
								<select name="branCodes" id="sltBran" class="form-select" data-inline="true" v-model="branCode">
									<option value="">全部</option>
									<option v-for="branInfo in branMenu" :value="branInfo.branCode">
										{{ branInfo.branCode }} {{ branInfo.branName }}
									</option>
								</select>
							</div>
							<div class="col-md-6">
								<label class="form-label">查詢日期區間</label>
								<div class="input-group">
									<vue-field
										type="date"
										id="beginDate"
										name="beginDate"
										v-model="logStartDt"
										size="13"
										label="查詢日期起"
										class="form-control"
										maxlength="10"
										:class="{ 'is-invalid': showErrors && errors.logStartDt }"
									>
									</vue-field>

									<span class="input-group-text">~</span>

									<vue-field
										type="date"
										id="endDate"
										name="endDate"
										v-model="logEndDt"
										size="13"
										label="查詢日期迄"
										class="form-control"
										maxlength="10"
										:min="minValidEndDt"
										:class="{ 'is-invalid': showErrors && errors.logEndDt }"
									>
									</vue-field>
								</div>
							</div>
							<div class="col-lg-8 col-md-10">
								<label class="form-label">功能模組</label>
								<div class="row g-3">
									<div class="col-md-6">
										<div class="input-group">
											<span class="input-group-text">系統模組</span>
											<select
												name="moduleMenuCode"
												id="menuListId"
												class="form-select"
												v-model="moduleMenuCode"
												@change="getBranchFunctionMenu()"
											>
												<option value="">全部</option>
												<option v-for="module in moduleMenu" :value="module.menuCode">{{ module.menuName }}</option>
											</select>
										</div>
									</div>
									<div class="col-md-6">
										<div class="input-group">
											<span class="input-group-text">功能項目</span>
											<select name="menuCode" id="progListId" class="form-select" v-model="functionMenuCode">
												<option value="">全部</option>
												<option v-for="branchFunction in branchFunctionMenu" :value="branchFunction.menuCode">
													{{ branchFunction.menuName }}
												</option>
											</select>
										</div>
									</div>
								</div>
							</div>
							<div class="col-md-2">
								<button role="button" class="btn btn-primary btn-glow btn-searc=h" @click.prevent="gotoPage(0)">查詢</button>
							</div>
						</div>
					</div>
				</div>
				<div id="searchResult">
					<div class="card card-table">
						<div class="card-header">
							<h4>分行使用紀錄列表</h4>
							<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
						</div>
						<div class="table-responsive">
							<table class="table table-RWD table-hover text-center">
								<thead>
									<tr>
										<th>日期</th>
										<th>系統模組</th>
										<th class="text-start">功能項目</th>
										<th>分行(單位)代號</th>
										<th>分行(單位)名稱</th>
										<th>次數</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="item in pageData.content">
										<td data-th="日期">{{ item.logDt }}</td>
										<td data-th="系統模組">{{ item.menuOne }}</td>
										<td data-th="功能項目" class="text-start">{{ item.menuTwo }}</td>
										<td data-th="分行代號">{{ item.branCode }}</td>
										<td data-th="分行(單位)名稱">{{ item.branName }}</td>
										<td data-th="次數">{{ item.tcnt }}</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!--頁面內容 end-->
	</div>
</template>
<script>
import _ from 'lodash';
import { Field } from 'vee-validate';
import dynamicTitle from '@/views/components/dynamicTitle.vue';
import pagination from '@/views/components/pagination.vue';

export default {
	components: {
		'vue-field': Field,
		'vue-pagination': pagination,
		dynamicTitle
	},
	data: function () {
		return {
			//API 用參數
			buCode: null,
			branCode: '',
			moduleMenuCode: '',
			functionMenuCode: '',
			userCode: null,
			logStartDt: null,
			logEndDt: null,
			//下拉選單
			branMenu: [],
			moduleMenu: [],
			branchFunctionMenu: [],
			//主要顯示資料
			pageData: {
				type: Object,
				data: {
					content: [],
					number: 0,
					totalPages: 0,
					totalElements: 0,
					numberOfElements: 0
				}
			},
			pageable: {
				page: 0,
				size: 20,
				sort: 'bran_code',
				direction: 'ASC'
			}
		};
	},
	computed: {
		minValidEndDt() {
			return this.logStartDt ? this.logStartDt : null;
		}
	},
	watch: {
		logStartDt: function (newVal, oldVal) {
			this.showErrors = false;
			if (newVal && this.logEndDt && newVal > this.logEndDt) {
				this.logEndDt = null;
			}
		}
	},
	mounted: function () {
		var self = this;
		self.getBranMenu();
		self.getModuleMenu();
		self.getBranchFunctionMenu();
	},
	methods: {
		getBranMenu: function () {
			var self = this;
			self.$api.getAllBranchesMenuApi().then(function (ret) {
				self.branMenu = ret.data;
			});
		},
		getModuleMenu: function () {
			var self = this;
			self.$api.getModuleMenuApi().then(function (ret) {
				self.moduleMenu = ret.data;
			});
		},
		getBranchFunctionMenu: function () {
			var self = this;
			if (_.isBlank(self.moduleMenuCode)) {
				return;
			}

			self.$api
				.getBranchFunctionMenuApi({
					menuCode: self.moduleMenuCode
				})
				.then(function (ret) {
					self.branchFunctionMenu = ret.data;
				});
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: function (page) {
			var self = this;
			var url = _.toPageUrl('', page, self.pageable);

			self.$api
				.getBranAccessCntLogApi(
					{
						branCode: self.branCode,
						moduleMenuCode: self.moduleMenuCode,
						functionMenuCode: self.functionMenuCode,
						logStartDt: _.formatDate(self.logStartDt),
						logEndDt: _.formatDate(self.logEndDt)
					},
					url
				)
				.then(function (ret) {
					self.pageData = ret.data;
				});
		}
	}
};
</script>
