<template>
	<!-- Modal 2 基金 -->
	<vue-modal :is-open="isOpenModal['fund']" :before-close="closeModal('fund')">
		<template v-slot:content="props">
			<vue-pro-mgt-fund-modal
				:close="props.close"
				ref="fundMgtModal"
				:fin-req-code-menu="finReqCodeMenu"
				:action-type="actionType"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			></vue-pro-mgt-fund-modal>
		</template>
	</vue-modal>
	<!-- Modal 2 End -->

	<!-- Modal 3 信託-結構型商品 -->
	<vue-modal :is-open="isOpenModal['sp']" :before-close="closeModal('sp')">
		<template v-slot:content="props">
			<vue-pro-mgt-sp-modal
				:close="props.close"
				ref="spMgtModal"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:action-type="actionType"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			></vue-pro-mgt-sp-modal>
		</template>
	</vue-modal>
	<!-- Modal 3 End -->

	<!-- Modal 4 ETF -->
	<vue-modal :is-open="isOpenModal['etf']" :before-close="closeModal('etf')">
		<template v-slot:content="props">
			<vue-pro-mgt-etf-modal
				:close="props.close"
				ref="etfMgtModal"
				:fin-req-code-menu="finReqCodeMenu"
				:action-type="actionType"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			></vue-pro-mgt-etf-modal>
		</template>
	</vue-modal>
	<!-- Modal 4 End -->

	<!-- Modal 5 債券 -->
	<vue-modal :is-open="isOpenModal['bond']" :before-close="closeModal('bond')">
		<template v-slot:content="props">
			<vue-pro-mgt-bond-modal
				:close="props.close"
				ref="bondMgtModal"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:action-type="actionType"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			></vue-pro-mgt-bond-modal>
		</template>
	</vue-modal>
	<!-- Modal 5 End -->

	<!-- Modal 6 保險 -->
	<vue-modal :is-open="isOpenModal['ins']" :before-close="closeModal('ins')">
		<template v-slot:content="props">
			<vue-pro-mgt-ins-modal
				:close="props.close"
				ref="insMgtModal"
				:fin-req-code-menu="finReqCodeMenu"
				:action-type="actionType"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			></vue-pro-mgt-ins-modal>
		</template>
	</vue-modal>
	<!-- Modal 6 End -->

	<!-- Modal 7 組合商品 銀行結構型商品 DCI -->
	<vue-modal :is-open="isOpenModal['dci']" :before-close="closeModal('dci')">
		<template v-slot:content="props">
			<vue-pro-mgt-dci-modal
				:close="props.close"
				ref="dciMgtModal"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:action-type="actionType"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			></vue-pro-mgt-dci-modal>
		</template>
	</vue-modal>
	<!-- Modal 7 End -->

	<!-- Modal 9 海外股票 PFD -->
	<vue-modal :is-open="isOpenModal['pfd']" :before-close="closeModal('pfd')">
		<template v-slot:content="props">
			<vue-pro-mgt-pfd-modal
				:close="props.close"
				ref="pfdMgtModal"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:action-type="actionType"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			></vue-pro-mgt-pfd-modal>
		</template>
	</vue-modal>
	<!-- Modal 9 End -->
</template>
<script>
import _ from 'lodash';
import vueModal from '@/views/components/model.vue';
import vueProMgtFundModal from '@/views/pro/productInfo/include/fundMgtModal.vue';
import vueProMgtSpModal from '@/views/pro/productInfo/include/spMgtModal.vue';
import vueProMgtEtfModal from '@/views/pro/productInfo/include/etfMgtModal.vue';
import vueProMgtBondModal from '@/views/pro/productInfo/include/bondMgtModal.vue';
import vueProMgtInsModal from '@/views/pro/productInfo/include/insMgtModal.vue';
import vueProMgtDciModal from '@/views/pro/productInfo/include/dciMgtModal.vue';
import vueProMgtPfdModal from '@/views/pro/productInfo/include/pfdMgtModal.vue';
export default {
	components: {
		vueModal,
		vueProMgtFundModal,
		vueProMgtSpModal,
		vueProMgtEtfModal,
		vueProMgtBondModal,
		vueProMgtInsModal,
		vueProMgtDciModal,
		vueProMgtPfdModal
	},
	props: {},
	data: function () {
		return {
			actionType: null,
			gotoPage: () => void 0,
			proPriceRangeMenu: undefined,
			finReqCodeMenu: undefined,
			isOpenModal: {
				fund: false,
				sp: false,
				etf: false,
				pfd: false,
				bond: false,
				ins: false,
				dci: false,
				sec: false
			}
		};
	},
	methods: {
		getDetail: function (eventId) {
			var self = this;
			if (_.isBlank(eventId)) {
				return;
			}
			self.$api
				.getProductLogApi({
					eventId: eventId
				})
				.then(function (ret) {
					let item = ret.data;
					self.actionType = 'VIEW';
					self.benchmarkCode = null;
					self.benchmark = null;
					var eventId = null;
					if (item.status === 'P') {
						eventId = item.eventId;
					}
					if (item.pfcatCode == 'FUND') {
						self.$refs.fundMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
						self.isOpenModal.fund = true;
					} else if (item.pfcatCode == 'ETF') {
						self.$refs.etfMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
						self.isOpenModal.etf = true;
					} else if (item.pfcatCode == 'FB') {
						self.$refs.bondMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
						self.isOpenModal.bond = true;
					} else if (item.pfcatCode == 'INS') {
						self.$refs.insMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
						self.isOpenModal.ins = true;
					} else if (item.pfcatCode == 'DCD') {
						self.$refs.dciMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
						self.isOpenModal.dci = true;
					} else if (item.pfcatCode == 'SP') {
						self.$refs.spMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
						self.isOpenModal.sp = true;
					} else if (item.pfcatCode == 'SEC') {
						self.$refs.secMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
						self.isOpenModal.sec = true;
					} else if (item.pfcatCode == 'PFD') {
						self.$refs.pfdMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
						self.isOpenModal.pfd = true;
					} else {
						/*
          else if (
            item.pfcatCode == 'SP' ||
            item.pfcatCode == 'SSPF' ||
            item.pfcatCode == 'TSPD' ||
            item.pfcatCode == 'TSPF' ||
            item.pfcatCode == 'FSTD'
          ) {
            self.$refs.spModal.getProDatas(item.proCode, item.pfcatCode);
          } 
          */
						self.$refs.proModal.getProDatas(item.proCode, item.pfcatCode);
					}
				});
		},
		downloadFile: function (proFile) {
			var self = this;
			var eventId = proFile.eventId;
			var proFileId = proFile.proFileId;
			var url = self.config.apiPath + '/pro/proFile';
			var fileName = proFile.showName;
			self.$api
				.downloadProFileApi({
					proFileId: proFileId,
					eventId: eventId
				})
				.then(function (data) {
					var link = document.createElement('a');
					var url = URL.createObjectURL(data);
					link.download = fileName;
					link.href = url;
					document.body.appendChild(link);
					link.click();
					link.remove();
					setTimeout(() => URL.revokeObjectURL(url), 1000);
				});
		},
		downloadOtherFile: function (fileId) {
			var self = this;
			self.$api.downloadOtherFileApi({
				fileId: fileId
			});
		},
		closeModal: function (modalName) {
			this.isOpenModal[modalName] = false;
		}
	}
};
</script>
