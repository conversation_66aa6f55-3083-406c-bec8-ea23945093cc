{"status": 200, "data": {"cusAssetAmountMergeList": [], "alcRate": "100", "sumInvAmtLc": 0, "sumMktAmtLc": 0, "sumUmtLc": 0, "sumUmtFc": 0, "sumBalFc": 0, "sumUseBalFc": 0, "cusLoansMergeRespList": []}, "timestamp": "2025/07/09", "sqlTracer": [{"data": [], "sqlInfo": "SELECT CA.PFCAT_CODE,        PP.PFCAT_NAME,        SUM(CA.INV_AMT_LC)                                                            SUM_INV_AMT_LC,        SUM(CA.MKT_AMT_LC)                                                            SUM_MKT_AMT_LC,        IIF(TOT_INV_AMT_LC > 0, SUM(CA.INV_AMT_LC) / TOT_INV_AMT_LC, 0)               ALC_RATE,        IIF(SUM(CA.INV_AMT_LC) > 0, (SUM(CA.MKT_AMT_LC) / SUM(CA.INV_AMT_LC) - 1), 0) RTN_RATE FROM CUS_ASSETAMOUNT CA          INNER JOIN PRO_PFCATS PP ON CA.PFCAT_CODE = PP.PFCAT_CODE          INNER JOIN (SELECT SUM(CA.INV_AMT_LC) TOT_INV_AMT_LC                      FROM CUS_ASSETAMOUNT CA                               INNER JOIN PRO_PFCATS PP ON CA.PFCAT_CODE = PP.PFCAT_CODE                      WHERE CA.CUS_CODE = :cusCode) CA2 ON 1 = 1 WHERE CA.CUS_CODE = :cusCode GROUP BY CA.PFCAT_CODE, PP.PFCAT_NAME, TOT_INV_AMT_LC,class com.bi.pbs.cus.web.model.CusAssetAmountMergeResp,{cusCode=00956829}"}, {"data": [], "sqlInfo": " SELECT CL.CUR_CODE, PC.CUR_NAME, SUM(CL.USE_AMT_FC * PLFR.FX_RATE) SUM_USE_AMT_LC,SUM(CL.USE_AMT_FC * PLFR.FX_RATE)/CL2.TOT_USE_AMT_LC ALC_RATE,  SUM(USE_AMT_FC) SUM_USE_AMT_FC, SUM(BAL_FC) SUM_BAL_FC, SUM(USE_AMT_FC - BAL_FC) SUM_USE_BAL_FC  FROM CUS_LOANS CL  INNER JOIN PRO_LATEST_FX_RATES PLFR ON CL.CUR_CODE = PLFR.CUR_CODE  INNER JOIN (SELECT SUM(CL.USE_AMT_FC * PLFR.FX_RATE) TOT_USE_AMT_LC FROM CUS_LOANS CL INNER JOIN PRO_LATEST_FX_RATES PLFR ON CL.CUR_CODE = PLFR.CUR_CODE WHERE CL.CUS_CODE = :cusCode) CL2 ON 1=1  LEFT JOIN  PRO_CURRENCIES PC ON CL.CUR_CODE = PC.CUR_CODE  WHERE CL.CUS_CODE = :cusCode  GROUP BY CL.CUR_CODE, PC.CUR_NAME, CL2.TOT_USE_AMT_LC ,class com.bi.pbs.cus.web.model.CusLoansMergeResp,{cusCode=00956829}"}]}