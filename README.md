# pbs-web

## Swagger

[Swagger API UI](http://192.168.0.180:8081/pbs-api/swagger-ui/index.html)

## 目錄結構

```
pbs-web/
├── public/ # 靜態資源目錄
│ ├── lang/ # i18n 多國語言目錄
│ │ ├── langList.json # 專案使用語言設定檔案
│ │ ├── zh.json # 繁體中文詞彙表
│ │ ├── en.json # 英文詞彙表
│ │ └── ... # 更多語言詞彙表（如：ja.json等）
│ ├── validate/ # 表單驗證自定義語言設定（VeeValidate）
│ │ └── zh_TW.json # 部分規則自定義的繁體中文訊息
│ └── favicon.ico # 網站頁籤icon
├── src/ # 源碼目錄
│ ├── api/ # api 檔案目錄
│ │ │ └── mock/ # mock API 目錄
│ │ ├── apiService.js # 依環境載入不同 API
│ │ ├── stores.js # Vuex api
│ │ └── ...
│ ├── assets/ # 靜態資源（圖片、CSS、字型等）
│ │ └── css/ # CSS目錄
│ │ ├── bi/ # 自定義的 CSS 檔案目錄
│ │ │ ├── base.css
│ │ │ └── ...
│ │ └── framework-plugin/ # 套件 CSS 目錄
│ │ ├── fonts/
│ │ │ ├── @fortawesome/
│ │ │ │ └── ...
│ │ │ ├── bs-font/
│ │ │ │ └── ...
│ │ │ └── line-awesome/
│ │ │ └── ...
│ │ ├── fullcalendar/
│ │ │ └── main.min.css
│ │ ├── vue/
│ │ │ └── vue-loading.css
│ │ ├── bootstrap-icons.css
│ │ └── ...
│ ├── filters/ # filters目錄
│ │ └── filter.js # 自定義的全域fikter
│ ├── router/ # Vue Router 配置
│ │ ├── index.js # 主路由設定檔案
│ │ ├── adm.js # 管理員區域路由設定檔案
│ │ └── ... # 更多區域路由設定檔案
│ ├── store/ # Vuex 狀態管理
│ │ ├── menus.js
│ │ └── userInfo.js
│ ├── utils/ # 工具函數目錄
│ │ ├── bi/ # 自定義的函式庫
│ │ │ ├── base.js # 自定義 AJAX 封裝
│ │ │ └── module.js # sweetalert
│ │ ├── mixin/
│ │ │ └── userCodeComplement.js
│ │ ├── validate/
│ │ │ └── file.js # 檔案驗證規則(大小、類型等)
│ │ └── lodashExtensions.js # 自定義的 lodash 擴展函式
│ ├── views/ # 頁面目錄
│ │ ├── HomeView.vue # 首頁
│ │ ├── Login.vue
│ │ ├── ...
│ │ │── components/ # 通用元件
│ │ │ ├── model.vue
│ │ │ ├── biTree.vue
│ │ │ └── ...
│ │ │── adm/
│ │ │ ├── ADM0101/
│ │ │ │ └── admRole.vue
│ │ │ ├── ADM0103/
│ │ │ │ └── admMenuPreview.vue
│ │ │ └── ...
│ │ ├── cus/
│ │ │ ├── CUS003/
│ │ │ │ └── cusAll.html.vue
│ │ │ └── ...
│ │ └── ... # 更多區域頁面
│ ├── App.vue # 主 Vue 元件
│ └── main.js # 應用入口檔案
├── .env # 環境變數
├── .env.development
├── .env.production
├── .gitignore # Git 忽略檔案
├── index.html # 專案首頁（Vite 入口文件）
├── .prettierignore # prettier 忽略檔案
├── .prettierrc # 統一程式碼風格的設定檔案
├── package.json # npm 套件管理文件
├── README.md # 專案說明文件
└── vite.config.js # Vite 配置文件
```
