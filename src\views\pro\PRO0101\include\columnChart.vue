<template>
	<!-- 線圖 -->
	<div :ref="chartId" :id="chartId" style="height: 500px"></div>
</template>
<script>
import * as am5 from '@amcharts/amcharts5';
import * as am5xy from '@amcharts/amcharts5/xy';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import { toRaw } from 'vue';
export default {
	props: {
		chartId: String,
		proCode: String,
		propChartData: Array
	},
	data: function () {
		return {
			am5Obj: {}
		};
	},
	watch: {},
	mounted: function () {},
	methods: {
		initChart() {
			let self = this;

			let { am5Obj } = self;
			let firstLoad = false;
			//透過 toRaw 解決因為 vue data proxy 導致跟 amcharts 衝突
			let { xAxis, yAxis, root, chart, yRenderer, series } = toRaw(am5Obj);
			if (!root) {
				firstLoad = true;
				root = am5.Root.new(self.chartId);
				root._logo.dispose();

				// Set themes
				// https://www.amcharts.com/docs/v5/concepts/themes/
				root.setThemes([am5themes_Animated.new(root)]);

				//橫線條

				// Create chart
				// https://www.amcharts.com/docs/v5/charts/xy-chart/
				chart = root.container.children.push(
					am5xy.XYChart.new(root, {
						panX: false,
						panY: false,
						wheelX: 'none',
						wheelY: 'none',
						paddingLeft: 0
					})
				);

				// We don't want zoom-out button to appear while animating, so we hide it
				chart.zoomOutButton.set('forceHidden', true);

				// Create axes
				// https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
				yRenderer = am5xy.AxisRendererY.new(root, {
					minGridDistance: 30,
					minorGridEnabled: true
				});

				yRenderer.grid.template.set('location', 1);

				yAxis = chart.yAxes.push(
					am5xy.CategoryAxis.new(root, {
						maxDeviation: 0,
						categoryField: 'name',
						renderer: yRenderer,
						tooltip: am5.Tooltip.new(root, { themeTags: ['axis'] })
					})
				);

				xAxis = chart.xAxes.push(
					am5xy.ValueAxis.new(root, {
						maxDeviation: 0,
						min: 0,
						numberFormatter: am5.NumberFormatter.new(root, {
							numberFormat: "#,###a'%'"
						}),
						extraMax: 0.1,
						renderer: am5xy.AxisRendererX.new(root, {
							strokeOpacity: 0.1,
							minGridDistance: 80
						})
					})
				);

				// Add series
				// https://www.amcharts.com/docs/v5/charts/xy-chart/series/
				series = chart.series.push(
					am5xy.ColumnSeries.new(root, {
						name: 'Series 1',
						xAxis: xAxis,
						yAxis: yAxis,
						valueXField: 'value',
						categoryYField: 'name',
						tooltip: am5.Tooltip.new(root, {
							pointerOrientation: 'left',
							labelText: '{valueX}%'
						})
					})
				);

				// Rounded corners for columns
				series.columns.template.setAll({
					cornerRadiusTR: 5,
					cornerRadiusBR: 5,
					strokeOpacity: 0
				});

				// Make each column to be of a different color
				series.columns.template.adapters.add('fill', function (fill, target) {
					return chart.get('colors').getIndex(series.columns.indexOf(target));
				});

				series.columns.template.adapters.add('stroke', function (stroke, target) {
					return chart.get('colors').getIndex(series.columns.indexOf(target));
				});

				series.bullets.push(function () {
					return am5.Bullet.new(root, {
						sprite: am5.Label.new(root, {
							text: "{valueX.formatNumber('#.#')}%",
							fill: root.interfaceColors.get('alternativeText'),
							centerY: am5.p50,
							centerX: am5.p50,
							populateText: true
						})
					});
				});

				// 處理props proxy data問題
				var data = JSON.parse(JSON.stringify(self.propChartData));

				// 處理排序問題
				var chartData = [];
				var sortIndex = 0;
				for (var i = data.length - 1; i >= 0; i--) {
					chartData[sortIndex] = data[i];
					sortIndex++;
				}

				yAxis.data.setAll(chartData);
				series.data.setAll(chartData);
			}

			if (firstLoad) {
				Object.assign(am5Obj, { xAxis, yAxis, root, chart, yRenderer, series });

				chart.set(
					'cursor',
					am5xy.XYCursor.new(root, {
						behavior: 'none',
						xAxis: xAxis,
						yAxis: yAxis
					})
				);

				// Make stuff animate on load
				// https://www.amcharts.com/docs/v5/concepts/animations/
				series.appear(1000);
				chart.appear(1000, 100);
			}
		}
	} // methods end
};
</script>
