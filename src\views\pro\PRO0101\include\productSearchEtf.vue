<template>
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<!--頁面內容 ETF start-->
			<div class="tab-nav-tabs my-3">
				<ul class="nav nav-tabs nav-justified">
					<li class="nav-item">
						<a class="nav-link" :class="{ active: activeTab == 'common' }" data-bs-toggle="tab"
							@click="changeTab('common')">一般篩選</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" :class="{ active: activeTab == 'fast' }" data-bs-toggle="tab"
							@click="changeTab('fast')">快速篩選</a>
					</li>
				</ul>

				<div class="tab-content">
					<div class="tab-pane fade show" :class="{ 'active show': activeTab == 'common' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
								<h4>查詢條件</h4>
								<span class="tx-square-bracket">為必填欄位</span>
							</div>
							<div class="collapse show" id="collapseListGroup1">
								<div class="card-body">
									<form>
										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">商品代號 </label>
												<input class="form-control" id="prod_bank_pro_code" maxlength="20" v-model="bankProCode"
													size="25" type="text" value="" />
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">商品名稱</label>
												<input class="form-control" id="prod_pro_name" maxlength="20" v-model="proName" size="45"
													type="text" value="" />
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">計價幣別</label>
												<select class="selectpicker form-control" id="curMenuEtf" multiple title="請選擇幣別"
													v-model="curObjs" data-style="btn-white">
													<option value="">全部</option>
													<option v-for="(item, index) in curOption" :key="index" :value="item.value">
														{{ $filters.defaultValue(item.name, '--') }}
													</option>
												</select>
											</div>
										</div>

										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 風險等級 </label>
												<div class="form-check-group">
													<div class="form-check form-check-inline" v-for="(item, index) in riskMenu">
														<input type="checkbox" class="form-check-input" name="riskCodes" v-model="riskCodes"
															:id="'riskGrade-' + index" :value="item.riskCode" />
														<label :for="'riskGrade-' + index" class="form-check-label">{{ item.riskName }}</label>
													</div>
												</div>
											</div>
											<!-- TODO -->
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">限PI銷售</label>
												<div v-for="item in profInvestorMenu" class="form-check form-check-inline">
													<input class="form-check-input" :id="'profInvestor' + item.codeValue" v-model="profInvestorYn"
														type="radio" :value="item.codeValue" name="fastCode" />
													<label class="form-check-label" :for="'profInvestor' + item.codeValue">{{
														$filters.defaultValue(item.codeName, '--')
													}}</label>
												</div>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">ETF類型</label>
												<select class="form-select" id="proType" name="proType" title="請選擇類型" v-model="proTypeCode"
													data-style="btn-white">
													<option value="">全部</option>
													<option v-for="item in proTypeMenu" :value="item.proTypeCode">
														{{ $filters.defaultValue(item.proTypeName, '--') }}
													</option>
												</select>
											</div>
										</div>

										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">ISINCODE</label>
												<input class="form-control" id="isinCode" maxlength="20" v-model="isinCode" size="45"
													type="text" value="" />
											</div>
										</div>

										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">標準差(%)</label>
												<div class="input-group">
													<select name="local" class="form-select" v-model="sdVaule" @change="sdMenuChangeHandler()">
														<option value="">全部</option>
														<option v-for="item in sdMenu" :value="item.termValue">
															{{ $filters.defaultValue(item.termName, '--') }}
														</option>
													</select>
													<input type="text" class="form-control sdInput" size="5" v-model="sdRangeMin" />
													<div class="input-group-text sdInput">~</div>
													<input type="text" class="form-control sdInput" size="5" v-model="sdRangeMax" />
												</div>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">ETF規模(千萬)</label>
												<div class="input-group">
													<input type="number" class="form-control" size="5" v-model="etfSizeMin" />
													<div class="input-group-text">~</div>
													<input type="number" class="form-control" size="5" v-model="etfSizeMax" />
												</div>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">標的績效(%)</label>
												<div class="input-group">
													<select name="lipperRankType" class="form-select" v-model="perfTime"
														@change="perfTimeChangeHandler">
														<option value="">全部</option>
														<option v-for="item in perfMenu" :value="item.codeValue">
															{{ $filters.defaultValue(item.codeName, '--') }}
														</option>
													</select>
													<input type="number" class="form-control perfInput" size="5" v-model="perfMin" />
													<div class="input-group-text perfInput">~</div>
													<input type="number" class="form-control perfInput" size="5" v-model="perfMax" />
												</div>
											</div>
										</div>

										<div class="form-row">
											<div class="form-group col-12 col-lg-6">
												<label class="form-label">發行機構</label>
												<button type="button" class="btn btn-primary" @click="groupIssuerModalHandler()">選擇發行機構</button>
												<vue-modal :is-open="isOpenIssuerModal" :before-close="isOpenIssuerModal = false">
													<template v-slot:content="props">
														<vue-group-issuer-modal :close="props.close" ref="groupIssuerModalRef" id="groupIssuerModal"
															:pfcat-code="'ETF'" @selected="selectedIssuer"></vue-group-issuer-modal>
													</template>
												</vue-modal>
											</div>
											<div class="form-group col-12 col-lg-6">
												<label class="form-label">交易所</label>
												<button type="button" class="btn btn-primary" @click="groupProExchangeModalHandler()">
													選擇交易所
												</button>
												<vue-modal :is-open="isOpenExchangeModal" :before-close="isOpenExchangeModal = false">
													<template v-slot:content="props">
														<vue-group-pro-exchange-modal :close="props.close" ref="groupProExchangeModalRef"
															id="groupProExchangeModal" :pro-exchange-prop="proExchangeItem"
															@selected="selectedProExchange"></vue-group-pro-exchange-modal>
													</template>
												</vue-modal>
											</div>
										</div>

										<div class="form-row d-flex align-items-start">
											<div class="col">
												<div style="padding-left: 110px; padding-bottom: 15px" v-for="item in issuerItem">
													<span class="form-check-label"> {{ $filters.defaultValue(item.issuerName, '--') }}</span>
													<a href="#" @click="deleteIssuerItem(item.issuerCode)">
														<img :src="getImgURL('icon', 'i-cancel.png')" />
													</a>
												</div>
											</div>

											<div class="col">
												<div style="padding-left: 110px; padding-bottom: 15px" v-for="item in proExchangeItem">
													<span class="form-check-label"> {{ $filters.defaultValue(item.exchangeName, '--') }}</span>
													<a href="#" @click="deleteProExchangeItem(item.exchangeCode)">
														<img :src="getImgURL('icon', 'i-cancel.png')" />
													</a>
												</div>
											</div>
										</div>

										<div class="form-footer">
											<button class="btn btn-primary" @click.prevent="gotoPage(0)">查詢</button>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>

					<div class="tab-pane fade" :class="{ 'active show': activeTab == 'fast' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2">
								<h4>查詢條件</h4>
								<span class="tx-square-bracket">為必填欄位</span>
							</div>
							<div class="collapse show" id="collapseListGroup2">
								<div class="card-body">
									<form>
										<div class="form-row">
											<div class="form-group col-12 col-lg-12">
												<label class="form-label tx-require"> 篩選條件 </label>
												<div class="form-check-group" v-for="item in etfFastMenu" @change="fastChange(item.codeValue)">
													<input class="form-check-input" :id="'fast' + item.codeValue" name="fastCode"
														v-model="fastCode" :value="item.codeValue" type="radio" />
													<label class="form-check-label" :for="'fast' + item.codeValue">{{
														$filters.defaultValue(item.codeName, '--')
													}}</label>
												</div>
											</div>

											<div class="form-group col-12 col-lg-6" id="rangeFixedTrEtf" style="display: none">
												<label class="form-label tx-require"> 顯示區間</label>

												<select class="form-select" id="prod_protype_code" v-model="timeRange">
													<option v-for="item in timeRangeMenu" :value="item">
														{{ $filters.defaultValue(item.termName, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-6" id="proPerfTimeTrEtf" style="display: none">
												<label class="form-label tx-require">標的績效 </label>
												<select class="form-select" id="vfAstStat_stat_code" v-model="perf">
													<option v-for="item in perfMenu" :value="item.codeValue">
														{{ $filters.defaultValue(item.codeName, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-6" id="maxRowIdTrEtf" style="display: none">
												<label class="form-label tx-require">顯示資料筆數</label>
												<select class="form-select" id="maxRowId" v-model="rowNumber">
													<option value="">全部</option>
													<option v-for="item in rowNumerMenu" :value="item.rangeFixed">
														{{ $filters.defaultValue(item.termName, '--') }}
													</option>
												</select>
											</div>
										</div>
									</form>

									<div class="form-footer">
										<button class="btn btn-primary" @click.prevent="gotoFastPage(0)">查詢</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div id="searchResult" v-if="pageData.content.length > 0">
				<div class="tab-nav-line">
					<ul class="nav nav-line"
						style="background-color: transparent; margin-top: 10px; margin-bottom: 20px; border-bottom: 2px solid #e2e2e2">
						<li class="nav-item"><a href="#pie_chart1" data-bs-toggle="tab" class="nav-link active">基本資料</a></li>
						<li class="nav-item"><a href="#pie_chart2" data-bs-toggle="tab" class="nav-link">市場績效</a></li>
					</ul>
					<div class="tab-content">
						<div role="tabpanel" class="tab-pane active" id="pie_chart1">
							<div class="card card-table">
								<div class="card-header">
									<h4>查詢結果</h4>
									<div style="display: flex">
										<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
										<button type="button" class="btn btn-info ms-2"
											@click="performancesCompareModelHandler()">績效比較圖</button>
										<vue-modal :is-open="isOpenCompareModal" :before-close="isOpenCompareModal = false">
											<template v-slot:content="props">
												<vue-performances-compare-modal :close="props.close" ref="performancesCompareModalRef"
													id="performancesCompareModal"></vue-performances-compare-modal>
											</template>
										</vue-modal>
									</div>
								</div>
								<div class="table-responsive">
									<table class="table table-bordered table-blue">
										<thead>
											<tr>
												<th>加入比較</th>
												<th>
													商品代號<a v-if="activeTab === 'common'" href="#" class="icon-sort"
														@click="sort('BANK_PRO_CODE')"></a><a v-if="activeTab === 'fast'" href="#" class="icon-sort"
														@click="sortFast('BANK_PRO_CODE')"></a>
												</th>
												<th>
													商品中文名稱<a v-if="activeTab === 'common'" href="#" class="icon-sort"
														@click="sort('PRO_NAME')"></a><a v-if="activeTab === 'fast'" href="#" class="icon-sort"
														@click="sortFast('PRO_NAME')"></a>
												</th>
												<th>
													風險等級<a v-if="activeTab === 'common'" href="#" class="icon-sort"
														@click="sort('RISK_NAME')"></a><a v-if="activeTab === 'fast'" href="#" class="icon-sort"
														@click="sortFast('RISK_NAME')"></a>
												</th>
												<th>
													ETF類型<a v-if="activeTab === 'common'" href="#" class="icon-sort"
														@click="sort('PROTYPE_NAME')"></a><a v-if="activeTab === 'fast'" href="#" class="icon-sort"
														@click="sortFast('PROTYPE_NAME')"></a>
												</th>
												<th>
													計價幣別<a v-if="activeTab === 'common'" href="#" class="icon-sort"
														@click="sort('CUR_CODE')"></a><a v-if="activeTab === 'fast'" href="#" class="icon-sort"
														@click="sortFast('CUR_CODE')"></a>
												</th>
												<th>
													參考市值<a v-if="activeTab === 'common'" href="#" class="icon-sort"
														@click="sort('A_PRICE')"></a><a v-if="activeTab === 'fast'" href="#" class="icon-sort"
														@click="sortFast('A_PRICE')"></a>
												</th>
												<th>
													市值日期<a v-if="activeTab === 'common'" href="#" class="icon-sort"
														@click="sort('PRICE_DT')"></a><a v-if="activeTab === 'fast'" href="#" class="icon-sort"
														@click="sortFast('PRICE_DT')"></a>
												</th>
												<th>是否可銷售</th>
												<th>執行</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="(item, index) in pageData.content">
												<td data-th="加入比較" class="text-start text-center">
													<input class="form-check-input text-center" @click="checkoutPro(item)"
														:id="'id-' + item.bankProCode" type="checkbox" />
													<label class="form-check-label" :for="'id-' + item.bankProCode"></label>
												</td>
												<td data-th="商品代號">
													<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
												</td>
												<td class="text-start" data-th="商品中文名稱">
													<span>
														<a class="tx-link" @click="etfModalHandler(item.proCode, item.pfcatCode)">{{
															$filters.defaultValue(item.proName, '--')
														}}</a>
													</span>
												</td>
												<td data-th="風險等級">
													<span>{{ $filters.defaultValue(item.riskName, '--') }}</span>
												</td>
												<td data-th="ETF類型">
													<span>{{ $filters.defaultValue(item.proTypeName, '--') }}</span>
												</td>
												<td class="text-center" data-th="計價幣別">
													<span>{{ $filters.defaultValue(item.curCode, '--') }}</span>
												</td>
												<td class="text-center" data-th="參考市值">
													<span>{{ $filters.formatNumber(item.aprice, '0,0' || '--') }}</span>
												</td>
												<td class="text-center" data-th="市值日期">
													<span>{{ $filters.formatDate(item.priceDt, '--') }}</span>
												</td>
												<td class="text-center" data-th="是否可銷售">
													<span>{{ $filters.defaultValue(item.buyYnName, '--') }}</span>
												</td>
												<td class="text-center" data-th="執行">
													<button v-if="activeTab === 'fast' && fastCode === '06'" type="button" class="btn btn-primary"
														title="移除我的最愛" @click="remove(item.proCode)">
														移除最愛
													</button>
													<button v-else type="button" class="btn btn-dark btn-icon" data-bs-toggle="tooltip"
														title="加入我的最愛" @click="favoritesHandler(item.proCode, item.pfcatCode)">
														<i class="bi bi-heart text-danger"></i>
													</button>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
						<div role="tabpanel" class="tab-pane" id="pie_chart2">
							<div class="card card-table">
								<div class="card-header">
									<h4>查詢結果</h4>
									<div style="display: flex">
										<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
										<button type="button" class="btn btn-info ms-2"
											@click="performancesCompareModelHandler()">績效比較圖</button>
									</div>
								</div>
								<div class="table-responsive">
									<table class="table table-bordered table-blue">
										<thead>
											<tr>
												<th rowspan="2">加入比較</th>
												<!--<th rowspan="2" >序號</th>-->
												<th rowspan="2">商品代號</th>
												<th rowspan="2">商品中文名稱</th>
												<th colspan="7" id="trHead1">原幣報酬率</th>
												<th colspan="7" id="trHead2" style="display: none">台幣報酬率</th>
												<th rowspan="2">執行</th>
											</tr>
											<tr>
												<th>1日</th>
												<th>1個月</th>
												<th>3個月</th>
												<th>6個月</th>
												<th>1年</th>
												<th>3年</th>
												<th>今年以來</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="item in pageData.content">
												<td data-th="加入比較" class="text-start text-center">
													<input class="form-check-input text-center" @click="checkoutPro(item)"
														:id="'id-' + item.bankProCode" type="checkbox" />
													<label class="form-check-label" :for="'id-' + item.bankProCode"></label>
												</td>
												<td class="text-center" data-th="商品代號">
													<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
												</td>
												<td class="text-centertext-start" data-th="商品中文名稱">
													<span>
														<a class="tx-link" @click="etfModalHandler(item.proCode, item.pfcatCode)">{{
															$filters.defaultValue(item.proName, '--')
														}}</a>
													</span>
												</td>
												<td class="text-center" data-th="原幣報酬率">
													{{ $filters.formatNumber(item.returnFc, '0,0.00' || '--') }}
												</td>
												<td class="text-center" data-th="原幣報酬率">
													{{ $filters.formatNumber(item.fc1mReturn, '0,0.00' || '--') }}
												</td>
												<td class="text-center" data-th="原幣報酬率">
													{{ $filters.formatNumber(item.fc3mReturn, '0,0.00' || '--') }}
												</td>
												<td class="text-center" data-th="原幣報酬率">
													{{ $filters.formatNumber(item.fc6mReturn, '0,0.00' || '--') }}
												</td>
												<td class="text-center" data-th="原幣報酬率">
													{{ $filters.formatNumber(item.fc1yReturn, '0,0.00' || '--') }}
												</td>
												<td class="text-center" data-th="原幣報酬率">
													{{ $filters.formatNumber(item.fc3yReturn, '0,0.00' || '--') }}
												</td>
												<td class="text-center" data-th="原幣報酬率">
													{{ $filters.formatNumber(item.fcYtdReturn, '0,0.00' || '--') }}
												</td>
												<td class="text-center" data-th="執行">
													<button v-if="activeTab === 'fast' && fastCode === '06'" type="button" class="btn btn-primary"
														title="移除我的最愛" @click="remove(item.proCode)">
														移除最愛
													</button>
													<button v-else type="button" class="btn btn-dark btn-icon" data-bs-toggle="tooltip"
														title="加入我的最愛" @click="favoritesHandler(item.proCode, item.pfcatCode)">
														<i class="bi bi-heart text-danger"></i>
													</button>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>

						<div class="tx-note">
							<ol>
								<li><span>資料日期：</span></li>
								<li><span>商品是否可申購以交易系統為主</span></li>
							</ol>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';

import pagination from '@/views/components/pagination.vue';
import vueGroupIssuerModal from './groupIssuerModal.vue';
import vueGroupProExchangeModal from './groupProExchangeModal.vue';
import vueModal from '@/views/components/model.vue';
import performancesCompareModal from './performancesCompareModal.vue';
import { getImgURL } from '@/utils/imgURL.js';

export default {
	components: {
		vueGroupIssuerModal,
		vueGroupProExchangeModal,
		'vue-pagination': pagination,
		vueModal,
		'vue-performances-compare-modal': performancesCompareModal
	},
	props: {
		etfModalHandler: Function,
		favoritesHandler: Function,
		curOption: Array,
		riskMenu: Array
	},
	data: function () {
		return {
			sdRangeMin: '',
			sdRangeMax: '',
			timeRangeMenu: [],
			rowNumerMenu: [],

			activeTab: 'common',
			bankProCode: null, // 商品代號
			proName: null, //商品名稱
			curObjs: [], // 計價幣別 陣列物件
			riskCodes: [], //風險等級
			profInvestorYn: '', // 限PI銷售
			proTypeCode: '', // ETF類型
			isinCode: null,
			sdVaule: '', // 標準差
			etfSizeMin: null, // ETF規格-起
			etfSizeMax: null, // ETF規格-迄
			perfTime: '', // 標的績效
			perfMin: null, // 標的績效-報酬率-起
			perfMax: null, // 標的績效-報酬率-迄
			fastCode: '03', //快速篩選
			timeRange: null, // 快速 顯示區間
			rowNumber: null, // 快速 顯示資料筆數
			perf: 'PCTYTD', // 標的績效
			profInvestorMenu: [], // 限PI銷售選項
			proTypeMenu: [], // ETF類型選項
			sdMenu: [], // 標準差選項
			perfMenu: [], // 標的績效選項
			etfFastMenu: [], // 快速查詢選單

			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: ['PRO_CODE'],
				direction: 'ASC'
			},
			proCodes: [], //移除我的最愛按鈕、執行績效比較圖
			checkboxs: [], // 加入比較選項
			lipperIds: [], //選擇商品做績效比較圖
			issuerItem: [], //發行機構
			proExchangeItem: [], // 交易所

			isOpenIssuerModal: false,
			isOpenExchangeModal: false,
			isOpenCompareModal: false
		};
	},
	watch: {
		activeTab(newVal, oldVal) {
			var self = this;
			self.fastCode = '03';
			self.fastChange(self.fastCode);
		},
		curObjs(newVal, oldVal) {
			var self = this;
			if (newVal[0] !== oldVal[0]) {
				if (newVal[0] === '') {
					// $('#curMenuEtf').selectpicker('selectAll');
				} else if (oldVal[0] === '' && newVal[0] !== '') {
					// $('#curMenuEtf').selectpicker('deselectAll');
				}
			}
		}
	},
	mounted: function () {
		var self = this;
		// $('#curMenuEtf').selectpicker('refresh');
		$('.sdInput').hide();
		$('.perfInput').hide();
		self.getProfInvestorMenu(); // 限PI銷售選項
		self.getProTypeMenu(); // 取得ETF類型選項
		self.getSdMenu();
		self.getetfFastMenu();
		self.getTimeRangeMenu(); // 取得顯示區間選項
		self.getPerfMenu(); // 取得標的績效
		self.getRowNumerMenu(); // 取得顯示資料筆數選項
	},
	methods: {
		getImgURL,
		// 條件Tab切換
		changeTab: function (tabName) {
			var self = this;
			self.activeTab = tabName;
			self.pageData = {
				// 清空查詢資料
				content: []
			};
		},
		// 取得限PI銷售選項
		getProfInvestorMenu: async function () {
			var self = this;
			self.profInvestorMenu = [{ codeValue: '', codeName: '不限' }];
			var selectYnList = [];
			const ret = await this.$api.getAdmCodeDetail({
				codeType: 'SELECT_YN'
			});
			selectYnList = ret.data;
			self.profInvestorMenu.push(...selectYnList);
		},
		// ETF類型選單
		getProTypeMenu: async function () {
			var self = this;
			const r = await this.$api.getProTypeListApi({
				pfcatCode: 'ETF'
			});
			self.proTypeMenu = r.data;
		},
		getSdMenu: async function () {
			var self = this;
			const ret = await this.$api.getSdMenu();
		},
		sdMenuChangeHandler() {
			var self = this;
			if (self.sdVaule == 4) {
				$('.sdInput').show();
			} else {
				$('.sdInput').hide();
			}
		},
		perfTimeChangeHandler() {
			var self = this;
			if (self.perfTime !== '') {
				$('.perfInput').show();
			} else {
				$('.perfInput').hide();
			}
		},
		// 績效排行
		getPerfMenu: async function () {
			var self = this;
			const ret = await this.$api.getPerfMenuApi();
			self.perfMenu = ret.data;
		},
		// 取得顯示區間
		getTimeRangeMenu: async function () {
			var self = this;
			const ret = await this.$api.getTimeRangeMenuApi();
			self.timeRangeMenu = ret.data;
		},
		// 取得顯示資料筆數
		getRowNumerMenu: async function () {
			var self = this;
			const ret = await this.$api.getRowNumerMenuApi();
			self.rowNumerMenu = ret.data;
		},
		// 取得快速篩選條件選項
		getetfFastMenu: async function () {
			var self = this;
			const ret = await this.$api.getEtfFastMenuApi();
			self.etfFastMenu = ret.data;
		},
		fastChange(fastCode) {
			// 快速查詢切換
			var self = this;
			self.timeRange = {}; // 快速 顯示區間
			self.rowNumber = ''; // 快速 顯示資料筆數
			self.perf = '';
			self.pageData = {
				// 清空查詢資料
				content: []
			};
			if (fastCode === '05') {
				// 超人氣
				$('#proPerfTimeTrEtf').hide();
				$('#rangeFixedTrEtf').show();
				$('#maxRowIdTrEtf').show();
				self.timeRange = self.timeRangeMenu[0];
			} else if (fastCode === '07') {
				// 績效排行
				$('#rangeFixedTrEtf').hide();
				$('#maxRowIdTrEtf').show();
				$('#proPerfTimeTrEtf').show();
				self.perf = 'PCTYTD';
			} else if (fastCode === '08') {
				// 最高配息率商品
				$('#maxRowIdTrEtf').show();
				$('#rangeFixedTrEtf').hide();
				$('#proPerfTimeTrEtf').hide();
			} else {
				$('#maxRowIdTrEtf').hide();
				$('#rangeFixedTrEtf').hide();
				$('#proPerfTimeTrEtf').hide();
			}
		},
		// 由查詢結果標題觸發
		sort: function (columnName) {
			if (this.pageable.sort !== columnName) {
				this.pageable.sort = columnName;
				this.pageable.direction = 'DESC';
			} else {
				this.pageable.direction = this.pageable.direction == 'ASC' ? 'DESC' : 'ASC';
			}

			this.gotoPage(0);
		},
		// 由快速查詢結果標題觸發
		sortFast: function (columnName) {
			if (this.pageable.sort !== columnName) {
				this.pageable.sort = columnName;
				this.pageable.direction = 'DESC';
			} else {
				this.pageable.direction = this.pageable.direction == 'ASC' ? 'DESC' : 'ASC';
			}

			this.gotoFastPage(0, columnName);
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (page) {
			var self = this;
			var url = '';

			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;

			let sdItem = _.find(self.sdMenu, { termValue: self.sdVaule }); // 找出對應項目
			let max = null;
			let min = null;
			let isOther = false;
			if (!_.isNil(sdItem)) {
				if (sdItem.termValue == 4) {
					isOther = true;
					max = self.sdRangeMax;
					min = self.sdRangeMin;
				} else {
					isOther = false;
					max = sdItem.rangeMax;
					min = sdItem.rangeMin;
				}
			}

			var issuerCodes = [];
			self.issuerItem.forEach(function (item) {
				issuerCodes.push(item.issuerCode);
			});

			var exchangeCodes = [];
			self.proExchangeItem.forEach(function (item) {
				exchangeCodes.push(item.exchangeCode);
			});
			const payload = {
				bankProCode: self.bankProCode, // 商品代號
				proName: self.proName, //商品名稱
				curCodes: self.curObjs, // 計價幣別
				riskCodes: self.riskCodes, //風險等級
				profInvestorYn: self.profInvestorYn, // 限PI銷售
				protypeCode: self.proTypeCode, // ETF類型
				isinCode: self.isinCode,
				sdRangeMin: min, // 標準差最小值
				sdRangeMax: max, // 標準差最大值
				isOtherSdRange: isOther, // 自訂標準差
				etfSizeMin: self.etfSizeMin, // ETF規格-起
				etfSizeMax: self.etfSizeMax, // ETF規格-迄
				perfTimeCodeValue: self.perfTime, // 標的績效
				perfMin: self.perfMin, // 標的績效-報酬率-起
				perfMax: self.perfMax, // 標的績效-報酬率-起
				issuerExchangeCodes: issuerCodes, //發行機構
				exchangeCodes: exchangeCodes // 交易所
			};
			const ret = await this.$api.getEtfProductsApi(payload, url);

			self.pageData = ret.data;
			self.pageData.content.forEach((data) => {
				self.checkboxs.push(false);
			});
		},
		gotoFastPage: function (page, sortColumnName) {
			this.pageable.page = page;
			this.getFastPageData(page, sortColumnName);
		},
		getFastPageData: async function (page, sortColumnName) {
			var self = this;

			if (sortColumnName == null) {
				// 有指定的排序欄位則以排序欄位排序
				// 當「篩選條件」='聚焦商品'、'超人氣商品'、'我的最愛'：以商品代號排序。
				if (self.fastCode === '03' || self.fastCode === '06' || self.fastCode === '09') {
					self.pageable.sort = 'BANK_PRO_CODE';
				} else if (self.fastCode === '08') {
					// 當「篩選條件」='最高配息率商品'：以配息率排序。
					self.pageable.sort = 'INT_RATE';
				} else if (self.fastCode === '08') {
					// 當「篩選條件」='績效排行'：以所選擇的標的績效排序。
					self.pageable.sort = 'FC_RETURN';
				}
			}

			var url = '';
			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;
			const payload = {
				filterCodeValue: self.fastCode,
				timeRangeType: self.timeRange.rangeType, // 顯示區間類型
				timeRangeFixed: self.timeRange.rangeFixed, // 顯示區間數值
				rowNumberFixed: self.rowNumber,
				perfTimeCode: self.perf
			};
			const ret = await this.$api.getEtfFastPageDataApi(payload, url);
			self.pageData = ret.data;
			self.pageData.content.forEach((data) => {
				self.checkboxs.push(false);
			});
		},
		// 加入比較checkbox選項
		checkoutPro: function (item) {
			var self = this;
			if (!self.proCodes.includes(item.proCode)) {
				self.proCodes.push(item.proCode);
			} else {
				_.remove(self.proCodes, (code) => code === item.proCode);
			}
			if (!self.lipperIds.includes(item.lipperId)) {
				self.lipperIds.push(item.lipperId);
			} else {
				_.remove(self.lipperIds, (code) => code === item.lipperId);
			}
		},
		//執行績效比較圖
		performancesCompareModelHandler: function () {
			var self = this;
			if (self.proCodes.length > 0) {
				if (self.proCodes.length > 6) {
					this.$bi.alert('最多加入6筆');
				} else {
					self.$refs.performancesCompareModalRef.compareEtfPropItem(self.proCodes, self.lipperIds);
					this.isOpenCompareModal = true;
				}
			} else {
				this.$bi.alert('至少要勾選一項商品');
			}
		},
		// 刪除我的最愛
		remove: async function (proCode) {
			var self = this;
			const ret = await this.$api.deleteFavoriteApi({ proCode: proCode });
			this.$bi.alert('刪除成功');
			self.checkboxs = [];
			self.proCodes = [];
			self.gotoFastPage(0);
		},
		groupIssuerModalHandler: function () {
			//顯示發行機構 model
			var self = this;
			this.$refs.groupIssuerModalRef.issuerPropItem(self.issuerItem);
			this.isOpenIssuerModal = true;
		},
		selectedIssuer(issuerItem) {
			// 顯示發行機構選擇項目
			var self = this;
			this.isOpenIssuerModal = false;
			self.issuerItem = issuerItem; //取得發行機構資料
		},
		deleteIssuerItem(issuerCode) {
			var self = this;
			_.remove(self.issuerItem, (item) => item.issuerCode === issuerCode); // 移除刪除項目
		},
		groupProExchangeModalHandler: function () {
			//顯示交易所 model
			var self = this;
			this.$refs.groupProExchangeModalRef.proExchangePropItem(self.proExchangeItem);
			this.isOpenExchangeModal = true;
		},
		selectedProExchange(proExchangeItem) {
			// 顯示交易所選擇項目
			var self = this;
			this.isOpenExchangeModal = false;
			self.proExchangeItem = proExchangeItem; //取得交易所資料
		},
		deleteProExchangeItem(proExchangeCode) {
			//
			var self = this;
			let item = _.find(self.proExchangeItem, {
				exchangeCode: proExchangeCode
			}); // 找出對應項目
			let index = self.proExchangeItem.indexOf(item);
			if (index != -1) {
				self.proExchangeItem.splice(index, 1); // 移除刪除項目
			}
		}
	} // methods end
};
</script>
