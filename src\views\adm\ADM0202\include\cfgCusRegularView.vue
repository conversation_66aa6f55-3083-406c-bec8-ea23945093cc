<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form-collapse">
			<div class="card card-table">
				<div class="card-header">
					<h4>定期資產檢視列表</h4>
				</div>
				<div class="table-responsive">
					<table class="table table-RWD table-hover text-center">
						<thead>
							<tr>
								<th>客戶資產等級中文名稱</th>
								<th>客戶資產等級英文名稱</th>
								<th>維護人員</th>
								<th>異動日期</th>
								<th>定期檢視頻率</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="item in queryResult">
								<td data-th="客戶資產等級中文名稱">{{ item.graName }}</td>
								<td data-th="客戶資產等級英文名稱">{{ item.graEname }}</td>
								<td data-th="維護人員">{{ item.modifyBy }} {{ item.userName }}</td>
								<td data-th="異動日期">{{ item.modifyDt }}</td>
								<td data-th="定期檢視頻率">每<input type="number" min="0" maxlength="9" v-model="item.reviewFreqAsset" />個月</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			<div class="form-footer" style="margin-top: 1rem; margin-bottom: 1rem">
				<button class="btn btn-primary btn-glow btn-save" @click="edit()">修改</button>&nbsp;
				<button class="btn btn-primary btn-glow btn-save" @click="cancelEdit()">取消修改</button>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import Swal from 'sweetalert2';
import { Field, Form } from 'vee-validate';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	data: function () {
		return {
			queryResult: []
		};
	},
	mounted: function () {
		var self = this;
		self.getGrades();
	},
	methods: {
		getGrades: function () {
			var self = this;
			self.$api.getGradesApi().then(function (ret) {
				self.queryResult = ret.data;
			});
		},
		edit: function () {
			var self = this;
			const changedGradesItems = _.clone(self.queryResult)
				.map((item) => {
					if (
						item.reviewFreqAsset == null ||
						item.reviewFreqAsset === '' ||
						item.reviewFreqAsset === '0' ||
						item.reviewFreqAsset === undefined
					) {
						item.reviewFreqAsset = 0;
					} else {
						if (_.isNumeric(item.reviewFreqAsset)) {
							if (item.reviewFreqAsset > 999) {
								self.sendErrMsg('定期檢視頻率不得超過999');
								return null;
							} else {
								const num = parseInt(item.reviewFreqAsset);
								if (num < 0) {
									self.sendErrMsg('定期檢視頻率不得為負');
									return null;
								}
								if (num > 999) {
									self.sendErrMsg('定期檢視頻率不得超過999'); // TODO 待SA提供
									return null;
								}
								item.reviewFreqAsset = num;
							}
						} else {
							self.sendErrMsg('定期檢視頻率須為數字');
							return null;
						}
					}
					return item;
				})
				.filter((item) => item != null);
			if (changedGradesItems.length !== self.queryResult.length) {
				return;
			}

			self.$api
				.patchGradesApi(changedGradesItems)
				.then(function (ret) {
					Swal.fire({
						text: '儲存成功',
						icon: 'success',
						showCloseButton: true,
						confirmButtonText: '確認',
						buttonsStyling: false,
						customClass: {
							confirmButton: 'btn btn-success'
						}
					});
					self.getGrades();
				})
				.catch(function (err) {
					self.sendErrMsg('儲存失敗');
				});
		},
		cancelEdit: function () {
			var self = this;
			self.getGrades();
		},
		sendErrMsg: function (msg) {
			Swal.fire({
				text: msg,
				icon: 'error',
				showCloseButton: true,
				confirmButtonText: '確認',
				buttonsStyling: false,
				customClass: {
					confirmButton: 'btn btn-danger'
				}
			});
			return;
		}
	}
};
</script>
