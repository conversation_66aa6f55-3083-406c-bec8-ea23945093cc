<template>
	<dynamic-title></dynamic-title>
	<div>
		<div class="row">
			<div class="col-12">
				<div class="card card-form-collapse">
					<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formsearch1">
						<h4>個人代理設定</h4>
					</div>
					<div class="card-body collapse show" id="formsearch1">
						<vue-form v-slot="{ errors }" ref="loginDeputy">
							<table class="table table-bordered">
								<tr>
									<th class="wd-12p">使用者代碼</th>
									<th class="wd-8p">使用者姓名</th>
									<th class="wd-12p">代理人員</th>
									<th class="wd-22p">代理日起</th>
									<th class="wd-22p">代理日迄</th>
									<th class="wd-7p">代理天數</th>
									<th class="wd-7p">執行</th>
								</tr>
								<tr>
									<td>
										<span class="tx-16">{{ loginUserCode }}</span>
									</td>
									<td>
										<span class="tx-16">{{ loginUserName }}</span>
									</td>
									<td>
										<vue-field
											type="text"
											name="loginDeputyUserCode"
											v-model="loginDeputyUserCode"
											id="deputyUserCode"
											class="form-control"
											label="代理人員代碼"
											:disabled="disableDeputy"
											data-vv-scope="loginDeputyWatch"
											@blur="onLoginDeputyUserCodeInput"
										></vue-field>
										<div style="height: 25px">
											<span>{{ loginDeputyUserName }}</span>
											<span class="text-danger" v-show="errors.loginDeputyUserCode">{{ errors.loginDeputyUserCode }}</span>
										</div>
									</td>
									<td>
										<div class="input-group">
											<span class="input-group-text">日期</span>
											<vue-field
												type="date"
												name="loginStdDt"
												id="divStdDt0"
												:class="{ 'is-invalid': errors.loginStdDt }"
												class="form-control wd-30p-f mn-wd-120-f"
												v-model="loginStdDt"
												:disabled="disableDeputy"
												label="代理日起"
												rules="required"
												data-vv-scope="loginDeputyWatch"
												:min="nowDt"
											></vue-field>
											<span class="input-group-text">時間</span>
											<vue-field
												as="select"
												name="loginStdHr"
												id="sesBegHr"
												class="form-select"
												data-inline="true"
												v-model="loginStdHr"
												:disabled="disableDeputy"
												label="代理日時間起"
												rules="required"
												data-vv-scope="loginDeputyWatch"
											>
												<option v-for="time in generateTimeOptions()" :key="time" :value="time">{{ time }}</option>
											</vue-field>
										</div>
										<div style="height: 25px">
											<span class="text-danger">{{ errors.loginStdDt }}</span>
											<span class="text-danger">{{ errors.loginStdHr }}</span>
										</div>
									</td>
									<td>
										<div class="input-group">
											<span class="input-group-text">日期</span>
											<vue-field
												type="date"
												name="loginEndDt"
												id="divEndDt0"
												class="form-control wd-30p-f mn-wd-120-f"
												v-model="loginEndDt"
												:disabled="disableDeputy"
												label="代理日迄"
												rules="required"
												data-vv-scope="loginDeputyWatch"
												:min="nowDt"
											>
											</vue-field>
											<span class="input-group-text">時間</span>
											<vue-field
												as="select"
												name="loginEndHr"
												id="sesEndHr"
												class="form-select"
												data-inline="true"
												v-model="loginEndHr"
												:disabled="disableDeputy"
												label="代理日時間迄"
												rules="required"
												data-vv-scope="loginDeputyWatch"
											>
												<option v-for="time in generateTimeOptions()" :key="time" :value="time">{{ time }}</option>
											</vue-field>
										</div>
										<div style="height: 25px">
											<span class="text-danger" v-show="errors.loginEndDt">{{ errors.loginEndDt }}</span>
											<span class="text-danger" v-show="errors.loginEndHr">{{ errors.loginEndHr }}</span>
										</div>
									</td>
									<td>
										{{ loginTotDepDays }}
									</td>
									<td>
										<button
											v-if="!hasDeputy"
											role="button"
											class="btn btn-primary btn-glow btn-ok"
											@click.prevent="doInsertLoginDeputy()"
										>
											確認
										</button>
										<button
											v-if="hasDeputy && !isDeputyStarted"
											role="button"
											class="btn btn-primary btn-glow btn-ok"
											@click.prevent="cancelLoginDeputy()"
										>
											終止代理
										</button>
									</td>
								</tr>
							</table>
						</vue-form>
					</div>
				</div>

				<div v-if="isRmMgr || loginRoleType == 'HQ'" id="editDiv">
					<div class="card card-form-collapse">
						<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formsearch2">
							<h4>轄下人員/主管代理人設定</h4>
						</div>
						<div class="card-body collapse show" id="formsearch2">
							<vue-form v-slot="{ errors }" ref="deputy">
								<table class="table table-bordered">
									<tr>
										<th class="wd-12p">使用者代碼</th>
										<th class="wd-8p">使用者姓名</th>
										<th class="wd-12p">代理人員</th>
										<th class="wd-22p">代理日起</th>
										<th class="wd-22p">代理日迄</th>
										<th class="wd-7p">代理天數</th>
										<th class="wd-7p">執行</th>
									</tr>
									<tr>
										<td>
											<span class="tx-16">{{ userCode }}</span>
										</td>
										<td>
											<span class="tx-16">{{ userName }}</span>
										</td>
										<td>
											<vue-field
												type="text"
												name="deputyUserCode"
												v-model="deputyUserCode"
												id="deputyUserCode"
												class="form-control"
												label="代理人員代碼"
												:disabled="!hasUpdateItem"
												data-vv-scope="deputyWatch"
												@blur="onDeputyUserCodeInput"
											></vue-field>
											<div style="height: 25px">
												<span>{{ deputyUserName }}</span>
												<span class="text-danger" v-show="errors.deputyUserCode">{{ errors.deputyUserCode }}</span>
											</div>
										</td>
										<td>
											<div class="input-group">
												<span class="input-group-text">日期</span>
												<vue-field
													type="date"
													name="stdDt"
													id="divStdDt0"
													:class="{ 'is-invalid': errors.stdDt }"
													class="form-control wd-30p-f mn-wd-120-f"
													v-model="stdDt"
													:disabled="!hasUpdateItem"
													label="代理日起"
													rules="required"
													data-vv-scope="deputyWatch"
													:min="nowDt"
												></vue-field>
												<span class="input-group-text">時間</span>
												<vue-field
													as="select"
													name="stdHr"
													id="sesBegHr"
													class="form-select"
													data-inline="true"
													v-model="stdHr"
													:disabled="!hasUpdateItem"
													label="代理日時間起"
													rules="required"
													data-vv-scope="deputyWatch"
												>
													<option v-for="time in generateTimeOptions()" :key="time" :value="time">{{ time }}</option>
												</vue-field>
											</div>
											<div style="height: 25px">
												<span class="text-danger">{{ errors.stdDt }}</span>
												<span class="text-danger">{{ errors.stdHr }}</span>
											</div>
										</td>
										<td>
											<div class="input-group">
												<span class="input-group-text">日期</span>
												<vue-field
													type="date"
													name="endDt"
													id="divEndDt0"
													class="form-control wd-30p-f mn-wd-120-f"
													v-model="endDt"
													:disabled="!hasUpdateItem"
													label="代理日迄"
													rules="required"
													data-vv-scope="deputyWatch"
													:min="nowDt"
												>
												</vue-field>
												<span class="input-group-text">時間</span>
												<vue-field
													as="select"
													name="endHr"
													id="sesEndHr"
													class="form-select"
													data-inline="true"
													v-model="endHr"
													:disabled="!hasUpdateItem"
													label="代理日時間迄"
													rules="required"
													data-vv-scope="deputyWatch"
												>
													<option v-for="time in generateTimeOptions()" :key="time" :value="time">{{ time }}</option>
												</vue-field>
											</div>
											<div style="height: 25px">
												<span class="text-danger" v-show="errors.endDt">{{ errors.endDt }}</span>
												<span class="text-danger" v-show="errors.endHr">{{ errors.endHr }}</span>
											</div>
										</td>
										<td>
											{{ updateItem != null ? updateItem.totDepDays : '' }}
										</td>
										<td>
											<button
												v-if="userCode"
												:disabled="updateItem.updated"
												role="button"
												class="btn btn-primary btn-glow btn-ok"
												@click.prevent="doInsertDeputy()"
											>
												確認
											</button>
											<button
												v-if="userCode"
												role="button"
												class="btn btn-primary btn-glow btn-ok"
												@click.prevent="clearDeputyForm()"
											>
												取消
											</button>
										</td>
									</tr>
								</table>
							</vue-form>
						</div>
					</div>
					<div class="divider"></div>
					<div v-if="!isRmMgr || loginRoleType == 'HQ'" class="form-group">
						<label class="form-label">組織(分區/分行)</label>
						<div class="col-lg-2 col-12">
							<select name="areaBranCode" id="areaBranCode" class="form-select" v-model="groupCode">
								<option value="" selected>全部</option>
								<option v-for="item in areaList" :value="item.branCode">{{ item.branCode }} {{ item.branName }}</option>
							</select>
						</div>
						&nbsp;
						<div class="col-lg-2 col-12">
							<select name="branCode" id="branCode" class="form-select" v-model="branCode">
								<option value="" selected>全部</option>
								<option v-for="item in branList" :value="item.branCode">{{ item.branCode }} {{ item.branName }}</option>
							</select>
						</div>
						&nbsp;
						<button
							type="button"
							class="btn btn-icon btn-glow"
							data-bs-toggle="tooltip"
							data-bs-original-title=""
							@click.prevent="getUnderUserDeputies()"
						>
							<i class="bi bi-search"></i>
						</button>
					</div>
				</div>

				<div v-if="isRmMgr || loginRoleType == 'HQ'" class="card card-table">
					<div class="card-header">
						<h4>轄下人員/主管代理人列表</h4>
						<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
					</div>
					<template v-if="pageData.totalElements > 0">
						<div class="table-responsive">
							<table class="table table-RWD table-hover table-bordered" id="tblRangeData">
								<thead>
									<tr>
										<th width="18%">分行</th>
										<th width="10%">使用者代碼</th>
										<th width="10%">使用者姓名</th>
										<th class="text-start" width="15%">使用者角色</th>
										<th width="13%">代理人員</th>
										<th width="14%">代理日起</th>
										<th width="14%">代理日迄</th>
										<th width="14%">代理天數</th>
										<th width="6%">執行</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="(item, i) in pageData.content">
										<td data-th="分行">{{ item.branCode }} {{ item.branName }}</td>
										<td data-th="使用者代碼">{{ item.userCode }}</td>
										<td data-th="使用者姓名">{{ item.userName }}</td>
										<td data-th="使用者角色" class="text-start" style="max-width: 100px; overflow: hidden; white-space: normal">
											{{ item.userRoleNames }}
										</td>
										<td data-th="代理人員">{{ item.deputyUserCode }} {{ item.deputyUserName }}</td>
										<td data-th="代理日起">{{ item.stdDt }}</td>
										<td data-th="代理日迄">{{ item.endDt }}</td>
										<td data-th="代理天數">{{ $filters.formatAmt(item.totDepDays) }}</td>
										<td data-th="執行">
											<button
												v-if="item.deputyUserCode == null"
												type="button"
												class="btn btn-info btn-icon btn-glow"
												data-bs-toggle="tooltip"
												@click="doUpdateDeputies(item)"
												data-bs-original-title="編輯"
											>
												<i class="bi bi-pen"></i>
											</button>
											<button
												v-if="item.deputyUserCode != null"
												type="button"
												class="btn btn-danger btn-icon btn-glow"
												data-bs-toggle="tooltip"
												data-bs-original-title="終止代理"
												@click="cancelDeputy(item)"
											>
												<i class="bi bi-slash-circle"></i>
											</button>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</template>
				</div>
			</div>
		</div>
		<!--頁面內容 end-->
	</div>
</template>
<script>
import _ from 'lodash';
import Swal from 'sweetalert2';
import moment from 'moment';
import { Field, Form } from 'vee-validate';
import dynamicTitle from '@/views/components/dynamicTitle.vue';
import pagination from '@/views/components/pagination.vue';
import userCodeComplement from '../../../utils/mixin/userCodeComplement';

export default {
	components: {
		'vue-pagination': pagination,
		'vue-form': Form,
		'vue-field': Field,
		dynamicTitle
	},
	mixins: [userCodeComplement],
	data: function () {
		return {
			//API 用參數
			loginUserCode: null,
			loginUserName: null,
			loginRoleType: null,
			loginCreateByName: null,
			loginStdDt: null,
			loginStdHr: '07:00',
			loginEndDt: null,
			loginEndHr: '07:00',
			loginTotDepDays: null,
			loginDeputyUserCode: null,
			loginDeputyUserName: null,
			loginDeputyBranCode: null,

			updateItem: null,
			userCode: null,
			userName: null,
			totDepDays: null,
			stdDt: null,
			stdHr: '07:00',
			endDt: null,
			endHr: '07:00',
			deputyUserCode: null,
			deputyUserName: null,
			deputyBranCode: null,

			nowDt: moment().format('YYYY-MM-DD'),

			groupCode: '',
			branCode: '',

			//畫面邏輯判斷用參數
			hasDeputy: false,
			isRmMgr: false,
			isDeputyStarted: false,
			disableDeputy: false,
			//API邏輯判斷用參數
			hasUpdateItem: false,

			//畫面顯示用參數
			deputiesRms: [],
			branList: [],
			areaList: [],
			//主要顯示資料
			underUserDeputies: [],
			pageData: {},
			pageable: {
				page: 0,
				size: 10,
				sort: 'user_code',
				direction: 'ASC'
			}
		};
	},
	computed: {
		userInfo: function () {
			return this.$store.getters['userInfo/info'].data;
		}
	},
	watch: {
		userInfo: {
			immediate: true,
			handler: function (newVal, oldVal) {
				var self = this;
				if (newVal) {
					self.loginUserName = newVal.userName;
					self.loginUserCode = newVal.userCode;
					self.loginRoleType = newVal.roleType;
					self.checkIsDeputiesRmMgr();
				}
			}
		},
		updateItem: function () {
			var self = this;
			if (self.updateItem != null) {
				self.hasUpdateItem = true;
			} else {
				self.hasUpdateItem = false;
			}
		},
		groupCode: function (newVal) {
			this.branCode = '';
			this.branList = [];
			if (newVal != null) {
				this.getBranList();
			}
		}
	},
	mounted: function () {
		var self = this;
		self.getLoginUserDeputies();
		self.getAreaList();
	},
	methods: {
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getUnderUserDeputies(page);
		},
		getLoginUserDeputies: async function () {
			var self = this;
			await self.$api.getUserDeputiesApi().then(function (ret) {
				if (ret.data != null) {
					var nowData = new Date();
					var stdDt = new Date(ret.data.stdDt);
					var endDt = new Date(ret.data.endDt);
					self.disableDeputy = true;
					if (!_.isEmpty(ret.data.stdDt)) {
						self.hasDeputy = true;
						if (stdDt.getTime() < nowData.getTime()) {
							self.isDeputyStarted = true;
						}
					}

					if (_.isEmpty(ret.data.endDt) || endDt.getTime() < nowData.getTime()) {
						self.hasDeputy = false;
						self.isDeputyStarted = false;
					}

					self.loginDeputyUserCode = ret.data.deputyUserCode;
					self.loginStdDt = ret.data.stdDate;
					self.loginStdHr = ret.data.stdTime;
					self.loginEndDt = ret.data.endDate;
					self.loginEndHr = ret.data.endTime;
					self.loginTotDepDays = ret.data.totDepDays;
					self.getAdmUsersList({
						userCode: self.loginDeputyUserCode,
						targetName: 'loginDeputyUserName'
					});
				}
			});
		},
		checkIsDeputiesRmMgr: function () {
			var self = this;
			self.$api.getDeputiesRmMgrApi().then(function (ret) {
				self.deputiesRms = ret.data;
				if (self.deputiesRms.includes(self.userInfo.roleCode)) {
					self.isRmMgr = true;
					self.getUnderUserDeputies();
				}
			});
		},
		getUnderUserDeputies: function (page) {
			var self = this;
			var url = _.toPageUrl('', page, self.pageable);
			self.$api
				.getUnderUserDeputiesPageDataApi(
					{
						groupCode: self.groupCode,
						branCode: self.branCode
					},
					url
				)
				.then(function (ret) {
					self.pageData = ret.data;
				});
		},
		//init update form
		doUpdateDeputies: function (item) {
			var self = this;
			item.userRoleCodes = self.userInfo.roleCode;
			self.updateItem = item;
			self.userCode = item.userCode;
			self.userName = item.userName;
			self.totDepDays = item.totDepDays;
			self.stdDt = item.stdDate;
			self.stdHr = item.stdTime;
			self.endDt = item.endDate;
			self.endHr = item.endTime;
		},
		//update
		doInsertLoginDeputy: function () {
			var self = this;

			var stdDt = new Date(self.loginStdDt + ' ' + self.loginStdHr + ':00');
			var endDt = new Date(self.loginEndDt + ' ' + self.loginEndHr + ':00');
			if (!self.checkDateRange(stdDt, endDt)) {
				return;
			}
			if (self.loginDeputyUserCode == self.loginUserCode) {
				Swal.fire({
					icon: 'error',
					text: '代理人員不可為本人。',
					showCloseButton: true,
					confirmButtonText: '確認',
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}
			self.$refs.loginDeputy.validate().then(function (pass) {
				if (pass.valid) {
					self.checkLoginDeputyUserCode();
				}
			});
		},
		//input deputy user check
		checkLoginDeputyUserCode: function () {
			var self = this;
			self.$api
				.getDeputyUserCodeApi({
					deputyUserCode: self.loginDeputyUserCode
				})
				.then(function (ret) {
					if (ret.data == null) {
						Swal.fire({
							icon: 'error',
							text: '此員工編號無法設為代理人，請重新輸入!',
							showCloseButton: true,
							confirmButtonText: '確認',
							buttonStyling: false,
							customClass: {
								confirmButton: 'btn btn-danger'
							}
						});
						return;
					} else {
						self.loginDeputyBranCode = ret.data.deputyBranCode;
						self.chkValidDeputiesTime();
					}
				});
		},
		insertLoginDeputy: function () {
			var self = this;

			var stdDt = self.loginStdDt + ' ' + self.loginStdHr;
			var endDt = self.loginEndDt + ' ' + self.loginEndHr;

			self.$api
				.postInsertDeputyApi({
					userCode: self.loginUserCode,
					roleMetadata: self.userInfo.roleCode,
					branCode: self.userInfo.branCode,
					deputyUserCode: self.loginDeputyUserCode,
					deputyBranCode: self.loginDeputyBranCode,
					stdDt: stdDt,
					endDt: endDt
				})
				.then(function (ret) {
					Swal.fire({
						icon: 'success',
						text: '新增代理人成功。',
						showCloseButton: true,
						confirmButtonText: '確認',
						buttonStyling: false,
						customClass: {
							confirmButton: 'btn btn-success'
						}
					});
					self.getLoginUserDeputies();
				});
		},
		chkValidDeputiesTime: function () {
			var self = this;

			var stdDt = self.loginStdDt + ' ' + self.loginStdHr;
			var endDt = self.loginEndDt + ' ' + self.loginEndHr;

			self.$api
				.getchkValidDeputiesTimeApi({
					stdDt: stdDt,
					endDt: endDt
				})
				.then(async function (ret) {
					if (ret.data.validYn == 'N') {
						Swal.fire({
							icon: 'error',
							text: '同一時段已設定其他代理，無法進行設定。',
							showCloseButton: true,
							confirmButtonText: '確認',
							buttonStyling: false,
							customClass: {
								confirmButton: 'btn btn-danger'
							}
						});
						return;
					} else {
						const checkLoginStdDtBusinessDt = await self.doCheckIsBusinessDt(self.loginStdDt);
						const checkLoginEndDtBusinessDt = await self.doCheckIsBusinessDt(self.loginEndDt);
						if (!_.isBlank(checkLoginStdDtBusinessDt)) {
							Swal.fire({
								icon: 'error',
								text: '代理日起始日為非營業日。',
								showCloseButton: true,
								confirmButtonText: '確認',
								buttonStyling: false,
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							});
							return;
						}
						if (!_.isBlank(checkLoginEndDtBusinessDt)) {
							Swal.fire({
								icon: 'error',
								text: '代理日結束日為非營業日。',
								showCloseButton: true,
								confirmButtonText: '確認',
								buttonStyling: false,
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							});
							return;
						}
						self.insertLoginDeputy();
					}
				});
		},
		doCheckIsBusinessDt: async function (date) {
			var self = this;
			self.$api
				.getdoCheckIsBusinessDtApi({
					date: date
				})
				.then(function (ret) {
					return ret.data;
				});
		},
		doInsertDeputy: function () {
			var self = this;
			self.$refs.deputy.validate().then(function (pass) {
				if (pass.valid) {
					self.checkDeputyUserCode();
				}
			});
		},
		checkDeputyUserCode: function () {
			var self = this;
			var item = self.updateItem;

			self.$api
				.getcheckDeputyUserCodeApi({
					userCode: item.userCode,
					deputyUserCode: self.deputyUserCode
				})
				.then(async function (ret) {
					if (ret.data == null) {
						Swal.fire({
							icon: 'error',
							text: '此員工編號無法設為代理人，請重新輸入。',
							showCloseButton: true,
							confirmButtonText: '確認',
							buttonStyling: false,
							customClass: {
								confirmButton: 'btn btn-danger'
							}
						});
						return;
					} else {
						const checkStdDtBusinessDt = await self.doCheckIsBusinessDt(self.stdDt);
						const checkEndDtBusinessDt = await self.doCheckIsBusinessDt(self.endDt);
						if (!_.isBlank(checkStdDtBusinessDt)) {
							Swal.fire({
								icon: 'error',
								text: '代理日起始日為非營業日。',
								showCloseButton: true,
								confirmButtonText: '確認',
								buttonStyling: false,
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							});
							return;
						}
						if (!_.isBlank(checkEndDtBusinessDt)) {
							Swal.fire({
								icon: 'error',
								text: '代理日結束日為非營業日。',
								showCloseButton: true,
								confirmButtonText: '確認',
								buttonStyling: false,
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							});
							return;
						}
						self.deputyBranCode = ret.data.deputyBranCode;
						self.insertDeputy();
					}
				});
		},
		insertDeputy: function () {
			var self = this;

			// 代理日期時間檢核
			var chkStdDt = new Date(self.stdDt + ' ' + self.stdHr + ':00');
			var chkEndDt = new Date(self.endDt + ' ' + self.endHr + ':00');
			if (!self.checkDateRange(chkStdDt, chkEndDt)) {
				return;
			}

			var item = self.updateItem;
			var stdDt = self.stdDt + ' ' + self.stdHr + ':00';
			var endDt = self.endDt + ' ' + self.endHr + ':00';

			self.$api
				.postInsertDeputyApi({
					userCode: item.userCode,
					roleMetadata: item.userRoleCodes,
					branCode: item.branCode,
					deputyUserCode: self.deputyUserCode,
					deputyBranCode: self.deputyBranCode,
					stdDt: stdDt,
					endDt: endDt
				})
				.then(function (ret) {
					Swal.fire({
						icon: 'success',
						text: '新增代理人成功。',
						showCloseButton: true,
						confirmButtonText: '確認',
						buttonStyling: false,
						customClass: {
							confirmButton: 'btn btn-success'
						}
					});
					self.clearDeputyForm();
					self.checkIsDeputiesRmMgr();
					self.getLoginUserDeputies();
					self.getUnderUserDeputies(self.pageable.page);
				});
		},
		//delete
		cancelLoginDeputy: function () {
			var self = this;
			this.$bi.confirm('確定要刪除此筆資料嗎?', {
				event: {
					confirmOk: function () {
						self.$api
							.deleteUserdeputyApi({
								userCode: self.loginUserCode,
								deputyUserCode: self.loginDeputyUserCode
							})
							.then(function (ret) {
								Swal.fire({
									icon: 'success',
									text: '刪除代理人成功。',
									showCloseButton: true,
									confirmButtonText: '確認',
									buttonStyling: false,
									customClass: {
										confirmButton: 'btn btn-success'
									}
								});
								self.$refs.loginDeputy.resetForm(); // 重置表單，清除錯誤提示
								self.loginStdDt = null;
								self.loginStdHr = '07:00';
								self.loginEndDt = null;
								self.loginEndHr = '07:00';
								self.loginDeputyUserCode = null;
								self.loginDeputyUserName = null;
								self.loginDeputyBranCode = null;
								self.loginTotDepDays = null;
								self.hasDeputy = false;
								self.disableDeputy = false;
								self.checkIsDeputiesRmMgr();
							});
					}
				}
			});
		},
		clearDeputyForm: function () {
			var self = this;
			self.updateItem = null;
			self.userCode = null;
			self.userName = '';
			self.deputyUserName = null;
			self.totDepDays = '';
			self.$refs.deputy.resetForm();
		},
		cancelDeputy: function (item) {
			var self = this;
			self.$bi.confirm('確定要刪除此筆資料嗎?', {
				event: {
					confirmOk: function () {
						self.$api
							.deleteUserdeputyApi({
								userCode: self.loginUserCode,
								deputyUserCode: self.loginDeputyUserCode
							})
							.then(function (ret) {
								Swal.fire({
									icon: 'success',
									text: '刪除代理人成功。',
									showCloseButton: true,
									confirmButtonText: '確認',
									buttonStyling: false,
									customClass: {
										confirmButton: 'btn btn-success'
									}
								});
								self.getUnderUserDeputies();
							});
					}
				}
			});
		},
		//util function
		isValidate: function (userCode, roleCodes, branCode, deputyUserCode, deputyBranCode, stdDtDate, stdDtTime, endDtDate, endDtTime) {
			var self = this;
			var isValidate = true;

			if (
				_.isBlank(userCode) ||
				_.isBlank(roleCodes) ||
				_.isBlank(branCode) ||
				_.isBlank(deputyUserCode) ||
				_.isBlank(deputyBranCode) ||
				_.isBlank(stdDtDate) ||
				_.isBlank(stdDtTime) ||
				_.isBlank(endDtDate) ||
				_.isBlank(endDtTime)
			) {
				Swal.fire({
					icon: 'error',
					text: '缺少必填欄位。',
					showCloseButton: true,
					confirmButtonText: '確認',
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				isValidate = false;
			}
			return isValidate;
		},
		//檢核代理日期區間
		checkDateRange: function (stdDt, endDt) {
			// 起日是否大於迄日
			if (stdDt > endDt) {
				Swal.fire({
					icon: 'error',
					text: '代理起日不可大於代理迄日',
					showCloseButton: true,
					confirmButtonText: '確認',
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return false;
			}
			// 代理日起日<現在日期時間
			var nowData = new Date();
			if (stdDt < nowData) {
				Swal.fire({
					icon: 'error',
					text: '無法設定過去代理時間',
					showCloseButton: true,
					confirmButtonText: '確認',
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return false;
			}
			return true;
		},
		generateTimeOptions() {
			const startTime = 7 * 60; // 07:00 in minutes
			const endTime = 18 * 60; // 18:00 in minutes
			const interval = 10; // 10 minutes

			return _.map(_.range(startTime, endTime + interval, interval), (minutes) => {
				const hours = Math.floor(minutes / 60);
				const mins = minutes % 60;
				return `${_.padStart(hours.toString(), 2, '0')}:${_.padStart(mins.toString(), 2, '0')}`;
			});
		},
		getAreaList: function () {
			var self = this;
			this.getAdmBranches(['10', '50'], null).then(function (resp) {
				self.areaList = resp.data;
			});
		},
		getBranList: function () {
			var self = this;
			this.getAdmBranches(null, self.groupCode).then(function (resp) {
				self.branList = resp.data;
			});
		},
		getAdmBranches: function (branLvlCode = null, parentBranCode = null) {
			var self = this;
			return self.$api.getAdmBranchesApi({
				parentBranCode: parentBranCode,
				branLvlCode: branLvlCode
			});
		},
		getAdmUsersList: function (params) {
			var self = this;

			self.$api
				.getAdmUsersListApi({
					branCode: '',
					userCode: params.userCode,
					parentBranCode: '',
					roleCode: ''
				})
				.then(function (ret) {
					if (ret.data == null || ret.data.length === 0) {
						self[params.targetName] = null;
						Swal.fire({
							icon: 'error',
							text: '此員編不存在!',
							showCloseButton: true,
							confirmButtonText: '確認',
							buttonStyling: false,
							customClass: {
								confirmButton: 'btn btn-danger'
							}
						});
						return;
					} else {
						self[params.targetName] = ret.data[0].userName; // 取得代理人姓名
					}
				});
		},
		onLoginDeputyUserCodeInput: function () {
			var self = this;
			self.loginDeputyUserCode = self.complementUserCode(self.loginDeputyUserCode);
			if (self.loginDeputyUserCode) {
				self.getAdmUsersList({
					userCode: self.loginDeputyUserCode,
					targetName: 'loginDeputyUserName'
				});
			} else {
				self.loginDeputyUserName = '';
			}
		},
		onDeputyUserCodeInput: function () {
			var self = this;
			self.deputyUserCode = self.complementUserCode(self.deputyUserCode);
			if (self.deputyUserCode) {
				self.getAdmUsersList({
					userCode: self.deputyUserCode,
					targetName: 'deputyUserName'
				});
			} else {
				self.deputyUserName = '';
			}
		}
	}
};
</script>
