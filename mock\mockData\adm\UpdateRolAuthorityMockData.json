{"status": 200, "data": {"executeResult": "S", "status": "P", "eventId": "EVN20250410000014"}, "timestamp": "2025/04/10", "sqlTracer": [{"data": {"createDt": "2025-01-16T00:00:00", "createBy": "SYSBATCH", "roleCode": "02", "roleName": "分行幹部", "roleType": "BM", "certificateYn": "Y", "homeType": "TYPE2"}, "sqlInfo": "SELECT CERTIFICATE_YN, CREATE_<PERSON>AN_CODE AS createBranCode, CREATE_BY AS createBy, CREATE_DT AS createDt, DESCRIPTION, HOME_TYPE, MODIFY_<PERSON><PERSON>_CODE AS modifyBranCode, MODIFY_BY AS modifyBy, MODIFY_DT AS modifyDt, R<PERSON><PERSON>_CODE, ROLE_ENAME, ROLE_NAME, ROLE_TYPE FROM ADM_ROLES WHERE ROLE_CODE = :roleCode,class com.bi.pbs.adm.model.AdmRoles,MapSqlParameterSource {roleCode=02}"}, {"data": {"createDt": "2024-12-11T00:00:00", "createBy": "SYS", "createBranCode": "", "modifyDt": "1900-01-01T00:00:00", "modifyBy": "", "modifyBranCode": "", "wfgId": "WFG20121005008", "wfgName": "系統角色維護", "wfgEname": "", "wfgRoleKind": "rule4", "activeYn": "Y", "activeDt": "2013-01-06 00:00:00.0", "confirmAction": "", "className": "AdmRoleMenuWkfProcessor", "inactiveDt": "1900-01-01 00:00:00.0", "initWfgsCode": "STP20121005001", "menuCode": "M00-01"}, "sqlInfo": "SELECT ACTIVE_DT, ACTIVE_YN, CLASS_NAME, CONFIRM_ACTION, CREATE_<PERSON>AN_CODE AS createBranCode, CREATE_BY AS createBy, CREATE_DT AS createDt, INACTIVE_DT, INIT_WFGS_CODE, MENU_CODE, <PERSON><PERSON><PERSON><PERSON>_<PERSON>AN_CODE AS modifyBranCode, MODIFY_BY AS modifyBy, MODIFY_DT AS modifyDt, WFG_ENAME, WFG_ID, WFG_NAME, WFG_ROLE_KIND, WFG_VERSION FROM WKF_ENGINES WHERE WFG_ID = :wfgId,class com.bi.pbs.wkf.model.WkfEngines,MapSqlParameterSource {wfgId=WFG20121005008}"}, {"data": {"createDt": "2025-03-25T00:00:00", "createBy": "SYS", "createBranCode": "", "modifyDt": "1900-01-01T00:00:00", "modifyBy": "", "modifyBranCode": "", "wfgId": "WFG20121005008", "wfgsCode": "STP20121005001", "actionCode": "ACT20121005003", "nextWfgsCode": "STP20121005002"}, "sqlInfo": " SELECT WEF.*  FROM WKF_ENGINE_FLOWS WEF  JOIN WKF_ENGINE_ACTIONS WEA ON WEF.WFG_ID = WEA.WFG_ID AND WEF.ACTION_CODE = WEA.ACTION_CODE  WHERE WEF.WFG_ID = :wfgId AND WEF.WFGS_CODE = :wfgsCode ,class com.bi.pbs.wkf.model.WkfEngineFlows,{wfgsCode=STP20121005001, wfgId=WFG20121005008}"}, {"data": [{"createDt": "2025-02-05T00:00:00", "createBy": "SYS", "createBranCode": "", "modifyDt": "1900-01-01T00:00:00", "modifyBy": "", "modifyBranCode": "", "wfgsCode": "STP20121005001", "wfgsRole": "-ALL-"}], "sqlInfo": "SELECT * FROM WKF_ENGINE_ROLES WHERE WFGS_CODE = :wfgsCode ,class com.bi.pbs.wkf.model.WkfEngineRoles,{wfgsCode=STP20121005001}"}, {"data": {"createDt": "2024-11-20T00:00:00", "createBy": "SYS", "createBranCode": "", "modifyBy": "", "modifyBranCode": "", "wfgId": "WFG20121005008", "actionCode": "ACT20121005003", "actionName": "待覆核", "actionEname": "", "actionStatus": "P", "methodName": "approval"}, "sqlInfo": " SELECT * FROM WKF_ENGINE_ACTIONS WHERE ACTION_CODE= :actionCode ,class com.bi.pbs.wkf.model.WkfEngineActions,{actionCode=ACT20121005003}"}, {"data": [{"menuCode": "M0", "progCode": "", "menuName": "系統管理", "menuEname": "", "description": "", "parentMenuCode": "", "depths": 1, "version": "1.0", "strset": "00", "showYn": "Y", "showOrder": 10, "showPriority": 0, "verifyType": "0", "activeYn": "Y", "tabYn": "N", "createDt": "2024-12-29T00:00:00", "createBy": "SYSBATCH", "leafCode": "M02-00"}, {"menuCode": "M02", "progCode": "", "menuName": "代理人", "menuEname": "", "description": "", "parentMenuCode": "M0", "depths": 2, "version": "1.0", "strset": "0002", "showYn": "Y", "showOrder": 4, "showPriority": 0, "verifyType": "0", "activeYn": "Y", "tabYn": "N", "createDt": "2024-12-29T00:00:00", "createBy": "SYSBATCH", "leafCode": "M02-00"}, {"menuCode": "M02-00", "progCode": "ADM-201", "menuName": "代理人設定", "menuEname": "", "description": "", "parentMenuCode": "M02", "depths": 3, "version": "1.0", "strset": "000200", "showYn": "Y", "showOrder": 1, "showPriority": 0, "verifyType": "0", "activeYn": "Y", "tabYn": "N", "createDt": "2024-12-29T00:00:00", "createBy": "SYSBATCH", "leafCode": "M02-00"}], "sqlInfo": " WITH TREE AS (  \tSELECT  \t\t*, MENU_CODE LEAF_CODE  \t\tFROM ADM_MENUS  \tWHERE MENU_CODE IN ( :leafMenuCodes )  \tUNION ALL  \tSELECT  \t\tM.*, L.LEAF_CODE  \t\tFROM ADM_MENUS M  \tINNER JOIN TREE L  \tON M.MENU_CODE = L.PARENT_MENU_CODE  )  SELECT DISTINCT *  FROM TREE  ORDER BY MENU_CODE ,class com.bi.pbs.cus.web.model.MenusInfo,{leafMenuCodes=[M02-00]}"}, {"data": [1, 1, 1], "sqlInfo": "INSERT INTO ADM_ROLE_MENU_MAP_LOG (R<PERSON><PERSON>_MAP_ID, MENU_CODE, ROL<PERSON>_CODE, PERMISSION, CREATE_DT, MODIFY_DT, CREATE_BY, MODIFY_BY, CREATE_BRAN_CODE, CREATE_DEPUTY) VALUES (:roleMapId, :menuCode, :roleCode, :permission, :createDt, :modifyDt, :createBy, :modifyBy, :createBranCode, :createDeputy),[Ljava.util.HashMap;@41ba2980"}, {"data": 1, "sqlInfo": "INSERT INTO WKF_EVENT_HISTORY (WKF_HISTORY_ID, EVENT_ID, WFG_ID, ROLE_CODE, BRAN_CODE, POS_CODE, USER_CODE, WF<PERSON>_CODE, ACTION_CODE, STEP_CONTENT, STATUS, CREATE_DT, CREATE_BY, CREATE_<PERSON>AN_CODE, MODIFY_DT, MODIFY_BY, MODIFY_BRAN_CODE) VALUES (:wkfHistoryId, :eventId, :wfgId, :roleCode, :branCode, :posCode, :userCode, :wfgsCode, :actionCode, :stepContent, :status, :createDt, :createBy, :createBranCode, :modifyDt, :modifyBy, :modifyBranCode),{eventId=org.springframework.jdbc.core.SqlParameterValue@5e707de, modifyBy=org.springframework.jdbc.core.SqlParameterValue@443aa25b, wkfHistoryId=org.springframework.jdbc.core.SqlParameterValue@31f1eb99, branCode=org.springframework.jdbc.core.SqlParameterValue@2117220e, createDt=org.springframework.jdbc.core.SqlParameterValue@1c731805, createBranCode=org.springframework.jdbc.core.SqlParameterValue@d4a49aa, userCode=org.springframework.jdbc.core.SqlParameterValue@33d9c6b9, wfgId=org.springframework.jdbc.core.SqlParameterValue@2bc6959e, modifyBranCode=org.springframework.jdbc.core.SqlParameterValue@2ff113d3, createBy=org.springframework.jdbc.core.SqlParameterValue@70a9189e, roleCode=org.springframework.jdbc.core.SqlParameterValue@db8e43c, wfgsCode=org.springframework.jdbc.core.SqlParameterValue@12cdea70, posCode=org.springframework.jdbc.core.SqlParameterValue@35850c71, actionCode=org.springframework.jdbc.core.SqlParameterValue@d508fb7, modifyDt=org.springframework.jdbc.core.SqlParameterValue@47030325, stepContent=org.springframework.jdbc.core.SqlParameterValue@3ae0ffdf, status=org.springframework.jdbc.core.SqlParameterValue@271b9a76}"}, {"sqlInfo": "SELECT BRAN_CODE, CREATE_<PERSON>AN_CODE AS createBranCode, CREATE_BY AS createBy, CREATE_DEPUTY AS createDeputy, CREATE_DT AS createDt, EVENT_ID, <PERSON>XTRA, MODIFY_<PERSON>AN_CODE AS modifyBranCode, <PERSON><PERSON>IFY_BY AS modifyBy, <PERSON><PERSON>IFY_DEPUTY AS modifyDeputy, MODIFY_DT AS modifyDt, OWNER_CREATE_BY, OWNER_MODIFY_BY, POS_CODE, ROLE_CODE, STATUS, USER_CODE, WFGS_CODE, WFG_ID, WKF_HISTORY_ID FROM WKF_EVENTS WHERE EVENT_ID = :eventId,class com.bi.pbs.wkf.model.WkfEvents,{eventId=org.springframework.jdbc.core.SqlParameterValue@1c35a102}"}, {"data": 1, "sqlInfo": "INSERT INTO WKF_EVENTS (EVENT_ID, WFG_ID, R<PERSON><PERSON>_CODE, USER_CODE, BR<PERSON>_CODE, POS_CODE, WF<PERSON>_CODE, WKF_HISTORY_ID, STATUS, OWNER_CREATE_BY, OWNER_MODIFY_BY, EXTRA, CREATE_DEPUTY, MODIFY_DEPUTY, CREATE_DT, CREATE_BY, CREATE_BRAN_CODE, MODIFY_DT, MODIFY_BY, MODIFY_BRAN_CODE) VALUES (:eventId, :wfgId, :roleCode, :userCode, :branCode, :posCode, :wfgsCode, :wkfHistoryId, :status, :ownerCreateBy, :ownerModifyBy, :extra, :createDeputy, :modifyDeputy, :createDt, :createBy, :createBranCode, :modifyDt, :modifyBy, :modifyBranCode),{eventId=org.springframework.jdbc.core.SqlParameterValue@6adce949, modifyBy=org.springframework.jdbc.core.SqlParameterValue@5b5068a1, ownerModifyBy=org.springframework.jdbc.core.SqlParameterValue@54a9261c, modifyDeputy=org.springframework.jdbc.core.SqlParameterValue@aadf9a8, wkfHistoryId=org.springframework.jdbc.core.SqlParameterValue@7b6df15, branCode=org.springframework.jdbc.core.SqlParameterValue@5ffbaa22, createDt=org.springframework.jdbc.core.SqlParameterValue@427ffe07, createBranCode=org.springframework.jdbc.core.SqlParameterValue@27fa2f59, userCode=org.springframework.jdbc.core.SqlParameterValue@160502c4, wfgId=org.springframework.jdbc.core.SqlParameterValue@142d109c, modifyBranCode=org.springframework.jdbc.core.SqlParameterValue@291f4bd8, ownerCreateBy=org.springframework.jdbc.core.SqlParameterValue@5fe92945, createBy=org.springframework.jdbc.core.SqlParameterValue@61c4135e, roleCode=org.springframework.jdbc.core.SqlParameterValue@1d91460a, wfgsCode=org.springframework.jdbc.core.SqlParameterValue@4e039feb, extra=org.springframework.jdbc.core.SqlParameterValue@58b6346f, posCode=org.springframework.jdbc.core.SqlParameterValue@19a982c4, createDeputy=org.springframework.jdbc.core.SqlParameterValue@e1f36ed, modifyDt=org.springframework.jdbc.core.SqlParameterValue@5cb10c02, status=org.springframework.jdbc.core.SqlParameterValue@354d5eb7}"}, {"data": [1, 1], "sqlInfo": "INSERT INTO WKF_VARITEM_DATALIST (EVENT_ID, VARI<PERSON>M_CODE, VARITEM_VALUE, CREATE_DT, CREATE_BY, CREATE_<PERSON>AN_CODE, CREATE_DEPUTY, MODIFY_DT, MODIFY_BY, MODIFY_<PERSON>AN_CODE, MODIFY_DEPUTY) VALUES (:eventId, :varItemCode, :varItemValue, :createDt, :createBy, :createBranCode, :createDeputy, :modifyDt, :modifyBy, :modifyBranCode, :modifyDeputy),[Ljava.util.HashMap;@60a75578"}]}