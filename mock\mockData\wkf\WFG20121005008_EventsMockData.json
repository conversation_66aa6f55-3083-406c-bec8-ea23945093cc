{"status": 200, "data": [{"eventId": "EVN20250421000001", "wfgId": "WFG20121005008", "createDt": "2025/04/21 11:45:54", "createBy": "112790", "userName": "簡OO", "status": "P", "itemDatas": [{"varItemCode": "ITM20130107001", "varItemValue": "03"}, {"varItemCode": "ITM20130107002", "varItemValue": "分行經理"}], "wkfEngineFlows": [{"wfgId": "WFG20121005008", "actionName": "待覆核", "actionStatus": "P", "actionCode": "ACT20121005003"}, {"wfgId": "WFG20121005008", "actionName": "覆核完成", "actionStatus": "A", "actionCode": "ACT20121005004"}, {"wfgId": "WFG20121005008", "actionName": "退回修改", "actionStatus": "R", "actionCode": "ACT20121005005"}], "buttonYn": "Y"}, {"eventId": "EVN20250418000012", "wfgId": "WFG20121005008", "createDt": "2025/04/18 15:11:59", "createBy": "112790", "userName": "簡OO", "status": "P", "itemDatas": [{"varItemCode": "ITM20130107001", "varItemValue": "08"}, {"varItemCode": "ITM20130107002", "varItemValue": "區長(財管業務)"}], "wkfEngineFlows": [{"wfgId": "WFG20121005008", "actionName": "待覆核", "actionStatus": "P", "actionCode": "ACT20121005003"}, {"wfgId": "WFG20121005008", "actionName": "覆核完成", "actionStatus": "A", "actionCode": "ACT20121005004"}, {"wfgId": "WFG20121005008", "actionName": "退回修改", "actionStatus": "R", "actionCode": "ACT20121005005"}], "buttonYn": "Y"}, {"eventId": "EVN20250418000011", "wfgId": "WFG20121005008", "createDt": "2025/04/18 15:07:58", "createBy": "112790", "userName": "簡OO", "status": "P", "itemDatas": [{"varItemCode": "ITM20130107001", "varItemValue": "05"}, {"varItemCode": "ITM20130107002", "varItemValue": "FC"}], "wkfEngineFlows": [{"wfgId": "WFG20121005008", "actionName": "待覆核", "actionStatus": "P", "actionCode": "ACT20121005003"}, {"wfgId": "WFG20121005008", "actionName": "覆核完成", "actionStatus": "A", "actionCode": "ACT20121005004"}, {"wfgId": "WFG20121005008", "actionName": "退回修改", "actionStatus": "R", "actionCode": "ACT20121005005"}], "buttonYn": "Y"}, {"eventId": "EVN20250410000014", "wfgId": "WFG20121005008", "createDt": "2025/04/10 16:32:01", "createBy": "112790", "userName": "簡OO", "status": "P", "itemDatas": [{"varItemCode": "ITM20130107001", "varItemValue": "02"}, {"varItemCode": "ITM20130107002", "varItemValue": "分行幹部"}], "wkfEngineFlows": [{"wfgId": "WFG20121005008", "actionName": "待覆核", "actionStatus": "P", "actionCode": "ACT20121005003"}, {"wfgId": "WFG20121005008", "actionName": "覆核完成", "actionStatus": "A", "actionCode": "ACT20121005004"}, {"wfgId": "WFG20121005008", "actionName": "退回修改", "actionStatus": "R", "actionCode": "ACT20121005005"}], "buttonYn": "Y"}, {"eventId": "EVN20250410000013", "wfgId": "WFG20121005008", "createDt": "2025/04/10 16:27:46", "createBy": "112790", "userName": "簡OO", "status": "P", "itemDatas": [{"varItemCode": "ITM20130107001", "varItemValue": "01"}, {"varItemCode": "ITM20130107002", "varItemValue": "分行經辦"}], "wkfEngineFlows": [{"wfgId": "WFG20121005008", "actionName": "待覆核", "actionStatus": "P", "actionCode": "ACT20121005003"}, {"wfgId": "WFG20121005008", "actionName": "覆核完成", "actionStatus": "A", "actionCode": "ACT20121005004"}, {"wfgId": "WFG20121005008", "actionName": "退回修改", "actionStatus": "R", "actionCode": "ACT20121005005"}], "buttonYn": "Y"}, {"eventId": "EVN20250410000012", "wfgId": "WFG20121005008", "createDt": "2025/04/10 16:24:24", "createBy": "112790", "userName": "簡OO", "status": "P", "itemDatas": [{"varItemCode": "ITM20130107001", "varItemValue": "00"}, {"varItemCode": "ITM20130107002", "varItemValue": "理財專員"}], "wkfEngineFlows": [{"wfgId": "WFG20121005008", "actionName": "待覆核", "actionStatus": "P", "actionCode": "ACT20121005003"}, {"wfgId": "WFG20121005008", "actionName": "覆核完成", "actionStatus": "A", "actionCode": "ACT20121005004"}, {"wfgId": "WFG20121005008", "actionName": "退回修改", "actionStatus": "R", "actionCode": "ACT20121005005"}], "buttonYn": "Y"}, {"eventId": "EVN20250410000011", "wfgId": "WFG20121005008", "createDt": "2025/04/10 16:23:51", "createBy": "112790", "userName": "簡OO", "status": "P", "itemDatas": [{"varItemCode": "ITM20130107001", "varItemValue": "31"}, {"varItemCode": "ITM20130107002", "varItemValue": "風管科經辦"}], "wkfEngineFlows": [{"wfgId": "WFG20121005008", "actionName": "待覆核", "actionStatus": "P", "actionCode": "ACT20121005003"}, {"wfgId": "WFG20121005008", "actionName": "覆核完成", "actionStatus": "A", "actionCode": "ACT20121005004"}, {"wfgId": "WFG20121005008", "actionName": "退回修改", "actionStatus": "R", "actionCode": "ACT20121005005"}], "buttonYn": "Y"}], "timestamp": "2025/04/21", "sqlTracer": [{"data": [{"eventId": "EVN20250421000001", "wfgId": "WFG20121005008", "createDt": "2025/04/21 11:45:54", "createBy": "112790", "userName": "簡OO", "status": "P", "itemDatas": [{"varItemCode": "ITM20130107001", "varItemValue": "03"}, {"varItemCode": "ITM20130107002", "varItemValue": "分行經理"}], "wkfEngineFlows": [{"wfgId": "WFG20121005008", "actionName": "待覆核", "actionStatus": "P", "actionCode": "ACT20121005003"}, {"wfgId": "WFG20121005008", "actionName": "覆核完成", "actionStatus": "A", "actionCode": "ACT20121005004"}, {"wfgId": "WFG20121005008", "actionName": "退回修改", "actionStatus": "R", "actionCode": "ACT20121005005"}], "buttonYn": "Y"}, {"eventId": "EVN20250418000012", "wfgId": "WFG20121005008", "createDt": "2025/04/18 15:11:59", "createBy": "112790", "userName": "簡OO", "status": "P", "itemDatas": [{"varItemCode": "ITM20130107001", "varItemValue": "08"}, {"varItemCode": "ITM20130107002", "varItemValue": "區長(財管業務)"}], "wkfEngineFlows": [{"wfgId": "WFG20121005008", "actionName": "待覆核", "actionStatus": "P", "actionCode": "ACT20121005003"}, {"wfgId": "WFG20121005008", "actionName": "覆核完成", "actionStatus": "A", "actionCode": "ACT20121005004"}, {"wfgId": "WFG20121005008", "actionName": "退回修改", "actionStatus": "R", "actionCode": "ACT20121005005"}], "buttonYn": "Y"}, {"eventId": "EVN20250418000011", "wfgId": "WFG20121005008", "createDt": "2025/04/18 15:07:58", "createBy": "112790", "userName": "簡OO", "status": "P", "itemDatas": [{"varItemCode": "ITM20130107001", "varItemValue": "05"}, {"varItemCode": "ITM20130107002", "varItemValue": "FC"}], "wkfEngineFlows": [{"wfgId": "WFG20121005008", "actionName": "待覆核", "actionStatus": "P", "actionCode": "ACT20121005003"}, {"wfgId": "WFG20121005008", "actionName": "覆核完成", "actionStatus": "A", "actionCode": "ACT20121005004"}, {"wfgId": "WFG20121005008", "actionName": "退回修改", "actionStatus": "R", "actionCode": "ACT20121005005"}], "buttonYn": "Y"}, {"eventId": "EVN20250410000014", "wfgId": "WFG20121005008", "createDt": "2025/04/10 16:32:01", "createBy": "112790", "userName": "簡OO", "status": "P", "itemDatas": [{"varItemCode": "ITM20130107001", "varItemValue": "02"}, {"varItemCode": "ITM20130107002", "varItemValue": "分行幹部"}], "wkfEngineFlows": [{"wfgId": "WFG20121005008", "actionName": "待覆核", "actionStatus": "P", "actionCode": "ACT20121005003"}, {"wfgId": "WFG20121005008", "actionName": "覆核完成", "actionStatus": "A", "actionCode": "ACT20121005004"}, {"wfgId": "WFG20121005008", "actionName": "退回修改", "actionStatus": "R", "actionCode": "ACT20121005005"}], "buttonYn": "Y"}, {"eventId": "EVN20250410000013", "wfgId": "WFG20121005008", "createDt": "2025/04/10 16:27:46", "createBy": "112790", "userName": "簡OO", "status": "P", "itemDatas": [{"varItemCode": "ITM20130107001", "varItemValue": "01"}, {"varItemCode": "ITM20130107002", "varItemValue": "分行經辦"}], "wkfEngineFlows": [{"wfgId": "WFG20121005008", "actionName": "待覆核", "actionStatus": "P", "actionCode": "ACT20121005003"}, {"wfgId": "WFG20121005008", "actionName": "覆核完成", "actionStatus": "A", "actionCode": "ACT20121005004"}, {"wfgId": "WFG20121005008", "actionName": "退回修改", "actionStatus": "R", "actionCode": "ACT20121005005"}], "buttonYn": "Y"}, {"eventId": "EVN20250410000012", "wfgId": "WFG20121005008", "createDt": "2025/04/10 16:24:24", "createBy": "112790", "userName": "簡OO", "status": "P", "itemDatas": [{"varItemCode": "ITM20130107001", "varItemValue": "00"}, {"varItemCode": "ITM20130107002", "varItemValue": "理財專員"}], "wkfEngineFlows": [{"wfgId": "WFG20121005008", "actionName": "待覆核", "actionStatus": "P", "actionCode": "ACT20121005003"}, {"wfgId": "WFG20121005008", "actionName": "覆核完成", "actionStatus": "A", "actionCode": "ACT20121005004"}, {"wfgId": "WFG20121005008", "actionName": "退回修改", "actionStatus": "R", "actionCode": "ACT20121005005"}], "buttonYn": "Y"}, {"eventId": "EVN20250410000011", "wfgId": "WFG20121005008", "createDt": "2025/04/10 16:23:51", "createBy": "112790", "userName": "簡OO", "status": "P", "itemDatas": [{"varItemCode": "ITM20130107001", "varItemValue": "31"}, {"varItemCode": "ITM20130107002", "varItemValue": "風管科經辦"}], "wkfEngineFlows": [{"wfgId": "WFG20121005008", "actionName": "待覆核", "actionStatus": "P", "actionCode": "ACT20121005003"}, {"wfgId": "WFG20121005008", "actionName": "覆核完成", "actionStatus": "A", "actionCode": "ACT20121005004"}, {"wfgId": "WFG20121005008", "actionName": "退回修改", "actionStatus": "R", "actionCode": "ACT20121005005"}], "buttonYn": "Y"}], "sqlInfo": "SELECT EVT.*, USR.USER_NAME, WE.CONFIRM_ACTION, EVT.WFGS_CODE CUR_WFGS_CODE, CASE WHEN ((WE.WFG_ROLE_KIND = 'rule2' AND EXISTS (SELECT 1 FROM WKF_ENGINE_ROLES WER WHERE EVT.WFGS_CODE = WER.WFGS_CODE AND WER.WFGS_ROLE = :roleCode ))) OR (WE.WFG_ROLE_KIND = 'rule3' AND JSON_VALUE (EXTRA, '$.nextUserCode') = :userCode ) OR (WE.WFG_ROLE_KIND = 'rule4') OR (WE.WFG_ROLE_KIND = 'rule5' AND EXISTS (SELECT 1 FROM WKF_ENGINE_ROLES wer WHERE EVT.WFGS_CODE = wer.WFGS_CODE AND wer.WFGS_ROLE = :roleCode ) AND (EVT.USER_CODE <> :userCode OR (EVT.POS_CODE <> :posCode AND JSON_VALUE (EXTRA, '$.eventList[0].branCode' ) = :branCode ) OR (EVT.POS_CODE <> :posCode AND JSON_VALUE (EXTRA, '$.eventList[0].secBranCode' ) = :branCode )) ) THEN 'Y' ELSE 'N' END AS BUTTON_YN FROM WKF_EVENTS EVT JOIN WKF_ENGINES WE ON EVT.WFG_ID = WE.WFG_ID JOIN ADM_USERS USR ON EVT.CREATE_BY = USR.USER_CODE WHERE EVT.WFG_ID IN ( :wfgIds ) AND EVT.STATUS = 'P' AND EVT.CREATE_BY <> :userCode AND ((WE.WFG_ROLE_KIND IN ('rule2', 'rule4') AND EXISTS (SELECT 1 FROM (SELECT P.BRAN_CODE, P.POS_CODE, P.ROLE_CODE, P.BU_CODE, AUPM.USER_CODE FROM ADM_POS_ACCESS_MAP PAM JOIN ADM_POSITIONS P ON PAM.ACCESS_POS_CODE = P.POS_CODE LEFT JOIN ADM_USER_POS_MAP AUPM ON P.POS_CODE = AUPM.POS_CODE WHERE PAM.POS_CODE =  '891_41' ) auth WHERE JSON_VALUE(extra, '$.eventList[0].posCode') = auth.pos_code)) OR (WE.WFG_ROLE_KIND = 'rule3' AND JSON_VALUE (extra, '$.nextUserCode') = :userCode ) OR (WE.WFG_ROLE_KIND = 'rule5' AND (JSON_VALUE (extra, '$.eventList[0].branCode' ) = :branCode OR JSON_VALUE (extra, '$.eventList[0].secBranCode' ) = :branCode )))  ORDER BY EVT.EVENT_ID DESC ,class com.bi.pbs.wkf.web.model.WkfEventResp,{branCode=891, roleCode=41, posCode=891_41, wfgIds=[WFG20121005008], userCode=118019}"}, {"data": [{"eventId": "EVN20250410000011", "varItemCode": "ITM20130107001", "varItemValue": "31"}, {"eventId": "EVN20250410000012", "varItemCode": "ITM20130107001", "varItemValue": "00"}, {"eventId": "EVN20250410000013", "varItemCode": "ITM20130107001", "varItemValue": "01"}, {"eventId": "EVN20250410000014", "varItemCode": "ITM20130107001", "varItemValue": "02"}, {"eventId": "EVN20250418000011", "varItemCode": "ITM20130107001", "varItemValue": "05"}, {"eventId": "EVN20250418000012", "varItemCode": "ITM20130107001", "varItemValue": "08"}, {"eventId": "EVN20250421000001", "varItemCode": "ITM20130107001", "varItemValue": "03"}, {"eventId": "EVN20250410000011", "varItemCode": "ITM20130107002", "varItemValue": "風管科經辦"}, {"eventId": "EVN20250410000012", "varItemCode": "ITM20130107002", "varItemValue": "理財專員"}, {"eventId": "EVN20250410000013", "varItemCode": "ITM20130107002", "varItemValue": "分行經辦"}, {"eventId": "EVN20250410000014", "varItemCode": "ITM20130107002", "varItemValue": "分行幹部"}, {"eventId": "EVN20250418000011", "varItemCode": "ITM20130107002", "varItemValue": "FC"}, {"eventId": "EVN20250418000012", "varItemCode": "ITM20130107002", "varItemValue": "區長(財管業務)"}, {"eventId": "EVN20250421000001", "varItemCode": "ITM20130107002", "varItemValue": "分行經理"}], "sqlInfo": " SELECT EVENT_ID, WVD.VARITEM_CODE, WVD.VARITEM_VALUE  FROM WKF_VARITEM_DATALIST WVD  JOIN WKF_VARITEM_MAPPING WVM ON WVD.VARITEM_CODE = WVM.VARITEM_CODE  WHERE EVENT_ID IN ( :eventIds )  ORDER BY WVM.SHOW_ORDER ,class com.bi.pbs.wkf.model.WkfVarItemDataList,{eventIds=[EVN20250421000001, EVN20250418000012, EVN20250418000011, EVN20250410000014, EVN20250410000013, EVN20250410000012, EVN20250410000011]}"}, {"data": [{"wfgId": "WFG20121005008", "actionName": "待覆核", "actionStatus": "P", "actionCode": "ACT20121005003"}, {"wfgId": "WFG20121005008", "actionName": "覆核完成", "actionStatus": "A", "actionCode": "ACT20121005004"}, {"wfgId": "WFG20121005008", "actionName": "退回修改", "actionStatus": "R", "actionCode": "ACT20121005005"}], "sqlInfo": " SELECT WEF.* , WEA.ACTION_STATUS , WEA.ACTION_NAME  FROM WKF_MENUS WM  JOIN WKF_MENU_ENGINE_MAPPING WMEM ON WM.MENU_CODE = WMEM.MENU_CODE  JOIN WKF_ENGINES WE ON WMEM.WFG_ID = WE.WFG_ID  JOIN WKF_ENGINE_FLOWS WEF ON WEF.WFG_ID = WE.WFG_ID  JOIN WKF_ENGINE_ACTIONS WEA ON WEF.ACTION_CODE = WEA.ACTION_CODE  WHERE 1<>1  OR WE.WFG_ID IN ( :wfgIds ) ,class com.bi.pbs.wkf.web.model.WkfEngineFlowsResp,{wfgIds=[WFG20121005008]}"}]}