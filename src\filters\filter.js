import moment from 'moment';
import _ from 'lodash';
import numeral from 'numeral';

export const formatAmt = function (value, format) {
	if (_.isNil(value)) {
		value = 0;
	}
	if (!_.isNumber(value) || _.isNaN(value)) {
		return 'NaN';
	}
	return numeral(value).format(format ? format : '0,0.00');
};

export const formatNumber = function (value, format) {
	if (_.isNil(value)) {
		return '--';
	}
	if (!_.isNumber(value) || _.isNaN(value)) {
		return 'NaN';
	}
	return numeral(value).format(format ? format : '0,0.[00]');
};

export const formatNumberZero = function (value, format) {
	if (_.isNil(value)) {
		value = 0;
	}
	if (!_.isNumber(value) || _.isNaN(value)) {
		return 'NaN';
	}
	return numeral(value).format(format ? format : '0,0.[00]');
};

export const formatNum = function (value, format) {
	if (_.isNil(value)) {
		return '--';
	}
	if (!_.isNumber(value) || _.isNaN(value)) {
		return 'NaN';
	}
	return numeral(value).format(format ? format : '0');
};

export const formatDate = function (value) {
	if (!value) {
		return '';
	}
	if (value.length === 10) {
		if (value.indexOf('/') !== 0) {
			return moment(value, 'YYYY/MM/DD').format('YYYY/MM/DD');
		} else if (value.indexOf('-') !== 0) {
			return moment(value, 'YYYY-MM-DD').format('YYYY-MM-DD');
		}
	}
	var mom = moment(value);
	if (mom.isValid()) {
		return mom.format('YYYY/MM/DD');
	}
	return 'Date Error';
};

export const formatDateTime = function (value) {
	if (!value) {
		return '';
	}
	var mom = moment(value);
	if (mom.isValid()) {
		return mom.format('YYYY/MM/DD HH:mm:ss');
	}
	return 'DateTime Error';
};

export const formatDateTimeFromNow = function (value) {
	if (!value) {
		return '';
	}
	var mom = moment(value);
	if (mom.isValid()) {
		return mom.fromNow();
	}
	return 'DateTime Error';
};

export const formatPct = function (value) {
	if (_.isNil(value)) {
		value = 0;
	}
	if (!_.isNumber(value) || _.isNaN(value)) {
		return 'NaN';
	}
	return numeral(value).multiply(100).format('0.00');
};

export const formatSuffix = function (value, suffix) {
	if (_.isNaN(value) || _.isNil(value) || _.isNull(value) || _.isUndefined(value)) {
		return value;
	}
	return value + suffix;
};

export const toIntLocaleString = function (number) {
	return Math.round(number).toLocaleString();
};

export const toUpperCaseLetter = function (number) {
	if (!_.isInteger(number)) {
		return '';
	}
	return String.fromCharCode('A'.charCodeAt(0) + number);
};

export const formatPhoneNumber = function (number) {
	return number.replace(/(\d{4})\-?(\d{3})\-?(\d{3})/, '$1-$2-$3');
};

export const maskPhoneNumber = function (number) {
	number = _.trim(number);
	number = _.replace(number, '-', '');
	number = _.replace(number, /^(\d{4})(\d*)(\d{3})$/, '$1-***-$3');
	return number;
};

export const maskCusInfo = function (text = '', from = 0, length = -1, segmenter = new Intl.Segmenter({ granularity: 'grapheme' })) {
	if (!text) return text;
	const segments = Array.from(segmenter.segment(text)).map((each) => each.segment);
	from = Math.round(from);
	length = Math.round(length);
	const startInclude = from < 0 ? ((from = segments.length - Math.abs(from)) < 0 ? 0 : from) : from < segments.length ? from : segments.length;
	const endExclude = length < 0 ? segments.length : length > segments.length - startInclude ? segments.length : startInclude + length;
	for (let index = startInclude; index < endExclude; index++) {
		segments[index] = '*';
	}
	return segments.join('');
};

export const formatTwUnit = function (value, precision) {
	return _.formatTwUnit(value, precision);
};

export const defaultValue = function (value, defaultValue) {
	if (_.isNaN(value) || _.isNil(value) || _.isNull(value) || _.isUndefined(value) || _.isEmpty(value)) {
		return defaultValue;
	}
	return value;
};

export const clsSort = function (value, pageable) {
	if (_.toUpper(value) !== _.toUpper(pageable.sort)) {
		return 'fas fa-sort';
	} else if (_.toUpper(pageable.direction) === 'ASC') {
		return 'fas fa-caret-up';
	} else {
		return 'fas fa-caret-down';
	}
};

//需要四捨五入數值使用
export const formatDividend = function (value, format) {
	if (_.isNil(value)) {
		return '--';
	}
	if (!_.isNumber(value) || _.isNaN(value)) {
		return 'NaN';
	}

	//需要四捨五入顯示數值
	//負數
	if (0 > value) {
		value = Math.abs(value);
		return '-' + numeral(value).format(format ? format : '0,0.00');
	} else {
		//正數
		return numeral(value).format(format ? format : '0,0.00');
	}
};

//無條件捨去使用
export const formatCurAmt = function (value, precision, customFormat) {
	if (_.isNil(value)) {
		return '--';
	}
	if (!_.isNumber(value) || _.isNaN(value)) {
		return 'NaN';
	}

	var format = '0,0.00'; // 所有幣別預設皆顯示至小數點後二位

	if (_.isNil(precision)) {
		precision = 2;
	}
	if (precision == 0) {
		format = '0,0'; // 新台幣別顯示至整數
	}
	if (!_.isNil(customFormat)) {
		format = customFormat;
	}

	// 取得小數位數
	var exp = precision;
	var pow = Math.pow(10, exp); // 10 的幂次方
	//顯示所需位數，不需要四捨五入
	return numeral(Math.floor((value * pow).toFixed(exp)) / pow).format(format ? format : '0,0.00');
};

//無條件捨去
export const formatSpecialAmt = function (value, precision, format, isHalfUp) {
	if (_.isNil(value)) {
		return '--';
	}
	if (!_.isNumber(value) || _.isNaN(value)) {
		return 'NaN';
	}

	if (!format) {
		format = '0,0.00'; // 所有幣別預設皆顯示至小數點後二位
	}

	if (_.isNil(precision)) {
		precision = 2;
	}

	if (precision == 0) {
		value = value.toFixed(0);
	}

	//四捨五入
	if (isHalfUp) {
		if (0 > value) {
			value = Math.abs(value);
			return '-' + numeral(value).format(format ? format : '0,0.00');
		} else {
			//正數
			return numeral(value).format(format ? format : '0,0.00');
		}
	}

	// 取得 format 小數位數
	var strs = _.isNil(format) ? [] : _.split(format, '.');
	var exp = _.size(strs) > 1 ? strs[1].length : 2; // 小數點後位數, 預設兩位
	var pow = Math.pow(10, exp); // 10 的幂次方
	//顯示所需位數，不需要四捨五入
	return numeral(Math.floor((value * pow).toFixed(exp)) / pow).format(format ? format : '0,0.00');
};

export const formatFlucWithView = function (value, suffix) {
	if (!_.isNumber(value) || _.isNaN(value)) {
		return NaN;
	}
	var formatValue = value > 0.01 || value <= -0.01 ? numeral(value).format('0,0.[00]') : numeral(value).format('0,0.[00000]');
	if (suffix) formatValue = formatValue + suffix;
	formatValue = '<span class="' + (_.isPositive(value) ? 'font-Rise' : 'font-Fall') + '">' + formatValue + '</span>';
	return formatValue;
};

export const formatTwMillionUnit = function (value) {
	if (_.isNumeric(value)) {
		value = value / 1000000;
		if (value > 0.01) {
			value = _.round(value, 2);
		} else if (value > 0.0001) {
			value = _.round(value, 4);
		} else if (value > 0.000001) {
			value = _.round(value, 6);
		}
		return numeral(value).format('0,0.[00000000]') + '百萬';
	} else {
		return '--';
	}
};

export const sumTotal = function (arrays, sumName) {
	return arrays
		? arrays.reduce(function (a, v) {
				var vv = v[sumName];
				if (_.isNil(vv) || !_.isNumeric(vv)) {
					return a;
				}
				return a + vv;
			}, 0)
		: 0;
};
