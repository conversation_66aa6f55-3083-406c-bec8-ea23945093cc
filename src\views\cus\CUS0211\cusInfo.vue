<template>
	<!--filemgr-sidebar start-->
	<div class="filemgr-wrapper filemgr-wrapper-two" :class="{ hidemenu: isHideMenu }">
		<span id="filemgrMenuclose" @click="hideMenu()"></span>
		<vue-cus-info-sidebar :cus-code="cusCode" :page-code="pageCode" :set-auth="setAuth" :set-customer="setCustomer"></vue-cus-info-sidebar>
		<div class="filemgr-content with-sectionnav">
			<vue-cus-info-page
				v-if="hasAuth == true"
				:cus-code="cusCode"
				:role-code="roleCode"
				:user-code="userCode"
				:has-auth="hasAuth"
				:customer="customer"
			></vue-cus-info-page>
		</div>
	</div>
	<!-- filemgr-content -->
</template>
<script>
import vueCusInfoSidebar from '@/views/cus/include/cusInfoSidebar.vue';
import vueCusInfoPage from './include/cusInfo.vue';
export default {
	components: {
		vueCusInfoSidebar,
		vueCusInfoPage
	},
	data: function () {
		return {
			pageCode: 1,
			cusCode: null,
			roleCode: null,
			userCode: null,
			//畫面判斷用邏輯
			customer: {
				cusName: null,
				idnEntityType: null
			},
			hasAuth: false,
			isHideMenu: false
		};
	},
	watch: {
		userInfo: {
			immediate: true,
			handler: function (newVal, oldVal) {
				var self = this;
				if (newVal) {
					self.roleCode = newVal.roleCode;
					self.userCode = newVal.userCode;
				}
			}
		}
	},
	computed: {
		userInfo: function () {
			return this.$store.getters['userInfo/info'];
		}
	},
	mounted: function () {
		var self = this;
		self.cusCode = self.prop.cusCode;
	},
	methods: {
		setAuth: function (val) {
			var self = this;
			self.hasAuth = val;
		},
		setCustomer: function (val) {
			var self = this;
			self.customer = val;
		},
		hideMenu: function () {
			var self = this;
			self.isHideMenu = !self.isHideMenu;
		}
	}
};
</script>
