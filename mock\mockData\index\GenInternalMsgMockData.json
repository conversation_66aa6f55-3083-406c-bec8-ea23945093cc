{"status": 200, "data": [], "timestamp": "2025/04/21", "sqlTracer": [{"data": [], "sqlInfo": "SELECT MSG.MSG_DATETIME ,MSG.MSG_CONTENT ,MSG.SENDER_USER_CODE , CASE WHEN MSG.SENDER_USER_CODE='SYSBATCH' THEN '系統' ELSE AU.USER_NAME END AS USER_NAME  FROM GEN_INTERNAL_MSG MSG LEFT JOIN ADM_USERS AU ON MSG.SENDER_USER_CODE = AU.USER_CODE WHERE RECEIVER_USER_CODE=:userId AND CAST(MSG_DATETIME AS DATE)=CAST(GETDATE() AS DATE) ORDER BY MSG.MSG_DATETIME DESC ,class com.bi.pbs.gen.web.model.GenInternalMsgResp,{userId=112790}"}]}