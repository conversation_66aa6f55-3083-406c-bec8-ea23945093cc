<template>
	<dynamic-title></dynamic-title>
	<div>
		<div class="row">
			<div class="col-12">
				<vue-form v-slot="{ errors }" ref="queryForm">
					<div class="card card-form">
						<div class="card-header">
							<h4>查詢條件</h4>
							<span class="tx-square-bracket">為必填欄位</span>
						</div>
						<div class="card-body">
							<div class="row g-3 align-items-end">
								<div class="col-lg-2">
									<label class="form-label">使用人代碼</label> &nbsp;{{ user.userName || '' }}
									<input
										type="text"
										name="userCode"
										id="userCode"
										v-model="userCode"
										@blur="isUserExists"
										class="form-control"
										label="使用人代碼"
									/>
									<div style="height: 25px"></div>
								</div>
								<div class="col-lg-5">
									<label class="form-label tx-require">查詢日期區間(起)</label>
									<div class="input-group">
										<span class="input-group-text">日期</span>
										<vue-field
											:class="{ 'is-invalid': errors.logStartDt }"
											name="logStartDt"
											type="date"
											id="logStartDt"
											class="form-control wd-30p-f"
											v-model="logStartDt"
											label="查詢日期區間(起)"
											rules="required"
										></vue-field>
										<span class="input-group-text">時間</span>
										<select name="startHour" id="startHour" class="form-select" v-model="logStartHour">
											<option value="00">00</option>
											<option value="01">01</option>
											<option value="02">02</option>
											<option value="03">03</option>
											<option value="04">04</option>
											<option value="05">05</option>
											<option value="06">06</option>
											<option value="07">07</option>
											<option value="08">08</option>
											<option value="09">09</option>
											<option value="10">10</option>
											<option value="11">11</option>
											<option value="12">12</option>
											<option value="13">13</option>
											<option value="14">14</option>
											<option value="15">15</option>
											<option value="16">16</option>
											<option value="17">17</option>
											<option value="18">18</option>
											<option value="19">19</option>
											<option value="20">20</option>
											<option value="21">21</option>
											<option value="22">22</option>
											<option value="23">23</option>
										</select>
										<select name="startMin" id="startMin" class="form-select" v-model="logStartMinute">
											<option value="00">00</option>
											<option value="15">15</option>
											<option value="30">30</option>
											<option value="45">45</option>
										</select>
									</div>
									<div style="height: 25px">
										<span class="text-danger" v-show="errors.logStartDt">{{ errors.logStartDt }}</span>
									</div>
								</div>
								<div class="col-lg-5">
									<label class="form-label tx-require">查詢日期區間(迄)</label>
									<div class="input-group">
										<span class="input-group-text">日期</span>
										<vue-field
											:class="{ 'is-invalid': errors.logEndDt }"
											name="logEndDt"
											type="date"
											id="endDate"
											class="form-control wd-30p-f"
											v-model="logEndDt"
											rules="required"
											label="查詢日期區間(迄)"
										></vue-field>
										<span class="input-group-text">時間</span>
										<select name="endHour" id="endHour" class="form-select" v-model="logEndHour">
											<option value="00">00</option>
											<option value="01">01</option>
											<option value="02">02</option>
											<option value="03">03</option>
											<option value="04">04</option>
											<option value="05">05</option>
											<option value="06">06</option>
											<option value="07">07</option>
											<option value="08">08</option>
											<option value="09">09</option>
											<option value="10">10</option>
											<option value="11">11</option>
											<option value="12">12</option>
											<option value="13">13</option>
											<option value="14">14</option>
											<option value="15">15</option>
											<option value="16">16</option>
											<option value="17">17</option>
											<option value="18">18</option>
											<option value="19">19</option>
											<option value="20">20</option>
											<option value="21">21</option>
											<option value="22">22</option>
											<option value="23">23</option>
										</select>
										<select name="endMin" id="endMin" class="form-select" v-model="logEndMinute">
											<option value="00">00</option>
											<option value="15">15</option>
											<option value="30">30</option>
											<option value="45">45</option>
										</select>
									</div>
									<div style="height: 25px">
										<span class="text-danger" v-show="errors.logEndDt">{{ errors.logEndDt }}</span>
									</div>
								</div>
								<div class="col-md-auto">
									<label class="form-label">執行項目</label><br />
									<div class="form-check form-check-inline" v-for="actionType in itemsActionTypeMenu">
										<input
											class="form-check-input"
											type="radio"
											name="selActiveSet"
											:value="actionType.codeValue"
											v-model="actionTypes"
											id="selActiveSet-1"
											@click="getFunctionMenu(actionType.codeValue)"
										/>
										<label class="form-check-label checkboxLabel">{{ actionType.codeName }}</label>
									</div>
								</div>
								<div class="col-lg-4 col-md-4">
									<label class="form-label">功能模組</label>
									<div class="input-group">
										<span class="input-group-text">功能項目</span>
										<select name="strTarget" id="target" class="form-select" v-model="targetId">
											<option value="" selected="selected">全部</option>
											<option v-for="carryItem in carryItemMenu" :value="carryItem.progCode">
												{{ carryItem.target }}
											</option>
										</select>
									</div>
								</div>
								<div class="col-lg-2">
									<button role="button" class="btn btn-primary btn-glow btn-search" @click.prevent="gotoPage(0)">查詢</button>
								</div>
							</div>
						</div>
					</div>
				</vue-form>
				<div id="searchResult">
					<div class="card card-table">
						<div class="card-header">
							<h4>使用者資料產生紀錄</h4>
							<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
						</div>
						<div class="table-responsive">
							<table class="table table-RWD table-hover text-center">
								<thead>
									<tr>
										<th>日期</th>
										<th>分行(單位)代號</th>
										<th>分行(單位)名稱</th>
										<th>行員編號</th>
										<th>行員名稱</th>
										<th>功能項目</th>
										<th>客戶ID/統編/商品代碼</th>
										<th>執行項目</th>
										<th>次數</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="item in pageData.content">
										<td data-th="日期">{{ $filters.formatDate(item.logDate) }}</td>
										<td data-th="分行(單位)代號">{{ item.branCode }}</td>
										<td data-th="分行(單位)名稱">{{ item.branName }}</td>
										<td data-th="行員編號">{{ item.userCode }}</td>
										<td data-th="行員名稱">{{ item.userName }}</td>
										<td data-th="功能項目">{{ item.target }}</td>
										<td data-th="客戶ID/統編/商品代碼">{{ item.cusCode }} {{ item.docId }}</td>
										<td data-th="執行項目">{{ item.actionType }}</td>
										<td data-th="次數">{{ item.num }}</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!--頁面內容 end-->
	</div>
</template>
<script>
import _ from 'lodash';
import Swal from 'sweetalert2';
import { Field, Form } from 'vee-validate';
import dynamicTitle from '@/views/components/dynamicTitle.vue';
import pagination from '@/views/components/pagination.vue';

export default {
	components: {
		'vue-pagination': pagination,
		'vue-form': Form,
		'vue-field': Field,
		dynamicTitle
	},
	data: function () {
		return {
			//API 用參數
			userCode: null,
			targetId: '',
			actionTypes: [],
			logStartDt: null,
			logStartHour: '00',
			logStartMinute: '00',
			logEndDt: null,
			logEndHour: '00',
			logEndMinute: '00',
			//下拉選單
			carryItemMenu: [],
			itemsActionTypeMenu: [],
			//主要顯示資料
			pageData: {},
			pageable: {
				page: 0,
				size: 20,
				sort: 'log_date',
				direction: 'ASC'
			},
			user: {}
		};
	},
	mounted: function () {
		var self = this;
		self.getItemsActionTypeMenu();
	},
	methods: {
		isUserExists() {
			var self = this;
			if (self.userCode && self.userCode.trim() !== '') {
				self.$api
					.getUserInfoApi({
						userCode: self.userCode,
						path: 'auditUsageDataProduce'
					})
					.then(function (ret) {
						if (ret.data) {
							self.user = ret.data;
						} else {
							self.user = {};
						}
					})
					.catch(function (ret) {
						self.user = {};
					});
			} else {
				self.user = {};
			}
		},
		getFunctionMenu: function (actionMode) {
			var self = this;
			self.$api
				.getCarryOutItemsActionTypeApi({
					actionMode: actionMode
				})
				.then(function (ret) {
					self.carryItemMenu = ret.data;
				});
		},
		getItemsActionTypeMenu: function () {
			var self = this;
			self.$api.getItemsActionTypeMenuApi().then(function (ret) {
				self.itemsActionTypeMenu = ret.data;
			});
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: function (page) {
			var self = this;

			if (self.logStartDt > self.logEndDt) {
				Swal.fire({
					icon: 'error',
					text: '查詢日期區間(起)不可大於查詢日期區間(迄)。',
					showCloseButton: true,
					confirmButtonText: '確認',
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}

			var stime = new Date(self.logStartDt).getTime();
			var etime = new Date(self.logEndDt).getTime();
			var usedTime = etime - stime;
			var days = Math.floor(usedTime / (24 * 3600 * 1000));
			if (days > 30) {
				Swal.fire({
					icon: 'error',
					text: '查詢日期區間不可超過30天。',
					showCloseButton: true,
					confirmButtonText: '確認',
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}

			self.$refs.queryForm.validate().then(function (pass) {
				if (pass.valid) {
					var url = _.toPageUrl('', page, self.pageable);

					var logStdDt = self.logStartDt + ' ' + self.logStartHour + ':' + self.logStartMinute;
					var logEndDt = self.logEndDt + ' ' + self.logEndHour + ':' + self.logEndMinute;

					self.$api
						.getCarryOutItemsApi(
							{
								userCode: self.userCode,
								progCode: self.targetId,
								actionTypes: self.actionTypes,
								logStdDt: logStdDt,
								logEndDt: logEndDt
							},
							url
						)
						.then(function (ret) {
							self.pageData = ret.data;
						});
				}
			});
		}
	}
};
</script>
