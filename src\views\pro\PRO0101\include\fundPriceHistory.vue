<template>
	<div class="col-12">
		<h4>
			基金淨值線圖 <small class="small-text">資料更新日：{{ $filters.defaultValue($filters.formatDate(latestDate), '--') }}</small>
		</h4>
		<br />
		<div class="btn-group m-l-20">
			<span class="btn btn-sm btn-default" :class="clsPeriod('5D')" @click="changePeriod('5D')">5日</span>
			<span class="btn btn-sm btn-default" :class="clsPeriod(1)" @click="changePeriod(1)">1月</span>
			<span class="btn btn-sm btn-default" :class="clsPeriod(6)" @click="changePeriod(6)">6月</span>
			<span class="btn btn-sm btn-default" :class="clsPeriod('YTD')" @click="changePeriod('YTD')">今年以來</span>
			<span class="btn btn-sm btn-default" :class="clsPeriod(12)" @click="changePeriod(12)">1年</span>
			<span class="btn btn-sm btn-default" :class="clsPeriod(60)" @click="changePeriod(60)">5年</span>
		</div>

		<!-- 線圖 -->
		<vue-price-chart ref="fundPriceChartRef" :chart-id="priceChartId"></vue-price-chart>
	</div>
</template>
<script>
import _ from 'lodash';
import moment from 'moment';
import vuePriceChart from './priceChart.vue';

export default {
	components: {
		vuePriceChart
	},
	props: {
		fundCode: String
	},
	data: function () {
		return {
			priceChartId: 'fundPriceChartId',
			selectedPeriod: 'YTD',
			prices: [],
			pricChartData: [{ name: '淨值', datas: {} }]
		};
	},
	watch: {
		fundCode: {
			handler: function (newVal, oldVal) {
				this.getPrices();
			}
		}
	},
	computed: {
		latestDate: function () {
			if (this.prices.length > 0) {
				return _.orderBy(
					_.filter(this.prices, function (item) {
						return item.dataDate;
					}),
					['dataDate'],
					['desc']
				)[0].dataDate;
			} else {
				return null;
			}
		}
	},
	mounted: function () {},
	methods: {
		changePeriod: function (period) {
			this.selectedPeriod = period;
			this.getPrices();
		},
		clsPeriod: function (period) {
			return this.selectedPeriod == period ? 'active' : null;
		},
		getPrices: async function () {
			var self = this;
			var period = self.selectedPeriod;
			var beginDate;
			var endDate;
			if (period === 'YTD') {
				beginDate = moment().startOf('year').format('YYYY/MM/DD');
				endDate = moment().format('YYYY/MM/DD');
			} else if (period === '5D') {
				beginDate = moment().subtract(5, 'day').format('YYYY/MM/DD');
				endDate = moment().format('YYYY/MM/DD');
			} else if (period !== 'MAX') {
				beginDate = moment().subtract(period, 'months').format('YYYY/MM/DD');
				endDate = moment().format('YYYY/MM/DD');
			}
			var priceData = [];
			const ret = await this.$api.getPreMonthEndPriceApi({
				prCode: self.fundCode,
				beginDate: beginDate,
				endDate: endDate
			});

			if (!_.isNil(ret.data)) {
				self.prices = ret.data;
				self.prices.forEach((d) => {
					var pData = {
						date: Date.parse(d.dataDate),
						value: d.priceLc
					};
					priceData.push(pData);
				});
				self.pricChartData[0].datas = priceData;
				self.pricChartData[0].name = '淨值';
			} else {
				self.pricChartData[0].datas = [];
				self.pricChartData[0].name = '淨值';
			}
			self.$refs.fundPriceChartRef.initChart(self.pricChartData);
		}
	}
};
</script>
