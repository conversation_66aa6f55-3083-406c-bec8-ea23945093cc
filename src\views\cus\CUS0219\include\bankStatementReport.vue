<template>
	<div>
		<div>
			<div class="row">
				<div class="col-12">
					<div class="text-end">
						<button name="Submit1" type="button" class="btn btn-primary" @click="generateCuaAssetReport()">產生資產整合報告書</button>
					</div>
					<div class="card card-table mt-3 table-responsive">
						<div class="card-header">
							<h4>資產整合報告書</h4>
						</div>
						<table class="table">
							<thead>
								<tr>
									<th>產生日期</th>
									<th class="wd-100 text-center">檢視</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="item in cusFiles">
									<td>{{ item.createDt }}</td>
									<td class="text-center">
										<button
											type="button"
											class="btn btn-dark btn-icon"
											title="檢視"
											data-bs-toggle="tooltip"
											@click="downloadFile(item.fileId)"
										>
											<i class="bi bi-search"></i>
										</button>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		cusCode: String
	},
	data: function () {
		return {
			cusFiles: []
		};
	},
	mounted: function () {
		if (this.cusCode) {
			this.getFiles();
		}
	},
	methods: {
		getFiles: async function () {
			const ret = await this.$api.getCusFilesList({
				cusCode: this.cusCode,
				fileType: 'AST'
			});
			this.cusFiles = ret.data;
		},
		downloadFile: async function (id) {
			var self = this;
			await self.$api.downloadCusFile(id);
		},
		generateCuaAssetReport: async function () {
			if (this.cusFiles.some((cf) => moment().isSame(cf.createDt, 'day'))) {
				this.$bi.alert('今日已產生過報告書，請於資產現況報告書清單檢視');
			} else {
				const ret = await this.$api.generateCuaAssetReportApi({
					cusCode: this.cusCode
				});
				this.getFiles();
			}
		}
	}
};
</script>
