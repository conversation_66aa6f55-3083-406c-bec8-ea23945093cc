// css
import './assets/css/framework-plugin/vue/vue-loading.css';
import './assets/css/framework-plugin/fonts/line-awesome/css/line-awesome.min.css';
import './assets/css/framework-plugin/fonts/@fortawesome/fontawesome-free/css/all.css';
import './assets/css/framework-plugin/bootstrap.min.css';
import './assets/css/framework-plugin/bootstrap.css';
import './assets/css/framework-plugin/bootstrap-select.min.css';
import './assets/css/framework-plugin/dashforge.css';
import './assets/css/framework-plugin/dashforge.profile.css';
import './assets/css/framework-plugin/dashforge.filemgr.css';
import './assets/css/framework-plugin/skin.light.css';
import './assets/css/framework-plugin/calendarstyle.css';
import './assets/css/framework-plugin/fonts/bs-font/bootstrap-icons.css';

import './assets/css/bi/marquee.css';
import './assets/css/bi/table.css';
import './assets/css/bi/layout.css';
import './assets/css/bi/style.css';
import './assets/css/bi/base.css';
import './assets/css/bi/bi.tree.css';
import './assets/css/bi/module.css';

// Framework plugin
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
// moment.js
import moment from 'moment';
moment.defineLocale('zh-tw', {
	months: '一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月'.split('_'),
	monthsShort: '1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月'.split('_'),
	weekdays: '星期日_星期一_星期二_星期三_星期四_星期五_星期六'.split('_'),
	weekdaysShort: '週日_週一_週二_週三_週四_週五_週六'.split('_'),
	weekdaysMin: '日_一_二_三_四_五_六'.split('_'),
	longDateFormat: {
		LT: 'HH:mm',
		LTS: 'HH:mm:ss',
		L: 'YYYY/MM/DD',
		LL: 'YYYY年M月D日',
		LLL: 'YYYY年M月D日 HH:mm',
		LLLL: 'YYYY年M月D日dddd HH:mm',
		l: 'YYYY/M/D',
		ll: 'YYYY年M月D日',
		lll: 'YYYY年M月D日 HH:mm',
		llll: 'YYYY年M月D日dddd HH:mm'
	},
	meridiemParse: /凌晨|早上|上午|中午|下午|晚上/,
	meridiemHour: function (e, t) {
		return (
			12 === e && (e = 0),
			'凌晨' === t || '早上' === t || '上午' === t ? e : '中午' === t ? (11 <= e ? e : e + 12) : '下午' === t || '晚上' === t ? e + 12 : void 0
		);
	},
	meridiem: function (e, t, d) {
		e = 100 * e + t;
		return e < 600 ? '凌晨' : e < 900 ? '早上' : e < 1130 ? '上午' : e < 1230 ? '中午' : e < 1800 ? '下午' : '晚上';
	},
	calendar: {
		sameDay: '[今天] LT',
		nextDay: '[明天] LT',
		nextWeek: '[下]dddd LT',
		lastDay: '[昨天] LT',
		lastWeek: '[上]dddd LT',
		sameElse: 'L'
	},
	dayOfMonthOrdinalParse: /\d{1,2}(日|月|週)/,
	ordinal: function (e, t) {
		switch (t) {
			case 'd':
			case 'D':
			case 'DDD':
				return e + '日';
			case 'M':
				return e + '月';
			case 'w':
			case 'W':
				return e + '週';
			default:
				return e;
		}
	},
	relativeTime: {
		future: '%s後',
		past: '%s前',
		s: '幾秒',
		ss: '%d 秒',
		m: '1 分鐘',
		mm: '%d 分鐘',
		h: '1 小時',
		hh: '%d 小時',
		d: '1 天',
		dd: '%d 天',
		M: '1 個月',
		MM: '%d 個月',
		y: '1 年',
		yy: '%d 年'
	}
});

// sweetalert2
import Swal from 'sweetalert2';

// lodash
import _ from 'lodash';
import './utils/lodashExtensions'; // 自定義的 lodash extension

// numeral
import numeral from 'numeral';

// vuex
import { createStore } from 'vuex';
import { userInfo } from './stores/userInfo.js';
import { menus } from './stores/menus.js';
import { tabStatus } from './stores/tabStatus.js';
const store = createStore({
	modules: {
		userInfo,
		menus,
		tabStatus
	}
});

// vee-validate
import { defineRule, configure } from 'vee-validate';
import * as VeeValidateRules from '@vee-validate/rules';
import { loadLocaleFromURL, localize } from '@vee-validate/i18n';
// 註冊內建規則
Object.entries(VeeValidateRules.all).forEach(([name, rule]) => {
	defineRule(name, rule);
});
// 載入語言文件並配置
loadLocaleFromURL('./validate/zh_TW.json');
configure({ generateMessage: localize('zh_TW') });
// 註冊自定義規則
import './utils/validate/file.js';

// vue-loading-overlay
import { LoadingPlugin } from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/css/index.css';
// jquery
import $ from 'jquery';
window.$ = window.jQuery = $;

// 或者如果你想用 ES6 方式：
import * as bootstrap from 'bootstrap';
window.bootstrap = bootstrap;

// 延遲載入 bootstrap-select，確保 Bootstrap 完全載入
setTimeout(() => {
	import('bootstrap-select')
		.then(() => {
			console.log('bootstrap-select loaded successfully');
		})
		.catch((err) => {
			console.error('Error loading bootstrap-select:', err);
		});
}, 100);

// dashforge by bi (由於依賴jQuery，使用非同步確保其不會早於jQuery載入完成前執行)
import('./utils/bi/dashforge/dashforge.js')
	.then(() => {
		console.log('dashforge.js已加载，且jQuery準備好');
	})
	.catch((err) => {
		console.error('import dashforge.js 時發生錯誤:', err);
	});

// perfect-scrollbar
// import PerfectScrollbar from 'perfect-scrollbar'

// js.cookie
// import Cookies from 'js-cookie

// base.js by bi
import { biAjax } from './utils/bi/base.js';
// module.js  by bi
import { biModule } from './utils/bi/module.js';
let bi = {
	...biAjax,
	headers: {},
	...biModule
};

// amcharts
// import * as am5 from '@amcharts/amcharts5';
// import * as am5xy from '@amcharts/amcharts5/xy';
// import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
// import './assets/js/framework-plugin/amcharts/index.js'
// import './assets/js/framework-plugin/amcharts/xy.js'
// import './assets/js/framework-plugin/amcharts/Animated.js'
// import './assets/js/framework-plugin/amcharts/percent.js'

// vue-i18n
import { createI18n } from 'vue-i18n';

import { createApp } from 'vue';

import App from './App.vue';
import router from './router';

const app = createApp(App);

// 全域註冊套件的功能
// moment.js
app.config.globalProperties.$moment = moment;
// sweetalert2
app.config.globalProperties.$swal = Swal;
// lodash
app.config.globalProperties.$_ = _;
// numeral
app.config.globalProperties.$numeral = numeral;
// Vuex
app.use(store);
// vue-loading-overlay
app.use(LoadingPlugin);
app.use(ElementPlus);
// base.js
// module.js
app.config.globalProperties.$bi = bi;

// filter
import * as filters from './filters/filter.js';
app.config.globalProperties.$filters = filters;

// api
import api from '@/api/apiService.js';
app.config.globalProperties.$api = api;

// mixin
// import userCodeComplement from './utils/mixin/userCodeComplement.js'
// app.mixin(userCodeComplement)

//AG-Grid
import { AllCommunityModule, ModuleRegistry, ClientSideRowModelModule } from 'ag-grid-community';
import { IntegratedChartsModule } from 'ag-grid-enterprise';
import { AgChartsEnterpriseModule } from 'ag-charts-enterprise';
import { MenuModule } from 'ag-grid-enterprise';
ModuleRegistry.registerModules([AllCommunityModule, ClientSideRowModelModule, IntegratedChartsModule.with(AgChartsEnterpriseModule), MenuModule]);

app.use(router);

// vue-i18n
async function initVueI18n() {
	try {
		const langListResponse = await fetch(`./lang/langList.json`);
		const langList = (await langListResponse.json()).langList;
		let messages = {};
		// 使用 Promise.all 等待所有詞彙表載入完成
		const langPromises = langList.map(async (langCode) => {
			const langResponse = await fetch(`./lang/${langCode}.json`);
			const lang = await langResponse.json();
			messages[langCode] = lang;
		});
		await Promise.all(langPromises);

		const i18n = createI18n({
			locale: langList[0], // 設定預設語言為langList.json第一個
			globalInjection: true,
			messages
		});
		app.use(i18n);
		app.mount('#app');
	} catch (error) {
		console.log('遇到錯誤，繼續執行其他操作', error);
	}
}
initVueI18n();
