<template>
	<div class="col-lg-4 ps-lg-3">
		<div class="d-none d-lg-block">
			<div class="d-grid gap-2 mb-3">
				<router-link class="btn btn-lg btn-dark tx-16 tx-bold d-flex justify-content-between" :to="'/cus/cusSearch'">
					<span><img :src="getImgURL('icon', 'ico-single.png')" /> 單一條件客戶查詢</span> <i
						class="bi bi-shuffle"></i></router-link>
			</div>
		</div>
		<div class="card card-table">
			<div class="card-header">
				<h4>歷史查詢名單</h4>
				<span class="tx-square-bracket">最多可儲存五組查詢結果</span>
			</div>
			<div class="table-responsive">
				<table class="table table-RWD table-hover text-center">
					<thead>
						<tr>
							<th class="text-start">查詢結果</th>
							<th>建立日期</th>
							<th>執行</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="item in searchHistory">
							<td class="text-start" data-th="查詢結果">
								<a href="#" class="tx-link" @click="getSearchHistoryByResultCode(item.resultCode)">{{ item.resultName
									}}</a>
							</td>
							<td data-th="建立日期">{{ item.createDt }}</td>
							<td data-th="執行">
								<button type="button" class="btn tx-danger btn-icon-only JQ-logDelete" id="btnQuery"
									data-bs-toggle="tooltip" data-bs-original-title="刪除" @click="deleteLog(item)">
									<i class="bi bi-trash"></i>
								</button>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</template>
<script>
import { getImgURL } from '@/utils/imgURL.js';
export default {
	props: {
		search: Function
	},
	data() {
		return {
			//主要顯示資料
			searchHistory: []
		};
	},
	computed: {
		userInfo() {
			return this.$store.getters['userInfo/info'];
		}
	},
	watch: {
		userInfo(newVal, oldVal) {
			if (newVal) {
				this.getSearchHistory();
			}
		}
	},
	mounted() {
		this.getSearchHistory();
	},
	methods: {
		getImgURL,
		async getSearchHistory() {
			const ret = await this.$api.getSearchHistoryApi({});
			this.searchHistory = ret.data;
		},
		async deleteLog(item) {
			this.$bi.confirm('是否確定刪除' + item.resultName, {
				event: {
					confirmOk: async () => {
						await this.$api.postCusSearchLog({
							resultCode: item.resultCode,
							resultName: item.resultName,
							logType: 'D'
						});
						await this.$api.deleteSearchResult({
							resultCode: item.resultCode
						});
						this.$bi.alert('刪除成功');
						this.getSearchHistory();
					}
				}
			});
		},
		getSearchHistoryByResultCode: function (resultCode) {
			this.search({
				queryType: 'RESULT_CODE',
				resultCode: resultCode
			});
		}
	}
};
</script>
