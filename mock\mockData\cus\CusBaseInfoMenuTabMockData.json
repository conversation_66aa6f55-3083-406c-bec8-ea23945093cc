{"status": 200, "data": [{"code": "M20-0520", "name": "本行顧客資料", "parentCode": "M20-052", "url": "vue-cus-basic-info", "order": 1, "leafYn": "Y", "leaf": false}, {"code": "M20-0521", "name": "公司基本資料", "parentCode": "M20-052", "url": "vue-cus-company", "order": 2, "leafYn": "Y", "leaf": false}, {"code": "M20-0522", "name": "家庭與親友資料", "parentCode": "M20-052", "url": "vue-cus-home-friend", "order": 3, "leafYn": "Y", "leaf": false}, {"code": "M20-0523", "name": "重要節日設定", "parentCode": "M20-052", "url": "vue-cus-important-holiday", "order": 4, "leafYn": "Y", "leaf": false}, {"code": "M20-0524", "name": "其他補充資料", "parentCode": "M20-052", "url": "vue-cus-other-info", "order": 5, "leafYn": "Y", "leaf": false}], "timestamp": "2025/07/12", "sqlTracer": [{"data": [{"code": "M20-0520", "name": "本行顧客資料", "parentCode": "M20-052", "url": "vue-cus-basic-info", "order": 1, "leafYn": "Y", "leaf": false}, {"code": "M20-0521", "name": "公司基本資料", "parentCode": "M20-052", "url": "vue-cus-company", "order": 2, "leafYn": "Y", "leaf": false}, {"code": "M20-0522", "name": "家庭與親友資料", "parentCode": "M20-052", "url": "vue-cus-home-friend", "order": 3, "leafYn": "Y", "leaf": false}, {"code": "M20-0523", "name": "重要節日設定", "parentCode": "M20-052", "url": "vue-cus-important-holiday", "order": 4, "leafYn": "Y", "leaf": false}, {"code": "M20-0524", "name": "其他補充資料", "parentCode": "M20-052", "url": "vue-cus-other-info", "order": 5, "leafYn": "Y", "leaf": false}], "sqlInfo": " SELECT DISTINCT      M<PERSON><PERSON><PERSON>_CODE CODE,      <PERSON><PERSON>ME<PERSON>_NAME NAME,      M.PARENT_MENU_CODE PARENT_CODE,      M<PERSON>SHOW_ORDER 'ORDER',      M.MENU_ICON ICON,      <PERSON><PERSON>TAB_YN LEAF_YN,      AP.PROG_CLASSNAME URL  FROM ADM_MENUS M      JOIN ADM_ROLE_MENU_MAP ARMM ON ARMM.MENU_CODE = M.MENU_CODE      LEFT JOIN ADM_PROGS AP ON AP.PROG_CODE = M.PROG_CODE  WHERE ARMM.ROLE_CODE = :roleCode    AND M.ACTIVE_YN = 'Y'    AND M.PARENT_MENU_CODE = :parentMenuCode    AND M.TAB_YN = :tabYn ORDER BY M.SHOW_ORDER ASC, M.MENU_CODE ASC ,class com.bi.frame.menu.model.MenuTree,{roleCode=98, tabYn=Y, parentMenuCode=M20-052}"}]}