<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form-collapse">
			<div class="card-header" data-bs-toggle="collapse" data-bs-target="#card-body">
				<h4>請輸入下列資料</h4>
				<span class="tx-square-bracket">為必填欄位</span>
			</div>

			<vue-form v-slot="{ errors }" ref="queryForm">
				<div class="card-body collapse show" id="card-body">
					<div class="row g-3 align-items-end">
						<div class="col-md-4">
							<div class="form-group" style="margin-bottom: 0px">
								<label class="form-label tx-require">可服務的最低理專等級</label>
								<vue-field
									as="select"
									class="form-select"
									:class="{ 'is-invalid': errors.rmsLvlCode }"
									style="margin-left: 3.5rem"
									v-model="rmsLvlCode"
									name="rmsLvlCode"
									label="可服務的最低理專等級"
									rules="required"
								>
									<option selected value>--</option>
									<option v-for="option in rmsLvlCodeList" :value="option.rmLvlCode">{{ option.rmLvlName }}</option>
								</vue-field>
							</div>
							<div class="text-danger" style="height: 25px">{{ errors.rmsLvlCode }}</div>
						</div>
						<div class="col-md-4">
							<div class="form-group" style="margin-bottom: 0px">
								<label style="margin-bottom: 0.5rem; font-weight: bold; margin-right: 1.5rem">客戶等級代碼</label><br />
								<label style="margin-bottom: 0.5rem; font-weight: bold">{{ graCode }}</label>
							</div>
							<div style="height: 25px"></div>
						</div>
						<div class="col-md-4">
							<div class="form-group" style="margin-bottom: 0px">
								<label class="form-label tx-require">等級中文名稱</label>
								<vue-field
									type="text"
									class="form-control"
									:class="{ 'is-invalid': errors.graName }"
									v-model="graName"
									name="graName"
									label="等級中文名稱"
									rules="required"
								></vue-field>
							</div>
							<div class="text-danger" style="height: 25px">{{ errors.graName }}</div>
						</div>
						<div class="col-md-4">
							<div class="form-group" style="margin-bottom: 0px">
								<label class="form-label tx-require">AUA下限(新臺幣)</label>
								<vue-field
									type="text"
									class="form-control ms-4"
									:class="{ 'is-invalid': errors.auaMin }"
									v-model="auaMin"
									name="auaMin"
									label="AUA下限(新臺幣)"
									rules="required"
								></vue-field>
							</div>
							<span class="text-danger" style="height: 25px">{{ errors.auaMin }}</span>
						</div>
						<div class="col-md-4">
							<div class="form-group" style="margin-bottom: 0px">
								<label class="form-label tx-require">AUA上限(新臺幣)</label>
								<vue-field
									type="text"
									name="auaMax"
									:class="{ 'is-invalid': errors.auaMax }"
									min="1"
									class="form-control ms-4"
									v-model="auaMax"
									label="AUA上限(新臺幣)"
									rules="required"
								></vue-field>
							</div>
							<div class="text-danger" style="height: 25px">{{ errors.auaMax }}</div>
						</div>
						<div class="form-footer">
							<button class="btn btn-primary btn-glow btn-save" @click.prevent="edit()">修改</button>&nbsp;
							<button class="btn btn-primary btn-glow btn-save" @click="cancelEdit()">取消修改</button>
						</div>
					</div>
				</div>
			</vue-form>
		</div>

		<div class="card card-table">
			<div class="card-header">
				<h4>客戶資產等級列表</h4>
			</div>
			<div class="table-responsive">
				<table class="table table-RWD table-hover text-center">
					<thead>
						<tr>
							<th>客戶等級代碼</th>
							<th>資產等級中文名稱</th>
							<th>資產範圍</th>
							<th>可服務的最低理專等級</th>
							<th>維護人員</th>
							<th>異動日期</th>
							<th>執行</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="item in queryResultList">
							<td data-th="客戶等級代碼">{{ item.graCode }}</td>
							<td data-th="資產等級中文名稱">{{ item.graName }}</td>
							<td data-th="資產範圍">{{ item.auaMin }} ~ {{ item.auaMax }}</td>
							<td data-th="可服務的最低理專等級">{{ item.rmLvlName }}</td>
							<td data-th="維護人員">{{ item.modifyBy }} {{ item.userName }}</td>
							<td data-th="異動日期">{{ item.modifyDt }}</td>
							<td>
								<button
									type="button"
									class="btn btn-info btn-glow btn-icon btn-edit"
									data-bs-toggle="tooltip"
									data-bs-original-title="編輯"
									@click="beforeEdit(item)"
								>
									<i class="bi bi-pen"></i>
								</button>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import Swal from 'sweetalert2';
import { Field, Form } from 'vee-validate';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	data: function () {
		return {
			potCusUl: null,
			potCusValidDt: null,
			rmsLvlCode: null,
			graCode: null,
			graName: null,
			auaMin: null,
			auaMax: null,
			rmsLvlCodeList: [],
			queryResultList: []
		};
	},
	mounted: function () {
		var self = this;
		self.getRmLvlMenu();
		self.getCusGradesSetLists();
	},
	methods: {
		getRmLvlMenu: function () {
			var self = this;
			self.$api.getRmLvlMenuApi().then(function (ret) {
				self.rmsLvlCodeList = ret.data;
			});
		},
		getCusGradesSetLists: function () {
			var self = this;
			self.$api.getCusGradesSetListsApi().then(function (ret) {
				self.queryResultList = ret.data;
			});
		},
		beforeEdit: function (queryResult) {
			var self = this;
			self.rmsLvlCode = queryResult.rmLvlCode;
			self.graCode = queryResult.graCode;
			self.graName = queryResult.graName;
			self.auaMin = queryResult.auaMin;
			self.auaMax = queryResult.auaMax;
		},
		edit: function () {
			var self = this;
			if (_.isBlank(self.rmsLvlCode)) {
				self.sendErrMsg('請輸入可服務的最低理專等級');
				return;
			}
			if (_.isBlank(self.graName)) {
				self.sendErrMsg('請輸入等級中文名稱');
				return;
			}
			if (_.isBlank(self.auaMin)) {
				self.sendErrMsg('請輸入AUA下限(新臺幣)');
				return;
			}
			if (_.isBlank(self.auaMax)) {
				self.sendErrMsg('請輸入AUA上限(新臺幣)');
				return;
			}
			if (!_.isNumeric(self.auaMin)) {
				self.sendErrMsg('AUA下限輸入非數字');
				return;
			}
			if (!_.isNumeric(self.auaMax)) {
				self.sendErrMsg('AUA上限輸入非數字');
				return;
			}
			if (_.toNumber(self.auaMin) > _.toNumber(self.auaMax)) {
				self.sendErrMsg('AUA上限必須大於AUA下限');
				return;
			}

			self.$api
				.patchCusGradesSetListsApi({
					graCode: self.graCode,
					rmLvlCode: self.rmsLvlCode,
					graName: self.graName,
					auaMin: self.auaMin,
					auaMax: self.auaMax
				})
				.then(function (ret) {
					self.getCusGradesSetLists();
				})
				.catch(function (err) {
					self.sendErrMsg(err.message);
				});
		},
		cancelEdit: function () {
			var self = this;
			self.graCode = null;
			self.rmsLvlCode = null;
			self.graName = null;
			self.auaMin = null;
			self.auaMax = null;
		},
		sendErrMsg: function (msg) {
			Swal.fire({
				text: msg,
				icon: 'error',
				showCloseButton: true,
				confirmButtonText: '確認',
				buttonsStyling: false,
				customClass: {
					confirmButton: 'btn btn-danger'
				}
			});
			return;
		}
	}
};
</script>
