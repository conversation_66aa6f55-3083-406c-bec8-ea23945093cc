<template>
	<div class="container-body">
		<div class="container">
			<div class="logo">
				<img src="../assets/images/logo/logo-ifa.svg" alt="" />
			</div>

			<h2>會員登入</h2>

			<Form @submit="login" v-slot="{ errors }" ref="valid">
				<div class="input-group">
					<label for="company-code">商戶號</label>
					<Field
						type="email"
						id="company-code"
						name="tenantNumber"
						v-model="tenantNumber"
						rules="required"
						label="商戶號"
						:class="{
							'is-invalid': errors.tenantNumber
						}"
					></Field>
					<div class="invalid-feedback" v-show="errors.tenantNumber">
						{{ errors.tenantNumber }}
					</div>
				</div>
				<div class="input-group">
					<label for="login-email">帳號</label>
					<Field
						type="email"
						id="login-email"
						name="userCode"
						placeholder="<EMAIL>"
						v-model="userCode"
						rules="required"
						label="帳號"
						:class="{
							'is-invalid': errors.userCode
						}"
					></Field>
					<div class="invalid-feedback" v-show="errors.userCode">
						{{ errors.userCode }}
					</div>
				</div>
				<div class="input-group">
					<label for="login-password">密碼</label>
					<Field
						type="password"
						id="login-password"
						name="pwd"
						placeholder="請輸入密碼"
						v-model="pwd"
						rules="required"
						label="密碼"
						:class="{
							'is-invalid': errors.pwd
						}"
					></Field>
					<div class="invalid-feedback" v-show="errors.pwd">
						{{ errors.pwd }}
					</div>
				</div>
				<div class="input-group">
					<vue-recaptcha :sitekey="v2Sitekey" size="normal" theme="light" hl="zh-TW" @verify="recaptchaVerified" ref="vueRecaptcha">
					</vue-recaptcha>
				</div>
				<button class="btn btn-primary" type="submit">登入</button>
				<a href="#" class="btn btn-outline-primary" type="button">忘記密碼</a>
			</Form>
		</div>
	</div>
</template>

<script>
import { Form, Field } from 'vee-validate';
import vueRecaptcha from 'vue3-recaptcha2';
import { setToken } from '@/utils/auth.js';
import axios from 'axios';
// import userCodeComplement from '../utils/mixin/userCodeComplement.js';

export default {
	components: {
		Form,
		Field,
		vueRecaptcha
	},
	data: function () {
		return {
			userCode: 'sio', //[[${userCode ?: null}]],
			pwd: '123',
			tenantNumber: '00001',

			v2Sitekey: '6Lc5sxUrAAAAAEANxF1wda0mrtYBatIt6LS-8OOz',
			recaptchaResponse: null
			// valid: false, //[[${valid ?: false}]],
			// isUserAccountSwitch: false, //[[${isUserAccountSwitch ?: false}]],
			// userDeputies: null,
			// userPositions: null,
			// selectedUserCode: null,
			// selectedPosCode: null
		};
	},

	watch: {},
	methods: {
		recaptchaVerified: async function (respToke) {
			let ret = await this.$api.recaptchaVerifiedApi(respToke); // 回傳 token 並把 token 傳給後端驗證
			this.recaptchaResponse = ret.data.success;
		},
		login: async function () {
			if (!import.meta.env.VITE_API_MOCK_ENABLED && !this.recaptchaResponse) {
				this.$swal.fire({
					title: 'error',
					text: '請勾選『我不是機器人』以完成驗證。',
					icon: 'error'
				});
				return;
			}

			let ret = await this.$api.loginApi(this.tenantNumber, this.userCode, this.pwd);

			if (ret.data.accessToken && ret.data.refreshToken) {
				setToken('accessToken', ret.data.accessToken);
				setToken('refreshToken', ret.data.refreshToken);
				this.$router.push('/selectpos');
			} else {
				this.$swal.fire({
					title: 'error',
					text: '此帳號或密碼錯誤！',
					icon: 'error'
				});
			}
		}
	}
};
</script>

<style scoped>
@import '../assets/css/bi/login.css';
</style>
