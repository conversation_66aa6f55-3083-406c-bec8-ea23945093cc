<template>
	<dynamic-title></dynamic-title>
	<div>
		<div class="row">
			<div class="col-12">
				<div class="card card-form">
					<div class="card-header">
						<h4>查詢條件</h4>
						<span class="tx-square-bracket">為必填欄位</span>
					</div>
					<vue-form v-slot="{ errors }" ref="queryForm">
						<div class="card-body">
							<div class="row g-3 align-items-end">
								<div class="col-lg-2">
									<label class="form-label tx-require">使用人代碼</label> &nbsp;{{ user.userName || '' }}
									<vue-field
										type="text"
										name="userCode"
										rules="required"
										id="userCode"
										v-model="userCode"
										:class="{ 'is-invalid': errors.userCode }"
										@blur="isUserExists"
										class="form-control"
										label="使用人代碼"
									></vue-field>
									<div style="height: 25px">
										<span class="text-danger" v-show="errors.userCode">{{ errors.userCode }}</span>
									</div>
								</div>
								<div class="col-lg-5">
									<label class="form-label tx-require">查詢日期區間(起)</label>
									<div class="input-group">
										<span class="input-group-text">日期</span>
										<vue-field
											name="logStartDt"
											type="date"
											v-model="logStartDt"
											:class="{ 'is-invalid': errors.logStartDt }"
											class="form-control wd-30p-f"
											label="查詢日期區間(起)"
											rules="required"
										></vue-field>
										<span class="input-group-text">時間</span>
										<select name="startHour" id="startHour" class="form-select" v-model="logStartHour">
											<option value="00">00</option>
											<option value="01">01</option>
											<option value="02">02</option>
											<option value="03">03</option>
											<option value="04">04</option>
											<option value="05">05</option>
											<option value="06">06</option>
											<option value="07">07</option>
											<option value="08">08</option>
											<option value="09">09</option>
											<option value="10">10</option>
											<option value="11">11</option>
											<option value="12">12</option>
											<option value="13">13</option>
											<option value="14">14</option>
											<option value="15">15</option>
											<option value="16">16</option>
											<option value="17">17</option>
											<option value="18">18</option>
											<option value="19">19</option>
											<option value="20">20</option>
											<option value="21">21</option>
											<option value="22">22</option>
											<option value="23">23</option>
										</select>
										<select name="startMin" id="startMin" class="form-select" v-model="logStartMinute">
											<option value="00">00</option>
											<option value="15">15</option>
											<option value="30">30</option>
											<option value="45">45</option>
										</select>
									</div>
									<div style="height: 25px">
										<span class="text-danger" v-show="errors.logStartDt">{{ errors.logStartDt }}</span>
									</div>
								</div>

								<div class="col-lg-5">
									<label class="form-label tx-require">查詢日期區間(迄)</label>
									<div class="input-group">
										<span class="input-group-text">日期</span>
										<vue-field
											name="logEndDt"
											type="date"
											class="form-control wd-30p-f"
											:class="{ 'is-invalid': errors.logEndDt }"
											v-model="logEndDt"
											rules="required"
											label="查詢日期區間(迄)"
										></vue-field>
										<span class="input-group-text">時間</span>
										<select name="endHour" id="endHour" class="form-select" v-model="logEndHour">
											<option value="00">00</option>
											<option value="01">01</option>
											<option value="02">02</option>
											<option value="03">03</option>
											<option value="04">04</option>
											<option value="05">05</option>
											<option value="06">06</option>
											<option value="07">07</option>
											<option value="08">08</option>
											<option value="09">09</option>
											<option value="10">10</option>
											<option value="11">11</option>
											<option value="12">12</option>
											<option value="13">13</option>
											<option value="14">14</option>
											<option value="15">15</option>
											<option value="16">16</option>
											<option value="17">17</option>
											<option value="18">18</option>
											<option value="19">19</option>
											<option value="20">20</option>
											<option value="21">21</option>
											<option value="22">22</option>
											<option value="23">23</option>
										</select>
										<select name="endMin" id="endMin" class="form-select" v-model="logEndMinute">
											<option value="00">00</option>
											<option value="15">15</option>
											<option value="30">30</option>
											<option value="45">45</option>
										</select>
									</div>
									<div style="height: 25px">
										<span class="text-danger" v-show="errors.logEndDt">{{ errors.logEndDt }}</span>
									</div>
								</div>
								<div class="col-lg-10">
									<label class="form-label">功能模組</label>
									<div class="row g-3">
										<div class="col-lg-3 col-md-6">
											<div class="input-group">
												<span class="input-group-text">系統模組</span>
												<select name="moduleMenuCode" id="menuListId" class="form-select" v-model="moduleIndex">
													<option value="-1">全部</option>
													<option v-for="(module, mIndex) in moduleMenu" :value="mIndex">
														{{ module.menuName }}
													</option>
												</select>
											</div>
										</div>
										<div class="col-lg-3 col-md-6">
											<div class="input-group">
												<span class="input-group-text">功能項目</span>
												<select name="progCode" id="progListId" class="form-select" v-model="funcIndex">
													<option value="-1">全部</option>
													<option v-for="(funcInfo, fIndex) in functionMenu" :value="fIndex">
														{{ funcInfo.menuName }}
													</option>
												</select>
											</div>
										</div>
										<div class="col-lg-3 col-md-6">
											<div class="input-group">
												<span class="input-group-text">功能細項</span>
												<select name="progCode" id="progListId" class="form-select" v-model="funcDetailIndex">
													<option value="-1">全部</option>
													<option v-for="(funcDetail, fdIndex) in functionDetailMenu" :value="fdIndex">
														{{ funcDetail.menuName }}
													</option>
												</select>
											</div>
										</div>
										<div class="col-lg-3 col-md-6">
											<div class="input-group">
												<span class="input-group-text">頁籤</span>
												<select name="tablCode" id="tabListId" class="form-select" v-model="functionBookMarkMenuCode">
													<option value="">全部</option>
													<option v-for="funcBookMark in functionBookMarkMenu" :value="funcBookMark.menuCode">
														{{ funcBookMark.menuName }}
													</option>
												</select>
											</div>
										</div>
									</div>
								</div>
								<div class="col-lg-2">
									<button role="button" class="btn btn-primary btn-glow btn-searc=h" @click.prevent="gotoPage(0)">查詢</button>
								</div>
							</div>
						</div>
					</vue-form>
				</div>
				<div id="searchResult">
					<div class="card card-table">
						<div class="card-header">
							<h4>使用者使用明細紀錄列表</h4>
							<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
						</div>
						<div class="table-responsive">
							<table class="table table-RWD table-hover text-center">
								<thead>
									<tr>
										<th>日期</th>
										<th>時間</th>
										<th>系統模組</th>
										<th>功能項目</th>
										<th>功能細項</th>
										<th>頁籤</th>
										<th>分行(單位)名稱</th>
										<th>使用者</th>
										<th>IP</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="item in pageData.content">
										<td data-th="日期">{{ item.logDt }}</td>
										<td data-th="時間">{{ item.logTime }}</td>
										<td data-th="系統模組">{{ item.menuOne }}</td>
										<td data-th="功能項目">{{ item.menuTwo }}</td>
										<td data-th="功能細項">{{ item.menuThird }}</td>
										<td data-th="頁籤">{{ item.menuFour }}</td>
										<td data-th="單位名稱">{{ item.branCode }} {{ item.branName }}</td>
										<td data-th="使用者">{{ item.userCode }} {{ item.userName }}</td>
										<td data-th="IP">{{ item.remoteIp }}</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!--頁面內容 end-->
	</div>
</template>
<script>
import _ from 'lodash';
import Swal from 'sweetalert2';
import { Field, Form } from 'vee-validate';
import dynamicTitle from '@/views/components/dynamicTitle.vue';
import pagination from '@/views/components/pagination.vue';
import userCodeComplement from '../../../utils/mixin/userCodeComplement';

export default {
	components: {
		'vue-pagination': pagination,
		'vue-form': Form,
		'vue-field': Field,
		dynamicTitle
	},
	mixins: [userCodeComplement],
	data: function () {
		return {
			//API 用參數
			userCode: null,
			logStartDt: null,
			logStartHour: '00',
			logStartMinute: '00',
			logEndDt: null,
			logEndHour: '00',
			logEndMinute: '00',
			moduleMenuCode: null,
			functionMenuCode: null,
			functionDetailMenuCode: null,
			functionBookMarkMenuCode: '',

			//API邏輯判斷用參數
			moduleStrset: null,
			functioneStrset: null,
			functionDetailStrset: null,
			//畫面邏輯判斷用參數
			moduleIndex: -1,
			funcIndex: -1,
			funcDetailIndex: -1,

			//下拉選單
			moduleMenu: [],
			functionMenu: [],
			functionDetailMenu: [],
			functionBookMarkMenu: [],

			//主要顯示資料
			pageData: {},
			pageable: {
				page: 0,
				size: 20,
				sort: 'log_dt',
				direction: 'ASC'
			},

			// 判斷使用人代碼是否存在
			user: {}
		};
	},
	watch: {
		moduleIndex: function () {
			var self = this;
			if (self.moduleIndex == -1) {
				self.moduleMenuCode = null;
				self.moduleStrset = null;
				self.functionMenuCode = null;
				self.functioneStrset = null;
				self.functionDetailMenuCode = null;
				self.functionDetailStrset = null;
			} else {
				var module = self.moduleMenu[self.moduleIndex];
				self.moduleMenuCode = module.menuCode;
				self.moduleStrset = module.strset;
				self.funcIndex = -1;
				self.funcDetailIndex = -1;
			}
			self.functionBookMarkMenuCode = '';
			self.getFunctionMenu();
			self.getFunctionDetailMenu();
			self.getFunctionBookMarkMenu();
		},
		funcIndex: function () {
			var self = this;
			if (self.funcIndex == -1) {
				self.functionMenuCode = null;
				self.functioneStrset = null;
				self.functionDetailMenuCode = null;
				self.functionDetailStrset = null;
			} else {
				var func = self.functionMenu[self.funcIndex];
				self.functionMenuCode = func.menuCode;
				self.functioneStrset = func.strset;
				self.funcDetailIndex = -1;
			}
			self.functionBookMarkMenuCode = '';
			self.getFunctionDetailMenu();
			self.getFunctionBookMarkMenu();
		},
		funcDetailIndex: function () {
			var self = this;
			if (self.funcDetailIndex == -1) {
				self.functionDetailMenuCode = null;
				self.functionDetailStrset = null;
			} else {
				var funcDetail = self.functionDetailMenu[self.funcDetailIndex];
				self.functionDetailMenuCode = funcDetail.menuCode;
				self.functionDetailStrset = funcDetail.strset;
			}
			self.functionBookMarkMenuCode = '';
			self.getFunctionBookMarkMenu();
		}
	},
	mounted: function () {
		var self = this;
		self.getModuleMenu();
	},
	methods: {
		getModuleMenu: function () {
			var self = this;
			self.$api.getModuleMenuApi().then(function (ret) {
				self.moduleMenu = ret.data;
			});
		},
		getFunctionMenu: function () {
			var self = this;
			if (_.isBlank(self.moduleStrset)) {
				return;
			}

			self.$api
				.getUserFunctionMenuApi({
					depths: 2,
					strset: self.moduleStrset
				})
				.then(function (ret) {
					self.functionMenu = ret.data;
				});
		},
		getFunctionDetailMenu: function () {
			var self = this;
			if (_.isBlank(self.functioneStrset)) {
				return;
			}

			self.$api
				.getUserFunctionMenuApi({
					depths: 3,
					strset: self.functioneStrset
				})
				.then(function (ret) {
					self.functionDetailMenu = ret.data;
				});
		},
		getFunctionBookMarkMenu: function () {
			var self = this;
			if (_.isBlank(self.functionDetailStrset)) {
				return;
			}

			self.$api
				.getUserFunctionMenuApi({
					depths: 4,
					strset: self.functionDetailStrset
				})
				.then(function (ret) {
					self.functionBookMarkMenu = ret.data;
				});
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		isUserExists() {
			var self = this;
			self.userCode = self.complementUserCode(self.userCode);
			if (self.userCode && self.userCode.trim() !== '') {
				self.$api
					.getUserInfoApi({
						userCode: self.userCode,
						path: 'auditUsageDetail'
					})
					.then(function (ret) {
						if (ret.data) {
							self.user = ret.data;
						} else {
							self.user = {};
						}
					})
					.catch(function (ret) {
						self.user = {};
					});
			} else {
				self.user = {};
			}
		},
		getPageData: function (page) {
			var self = this;
			if (self.logStartDt > self.logEndDt) {
				Swal.fire({
					icon: 'error',
					text: '查詢日期區間(起)不可大於查詢日期區間(迄)。',
					showCloseButton: true,
					confirmButtonText: '確認',
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}

			var stime = new Date(self.logStartDt).getTime();
			var etime = new Date(self.logEndDt).getTime();
			var usedTime = etime - stime;
			var days = Math.floor(usedTime / (24 * 3600 * 1000));
			if (days > 7) {
				Swal.fire({
					icon: 'error',
					text: '查詢日期區間不可超過七天。',
					showCloseButton: true,
					confirmButtonText: '確認',
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}

			self.$refs.queryForm.validate().then(function (pass) {
				if (pass.valid) {
					var url = _.toPageUrl('', page, self.pageable);

					var logStartDt = self.logStartDt + ' ' + self.logStartHour + ':' + self.logStartMinute;
					var logEndDt = self.logEndDt + ' ' + self.logEndHour + ':' + self.logEndMinute;

					self.$api
						.getUserAccessLogsApi(
							{
								userCode: self.userCode,
								logStartDt: logStartDt,
								logEndDt: logEndDt,
								m1MenuCode: self.moduleMenuCode,
								m2MenuCode: self.functionMenuCode,
								m3MenuCode: self.functionDetailMenuCode,
								m4MenuCode: self.functionBookMarkMenuCode
							},
							url
						)
						.then(function (ret) {
							self.pageData = ret.data;
						});
				}
			});
		}
	}
};
</script>
