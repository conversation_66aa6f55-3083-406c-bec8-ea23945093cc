<template>
	<!-- 線圖 -->
	<div :id="chartId" style="height: 500px"></div>
</template>
<script>
import * as am5 from '@amcharts/amcharts5';
import * as am5xy from '@amcharts/amcharts5/xy';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import { toRaw } from 'vue';
export default {
	props: {
		chartId: String,
		propChartData: Array,
		propSelectedTech: Object
	},
	data: function () {
		return {
			am5Obj: {}
		};
	},
	watch: {
		propSelectedTech: {
			handler: function (newVal, oldVal) {
				let self = this;
				this.initChart();
			},
			deep: true
		}
	},
	mounted: function () {
		this.$nextTick(function () {
			this.initChart();
		});
	},
	beforeDestroy: function () {
		this.destroyChart();
	},
	methods: {
		initChart() {
			let self = this;
			let { am5Obj } = self;
			let firstLoad = false;
			//透過 toRaw 解決因為 vue data proxy 導致跟 amcharts 衝突
			let { xAxis, yAxis, root, chart, xRenderer, legend } = toRaw(am5Obj);

			if (!root) {
				firstLoad = true;
				root = am5.Root.new(self.chartId);
				root._logo.dispose();

				// Set themes
				// https://www.amcharts.com/docs/v5/concepts/themes/
				root.setThemes([am5themes_Animated.new(root)]);

				// Create chart
				// https://www.amcharts.com/docs/v5/charts/xy-chart/
				chart = root.container.children.push(
					am5xy.XYChart.new(root, {
						panX: false,
						panY: false,
						paddingLeft: 0,
						wheelX: 'none',
						wheelY: 'none',
						layout: root.verticalLayout
					})
				);

				// Add legend
				// https://www.amcharts.com/docs/v5/charts/xy-chart/legend-xy-series/
				legend = chart.children.push(
					am5.Legend.new(root, {
						centerX: am5.p50,
						x: am5.p50
					})
				);

				// Create axes
				// https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
				xRenderer = am5xy.AxisRendererX.new(root, {
					cellStartLocation: 0.1,
					cellEndLocation: 0.9,
					minorGridEnabled: true
				});

				xAxis = chart.xAxes.push(
					am5xy.CategoryAxis.new(root, {
						categoryField: 'year',
						renderer: xRenderer,
						tooltip: am5.Tooltip.new(root, {})
					})
				);

				xRenderer.grid.template.setAll({
					location: 1
				});

				xAxis.data.setAll(self.propChartData);

				yAxis = chart.yAxes.push(
					am5xy.ValueAxis.new(root, {
						renderer: am5xy.AxisRendererY.new(root, {
							strokeOpacity: 0.1
						})
					})
				);

				yAxis.children.moveValue(
					am5.Label.new(root, {
						text: self.propSelectedTech.name,
						rotation: -90,
						y: am5.p50,
						centerX: am5.p50
					}),
					0
				);

				// Add cursor
				chart.set(
					'cursor',
					am5xy.XYCursor.new(root, {
						xAxis: xAxis
					})
				);

				chart.get('colors').set('colors', [am5.color(0x19ad79), am5.color(0x007cc1), am5.color(0xe8a123)]);
			} else {
				chart.series.clear();
				// x軸小方塊 清掉重置
				legend.data.clear();
			}

			// y軸 換名字
			yAxis.children.removeIndex(0);
			yAxis.children.moveValue(
				am5.Label.new(root, {
					text: self.propSelectedTech.name,
					rotation: -90,
					y: am5.p50,
					centerX: am5.p50
				}),
				0
			);

			// Add series
			// https://www.amcharts.com/docs/v5/charts/xy-chart/series/
			function makeSeries(name, fieldName, tooltipText) {
				var series = chart.series.push(
					am5xy.ColumnSeries.new(root, {
						name: name,
						xAxis: xAxis,
						yAxis: yAxis,
						valueYField: fieldName,
						categoryXField: 'year'
					})
				);

				// 動態tooltipText
				series.columns.template.setAll({
					tooltipText: tooltipText,
					width: am5.percent(90),
					tooltipY: 0,
					strokeOpacity: 0
				});
				series.data.setAll(self.propChartData);

				if (firstLoad) {
					// Make stuff animate on load
					// https://www.amcharts.com/docs/v5/concepts/animations/
					series.appear();
				}

				series.bullets.push(function () {
					return am5.Bullet.new(root, {
						locationY: 0,
						sprite: am5.Label.new(root, {
							text: '{valueY}',
							fill: root.interfaceColors.get('alternativeText'),
							centerY: 0,
							centerX: am5.p50,
							populateText: true
						})
					});
				});

				legend.data.push(series);
			}

			self.propSelectedTech.valueList.forEach((element) => {
				makeSeries(element.name, element.value, self.propSelectedTech.tooltipText);
			});

			if (firstLoad) {
				Object.assign(am5Obj, { xAxis, yAxis, root, chart, xRenderer, legend });
				// Make stuff animate on load
				// https://www.amcharts.com/docs/v5/concepts/animations/
				chart.appear(1000, 100);
			}
		},
		destroyChart: function () {
			let { am5Obj } = this;
			let { root } = Vue.toRaw(am5Obj);
			if (root) {
				root.dispose();
			}
		}
	} // methods end
};
</script>
