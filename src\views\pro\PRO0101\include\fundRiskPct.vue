<template>
	<div class="col-sm-8 col-12">
		<h4>
			風險報酬觀測圖 <small class="small-text">資料更新日： {{ $filters.defaultValue($filters.formatDate(latestDate), '--') }}</small>
		</h4>
		<table width="100%" class="table table-bordered">
			<tbody>
				<tr>
					<th>名稱</th>
					<td colspan="3">{{ pointFund.fundName }}</td>
				</tr>
				<tr>
					<th width="20%">年化累積報酬率</th>
					<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(pointFund.acr, '%'), '--')"></p></td>
					<th width="20%">年化標準差</th>
					<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(pointFund.astd, '%'), '--')"></p></td>
				</tr>
				<tr>
					<th>幣別期間</th>
					<td colspan="3">
						<div class="row">
							<div class="col-4">
								<select class="form-select selectPicker w-100" v-model="searchData.techCurrencyCode" @change="get2x2Perfs">
									<option value="TWD">台幣</option>
									<option value="USD">美金</option>
								</select>
							</div>
							<div class="col-4 p-l-5">
								<select class="form-select selectPicker w-100" v-model="searchData.dataLength2x2" @change="get2x2Perfs">
									<option value="DATA_LENGTH_1Y">1年</option>
									<option value="DATA_LENGTH_3Y">3年</option>
									<option value="DATA_LENGTH_5Y">5年</option>
								</select>
							</div>
						</div>
					</td>
				</tr>
			</tbody>
		</table>
		<div class="row">
			<div class="col-12 text-center">
				<div class="text-center" v-if="fund2x2Perfs.length === 1">此檔基金所屬的分類市場只有該檔基金，故無特定象限位置</div>

				<!-- 雷達圖 -->
				<vue-fund-bubble-chart
					v-if="pointFund"
					ref="riskBubbleChartRef"
					:chart-id="riskBubbleChartId"
					:prop-chart-data="chartData"
					:fund-name="pointFund.fundName"
					style="height: 500px"
				></vue-fund-bubble-chart>

				<small>此圖顯示與此基金相同之Lipper global分類其他基金</small>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import vueFundBubbleChart from './fundBubbleChart.vue';
export default {
	components: {
		vueFundBubbleChart
	},
	props: {
		fundCode: String,
		globalClassCode: String
	},
	data: function () {
		return {
			searchData: {
				techCurrencyCode: 'TWD',
				dataLength2x2: 'DATA_LENGTH_1Y',
				globalClassCode: null
			},
			fund2x2Perfs: [],
			riskBubbleChartId: 'riskBubbleChartId'
		};
	},
	watch: {
		globalClassCode: {
			handler: function (newVal, oldVal) {
				this.searchData.globalClassCode = newVal;
			}
		}
	},
	computed: {
		latestDate: function () {
			if (this.fund2x2Perfs.length > 0) {
				return _.orderBy(
					_.filter(this.fund2x2Perfs, function (item) {
						return item.dataDate != null;
					}),
					['dataDate'],
					['desc']
				)[0].dataDate;
			} else {
				return null;
			}
		},
		chartData: function () {
			var self = this;
			if (self.fund2x2Perfs.length > 0) {
				return _.map(self.fund2x2Perfs, function (item) {
					if (item.fundCode === self.fundCode) {
						item.x = item.astd;
						item.y = item.acr;
					}
					return item;
				});
			}
			return null;
		},
		pointFund: function () {
			var fund2x2Perf = _.find(this.fund2x2Perfs, { fundCode: this.fundCode });
			return fund2x2Perf ? fund2x2Perf : {};
		}
	},
	mounted: function () {},
	methods: {
		get2x2Perfs: async function () {
			var self = this;
			self.searchData.globalClassCode = self.globalClassCode;
			const ret = await get2x2PerfsApi({
				searchData: self.searchData
			});
			self.fund2x2Perfs = ret.data;
			setTimeout(function () {
				self.$refs.riskBubbleChartRef.initChart();
			}, 1000);
		}
	}
};
</script>
