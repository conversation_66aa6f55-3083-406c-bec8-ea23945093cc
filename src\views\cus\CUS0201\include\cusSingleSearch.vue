<template>
	<div class="card card-form card-collapse mb-0">
		<div class="card-header">
			<h4>基本條件查詢<span class="tx-square-bracket">為必填欄位</span></h4>
			<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1"></div>
		</div>
		<div class="card-body">
			<form>
				<div class="row g-3 align-items-end">
					<div class="col-md-6">
						<vue-form v-slot="{ errors }" ref="cusName">
							<label class="form-label">客戶姓名</label>
							<div class="input-group">
								<vue-field
									name="cusName"
									:class="{ 'is-invalid': errors.cusName }"
									class="form-control"
									id="cusName"
									type="text"
									v-model="cusName"
									rules="required"
									label="客戶姓名"
								>
								</vue-field>
								<button class="btn btn-primary btn-glow" type="button" @click.prevent="queryByCusName()">
									<i class="bi bi-search"></i>
								</button>
							</div>
							<div style="height: 25px">
								<span class="text-danger" v-show="errors.cusName">{{ errors.cusName }}</span>
							</div>
						</vue-form>
					</div>
					<div class="col-md-6">
						<vue-form v-slot="{ errors }" ref="cusCode">
							<label class="form-label">客戶ID/統編</label>
							<div class="input-group">
								<vue-field
									name="cusCode"
									class="form-control JQ-uCase"
									id="cusCode"
									:class="{ 'is-invalid': errors.cusCode }"
									type="text"
									v-model="cusCode"
									rules="required"
									label="客戶ID/統編"
								></vue-field>
								<button class="btn btn-primary btn-glow" type="button" data-rel="byIdn" @click.prevent="queryByIdn()">
									<i class="bi bi-search"></i>
								</button>
							</div>
							<div style="height: 25px">
								<span class="text-danger" v-show="errors.cusCode">{{ errors.cusCode }}</span>
							</div>
						</vue-form>
					</div>
					<div class="col-md-6">
						<vue-form v-slot="{ errors }" ref="graCode">
							<label class="form-label">客戶資產等級</label>
							<div class="input-group">
								<vue-field
									as="select"
									name="graCode"
									class="form-select JQ-uCase"
									id="graCode"
									:class="{ 'is-invalid': errors.graCode }"
									v-model="graCode"
									rules="required"
									label="客戶資產等級"
								>
									<option :value="''">請選擇</option>
									<option v-for="(item, index) in cusGrades" :value="item.graCode" :key="index">{{ item.graName }}</option>
								</vue-field>
								<button class="btn btn-primary btn-glow" type="button" data-rel="byGraCode" @click.prevent="queryByGraCode()">
									<i class="bi bi-search"></i>
								</button>
							</div>
							<div style="height: 25px">
								<span class="text-danger" v-show="errors.graCode">{{ errors.graCode }}</span>
							</div>
						</vue-form>
					</div>
					<div class="col-md-6">
						<div class="d-flex align-items-end">
							<vue-user-condition
								:col-width="9"
								@change-area="areaCode = $event"
								class="m-0 me-2 flex-grow-1"
								@change-bran="branCode = $event"
								@change-user="userCode = $event"
							>
							</vue-user-condition>
							<button class="btn btn-primary btn-glow" type="button" data-rel="byBranCode" @click.prevent="queryByBranCode()">
								<i class="bi bi-search"></i>
							</button>
						</div>
						<div style="height: 25px">
							<span class="text-danger"></span>
						</div>
					</div>
					<div class="col-md-6">
						<vue-form v-slot="{ errors }" ref="cellPhone">
							<label class="form-label">手機</label>
							<div class="input-group">
								<vue-field
									name="cellPhone"
									class="form-control"
									id="cellPhone"
									type="text"
									:class="{ 'is-invalid': errors.cellPhone }"
									size="30"
									v-model="cellPhone"
									rules="required"
									label="手機"
								></vue-field>
								<button class="btn btn-primary btn-glow" type="button" @click.prevent="queryByMobile()">
									<i class="bi bi-search"></i>
								</button>
							</div>
							<div style="height: 25px">
								<span class="text-danger" v-show="errors.cellPhone">{{ errors.cellPhone }}</span>
							</div>
						</vue-form>
					</div>
					<div class="col-md-6">
						<vue-form v-slot="{ errors }" ref="contactPhone">
							<label class="form-label">市內電話</label>
							<div class="input-group">
								<vue-field
									name="contactPhone"
									class="form-control"
									id="homePhone"
									:class="{ 'is-invalid': errors.contactPhone }"
									type="text"
									size="30"
									v-model="contactPhone"
									rules="required"
									label="市內電話"
								></vue-field>
								<button class="btn btn-primary btn-glow" type="button" data-rel="byHomePhone" @click.prevent="queryByPhone()">
									<i class="bi bi-search"></i>
								</button>
							</div>
							<div style="height: 25px">
								<span class="text-danger" v-show="errors.contactPhone">{{ errors.contactPhone }}</span>
							</div>
						</vue-form>
					</div>
					<div class="col-md-6">
						<vue-form v-slot="{ errors }" ref="email">
							<label class="form-label">E-mail</label>
							<div class="input-group">
								<vue-field
									name="email"
									class="form-control"
									id="email"
									:class="{ 'is-invalid': errors.email }"
									type="text"
									size="50"
									v-model="email"
									rules="required"
									label="E-mail"
								></vue-field>
								<button class="btn btn-primary btn-glow" type="button" data-rel="byEmail" @click.prevent="queryByEmail()">
									<i class="bi bi-search"></i>
								</button>
							</div>
							<div style="height: 25px">
								<span class="text-danger" v-show="errors.email">{{ errors.email }}</span>
							</div>
						</vue-form>
					</div>
					<div class="col-md-6">
						<vue-form v-slot="{ errors }" ref="contactAddress">
							<label class="form-label">通訊地址</label>
							<div class="input-group">
								<vue-field
									name="contactAddress"
									class="form-control"
									id="address"
									type="text"
									:class="{ 'is-invalid': errors.contactAddress }"
									size="30"
									v-model="contactAddress"
									rules="required"
									label="通訊地址"
								></vue-field>
								<button class="btn btn-primary btn-glow" type="button" data-rel="byAddress" @click.prevent="queryByAddr()">
									<i class="bi bi-search"></i>
								</button>
							</div>
							<div style="height: 25px">
								<span class="text-danger" v-show="errors.contactAddress">{{ errors.contactAddress }}</span>
							</div>
						</vue-form>
					</div>
				</div>
			</form>
		</div>
	</div>

	<div class="tx-note mb-4">
		<ol>
			<li>顧客姓名為模糊搜尋。</li>
			<li>顧客資料日為T-1，若為當日新增的顧客，於T-1營業日才可查到。</li>
		</ol>
	</div>
</template>
<script>
import { Field, Form } from 'vee-validate';
import vueUserCondition from '@/views/components/userCondition.vue';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueUserCondition
	},
	props: {
		queryReq: Object,
		gotoPage: Function
	},
	data: function () {
		return {
			//查詢資料
			cusName: null,
			cusCode: null,
			graCode: '',
			smeYn: '',
			contactPhone: null,
			cellPhone: null,
			email: null,
			contactAddress: null,
			areaCode: '',
			userCode: '',
			branCode: '',
			account: null,

			//下拉選單
			selAreaList: [],
			selBranList: [],
			selUserList: [],
			cusGrades: []
		};
	},
	mounted: function () {
		var self = this;
		self.getCusGrades();
	},
	computed: {
		userInfo: function () {
			return this.$store.getters['userInfo/info'];
		}
	},
	methods: {
		getCusGrades: async function () {
			const self = this;
			const ret = await self.$api.getCusGradesApi();
			self.cusGrades = ret.data;
		},
		clearQueryReq: function () {
			var self = this;

			for (var key in self.queryReq) {
				delete self.queryReq[key];
			}
		},
		queryByCusName: function () {
			var self = this;
			self.$refs.cusName.validate().then(function (pass) {
				if (pass.valid) {
					self.clearQueryReq();
					self.queryReq.queryType = 'CUS_NAME';
					self.queryReq.cusName = self.cusName;
					self.gotoPage(0);
				}
			});
		},
		queryByIdn: function () {
			var self = this;
			self.$refs.cusCode.validate().then(function (pass) {
				if (pass.valid) {
					self.clearQueryReq();
					self.queryReq.queryType = 'IDN';
					self.queryReq.cusCode = self.cusCode;
					self.gotoPage(0);
				}
			});
		},
		queryByEmail: function () {
			var self = this;
			self.$refs.email.validate().then(function (pass) {
				if (pass.valid) {
					self.clearQueryReq();
					self.queryReq.queryType = 'EMAIL';
					self.queryReq.email = self.email;
					self.gotoPage(0);
				}
			});
		},
		queryByPhone: function () {
			var self = this;
			self.$refs.contactPhone.validate().then(function (pass) {
				if (pass.valid) {
					self.clearQueryReq();
					self.queryReq.queryType = 'CONTACT_PHONE';
					self.queryReq.contactPhone = self.contactPhone;
					self.gotoPage(0);
				}
			});
		},
		queryByMobile: function () {
			var self = this;
			self.$refs.cellPhone.validate().then(function (pass) {
				if (pass.valid) {
					self.clearQueryReq();
					self.queryReq.queryType = 'CELL_PHONE';
					self.queryReq.cellPhone = self.cellPhone;
					self.gotoPage(0);
				}
			});
		},
		queryByAddr: function () {
			var self = this;
			self.$refs.contactAddress.validate().then(function (pass) {
				if (pass.valid) {
					self.clearQueryReq();
					self.queryReq.queryType = 'CONTACT_ADDRESS';
					self.queryReq.contactAddress = self.contactAddress;
					self.gotoPage(0);
				}
			});
		},
		queryByBranCode: function () {
			this.clearQueryReq();
			this.queryReq.queryType = 'BRAN_CODE';
			this.queryReq.areaCode = this.areaCode;
			this.queryReq.userCode = this.userCode;
			this.queryReq.branCode = this.branCode;
			this.gotoPage(0);
		},
		queryByAccount: function () {
			var self = this;
			self.$refs.account.validate().then(function (pass) {
				if (pass.valid) {
					self.clearQueryReq();
					self.queryReq.queryType = 'ACCOUNT';
					self.queryReq.account = self.account;
					self.gotoPage(0);
				}
			});
		},
		queryByGraCode: function () {
			const self = this;
			self.$refs.graCode.validate().then(function (pass) {
				if (pass.valid) {
					self.clearQueryReq();
					self.queryReq.queryType = 'GRA_CODE';
					self.queryReq.graCode = self.graCode;
					self.gotoPage(0);
				}
			});
		}
	}
};
</script>
