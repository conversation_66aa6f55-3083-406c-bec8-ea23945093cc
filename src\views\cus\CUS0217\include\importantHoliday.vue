<template>
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<div class="card card-form">
				<div class="card-header">
					<h4>重要節日設定</h4>
					<span class="tx-square-bracket">為必填欄位</span>
				</div>
				<div class="card-body">
					<vue-form v-slot="{ errors }" ref="importantHoliday">
						<div class="form-row">
							<div class="form-group col-12 col-lg-4">
								<label class="form-label tx-require">日期</label>
								<div style="width: 100%; min-width: 0">
									<vue-field
										type="date"
										name="memoryDate"
										size="13"
										value=""
										class="form-control"
										maxlength="10"
										v-model="memoryDate"
										:class="{ 'is-invalid': errors.memoryDate }"
										rules="required"
										label="日期"
									></vue-field>
									<div class="text-danger" v-show="errors.memoryDate">{{ errors.memoryDate }}</div>
								</div>
							</div>
							<div class="form-group col-12 col-lg-8">
								<label class="form-label tx-require">說明</label>
								<div style="width: 100%; min-width: 0">
									<vue-field
										name="note"
										type="text"
										class="form-control"
										size="30"
										v-model="note"
										rules="required"
										label="說明"
										:class="{ 'is-invalid': errors.note }"
									></vue-field>
									<div class="text-danger" v-show="errors.note">{{ errors.note }}</div>
								</div>
							</div>
							<div class="form-group col-12 col-lg-auto">
								<label class="form-label tx-require">啟動提醒</label>
								<div style="width: 100%">
									<div class="form-check-group JQdata-hide">
										<div class="form-check form-check-inline">
											<vue-field
												type="radio"
												name="remindYn"
												class="form-check-input"
												id="b01_1"
												value="Y"
												v-model="remindYn"
												rules="required"
												label="啟動提醒"
											></vue-field>
											<label for="b01_1" class="form-check-label">是</label>
										</div>
										<div class="form-check form-check-inline">
											<vue-field
												type="radio"
												name="remindYn"
												class="form-check-input"
												id="b01_2"
												value="N"
												v-model="remindYn"
												rules="required"
												label="啟動提醒"
											></vue-field>
											<label for="b01_2" class="form-check-label">否</label>
										</div>
									</div>
									<div class="text-danger" style="height: 3px" v-show="errors.remindYn">{{ errors.remindYn }}</div>
								</div>
							</div>
							<div class="form-group col-12 col-lg-auto">
								<label class="form-label" style="min-width: 160px !important">到期日前幾個工作日提醒</label>
								<div style="width: 100%">
									<vue-field
										name="remindDays"
										type="number"
										class="form-control"
										:rules="{ remindDays: true, integer: true, min_value: 0 }"
										size="5"
										v-model="remindDays"
										label="工作日"
									></vue-field>
									<div class="text-danger" v-show="errors.remindDays && remindYn === 'Y'">{{ errors.remindDays }}</div>
								</div>
							</div>
						</div>
						<div class="form-footer">
							<div id="button1" v-if="id == null">
								<button class="btn btn-primary" type="button" @click="insertMemoryDates()">儲存</button>
							</div>
							<div id="button2" v-if="id">
								<input class="btn btn-primary" type="button" value="修改" @click="updateMemoryDates()" />
								<input class="btn btn-primary" type="button" value="取消修改" name="cancel" @click="cancel()" />
							</div>
						</div>
					</vue-form>
				</div>
			</div>
			<div class="card card-table">
				<div class="card-header">
					<h4>重要節日列表</h4>
				</div>
				<div class="table-responsive">
					<table class="table table-RWD text-center">
						<thead>
							<tr>
								<th>日期</th>
								<th class="text-start">說明</th>
								<th>啟動提醒</th>
								<th>到期日前幾個日曆日提醒</th>
								<th class="text-end">執行</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="memoryDate in memoryDates">
								<td>{{ memoryDate.memoDate }}</td>
								<td class="text-start">{{ memoryDate.memoNote }}</td>
								<td><span v-if="memoryDate.remindYn == 'Y'">是</span> <span v-if="memoryDate.remindYn == 'N'">否</span></td>
								<td>{{ memoryDate.remindDays }}</td>
								<td class="text-end">
									<button
										v-if="showButton"
										type="button"
										class="btn btn-info btn-glow btn-icon"
										data-bs-toggle="tooltip"
										title="編輯"
										id="edit"
										@click="doUpdate(memoryDate)"
									>
										<i class="bi bi-pen"></i>
									</button>
									<button
										v-if="showButton"
										type="button"
										class="btn btn-danger btn-glow btn-icon"
										data-bs-toggle="tooltip"
										title="刪除"
										@click="deleteMemoryDate(memoryDate.id)"
									>
										<i class="bi bi-trash"></i>
									</button>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { Field, Form, defineRule } from 'vee-validate';
export default {
	props: {
		cusCode: null
	},
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	data: function () {
		return {
			id: null,
			memoryDate: null,
			note: null,
			remindYn: null,
			remindDays: null,
			authYn: null,
			memoryDates: []
		};
	},
	computed: {
		showButton: function () {
			return this.authYn === 'Y';
		}
	},
	created: function () {
		var self = this;
		defineRule('remindDays', function () {
			if (self.remindYn === 'Y' && self.remindDays == null) {
				return '需填寫';
			} else {
				return true;
			}
		});
	},
	mounted: function () {
		var self = this;
		self.chkCustomerAuth();
		self.getMemoryDates();
	},
	methods: {
		chkCustomerAuth: async function () {
			var self = this;
			const resp = await self.$api.chkCustomerAuthApi({
				cusCode: self.cusCode,
				progCode: 'ACUS_005'
			});
			self.authYn = resp.data.authYn;
		},
		getMemoryDates: async function () {
			var self = this;
			const ret = await self.$api.getMemoryDateApi({
				cusCode: self.cusCode
			});
			self.memoryDates = ret.data;
		},
		doUpdate: function (memoryDate) {
			var self = this;
			self.id = memoryDate.id;
			self.remindYn = memoryDate.remindYn;
			self.remindDays = memoryDate.remindDays;
			self.note = memoryDate.memoNote;

			var dateYy = memoryDate.memoDate.slice(0, 4);
			var dateMm = memoryDate.memoDate.slice(5, 7);
			var dateDd = memoryDate.memoDate.slice(8, 10);
			self.memoryDate = dateYy + '-' + dateMm + '-' + dateDd;
		},
		insertMemoryDates: async function () {
			var self = this;
			var importantHoliday = self.$refs.importantHoliday;

			importantHoliday.validate().then(async function (pass) {
				if (pass.valid) {
					const ret = await self.$api.postMemoryDateApi({
						cusCode: self.cusCode,
						dateDt: self.memoryDate,
						remindYn: self.remindYn,
						remindDays: self.remindDays,
						note: self.note
					});
					self.$bi.alert('新增成功');
					self.getMemoryDates();
				}
			});
		},
		updateMemoryDates: async function () {
			var self = this;
			self.$refs.importantHoliday.validate().then(async function (pass) {
				if (pass.valid) {
					const ret = await self.$api.updateMemoryDateApi({
						id: self.id,
						cusCode: self.cusCode,
						dateDt: self.memoryDate,
						remindYn: self.remindYn,
						remindDays: self.remindDays,
						note: self.note
					});
					self.$bi.alert('更新成功');
					self.getMemoryDates();
				}
			});
		},
		deleteMemoryDate: async function (id) {
			var self = this;
			const ret = await self.$api.deleteMemoryDateApi({ id: id });
			self.$bi.alert('刪除成功');
			self.getMemoryDates();
			if (self.id == id) {
				self.id = null;
				self.cancel();
			}
		},
		cancel: function () {
			var self = this;
			self.id = null;
			self.note = null;
			self.memoryDate = null;
			self.remindYn = null;
			self.remindDays = null;
			self.$refs.importantHoliday.resetForm();
		}
	}
};
</script>
