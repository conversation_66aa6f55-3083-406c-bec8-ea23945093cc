<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form mb-3">
			<div class="card-header">
				<h4>分行分區設定</h4>
				<span class="tx-square-bracket">為必填欄位</span>
			</div>
			<div class="card-body">
				<div class="row g-3 align-items-end">
					<table class="table table-RWD table-horizontal-RWD table-bordered">
						<tbody>
							<tr>
								<th class="tx-require">上傳檔案</th>
								<td>
									<div class="input-group">
										<form>
											<input name="file" type="file" class="form-control" ref="fileInput" @change="addFile" />
										</form>
										<span class="input-group-text">(請確定上傳的檔案是CSV檔)</span>
										<a class="link-underline" th:href="@{/resources/adm/ADM0108/分行分區設定名單範例檔.csv}">範例文件</a>
									</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<div style="display: flex; align-items: center; justify-content: space-between">
			<div class="tx-note" style="margin-top: 0; margin-left: 0px">若分行分區上傳後，沒有點選「完成」即離開本功能，則名單資料將不會被保留</div>
			<div class="text-end">
				<button class="btn btn-primary" type="button" @click="postFcBranMapChk">上傳</button>
			</div>
		</div>
		<div class="card card-table" v-if="uploadResult.length > 0">
			<div class="card-header">
				<h4>名單上傳結果清單</h4>
			</div>
			<div class="table-responsive">
				<table class="table table-RWD table-hover table-bordered text-center">
					<thead>
						<tr>
							<th>檔案名稱</th>
							<th>上傳結果</th>
							<th>上傳筆數</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="item in uploadResult">
							<td data-th="檔案名稱">{{ item.fileName }}</td>
							<td data-th="上傳結果">
								<div v-for="resp in item.groupBranMapChkResp">
									{{ resp.statusMsg }}
								</div>
							</td>
							<td data-th="上傳筆數">{{ item.totalCount }}</td>
						</tr>
					</tbody>
				</table>
			</div>
			<div class="text-end mt-3">
				<button class="btn btn-primary btn-glow" @click="postGroupBranMap()" v-if="isSendBtn">完成</button>
			</div>
		</div>
	</div>
</template>
<script>
import { Form, Field } from 'vee-validate';
import _ from 'lodash';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	data: function () {
		return {
			uploadResult: [], //上傳名單list
			fileObject: null,
			validExts: ['csv', 'xlsx'],
			symbols: ['/', ':', '*', '?', '"', '<', '>', '|'],
			isSendBtn: false
		};
	},
	mounted: function () {},
	methods: {
		postFcBranMapChk: function () {
			var self = this;

			if (self.fileObject == '' || self.fileObject == null) {
				self.$bi.alert('請選擇上傳檔案');
				return;
			}
			if (self.fileObject.type !== 'text/csv' && !self.fileObject.name.endsWith('.csv')) {
				self.$bi.alert('此檔案非.csv檔案，請重新選擇');
				return;
			}

			var formData = new FormData();
			formData.append('fileObject', self.fileObject);

			self.$api.postGroupBranMapChkApi(formData).then(function (ret) {
				if (ret.data) {
					self.uploadResult.push({
						fileName: self.fileObject.name,
						groupBranMapChkResp: ret.data,
						totalCount: ret.data[0].totalCount
					});
					self.isSendBtn = !_.some(ret.data, (bean) => bean.statusCode === 'F');
					self.fileObject = null;
					self.$refs.fileInput.value = ''; // 清除文件選擇
				}
			});
		},
		addFile: function (e) {
			var self = this;
			var file = e.target.files[0];

			self.fileObject = file;
		},
		postGroupBranMap: function () {
			var self = this;
			self.$api.postBranPageData({}).then(function (ret) {
				self.$bi.alert('儲存成功');
				setTimeout(function () {
					window.location.reload();
				}, 2000);
			});
		}
	}
};
</script>
