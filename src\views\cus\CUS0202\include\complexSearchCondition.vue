<template>
	<ul class="list-group list-inline-tags mb-3" id="job">
		<li class="label">查詢條件：</li>
		<li class="list-group-item" v-show="cond.graName != null">
			<a href="#">
				<span><strong>客戶等級 : </strong> {{ cond.graName }}</span>
				<span class="img-delete JQ-delet" @click="deleteGra" title="" data-bs-original-title="刪除"></span>
			</a>
		</li>
		<li class="list-group-item" v-show="cond.area != null || cond.ca != null || cond.branCode != null">
			<a href="#">
				<span>
					<strong>組織 (區/分行/理專) : </strong>
					<span v-if="cond.area != null"><strong>區</strong> : {{ cond.area }} </span>
					<span v-if="cond.bran != null"><strong> 分行</strong> : {{ cond.bran }} </span>
					<span v-if="cond.user != null"><strong> 理專</strong> : {{ cond.user }} </span>
				</span>
				<span class="img-delete JQ-delet" @click="deleteBran" title="" data-bs-original-title="刪除"></span>
			</a>
		</li>
		<li class="list-group-item" v-show="cond.birth != null">
			<a href="#">
				<span><strong>客戶生日 (月/日) [限自然人] : </strong> {{ cond.birth }}</span>
				<span class="img-delete JQ-delet" @click="deleteBirth" title="" data-bs-original-title="刪除"></span>
			</a>
		</li>
		<li class="list-group-item" v-show="cond.rank != null">
			<a href="#">
				<span><strong>投資屬性 : </strong> {{ cond.rank }}</span>
				<span class="img-delete JQ-delet" @click="deleteRank" title="" data-bs-original-title="刪除"></span>
			</a>
		</li>
		<li class="list-group-item" v-show="req.aumAmountS != null">
			<a href="#">
				<span
					><strong>本行總資產(AUM) : 金額介於</strong>: {{ $filters.formatNumber(req.aumAmountS) }}~{{
						$filters.formatNumber(req.aumAmountE)
					}}
					元</span
				>
				<span class="img-delete JQ-delet" @click="deleteAum" title="" data-bs-original-title="刪除"></span>
			</a>
		</li>
		<li class="list-group-item" v-show="req.contbAmountS != null">
			<a href="#">
				<span>
					<strong>本行貢獻度 : </strong>
					<strong> 貢獻度介於</strong>: {{ $filters.formatNumber(req.contbAmountS) }}~{{ $filters.formatNumber(req.contbAmountE) }}元
					<span v-if="req.contbTime === 'DEF'"><strong> 時間區間</strong>: {{ cond.contbTimeS }}~{{ cond.contbTimeE }}</span>
					<span v-else><strong> 時間區間</strong> : {{ cond.contbTime }}</span>
				</span>
				<span class="img-delete JQ-delet" @click="deleteContb" title="" data-bs-original-title="刪除"></span>
			</a>
		</li>
		<li class="list-group-item" v-show="cond.savingType != null || cond.savingC != null || req.savingAmountS != null">
			<a href="#">
				<span>
					<strong>存款商品 : </strong>
					<span v-if="cond.savingType != null"><strong>存款種類</strong> : {{ cond.savingType }} </span>
					<span v-if="cond.savingC != null"><strong> 幣別</strong> : {{ cond.savingC }} </span>
					<span v-if="req.savingAmountS != null"
						><strong> 帳戶餘額介於</strong> : {{ $filters.formatNumber(req.savingAmountS) }}~{{
							$filters.formatNumber(req.savingAmountE)
						}}
					</span>
				</span>
				<span class="img-delete JQ-delet" @click="deleteSaving" title="" data-bs-original-title="刪除"></span>
			</a>
		</li>
		<li
			class="list-group-item"
			v-show="
				req.proCatList.length > 0 ||
				req.proC != null ||
				req.intType != null ||
				req.proCode != null ||
				req.mktAmountS != null ||
				req.invAmountS != null ||
				req.returnAmountS != null
			"
		>
			<a href="#">
				<span>
					<strong>投資商品 : </strong>
					<span v-if="req.proCatList.length > 0"><strong>商品主類</strong> : {{ cond.proCatList }} </span>
					<span v-if="req.proCode != null"><strong> 依商品代碼</strong> : {{ req.proCode }} </span>
					<span v-if="req.proC != null"><strong> 幣別</strong> : {{ cond.proC }} </span>
					<span v-if="req.intType != null"><strong> 配息狀況</strong> : {{ cond.intType }} </span>
					<span v-if="req.mktAmountS != null"
						><strong> 市值餘額</strong> : {{ $filters.formatNumber(req.mktAmountS) }}~{{ $filters.formatNumber(req.mktAmountE) }}
					</span>
					<span v-if="req.invAmountS != null"
						><strong> 投資本金</strong> : {{ $filters.formatNumber(req.invAmountS) }}~{{ $filters.formatNumber(req.invAmountE) }}
					</span>
					<span v-if="req.returnAmountS != null"
						><strong> 報酬率</strong> : {{ $filters.formatPct(req.returnAmountS) }}%~{{ $filters.formatPct(req.returnAmountE) }}%
					</span>
				</span>
				<span class="img-delete JQ-delet" @click="deletePro" title="" data-bs-original-title="刪除"></span>
			</a>
		</li>

		<li
			class="list-group-item"
			v-show="req.invType != null || req.debitType != null || req.fundC != null || req.efficiencyInv != null || req.fundType != null"
		>
			<a href="#">
				<span>
					<strong>基金商品 : </strong>
					<span v-if="req.invType != null"><strong> 申購類型</strong> : {{ cond.invType }} </span>
					<span v-if="req.debitType != null"><strong> 扣款類型</strong> : {{ cond.debitType }} </span>
					<span v-if="req.fundC != null"><strong> 幣別</strong> : {{ cond.fundC }} </span>
					<span v-if="req.efficiencyInv != null"><strong> 效率投資</strong> : {{ cond.efficiencyInv }} </span>
					<span v-if="req.fundType != null"><strong> 基金類型</strong> : {{ cond.fundType }} </span>
				</span>
				<span class="img-delete JQ-delet" @click="deleteFundPro" title="" data-bs-original-title="刪除"></span>
			</a>
		</li>
		<li class="list-group-item" v-show="req.insType != null || req.insC != null || req.insAAmountS != null">
			<a href="#">
				<span>
					<strong>人身保險商品 : </strong>
					<span v-if="req.insType != null"><strong>保險種類</strong> : {{ cond.insType }} </span>
					<span v-if="req.insC != null"><strong> 幣別</strong> : {{ cond.insC }} </span>
					<span v-if="req.insAAmountS != null"
						><strong> 累計所繳保費</strong> : {{ $filters.formatNumber(req.insAAmountS) }}~{{ $filters.formatNumber(req.insAAmountE) }}
					</span>
				</span>
				<span class="img-delete JQ-delet" @click="deleteIns" title="" data-bs-original-title="刪除"></span>
			</a>
		</li>
		<li
			class="list-group-item"
			v-show="
				cond.loanProCode != null ||
				cond.loanRemark != null ||
				req.loanStdDateS != null ||
				req.loanStdDateE != null ||
				req.loanPeriodS != null ||
				req.loanPeriodE != null ||
				req.appropriationS != null ||
				req.appropriationE != null ||
				req.loanOverAmountS != null ||
				req.loanOverAmountE != null ||
				req.conAppropriationS != null ||
				req.conAppropriationE != null ||
				req.conRateS != null ||
				req.conRateE != null ||
				cond.conReturnType != null ||
				req.conOverAmountS != null ||
				req.conOverAmountE != null ||
				req.conRepaymentRateS != null ||
				req.conRepaymentRateE != null
			"
		>
			<a href="#">
				<span>
					<strong>個人貸款-放款 : </strong>
					<span v-if="cond.loanProCode != null"><strong>申貸商品</strong> : {{ cond.loanProCode }} </span>
					<span v-if="cond.loanRemark != null"><strong> 貸款註記</strong> : {{ cond.loanRemark }} </span>
					<span v-if="req.loanStdDateS != null"><strong> 初貸日期</strong> : {{ req.loanStdDateS }}~{{ req.loanStdDateE }}</span>
					<span v-if="req.loanPeriodS != null"
						><strong> 貸款期數</strong> : {{ $filters.formatNumber(req.loanPeriodS) }}~{{ $filters.formatNumber(req.loanPeriodE) }}
					</span>
					<span v-if="req.appropriationS != null"
						><strong> 撥款金額(L+PB+CK)</strong> : {{ $filters.formatNumber(req.appropriationS) }}~{{
							$filters.formatNumber(req.appropriationE)
						}}
					</span>
					<span v-if="req.loanOverAmountS != null"
						><strong> 貸放餘額(L+PB+CK)</strong> : {{ $filters.formatNumber(req.loanOverAmountS) }}~{{
							$filters.formatNumber(req.loanOverAmountE)
						}}
					</span>
					<span v-if="req.conAppropriationS != null"
						><strong> 撥款金額</strong> : {{ $filters.formatNumber(req.conAppropriationS) }}~{{
							$filters.formatNumber(req.conAppropriationE)
						}}
					</span>
					<span v-if="req.conRateS != null"
						><strong> 承作利率</strong> : {{ $filters.formatPct(req.conRateS) }}%~{{ $filters.formatPct(req.conRateE) }}%
					</span>
					<span v-if="cond.conReturnType != null"><strong> 還本方式</strong> : {{ cond.conReturnType }} </span>
					<span v-if="req.conOverAmountS != null"
						><strong> 貸款餘額</strong> : {{ $filters.formatNumber(req.conOverAmountS) }}~{{ $filters.formatNumber(req.conOverAmountE) }}
					</span>
					<span v-if="req.conRepaymentRateS != null"
						><strong> 還款率</strong> : {{ $filters.formatPct(req.conRepaymentRateS) }}%~{{ $filters.formatPct(req.conRepaymentRateE) }}%
					</span>
				</span>
				<span class="img-delete JQ-delet" @click="deleteLoanDisbursement" title="" data-bs-original-title="刪除"></span>
			</a>
		</li>
		<li
			class="list-group-item"
			v-show="
				req.approveAmountS != null ||
				req.approveAmountE != null ||
				req.interestRateS != null ||
				req.interestRateE != null ||
				cond.status != null ||
				req.userAmountS != null ||
				req.userAmountE != null ||
				req.availableAmountS != null ||
				req.availableAmountE != null
			"
		>
			<a href="#">
				<span>
					<strong>個人貸款-額度 : </strong>
					<span v-if="req.approveAmountS != null"
						><strong>核准額度</strong> : {{ $filters.formatNumber(req.approveAmountS) }}~{{ $filters.formatNumber(req.approveAmountE) }}
					</span>
					<span v-if="req.interestRateS != null"
						><strong> 計息利率</strong> : {{ $filters.formatPct(req.interestRateS) }}%~{{ $filters.formatPct(req.interestRateE) }}%
					</span>
					<span v-if="cond.status != null"><strong> 額度狀態</strong> : {{ cond.status }} </span>
					<span v-if="req.userAmountS != null"
						><strong> 使用額度</strong> : {{ $filters.formatNumber(req.userAmountS) }}~{{ $filters.formatNumber(req.userAmountE) }}
					</span>
					<span v-if="req.availableAmountS != null"
						><strong> 使用額度</strong> : {{ $filters.formatNumber(req.availableAmountS) }}~{{
							$filters.formatNumber(req.availableAmountE)
						}}
					</span>
				</span>
				<span class="img-delete JQ-delet" @click="deleteLoanLimit" title="" data-bs-original-title="刪除"></span>
			</a>
		</li>
		<template v-for="selEx in selExDataItem">
			<li class="list-group-item" v-if="form.queList[selEx.quesectionId] != null && form.queList[selEx.quesectionId].length > 0">
				<a href="#">
					<span>
						<strong>{{ selEx.quesectionName }} : </strong>
						<template v-for="que in selEx.queitem">
							<span v-if="que.answer != null"
								><strong>{{ ' ' + que.queitemName }}</strong> : {{ que.answer.queitemselName }}
							</span>
						</template>
					</span>
					<span class="img-delete JQ-delet" @click="deleteSelEx(selEx)" title="" data-bs-original-title="刪除"></span>
				</a>
			</li>
		</template>
	</ul>
	<div class="col-lg-8">
		<div class="card card-form card-collapse mb-3">
			<div class="card-header">
				<h4>綜合條件查詢</h4>
				<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1"></div>
			</div>
			<div class="collapse show" id="collapseListGroup1">
				<div class="card-body">
					<v-form ref="form">
						<div class="row g-3 align-items-end">
							<div class="col-md-4">
								<label class="form-label">客戶資產等級</label>
								<div class="input-group mb-2">
									<select class="form-select" v-model="form.gra">
										<option :value="null">請選擇</option>
										<option v-for="item in selGrades" :value="item">{{ item.graName }}</option>
									</select>
									<a data-bs-toggle="tooltip" href="#" class="table-icon">
										<button type="button" class="btn btn-info btn-icon JQ-singleSearch" @click="addGra" title="新增">
											<i class="fa-solid fa-plus"></i>
										</button>
									</a>
								</div>
							</div>
							<div class="col-md-8">
								<label class="form-label"></label>
								<div class="d-flex align-items-start gap-2">
									<div class="flex-grow-1">
										<vue-user-condition
											:col-width="12"
											:area-label="'組織 (區/分行/理專)'"
											:bran-label="''"
											:user-label="''"
											:is-required="'area'"
											@change-area="(val) => (form.area = val)"
											@change-bran="(val) => (form.bran = val)"
											@change-user="(val) => (form.user = val)"
											@change-area-name="(val) => (form.areaName = val)"
											@change-bran-name="(val) => (form.branName = val || null)"
											@change-user-name="(val) => (form.userName = val || null)"
											@loaded="onUserConditionLoaded"
										>
										</vue-user-condition>
									</div>
									<a data-bs-toggle="tooltip" href="#" class="table-icon">
										<button
											type="button"
											class="btn btn-info btn-icon align-self-end"
											style="margin-top: 1.2rem; margin-left: 0.15rem"
											@click="addBran"
											title="新增"
										>
											<i class="fa-solid fa-plus"></i>
										</button>
									</a>
								</div>
							</div>
							<div class="col-md-8">
								<label class="form-label"> 客戶生日 (月/日) [限自然人]</label>
								<div class="input-group mb-2">
									<select class="form-select" v-model="form.birthMS">
										<option :value="null" size="15">請選擇</option>
										<option v-for="item in selMonth" :value="item.codeValue">{{ item.codeName }}</option>
									</select>
									<select name="cusSearchBirth.beginDd" class="form-select" v-model="form.birthDS">
										<option :value="null" size="15">請選擇</option>
										<option v-for="item in selDay" :value="item.codeValue">{{ item.codeName }}</option>
									</select>
									<span class="input-group-text">~</span>
									<select class="form-select" v-model="form.birthME">
										<option :value="null" size="15">請選擇</option>
										<option v-for="item in selMonth" :value="item.codeValue">{{ item.codeName }}</option>
									</select>
									<select class="form-select" v-model="form.birthDE">
										<option :value="null" size="15">請選擇</option>
										<option v-for="item in selDay" :value="item.codeValue">{{ item.codeName }}</option>
									</select>
									<a data-bs-toggle="tooltip" href="#" class="table-icon">
										<button type="button" class="btn btn-info btn-icon JQ-singleSearch" @click="addBirth" title="新增">
											<i class="fa-solid fa-plus"></i>
										</button>
									</a>
								</div>
							</div>
							<div class="col-md-12">
								<div class="tab-nav-tabs">
									<ul class="nav nav-tabs nav-justified" role="tablist">
										<li class="nav-item">
											<a data-bs-toggle="tab" href="#tab1" class="nav-link active">資產狀況</a>
										</li>
										<li class="nav-item">
											<a data-bs-toggle="tab" href="#tab2" class="nav-link">個人放款</a>
										</li>
										<li class="nav-item">
											<a data-bs-toggle="tab" href="#tab3" class="nav-link">其他資料</a>
										</li>
									</ul>
									<div class="tab-content">
										<div class="tab-pane fade show active" id="tab1">
											<div class="row g-2">
												<div class="col-md-12 bg-ligntblue p-3">
													<label class="form-label"><b>投資屬性：</b></label
													><br />
													<div class="row justify-content-between align-items-end">
														<div class="col-10">
															<div class="form-check form-check-inline" v-for="(item, i) in selRank">
																<input
																	class="form-check-input"
																	v-model="form.rank"
																	:id="'rankCode' + i"
																	type="radio"
																	:value="item"
																/>
																<label class="form-check-label" :for="'rankCode' + i">{{ item.codeName }}</label>
															</div>
														</div>
														<div class="col-2 text-end">
															<a data-bs-toggle="tooltip" href="#" class="table-icon">
																<button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	@click="addRank"
																	title="新增"
																>
																	<i class="fa-solid fa-plus"></i>
																</button>
															</a>
														</div>
													</div>
												</div>
												<div class="divider"></div>
												<div class="col-md-12 bg-ligntblue p-3">
													<label class="form-label"><b>本行總資產(AUM)：</b></label
													><br />
													<div class="row justify-content-between">
														<div class="col-10">
															<div class="input-group">
																<span class="input-group-text">介於</span>
																<input
																	class="form-control text-end JQ-float"
																	v-model="form.aumAmountS"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('aumAmountS', $event)"
																/>
																<span class="input-group-text">~</span>
																<input
																	class="form-control text-end JQ-float"
																	v-model="form.aumAmountE"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('aumAmountE', $event)"
																/>
																<span class="input-group-text">元</span>
															</div>
														</div>
														<div class="col-2 text-end">
															<a data-bs-toggle="tooltip" href="#" class="table-icon">
																<button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	@click="addAum"
																	title="新增"
																>
																	<i class="fa-solid fa-plus"></i>
																</button>
															</a>
														</div>
													</div>
												</div>
												<div class="divider"></div>
												<div class="col-md-12 bg-ligntblue p-3">
													<label class="form-label"><b>本行貢獻度：</b></label
													><br />
													<div class="row">
														<div class="col-md-10">
															<div class="input-group mb-2">
																<span class="input-group-text">貢獻度介於</span>
																<input
																	class="form-control text-end JQ-float"
																	v-model="form.contbAmountS"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('contbAmountS', $event)"
																/>
																<span class="input-group-text">~</span>
																<input
																	class="form-control text-end JQ-float"
																	v-model="form.contbAmountE"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('contbAmountE', $event)"
																/>
																<span class="input-group-text">元</span>
															</div>
														</div>
														<div class="col-10">
															<label class="form-label"> 時間區間 </label>
															<div class="form-check form-check-inline" v-for="(item, i) in selContbTime">
																<input
																	class="form-check-input"
																	:id="'contbTime_' + i"
																	type="radio"
																	:value="item"
																	v-model="form.contbTime"
																/>
																<label class="form-check-label" :for="'contbTime_' + i">{{ item.codeName }}</label>
															</div>
															<div class="input-group" v-show="form.contbTime?.codeValue === 'DEF'">
																<input class="JQ-datepicker form-control" type="date" v-model="form.contbTimeS" />
																<span class="input-group-text">~</span>
																<input class="JQ-datepicker form-control" type="date" v-model="form.contbTimeE" />
															</div>
														</div>
														<div class="col-2 text-end">
															<a data-bs-toggle="tooltip" href="#" class="table-icon">
																<button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	@click="addContb"
																	title="新增"
																>
																	<i class="fa-solid fa-plus"></i>
																</button>
															</a>
														</div>
													</div>
												</div>
												<div class="divider"></div>
												<div class="col-md-12 bg-ligntblue p-3">
													<label class="form-label"><b>存款商品：</b></label
													><br />
													<div class="row align-items-end">
														<div class="col-md-10">
															<div class="input-group">
																<div class="input-group-text">存款種類</div>
																<select class="form-select" v-model="form.savingType">
																	<option :value="null">請選擇</option>
																	<option v-for="item in selSavingProtype" :value="item">
																		{{ item.codeName }}
																	</option>
																</select>
																<div class="input-group-text">幣別</div>
																<select class="form-select" v-model="form.savingC">
																	<option :value="null">請選擇</option>
																	<option v-for="item in selCurencies" :value="item">{{ item.curName }}</option>
																</select>
															</div>
														</div>
														<div class="col-10">
															<div class="input-group mt-2">
																<span class="input-group-text">帳戶餘額 介於</span>
																<input
																	class="form-control text-end JQ-float"
																	v-model="form.savingAmountS"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('savingAmountS', $event)"
																/>
																<span class="input-group-text">~</span>
																<input
																	class="form-control text-end JQ-float"
																	v-model="form.savingAmountE"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('savingAmountE', $event)"
																/>
																<span class="input-group-text">元</span>
															</div>
														</div>
														<div class="col-2 text-end">
															<a data-bs-toggle="tooltip" href="#" class="table-icon">
																<button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	@click="addSaving"
																	title="新增"
																>
																	<i class="fa-solid fa-plus"></i>
																</button>
															</a>
														</div>
													</div>
												</div>
												<div class="divider"></div>
												<div class="col-md-12 bg-ligntblue p-3">
													<label class="form-label"><b>投資商品：</b></label
													><br />
													<div class="form-check form-check-inline" v-for="(item, i) in selProCat">
														<input
															class="form-check-input"
															v-model="form.proCatList"
															:id="'proCatList' + i"
															type="checkbox"
															:value="item"
														/>
														<label class="form-check-label" :for="'proCatList' + i">{{ item.pfcatName }}</label>
													</div>
													<div class="d-inline-flex" style="word-break: keep-all; align-items: center">
														<div class="input-group ms-1 wd-300">
															<span class="input-group-text">商品代碼</span>
															<input class="form-control" v-model="form.proCode" type="text" value="" />
															<a class="btn btn-info" href="#" @click.prevent="openProModal">查詢代碼</a>
														</div>
													</div>
													<div class="row align-items-center">
														<div class="col-auto">
															<div class="input-group">
																<div class="input-group-text">幣別</div>
																<select class="form-select" v-model="form.proC">
																	<option :value="null">請選擇</option>
																	<option v-for="item in selCurencies" :value="item">{{ item.curName }}</option>
																</select>
															</div>
														</div>
														<div class="col-auto">
															配息狀況
															<div class="form-check form-check-inline" v-for="(item, i) in selInvproInttype">
																<input
																	class="form-check-input"
																	:id="'intType' + i"
																	v-model="form.intType"
																	type="radio"
																	:value="item"
																/>
																<label class="form-check-label" :for="'intType' + i">{{ item.codeName }}</label>
															</div>
														</div>
													</div>
													<div class="row align-items-end">
														<div class="col-md-10">
															<div class="input-group mt-2">
																<span class="input-group-text">市值餘額</span>
																<input
																	v-model="form.mktAmountS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	value=""
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('mktAmountS', $event)"
																/>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.mktAmountE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	value=""
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('mktAmountE', $event)"
																/>
																<span class="input-group-text">元</span>
															</div>
															<div class="input-group mt-2">
																<span class="input-group-text">投資本金</span>
																<input
																	v-model="form.invAmountS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	value=""
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('invAmountS', $event)"
																/>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.invAmountE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	value=""
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('invAmountE', $event)"
																/>
																<span class="input-group-text">元</span>
															</div>
														</div>
														<div class="col-10">
															<div class="input-group mt-2">
																<span class="input-group-text">報酬率</span>
																<input
																	v-model="form.returnAmountS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	value=""
																/>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.returnAmountE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	value=""
																/>
																<span class="input-group-text">% (未實現損益含息)</span>
															</div>
														</div>
														<div class="col-2 text-end">
															<a data-bs-toggle="tooltip" href="#" class="table-icon">
																<button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	@click="addPro"
																	title="新增"
																>
																	<i class="fa-solid fa-plus"></i>
																</button>
															</a>
														</div>
													</div>
												</div>
												<div class="divider"></div>
												<div class="col-md-12 bg-ligntblue p-3">
													<label class="form-label"><b>基金商品：</b></label
													><br />
													<div class="row g-2">
														<div class="col-md-4">
															<div class="input-group">
																<span class="input-group-text">申購類型</span>
																<select class="form-select" v-model="form.invType">
																	<option :value="null">--</option>
																	<option v-for="item in selFundInvtype" :value="item">{{ item.codeName }}</option>
																</select>
															</div>
														</div>
														<div class="col-md-4">
															<div class="input-group">
																<span class="input-group-text">扣款類型</span>
																<select class="form-select" v-model="form.debitType">
																	<option :value="null">--</option>
																	<option v-for="item in selDeductCode" :value="item">{{ item.codeName }}</option>
																</select>
															</div>
														</div>
														<div class="col-md-4">
															<div class="input-group">
																<span class="input-group-text">幣別</span>
																<select class="form-select" v-model="form.fundC">
																	<option :value="null">--</option>
																	<option v-for="item in selCurencies" :value="item">{{ item.curName }}</option>
																</select>
															</div>
														</div>
														<div class="col-md-4">
															<div class="input-group">
																<span class="input-group-text">效率投資</span>
																<select class="form-select" v-model="form.efficiencyInv">
																	<option :value="null">--</option>
																	<option v-for="item in selSmartStock" :value="item">{{ item.codeName }}</option>
																</select>
															</div>
														</div>
														<div class="col-10 col-md-4">
															<div class="input-group">
																<span class="input-group-text">基金類型</span>
																<select class="form-select" v-model="form.fundType">
																	<option :value="null">--</option>
																	<option v-for="item in selFundProType" :value="item">
																		{{ item.proTypeName }}
																	</option>
																</select>
															</div>
														</div>
														<div class="col-2 col-md-4 text-end">
															<a data-bs-toggle="tooltip" href="#" class="table-icon">
																<button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	@click="addFundPro"
																	title="新增"
																>
																	<i class="fa-solid fa-plus"></i>
																</button>
															</a>
														</div>
													</div>
												</div>
												<div class="divider"></div>
												<div class="col-md-12 bg-ligntblue p-3">
													<label class="form-label"><b>人身保險商品：</b></label>
													<div class="row g-2">
														<div class="col-md-8">
															<div class="input-group">
																<span class="input-group-text">保險種類</span>
																<select class="form-select" v-model="form.insType">
																	<option :value="null">請選擇</option>
																	<option v-for="item in selInsProType" :value="item">
																		{{ item.proTypeName }}
																	</option>
																</select>
															</div>
														</div>
														<div class="col-md-4">
															<div class="input-group">
																<span class="input-group-text">幣別</span>
																<select class="form-select" v-model="form.insC">
																	<option :value="null">請選擇</option>
																	<option v-for="item in selCurencies" :value="item">{{ item.curName }}</option>
																</select>
															</div>
														</div>
														<div class="col-10">
															<div class="input-group">
																<span class="input-group-text">累計所繳保費</span>
																<input
																	v-model="form.insAAmountS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	value=""
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('insAAmountS', $event)"
																/>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.insAAmountE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	value=""
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('insAAmountE', $event)"
																/>
																<span class="input-group-text">元</span>
															</div>
														</div>
														<div class="col-2 text-end">
															<a data-bs-toggle="tooltip" href="#" class="table-icon">
																<button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	@click="addIns"
																	title="新增"
																>
																	<i class="fa-solid fa-plus"></i>
																</button>
															</a>
														</div>
													</div>
												</div>
											</div>
										</div>
										<div class="tab-pane fade" id="tab2">
											<div class="row g-2">
												<div class="col-md-12 bg-ligntblue p-3">
													<label class="form-label"><b>個人貸款-放款：</b></label
													><br />
													<div class="row g-2">
														<div class="col-md-5">
															<div class="input-group">
																<span class="input-group-text">申貸商品</span>
																<select class="form-select" v-model="form.loanProCode">
																	<option :value="null">請選擇</option>
																	<option v-for="item in selLoanProType" :value="item">
																		{{ item.proTypeName }}
																	</option>
																</select>
															</div>
														</div>
														<div class="col-md-5">
															<div class="input-group">
																<span class="input-group-text">貸款註記</span>
																<select class="form-select" v-model="form.loanRemark">
																	<option :value="null">--</option>
																	<option v-for="item in selLoanStatus" :value="item">{{ item.codeName }}</option>
																</select>
															</div>
														</div>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text">初貸日期</span>
																<input
																	class="JQ-datepicker form-control"
																	type="date"
																	size="13"
																	maxlength="10"
																	v-model="form.loanStdDateS"
																/>
																<span class="input-group-text">~</span>
																<input
																	class="JQ-datepicker form-control"
																	type="date"
																	size="13"
																	maxlength="10"
																	v-model="form.loanStdDateE"
																/>
															</div>
														</div>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text">貸款期數</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="5"
																	maxlength="3"
																	v-model="form.loanPeriodS"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('loanPeriodS', $event)"
																/>
																<span class="input-group-text">~</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="5"
																	maxlength="3"
																	v-model="form.loanPeriodE"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('loanPeriodE', $event)"
																/>
																<span class="input-group-text">期</span>
															</div>
														</div>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text">撥款金額(L+PB+CK)</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.appropriationS"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('appropriationS', $event)"
																/>
																<span class="input-group-text">~</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.appropriationE"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('appropriationE', $event)"
																/>
																<span class="input-group-text">元</span>
															</div>
														</div>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text"> 貸放餘額(L+PB+CK)</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.loanOverAmountS"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('loanOverAmountS', $event)"
																/>
																<span class="input-group-text">~</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.loanOverAmountE"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('loanOverAmountE', $event)"
																/>
																<span class="input-group-text">元</span>
															</div>
														</div>
														<div class="divider"></div>
														<label class="form-label tx-primary">※放款條件查詢：</label>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text">撥款金額</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.conAppropriationS"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('conAppropriationS', $event)"
																/>
																<span class="input-group-text">~</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.conAppropriationE"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('conAppropriationE', $event)"
																/>
																<span class="input-group-text">元</span>
															</div>
														</div>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text"> 承作利率</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.conRateS"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('conRateS', $event)"
																/>
																<span class="input-group-text">~</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.conRateE"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('conRateE', $event)"
																/>
																<span class="input-group-text">%</span>
															</div>
														</div>
														<div class="col-md-auto">
															<div class="input-group">
																<span class="input-group-text">還本方式</span>
																<select class="form-select" v-model="form.conReturnType">
																	<option :value="null">--</option>
																	<option v-for="item in selRepayCode" :value="item">{{ item.codeName }}</option>
																</select>
															</div>
														</div>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text">貸款餘額</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.conOverAmountS"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('conOverAmountS', $event)"
																/>
																<span class="input-group-text">~</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.conOverAmountE"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('conOverAmountE', $event)"
																/>
																<span class="input-group-text">元</span>
															</div>
														</div>
														<div class="col-10">
															<div class="input-group">
																<span class="input-group-text"> 還款率</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.conRepaymentRateS"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('conRepaymentRateS', $event)"
																/>
																<span class="input-group-text">~</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.conRepaymentRateE"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('conRepaymentRateE', $event)"
																/>
																<span class="input-group-text">%</span>
															</div>
														</div>
														<div class="col-2 text-end">
															<a data-bs-toggle="tooltip" href="#" class="table-icon">
																<button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	@click="addLoanDisbursement"
																	title="新增"
																>
																	<i class="fa-solid fa-plus"></i>
																</button>
															</a>
														</div>
													</div>
												</div>
												<div class="col-md-12 bg-ligntblue p-3">
													<label class="form-label"><b>個人貸款-額度：</b></label
													><br />
													<div class="row g-2">
														<label class="form-label tx-primary">※額度條件查詢：</label>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text">核准額度</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.approveAmountS"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('approveAmountS', $event)"
																/>
																<span class="input-group-text">~</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.approveAmountE"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('approveAmountE', $event)"
																/>
																<span class="input-group-text">元</span>
															</div>
														</div>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text"> 計息利率</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.interestRateS"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('interestRateS', $event)"
																/>
																<span class="input-group-text">~</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.interestRateE"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('interestRateE', $event)"
																/>
																<span class="input-group-text">%</span>
															</div>
														</div>
														<div class="col-md-auto">
															<div class="input-group">
																<span class="input-group-text">額度狀態</span>
																<select class="form-select" v-model="form.status">
																	<option :value="null">--</option>
																	<option v-for="item in selQuotaStatus" :value="item">{{ item.codeName }}</option>
																</select>
															</div>
														</div>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text">使用額度</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.userAmountS"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('userAmountS', $event)"
																/>
																<span class="input-group-text">~</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.userAmountE"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('userAmountE', $event)"
																/>
																<span class="input-group-text">元</span>
															</div>
														</div>
														<div class="col-10">
															<div class="input-group">
																<span class="input-group-text"> 可用餘額</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.availableAmountS"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('availableAmountS', $event)"
																/>
																<span class="input-group-text">~</span>
																<input
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	v-model="form.availableAmountE"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('availableAmountE', $event)"
																/>
																<span class="input-group-text">元</span>
															</div>
														</div>
														<div class="col-2 text-end">
															<a data-bs-toggle="tooltip" href="#" class="table-icon">
																<button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	@click="addLoanLimit"
																	title="新增"
																>
																	<i class="fa-solid fa-plus"></i>
																</button>
															</a>
														</div>
													</div>
												</div>
											</div>
										</div>
										<div class="tab-pane fade" id="tab3">
											<div class="row g-2">
												<div class="col-md-12 bg-ligntblue p-3" v-for="selEx in selExDataItem">
													<label class="form-label"
														><b>{{ selEx.quesectionName }}：</b></label
													><br />
													<div class="row g-2">
														<div class="col-md-10" v-for="que in selEx.queitem">
															<div class="input-group">
																<span class="input-group-text">{{ que.queitemName }}</span>
																<select class="form-select" v-model="que.answer">
																	<option :value="undefined">請選擇</option>
																	<option v-for="sel in que.itemsel" :value="sel">{{ sel.queitemselName }}</option>
																</select>
															</div>
														</div>
														<div class="col-2 text-end">
															<a data-bs-toggle="tooltip" href="#" class="table-icon">
																<button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	@click="addSelEx(selEx)"
																	title="新增"
																>
																	<i class="fa-solid fa-plus"></i>
																</button>
															</a>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="text-end mt-3">
									<input
										name="Submit1"
										type="button"
										class="btn btn-primary btn-lg JQ-singleSearch"
										@click="search"
										value="客戶查詢"
									/>
								</div>
							</div>
						</div>
					</v-form>
				</div>
			</div>
		</div>
	</div>
	<vue-pro-quick-search ref="proModal" :set-pro-code="setProCode"></vue-pro-quick-search>
	<vue-pro-quick-search ref="fundModal" :set-pro-code="setFundProCode"></vue-pro-quick-search>
</template>
<script>
import vueUserCondition from '@/views/components/userCondition.vue';
import vueProQuickSearch from './proQuickSearch.vue';
import { Form } from 'vee-validate';
import _ from 'lodash';
export default {
	props: {
		parentSearch: Function
	},
	components: {
		vueUserCondition,
		vueProQuickSearch,
		'v-form': Form
	},
	data() {
		return {
			req: {
				graCode: null,
				area: null,
				branCode: null,
				userCode: null,
				birthStart: null,
				birthEnd: null,
				rankCode: null,
				aumAmountS: null,
				aumAmountE: null,
				contbAmountS: null,
				contbAmountE: null,
				contbTime: null,
				contbTimeS: null,
				contbTimeE: null,
				savingType: [],
				savingC: null,
				savingAmountS: null,
				savingAmountE: null,
				proCatList: [],
				proCode: null,
				proC: null,
				intType: null,
				mktAmountS: null,
				mktAmountE: null,
				invAmountS: null,
				invAmountE: null,
				returnAmountS: null,
				returnAmountE: null,
				invType: null,
				debitType: null,
				fundC: null,
				efficiencyInv: null,
				fundType: null,
				insType: null,
				insC: null,
				insAAmountS: null,
				insAAmountE: null,

				loanProCode: null,
				loanRemark: null,
				loanStdDateS: null,
				loanStdDateE: null,
				loanPeriodS: null,
				loanPeriodE: null,
				appropriationS: null,
				appropriationE: null,
				loanOverAmountS: null,
				loanOverAmountE: null,
				conAppropriationS: null,
				conAppropriationE: null,
				conRateS: null,
				conRateE: null,
				conReturnType: null,
				conOverAmountS: null,
				conOverAmountE: null,
				conRepaymentRateS: null,
				conRepaymentRateE: null,

				approveAmountS: null,
				approveAmountE: null,
				interestRateS: null,
				interestRateE: null,
				status: null,
				userAmountS: null,
				userAmountE: null,
				availableAmountS: null,
				availableAmountE: null,

				queList: []
			},
			form: {
				gra: null,
				area: null,
				bran: null,
				user: null,
				areaName: null,
				branName: null,
				userName: null,
				birthMS: null,
				birthDS: null,
				birthME: null,
				birthDE: null,
				rank: null,
				aumAmountS: null,
				aumAmountE: null,
				contbAmountS: null,
				contbAmountE: null,
				contbTime: null,
				contbTimeS: null,
				contbTimeE: null,
				savingType: null,
				savingC: null,
				savingAmountS: null,
				savingAmountE: null,
				proCatList: [],
				proC: null,
				intType: null,
				proCode: null,
				mktAmountS: null,
				mktAmountE: null,
				invAmountS: null,
				invAmountE: null,
				returnAmountS: null,
				returnAmountE: null,
				invType: null,
				debitType: null,
				fundC: null,
				efficiencyInv: null,
				fundType: null,
				insType: null,
				insC: null,
				insAAmountS: null,
				insAAmountE: null,
				loanProCode: null,
				loanRemark: null,
				loanStdDateS: null,
				loanStdDateE: null,
				loanPeriodS: null,
				loanPeriodE: null,
				appropriationS: null,
				appropriationE: null,
				loanOverAmountS: null,
				loanOverAmountE: null,
				conAppropriationS: null,
				conAppropriationE: null,
				conRateS: null,
				conRateE: null,
				conReturnType: null,
				conOverAmountS: null,
				conOverAmountE: null,
				conRepaymentRateS: null,
				conRepaymentRateE: null,
				approveAmountS: null,
				approveAmountE: null,
				interestRateS: null,
				interestRateE: null,
				status: null,
				userAmountS: null,
				userAmountE: null,
				availableAmountS: null,
				availableAmountE: null,
				queList: {}
			},
			cond: {
				graName: null,
				area: null,
				bran: null,
				user: null,
				birth: null,
				rank: null,
				contbTime: null,
				contbTimeS: null,
				contbTimeE: null,
				savingType: null,
				savingC: null,
				proCatList: null,
				proC: null,
				intType: null,
				loanProCode: null,
				loanRemark: null,
				conReturnType: null,
				status: null
			},
			selGrades: [],
			selAreaList: [],
			selBranList: [],
			selUserList: [],
			selMonth: [],
			selDay: [],
			selRank: [],
			selContbTime: [],
			selCurencies: [],
			selSavingProtype: [],
			selProCat: [],
			selFundInvtype: [],
			selDeductCode: [],
			selSmartStock: [],
			selFundProType: [],
			selInsProType: [],
			selLoanProType: [],
			selLoanStatus: [],
			selRepayCode: [],
			selInvproInttype: [],
			selQuotaStatus: [],

			selExDataItem: [],

			selMultiCusList: []
		};
	},
	watch: {
		userInfo(newVal) {
			if (newVal) {
			}
		},
		'form.area'(newVal) {
			this.form.ca = null;

			if (newVal == null) {
				this.selBranList = [];
			} else {
				this.getAdmBranListByParentBranCode(newVal.branCode);
			}
		},
		'form.bran'(newVal) {
			this.form.branCode = null;

			if (newVal == null) {
				this.selUserList = [];
			} else {
				this.getAdmUserListByBranCode(newVal.branCode);
			}
		}
	},
	computed: {
		userInfo() {
			return this.$store.getters['userInfo/info'];
		}
	},
	mounted: async function () {
		this.getGrades();
		this.getAdmBranchesByRole();
		this.getProCurrenciesList();
		this.getProCat();
		this.getExtDataItem();

		var result = await Promise.all([
			this.getAdmCodeDetail('CUS_BIRTH_MONTH'),
			this.getAdmCodeDetail('CUS_BIRTH_DAY'),
			this.getAdmCodeDetail('CUS_RANK_ITEM'),
			this.getAdmCodeDetail('CUS_CONTB_TIME'),
			this.getAdmCodeDetail('CUS_SAVING_PROTYPE'),
			this.getAdmCodeDetail('CUS_INVPRO_INTTYPE'),
			this.getAdmCodeDetail('CUS_FUND_INVTYPE'),
			this.getProTypes('FUND'),
			this.getProTypes('INS'),

			this.getProTypes('LOAN'),
			this.getAdmCodeDetail('DEDUCT_CODE'),
			this.getAdmCodeDetail('CUS_SMART_STOCK'),
			this.getAdmCodeDetail('LOAN_STATUS'),
			this.getAdmCodeDetail('QUOTA_STATUS'),
			this.getAdmCodeDetail('REPAY_CODE')
		]);
		[
			this.selMonth,
			this.selDay,
			this.selRank,
			this.selContbTime,
			this.selSavingProtype,
			this.selInvproInttype,
			this.selFundInvtype,
			this.selFundProType,
			this.selInsProType,
			this.selLoanProType,
			this.selDeductCode,
			this.selSmartStock,
			this.selLoanStatus,
			this.selQuotaStatus,
			this.selRepayCode
		] = result;
	},
	methods: {
		search() {
			var queList = [];
			_.forEach(this.form.queList, function (answer) {
				_.forEach(answer, function (e) {
					queList.push(e.queitemselId);
				});
			});
			this.req.queList = queList;
			this.parentSearch(this.req);
		},
		async getAdmBranchesByRole() {
			const resp = await this.$api.getAdmBranchesApi({
				branLvlCode: '30',
				removeYn: 'N'
			});
			this.selAreaList = resp.data;
		},
		async getAdmBranListByParentBranCode(parentBranCode) {
			const resp = await this.$api.getAdmBranchesApi({
				parentBranCode: parentBranCode
			});
			this.selBranList = resp.data;
		},
		async getAdmUserListByBranCode(branCode) {
			const resp = await this.$api.getAdmUsersListApi({
				branCode: branCode,
				roleCode: '00'
			});
			this.selUserList = resp.data;
		},
		addGra() {
			if (_.isEmpty(this.form.gra)) {
				this.$bi.alert('請選擇客戶資產等級');
				return;
			}
			this.req.graCode = this.form.gra?.graCode;
			this.cond.graName = this.form.gra?.graName;
		},
		deleteGra() {
			this.req.graCode = null;
			this.cond.graName = null;
		},
		addBran() {
			if (_.isEmpty(this.form.area)) {
				this.$bi.alert('請選擇分區');
				return;
			}
			this.req.area = this.form.area;
			this.req.branCode = this.form.bran;
			this.req.userCode = this.form.user;
			this.cond.area = this.form.areaName;
			this.cond.bran = this.form.branName;
			this.cond.user = this.form.userName;
		},
		deleteBran() {
			this.req.area = null;
			this.req.branCode = null;
			this.req.userCode = null;
			this.cond.area = null;
			this.cond.bran = null;
			this.cond.user = null;
		},
		addBirth() {
			if (this.form.birthMS == null || this.form.birthDS == null || this.form.birthME == null || this.form.birthDE == null) {
				this.$bi.alert('範圍不能有空值');
			} else if (!this.checkDay(this.form.birthMS, this.form.birthDS)) {
				this.$bi.alert('開始日期不合理');
			} else if (!this.checkDay(this.form.birthME, this.form.birthDE)) {
				this.$bi.alert('結束日期不合理');
			} else {
				var birthStart = this.form.birthMS.padStart(2, '0') + this.form.birthDS.padStart(2, '0');
				var birthEnd = this.form.birthME.padStart(2, '0') + this.form.birthDE.padStart(2, '0');

				if (birthStart > birthEnd) {
					this.$bi.alert('開始不能小於結束日期');
				} else {
					this.req.birthStart = birthStart;
					this.req.birthEnd = birthEnd;
					this.cond.birth = this.form.birthMS + '月' + this.form.birthDS + '日~' + this.form.birthME + '月' + this.form.birthDE + '日';
				}
			}
		},
		deleteBirth() {
			this.req.birthStart = null;
			this.req.birthEnd = null;
			this.cond.birth = null;
		},
		addRank() {
			if (_.isEmpty(this.form.rank)) {
				this.$bi.alert('請選擇投資屬性');
				return;
			}
			this.req.rankCode = this.form.rank?.codeValue;
			this.cond.rank = this.form.rank?.codeName;
		},
		deleteRank() {
			this.req.rankCode = null;
			this.cond.rank = null;
		},
		addAum: async function () {
			await this.checkRangeRequire(this.form.aumAmountS, this.form.aumAmountE, 'AUM');

			this.req.aumAmountS = this.form.aumAmountS;
			this.req.aumAmountE = this.form.aumAmountE;
		},
		deleteAum() {
			this.req.aumAmountS = null;
			this.req.aumAmountE = null;
		},
		addContb: async function () {
			await this.checkRangeRequire(this.form.contbAmountS, this.form.contbAmountE, '貢獻度');

			if (_.isEmpty(this.form.contbTime)) {
				this.$bi.alert('請選擇時間區間');
				return;
			}
			if (this.form.contbTime?.codeValue === 'DEF') {
				await this.checkDateRangeRequire(this.form.contbTimeS, this.form.contbTimeE, '時間');
				this.req.contbTimeS = this.form.contbTimeS;
				this.req.contbTimeE = this.form.contbTimeE;
				this.cond.contbTimeS = this.form.contbTimeS;
				this.cond.contbTimeE = this.form.contbTimeE;
			} else {
				this.req.contbTimeS = null;
				this.req.contbTimeE = null;
				this.cond.contbTimeS = null;
				this.cond.contbTimeE = null;
			}
			this.req.contbAmountS = this.form.contbAmountS;
			this.req.contbAmountE = this.form.contbAmountE;
			this.req.contbTime = this.form.contbTime?.codeValue;
			this.cond.contbTime = this.form.contbTime?.codeName;
		},
		deleteContb() {
			this.req.contbTime = null;
			this.req.contbAmountS = null;
			this.req.contbAmountE = null;
			this.req.contbTimeS = null;
			this.req.contbTimeE = null;
			this.cond.contbTime = null;
		},
		addSaving: async function () {
			await this.checkRangeRequire(this.form.savingAmountS, this.form.savingAmountE, '帳戶餘額');

			if (this.form.savingType != null) {
				this.req.savingType = this.form.savingType?.codeValue.split(',');
				this.cond.savingType = this.form.savingType?.codeName;
			} else {
				this.req.savingType = [];
				this.cond.savingType = null;
			}
			this.req.savingC = this.form.savingC?.curCode ?? null;
			this.req.savingAmountS = this.form.savingAmountS;
			this.req.savingAmountE = this.form.savingAmountE;
			this.cond.savingC = this.form.savingC?.curName ?? null;
		},
		deleteSaving() {
			this.req.savingType = [];
			this.req.savingC = null;
			this.req.savingAmountS = null;
			this.req.savingAmountE = null;
			this.cond.savingType = null;
			this.cond.savingC = null;
		},
		addPro: async function () {
			if (_.isEmpty(this.form.proCatList) && _.isBlank(this.form.proCode)) {
				this.$bi.alert('請選擇投資商品或輸入商品代碼');
				return;
			}
			await this.checkRangeNoRequire(this.form.mktAmountS, this.form.mktAmountE, '市值餘額');
			await this.checkRangeNoRequire(this.form.invAmountS, this.form.invAmountE, '投資本金');
			await this.checkRangeNoRequire(this.form.returnAmountS, this.form.returnAmountE, '報酬率');

			var proCatList = [];
			var proCatName = [];
			_.forEach(this.form.proCatList, function (e) {
				proCatList.push(e.pfcatCode);
				proCatName.push(e.pfcatName);
			});
			this.req.proCatList = proCatList;
			this.req.proCode = this.form.proCode;
			this.req.proC = this.form.proC?.curCode ?? null;
			this.req.intType = this.form.intType?.codeValue;
			this.req.mktAmountS = this.form.mktAmountS;
			this.req.mktAmountE = this.form.mktAmountE;
			this.req.invAmountS = this.form.invAmountS;
			this.req.invAmountE = this.form.invAmountE;
			this.req.returnAmountS = this.form.returnAmountS != null ? this.form.returnAmountS / 100 : null;
			this.req.returnAmountE = this.form.returnAmountE != null ? this.form.returnAmountE / 100 : null;

			this.cond.proCatList = proCatName.join(', ') || null;
			this.cond.proC = this.form.proC?.curName ?? null;
			this.cond.intType = this.form.intType?.codeName ?? null;
		},
		deletePro() {
			this.req.proCatList = [];
			this.req.proCode = null;
			this.req.proC = null;
			this.req.intType = null;
			this.req.mktAmountS = null;
			this.req.mktAmountE = null;
			this.req.invAmountS = null;
			this.req.invAmountE = null;
			this.req.returnAmountS = null;
			this.req.returnAmountE = null;
			this.cond.proCatList = null;
			this.cond.proC = null;
			this.cond.intType = null;
		},
		addFundPro() {
			if (
				_.isEmpty(this.form.invType) &&
				_.isEmpty(this.form.debitType) &&
				_.isEmpty(this.form.fundC) &&
				_.isEmpty(this.form.efficiencyInv) &&
				_.isEmpty(this.form.fundType)
			) {
				this.$bi.alert('請選擇至少一個條件');
				return;
			}
			this.req.invType = this.form.invType?.codeValue ?? null;
			this.req.debitType = this.form.debitType?.codeValue ?? null;
			this.req.fundC = this.form.fundC?.curCode ?? null;
			this.req.efficiencyInv = this.form.efficiencyInv?.codeValue ?? null;
			this.req.fundType = this.form.fundType?.proTypeCode ?? null;

			this.cond.invType = this.form.invType?.codeName ?? null;
			this.cond.debitType = this.form.debitType?.codeName ?? null;
			this.cond.fundC = this.form.fundC?.curName ?? null;
			this.cond.efficiencyInv = this.form.efficiencyInv?.codeName ?? null;
			this.cond.fundType = this.form.fundType?.proTypeName ?? null;
		},
		deleteFundPro() {
			this.req.invType = null;
			this.req.debitType = null;
			this.req.fundC = null;
			this.req.efficiencyInv = null;
			this.req.fundType = null;
			this.cond.invType = null;
			this.cond.debitType = null;
			this.cond.fundC = null;
			this.cond.efficiencyInv = null;
			this.cond.fundType = null;
		},
		addIns: async function () {
			await this.checkRangeRequire(this.form.insAAmountS, this.form.insAAmountE, '累計所繳保費');

			this.req.insAAmountS = this.form.insAAmountS;
			this.req.insAAmountE = this.form.insAAmountE;
			this.req.insType = this.form.insType?.proTypeCode ?? null;
			this.req.insC = this.form.insC?.curCode ?? null;
			this.cond.insType = this.form.insType?.proTypeName ?? null;
			this.cond.insC = this.form.insC?.curName ?? null;
		},
		deleteIns() {
			this.req.insAAmountS = null;
			this.req.insAAmountE = null;
			this.req.insType = null;
			this.req.insC = null;
			this.cond.insType = null;
			this.cond.insC = null;
		},
		addLoanDisbursement: async function () {
			if (_.isEmpty(this.form.loanProCode) && _.isEmpty(this.form.loanRemark)) {
				this.$bi.alert('請選擇申貸商品或貸款註記');
				return;
			}
			await this.checkDateRangeNoRequire(this.form.loanStdDateS, this.form.loanStdDateE, '初貸日期');
			await this.checkRangeNoRequire(this.form.loanPeriodS, this.form.loanPeriodE, '貸款期數');
			await this.checkRangeNoRequire(this.form.appropriationS, this.form.appropriationE, '撥款金額');
			await this.checkRangeNoRequire(this.form.loanOverAmountS, this.form.loanOverAmountE, '貸放餘額');
			await this.checkRangeNoRequire(this.form.conAppropriationS, this.form.conAppropriationE, '撥款金額');
			await this.checkRangeNoRequire(this.form.conRateS, this.form.conRateE, '承作利率');
			await this.checkRangeNoRequire(this.form.conOverAmountS, this.form.conOverAmountE, '貸款餘額');
			await this.checkRangeNoRequire(this.form.conRepaymentRateS, this.form.conRepaymentRateE, '還款率');

			this.req.loanProCode = this.form.loanProCode?.proTypeCode ?? null;
			this.req.loanRemark = this.form.loanRemark?.codeValue ?? null;
			this.req.loanStdDateS = this.form.loanStdDateS;
			this.req.loanStdDateE = this.form.loanStdDateE;
			this.req.loanPeriodS = this.form.loanPeriodS;
			this.req.loanPeriodE = this.form.loanPeriodE;
			this.req.appropriationS = this.form.appropriationS;
			this.req.appropriationE = this.form.appropriationE;
			this.req.loanOverAmountS = this.form.loanOverAmountS;
			this.req.loanOverAmountE = this.form.loanOverAmountE;
			this.req.conAppropriationS = this.form.conAppropriationS;
			this.req.conAppropriationE = this.form.conAppropriationE;
			this.req.conRateS = this.form.conRateS != null ? this.form.conRateS / 100 : null;
			this.req.conRateE = this.form.conRateE != null ? this.form.conRateE / 100 : null;
			this.req.conReturnType = this.form.conReturnType?.codeValue ?? null;
			this.req.conOverAmountS = this.form.conOverAmountS;
			this.req.conOverAmountE = this.form.conOverAmountE;
			this.req.conRepaymentRateS = this.form.conRepaymentRateS != null ? this.form.conRepaymentRateS / 100 : null;
			this.req.conRepaymentRateE = this.form.conRepaymentRateE != null ? this.form.conRepaymentRateE / 100 : null;
			this.cond.loanProCode = this.form.loanProCode?.proTypeName ?? null;
			this.cond.loanRemark = this.form.loanRemark?.codeName ?? null;
			this.cond.conReturnType = this.form.conReturnType?.codeName ?? null;
		},
		deleteLoanDisbursement() {
			this.req.loanProCode = null;
			this.req.loanRemark = null;
			this.req.loanStdDateS = null;
			this.req.loanStdDateE = null;
			this.req.loanPeriodS = null;
			this.req.loanPeriodE = null;
			this.req.appropriationS = null;
			this.req.appropriationE = null;
			this.req.loanOverAmountS = null;
			this.req.loanOverAmountE = null;
			this.req.conAppropriationS = null;
			this.req.conAppropriationE = null;
			this.req.conRateS = null;
			this.req.conRateE = null;
			this.req.conReturnType = null;
			this.req.conOverAmountS = null;
			this.req.conOverAmountE = null;
			this.req.conRepaymentRateS = null;
			this.req.conRepaymentRateE = null;
			this.cond.loanProCode = null;
			this.cond.loanRemark = null;
			this.cond.conReturnType = null;
		},

		addLoanLimit: async function () {
			if (_.isEmpty(this.form.status)) {
				this.$bi.alert('請選擇額度狀態');
				return;
			}
			await this.checkRangeNoRequire(this.form.approveAmountS, this.form.approveAmountE, '核准額度');
			await this.checkRangeNoRequire(this.form.interestRateS, this.form.interestRateE, '計息利率');
			await this.checkRangeNoRequire(this.form.userAmountS, this.form.userAmountE, '使用額度');
			await this.checkRangeNoRequire(this.form.availableAmountS, this.form.availableAmountE, '可用餘額');

			this.req.approveAmountS = this.form.approveAmountS;
			this.req.approveAmountE = this.form.approveAmountE;
			this.req.interestRateS = this.form.interestRateS != null ? this.form.interestRateS / 100 : null;
			this.req.interestRateE = this.form.interestRateE != null ? this.form.interestRateE / 100 : null;
			this.req.status = this.form.status?.codeValue;
			this.req.userAmountS = this.form.userAmountS;
			this.req.userAmountE = this.form.userAmountE;
			this.req.availableAmountS = this.form.availableAmountS;
			this.req.availableAmountE = this.form.availableAmountE;
			this.cond.status = this.form.status?.codeName;
		},
		deleteLoanLimit() {
			this.req.approveAmountS = null;
			this.req.approveAmountE = null;
			this.req.interestRateS = null;
			this.req.interestRateE = null;
			this.req.status = null;
			this.req.userAmountS = null;
			this.req.userAmountE = null;
			this.req.availableAmountS = null;
			this.req.availableAmountE = null;
			this.cond.status = null;
		},
		addSelEx(selEx) {
			var answer = [];
			_.forEach(selEx.queitem, function (e) {
				if (e.answer != null) {
					answer.push(e.answer);
				}
			});

			this.form.queList[selEx.quesectionId] = answer;
		},
		deleteSelEx(selEx) {
			this.form.queList[selEx.quesectionId] = [];
		},
		checkDateRangeRequire(start, end, columnName) {
			return new Promise(function (resolve, reject) {
				if (_.isBlank(start) || _.isBlank(end)) {
					this.$bi.alert('請輸入' + columnName + '範圍');
				} else if (!_.isBlank(start) && !_.isBlank(end) && start > end) {
					this.$bi.alert(columnName + '起日不能大於迄日');
				} else {
					resolve();
				}
			});
		},
		checkDateRangeNoRequire(start, end, columnName) {
			return new Promise(function (resolve, reject) {
				if (_.isBlank(start) && !_.isBlank(end)) {
					this.$bi.alert('請輸入' + columnName + '起日');
				} else if (!_.isBlank(start) && _.isBlank(end)) {
					this.$bi.alert('請輸入' + columnName + '迄');
				} else if (!_.isBlank(start) && !_.isBlank(end) && start > end) {
					this.$bi.alert(columnName + '起日不能大於迄日');
				} else {
					resolve();
				}
			});
		},
		checkRangeRequire(start, end, name) {
			return new Promise(function (resolve, reject) {
				if (_.isBlank(start) || _.isBlank(end)) {
					this.$bi.alert('請輸入' + name + '範圍');
				} else if (_.toNumber(start) > _.toNumber(end)) {
					this.$bi.alert(name + '最小值不能大於最大值');
				} else {
					resolve();
				}
			});
		},
		checkRangeNoRequire(start, end, columnName) {
			return new Promise(function (resolve, reject) {
				if (_.isBlank(start) && !_.isBlank(end)) {
					this.$bi.alert('請輸入' + columnName + '最小值');
				} else if (!_.isBlank(start) && _.isBlank(end)) {
					this.$bi.alert('請輸入' + columnName + '最大值');
				} else if (!_.isBlank(start) && !_.isBlank(end) && _.toNumber(start) > _.toNumber(end)) {
					this.$bi.alert(columnName + '最小值不能大於最大值');
				} else {
					resolve();
				}
			});
		},
		openFundModal() {
			this.$refs.fundModal.show();
		},
		setFundProCode(proCode) {
			this.form.fundProCode = proCode;
		},
		openProModal() {
			this.$refs.proModal.show();
		},
		setProCode(proCode) {
			this.form.proCode = proCode;
		},
		async getAdmCodeDetail(codeType) {
			const resp = await this.$api.getAdmCodeDetail({
				codeType: codeType
			});
			return resp.data;
		},
		async getProCurrenciesList() {
			const resp = await this.$api.getProCurrenciesListApi();
			this.selCurencies = resp.data;
		},
		async getProCat() {
			const resp = await this.$api.getCusSaveResultCountApi({
				paramType: 'CUS',
				paramCode: 'INV_PFCAT_CODE'
			});
			const resp2 = await this.$api.getProCatApi({
				pfcatCodes: resp.data[0].paramValue
			});
			this.selProCat = resp2.data;
		},
		async getProTypes(pfcatCode) {
			const resp = await this.$api.getProTypesApi({
				pfcatCode: pfcatCode
			});
			return resp.data;
		},
		async getExtDataItem() {
			const resp = await this.$api.getExtDataItemApi();
			this.selExDataItem = resp.data;
			var queList = {};
			_.forEach(resp.data, function (e) {
				queList[e.quesectionId] = [];
			});
			this.form.queList = queList;
		},
		checkDay(month, day) {
			var result = true;

			switch (month) {
				case '4':
				case '6':
				case '9':
				case '11':
					result = day !== '31';
					break;
				case '2':
					var number = _.toNumber(day);
					result = _.toNumber(day) < 30;
					break;
			}

			return result;
		},
		async getGrades() {
			const resp = await this.$api.getCusGradesApi();
			this.selGrades = resp.data;
		},
		onUserConditionLoaded() {
			if (_.isEqual(this.userInfo.roleType, 'BM') || _.isEqual(this.userInfo.roleType, 'RM') || _.isEqual(this.userInfo.roleType, 'FC')) {
				this.addBran();
			}
		},
		onNumberKeydown(e) {
			if (e.key === '-') {
				e.preventDefault();
			}
		},
		onNumberInput(fieldName, event) {
			let val = event.target.value;
			let num = Number(val);
			if (val.includes('-') || isNaN(num) || num < 0) {
				event.target.value = '';
				this.form[fieldName] = '';
			}
		}
	}
};
</script>
