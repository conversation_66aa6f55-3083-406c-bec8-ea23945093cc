<template>
	<span v-for="fund in fundList">{{ fund.fundName }}<br /></span>
</template>
<script>
export default {
	props: {
		fundCode: String
	},
	data: function () {
		return {
			fundList: []
		};
	},
	watch: {
		fundCode: {
			handler: function (newVal, oldVal) {
				this.getFundList();
			}
		}
	},
	computed: {},
	created: function () {},
	mounted: function () {
		var self = this;
		self.getFundList();
	},
	methods: {
		getFundList: async function () {
			var self = this;

			const ret = await this.$api.getFundListApi({ fundCode: self.fundCode });
			self.fundList = ret.data;
		}
	}
};
</script>
