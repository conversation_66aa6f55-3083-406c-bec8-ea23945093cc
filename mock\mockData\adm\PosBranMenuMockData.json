{"status": 200, "data": [{"code": "002_00", "name": "石牌分行      _理財專員", "order": 0, "leaf": false, "nodes": [], "posCode": "002_00", "posName": "理財專員", "roleName": "理財專員", "roleCode": "00", "branCode": "002", "branName": "石牌分行      ", "parentBranCode": "6100"}, {"code": "002_01", "name": "石牌分行      _分行經辦", "order": 0, "leaf": false, "nodes": [], "posCode": "002_01", "posName": "分行經辦", "roleName": "分行經辦", "roleCode": "01", "branCode": "002", "branName": "石牌分行      ", "parentBranCode": "6100"}, {"code": "002_02", "name": "石牌分行      _分行幹部", "order": 0, "leaf": false, "nodes": [], "posCode": "002_02", "posName": "分行幹部", "roleName": "分行幹部", "roleCode": "02", "branCode": "002", "branName": "石牌分行      ", "parentBranCode": "6100"}, {"code": "002_03", "name": "石牌分行      _分行經理", "order": 0, "leaf": false, "nodes": [], "posCode": "002_03", "posName": "分行經理", "roleName": "分行經理", "roleCode": "03", "branCode": "002", "branName": "石牌分行      ", "parentBranCode": "6100"}, {"code": "002_04", "name": "石牌分行      _區域督導", "order": 0, "leaf": false, "nodes": [], "posCode": "002_04", "posName": "區域督導", "roleName": "區域督導", "roleCode": "04", "branCode": "002", "branName": "石牌分行      ", "parentBranCode": "6100"}], "timestamp": "2025/04/17", "sqlTracer": [{"data": [{"code": "002_00", "name": "石牌分行      _理財專員", "order": 0, "leaf": false, "nodes": [], "posCode": "002_00", "posName": "理財專員", "roleName": "理財專員", "roleCode": "00", "branCode": "002", "branName": "石牌分行      ", "parentBranCode": "6100"}, {"code": "002_01", "name": "石牌分行      _分行經辦", "order": 0, "leaf": false, "nodes": [], "posCode": "002_01", "posName": "分行經辦", "roleName": "分行經辦", "roleCode": "01", "branCode": "002", "branName": "石牌分行      ", "parentBranCode": "6100"}, {"code": "002_02", "name": "石牌分行      _分行幹部", "order": 0, "leaf": false, "nodes": [], "posCode": "002_02", "posName": "分行幹部", "roleName": "分行幹部", "roleCode": "02", "branCode": "002", "branName": "石牌分行      ", "parentBranCode": "6100"}, {"code": "002_03", "name": "石牌分行      _分行經理", "order": 0, "leaf": false, "nodes": [], "posCode": "002_03", "posName": "分行經理", "roleName": "分行經理", "roleCode": "03", "branCode": "002", "branName": "石牌分行      ", "parentBranCode": "6100"}, {"code": "002_04", "name": "石牌分行      _區域督導", "order": 0, "leaf": false, "nodes": [], "posCode": "002_04", "posName": "區域督導", "roleName": "區域督導", "roleCode": "04", "branCode": "002", "branName": "石牌分行      ", "parentBranCode": "6100"}], "sqlInfo": " SELECT AP.POS_CODE, AP.POS_NAME, AB.BRAN_NAME, AR.ROLE_NAME, AR.ROLE_CODE, AB.BRAN_CODE, AB.PARENT_BRAN_CODE, AB.DEPTHS  FROM  ADM_POSITIONS AP  LEFT JOIN ADM_BRANCHES AB ON AP.BRAN_CODE = AB.BRAN_CODE AND AB.BU_CODE= AP.BU_CODE  LEFT JOIN ADM_ROLES AR ON AP.ROLE_CODE = AR.ROLE_CODE  WHERE AB.BRAN_CODE = :branCode AND AB.BU_CODE = :buCode  ,class com.bi.pbs.adm.web.model.PosBranInfoResp,{branCode=002, buCode=Z}"}]}