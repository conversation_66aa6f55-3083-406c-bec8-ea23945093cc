const gen = [
	{
		// 文件公告訊息分類設定
		path: 'gen/bbsMgtCat',
		name: 'bbsMgtCat',
		component: () => import('../views/gen/GEN0100/bbsMgtCat.vue')
	},
	{
		// 文件公告訊息設定
		path: 'gen/bbsMgt',
		name: 'bbsMgt',
		component: () => import('../views/gen/GEN0101/bbsMgt.vue')
	},
	{
		// 文件公告訊息審核
		path: 'gen/bbsMgtRev/:wfgId',
		name: 'bbsMgtRev',
		component: () => import('../views/gen/GEN0005/bbsMgtRev.vue')
	},
	{
		// 公告及文件下載
		path: 'gen/bbsHead',
		name: 'bbsHead',
		component: () => import('../views/gen/GEN0102/bbsHead.vue')
	},
	{
		// 金融訊息維護
		path: 'gen/docPro',
		name: 'docPro',
		component: () => import('../views/gen/docPro/docPro.vue')
	},
	{
		// 金融：產品文件維護
		path: 'gen/docProDCD',
		name: 'docProDCD',
		component: () => import('../views/gen/docProDCD/docProDCD.vue')
	},
	{
		// 金融：行銷活動
		path: 'gen/docSales',
		name: 'docSales',
		component: () => import('../views/gen/docSales/docSales.vue')
	},
	{
		// 金融：研究報告
		path: 'gen/docResearch',
		name: 'docResearch',
		component: () => import('../views/gen/docResearch/docResearch.vue')
	},
	{
		// 金融：發行機構報告
		path: 'gen/docIssuers',
		name: 'docIssuers',
		component: () => import('../views/gen/GEN122/docIssuers.vue')
	},
	{
		// 金融：新聞事件
		path: 'gen/docNews',
		name: 'docNews',
		component: () => import('../views/gen/GEN123/docNews.vue')
	}
];
export default gen;
