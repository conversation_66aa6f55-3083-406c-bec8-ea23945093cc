<template>
	<div class="col-lg-4 ps-lg-3">
		<div class="d-none d-lg-block">
			<div class="d-grid gap-2 mb-3">
				<button class="btn btn-lg btn-dark btn-glow tx-16 tx-bold d-flex justify-content-between" type="button"
					@click="redirect()">
					<span><img :src="getImgURL('icon', 'ico-multi.png')" />綜合條件查詢</span> <i class="bi bi-shuffle"></i>
				</button>
			</div>
		</div>
		<div class="card card-table">
			<div class="card-header">
				<h4>歷史查詢名單</h4>
				<span class="tx-square-bracket">最多可儲存五組查詢結果</span>
			</div>
			<div class="table-responsive">
				<table class="table table-RWD table-hover text-center">
					<thead>
						<tr>
							<th class="text-start">查詢結果</th>
							<th>建立日期</th>
							<th>執行</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="item in searchHistory">
							<td class="text-start" data-th="查詢結果">
								<a class="tx-link JQ-logView" @click="getSearchHistoryByResultCode(item.resultCode)">{{ item.resultName
								}}</a>
							</td>
							<td data-th="建立日期">{{ item.createDt }}</td>
							<td data-th="執行">
								<button type="button" class="btn tx-danger btn-icon-only JQ-logDelete" data-bs-toggle="tooltip"
									data-bs-original-title="刪除" @click="deleteLog(item)">
									<i class="bi bi-trash"></i>
								</button>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</template>
<script>
import { getImgURL } from '@/utils/imgURL.js';
export default {
	props: {
		queryReq: Object,
		gotoPage: Function
	},
	data: function () {
		return {
			//API 用參數
			userCode: null,

			//主要顯示資料
			searchHistory: []
		};
	},
	watch: {
		userInfo: {
			immediate: true,
			handler: function (newVal, oldVal) {
				var self = this;
				if (newVal) {
					self.userCode = newVal.userCode;
					self.getSearchHistory();
				}
			}
		}
	},
	computed: {
		userInfo: function () {
			return this.$store.getters['userInfo/info'];
		}
	},
	methods: {
		getImgURL,
		getSearchHistory: async function () {
			var self = this;
			const ret = await self.$api.getSearchHistoryApi({
				userCode: self.userCode
			});
			self.searchHistory = ret.data;
		},
		deleteLog: async function (item) {
			var self = this;
			self.$bi.confirm('是否確定刪除' + item.resultName, {
				event: {
					confirmOk: async function () {
						await self.$api.postCusSearchLog({
							userCode: self.userInfo.userCode,
							resultCode: item.resultCode,
							logType: 'D',
							deputyUserCode: self.userInfo.principalUserCode
						});
						await self.$api.deleteSearchResult({
							resultCode: item.resultCode
						});
						self.$bi.alert('刪除成功');
						self.getSearchHistory();
					}
				}
			});
		},
		getSearchHistoryByResultCode: function (resultCode) {
			var self = this;
			Object.keys(self.queryReq).forEach((key) => delete self.queryReq[key]);
			self.queryReq.queryType = 'RESULT_CODE';
			self.queryReq.resultCode = resultCode;
			self.gotoPage(0);
		},
		redirect: function () {
			var self = this;
			self.$router.push('/cus/complexSearch');
		}
	}
};
</script>
