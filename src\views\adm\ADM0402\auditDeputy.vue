<template>
	<dynamic-title></dynamic-title>
	<div>
		<div class="row">
			<div class="col-12">
				<div class="card card-form">
					<div class="card-body">
						<vue-form @submit="getDeputiesLog" v-slot="{ errors }" ref="queryForm">
							<div class="form-row">
								<div class="form-group col-lg-6 col-12">
									<label class="form-label" style="min-width: 120px !important">查詢分行(單位)：</label><br />
									<div class="input-group">
										<select name="areaBranCode" id="areaBranCode" class="form-select" v-model="groupCode">
											<option value="">全部</option>
											<option v-for="item in areaList" :value="item.branCode">{{ item.branCode }} {{ item.branName }}</option>
										</select>
									</div>
								</div>
								<div class="form-group col-lg-6 col-12">
									<select name="branCode" id="branCode" class="form-select" v-model="branCode">
										<option value="">全部</option>
										<option v-for="item in branList" :value="item.branCode">{{ item.branCode }} {{ item.branName }}</option>
									</select>
								</div>
								<div class="form-group col-lg-6 col-12">
									<label class="form-label" style="min-width: 120px !important">代理人/被代理人：</label><br />
									<div class="input-group">
										<select name="deputyUser" id="deputyUser" class="form-select" v-model="deputyUser">
											<option :value="null">請選擇</option>
											<option v-for="item in deputies" :value="item.userCode">{{ item.userCode }} {{ item.userName }}</option>
										</select>
									</div>
								</div>
								<div class="form-group col-lg-6 col-12">
									<label class="form-label tx-require" style="min-width: 120px !important">代理日期區間：</label><br />
									<div class="input-group">
										<input type="date" name="beginDate" value="" id="beginDate" class="form-control" v-model="stdDt" />
										<span class="input-group-text">~</span>
										<input type="date" name="endDate" value="" id="endDate" class="form-control" v-model="endDt" />
									</div>
								</div>
							</div>
							<div class="form-footer">
								<button type="submit" class="btn btn-primary btn-search">查詢</button>
							</div>
						</vue-form>
					</div>
				</div>
				<div id="searchResult">
					<div class="card card-table">
						<div class="card-header">
							<h4>代理人設定紀錄列表</h4>
						</div>
						<div class="table-responsive">
							<table class="table table-RWD table-hover text-center">
								<thead>
									<tr>
										<th>維護日期</th>
										<th>維護人員</th>
										<th>維護類型</th>
										<th>
											被代理者 <br />
											(請假人員)
										</th>
										<th>被代理者角色</th>
										<th>代理者</th>
										<th>代理者角色</th>
										<th>所屬分行</th>
										<th>代理起始日</th>
										<th>代理终止日</th>
										<th>代理天數 <br /></th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="item in userDeputiesLog">
										<td>{{ item.createDt }}</td>
										<td>{{ item.createBy }} {{ item.createName }}</td>
										<td>{{ item.codeName }}</td>
										<td>{{ item.userCode }} {{ item.userName }}</td>
										<td>{{ item.roleNames }}</td>
										<td>{{ item.deputyUserCode }} {{ item.deputyUserName }}</td>
										<td>{{ item.deputyRoleNames }}</td>
										<td>{{ item.branName }}</td>
										<td>{{ item.stdDt }}</td>
										<td>{{ item.endDt }}</td>
										<td>{{ $filters.formatAmt(item.totDepDays) }}</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
					<div class="tx-note">代理天數為日曆天</div>
				</div>
			</div>
		</div>

		<!--頁面內容 end-->
	</div>
</template>
<script>
import _ from 'lodash';
import Swal from 'sweetalert2';
import { Field, Form } from 'vee-validate';
import dynamicTitle from '@/views/components/dynamicTitle.vue';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		dynamicTitle
	},
	data: function () {
		return {
			stdDt: null,
			endDt: null,
			//主要顯示資料
			userDeputiesLog: [],
			areaList: [],
			branList: [],
			deputies: [],
			deputyUser: '',
			groupCode: '',
			branCode: ''
		};
	},
	watch: {
		groupCode: function (newVal) {
			this.branCode = '';
			this.branList = [];
			if (newVal != null) {
				this.getBranList();
			}
		},
		branCode: function (newVal) {
			this.deputyUser = null;
			this.deputies = [];
			if (newVal != null) {
				this.getDeputies();
			}
		}
	},
	computed: {
		userInfo: function () {
			return this.$store.getters['userInfo/info'].data;
		}
	},
	mounted: function () {
		var self = this;
		var now = new Date();
		self.endDt = _.formatDate(now).replaceAll('/', '-');
		self.stdDt = _.formatDate(now.setMonth(now.getMonth() - 1)).replaceAll('/', '-');
		self.getAreaList();
	},
	methods: {
		getDeputiesLog: function () {
			var self = this;

			if (_.isBlank(self.stdDt) || _.isBlank(self.endDt)) {
				Swal.fire({
					icon: 'error',
					text: '請輸入日期區間',
					showCloseButton: true,
					confirmButtonText: '確認',
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			} else if (self.stdDt > self.endDt) {
				Swal.fire({
					icon: 'error',
					text: '查詢起日需小於迄日',
					showCloseButton: true,
					confirmButtonText: '確認',
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}

			self.$api
				.getDeputiesLogApi({
					parentBranCode: self.groupCode,
					branCode: self.branCode,
					userCode: self.deputyUser,
					stdDt: _.formatDate(self.stdDt),
					endDt: _.formatDate(self.endDt)
				})
				.then(function (resp) {
					self.userDeputiesLog = resp.data;
				});
		},
		getAreaList: function () {
			var self = this;
			if (_.isEqual(self.userInfo.roleType, 'HQ')) {
				this.getAdmBranches(['10', '50']).then(function (resp) {
					self.areaList = resp.data;
				});
			} else {
				var self = this;
				self.$api.getMinorAreaApi({}).then(function (ret) {
					if (ret.data) {
						self.areaList = ret.data;
					}
				});
			}
		},
		getBranList: function () {
			var self = this;
			if (_.isEqual(self.userInfo.roleType, 'HQ')) {
				this.getAdmBranches(null, self.groupCode).then(function (resp) {
					self.branList = resp.data;
				});
			} else {
				var self = this;
				self.$api
					.getBranchesApi({
						minorCode: self.groupCode
					})
					.then(function (ret) {
						if (ret.data) {
							self.branList = ret.data;
						}
					});
			}
		},
		getAdmBranches: function (branLvlCode = null, parentBranCode = null) {
			var self = this;
			return self.$api.getAdmBranchesApi({
				parentBranCode: parentBranCode,
				branLvlCode: branLvlCode
			});
		},
		getDeputies: function () {
			var self = this;
			self.$api
				.getDeputiesApi({
					branCode: self.branCode
				})
				.then(function (ret) {
					if (ret.data) {
						self.deputies = ret.data;
					}
				});
		}
	}
};
</script>
