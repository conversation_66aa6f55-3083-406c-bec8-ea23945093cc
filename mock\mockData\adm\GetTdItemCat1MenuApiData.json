{"status": 200, "data": [{"tdCat1Code": "TDCAT101", "tdCat1Name": "交易類"}, {"tdCat1Code": "TDCAT102", "tdCat1Name": "客戶類"}, {"tdCat1Code": "TDCAT103", "tdCat1Name": "商品類"}, {"tdCat1Code": "TDCAT104", "tdCat1Name": "法遵類"}], "timestamp": "2025/07/16", "sqlTracer": [{"data": [{"tdCat1Code": "TDCAT101", "tdCat1Name": "交易類"}, {"tdCat1Code": "TDCAT102", "tdCat1Name": "客戶類"}, {"tdCat1Code": "TDCAT103", "tdCat1Name": "商品類"}, {"tdCat1Code": "TDCAT104", "tdCat1Name": "法遵類"}], "sqlInfo": " SELECT TDCAT1_CODE,TDCAT1_NAME FROM WOB_TDITEM_CAT1 ,class com.bi.pbs.wob.web.model.TdItemCat1Resp,[Ljava.lang.Object;@6ecbf504"}]}