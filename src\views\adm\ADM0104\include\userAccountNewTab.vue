<template>
	<div>
		<div role="tabpanel" class="tab-pane fade show active">
			<div class="card card-form mb-0">
				<div class="card-header">
					<h4>使用者基本資料</h4>
					<span class="tx-square-bracket">為必填欄位</span>
				</div>
				<div class="card-body">
					<div class="row g-3 align-items-end">
						<table class="table table-RWD table-horizontal-RWD table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p tx-require">使用者代碼：</th>
									<td class="wd-35p">
										<div class="d-inline-block">
											<div class="input-group">
												<input @input="handleUserCodeInput" type="text" class="form-control" v-model="userCode" />
												<button class="btn btn-primary btn-search" type="button" @click="getEditUserInfo()">查詢</button>
											</div>
										</div>
									</td>
									<th class="wd-15p tx-require">帳號可用性：</th>
									<td class="wd-35p">
										<div>
											<Form v-slot="{ errors }" ref="queryForm">
												<div class="btn-group btn-group-sm">
													<input
														type="radio"
														name="startFlag"
														class="btn-check"
														id="rdoOn"
														autocomplete="off"
														value="Y"
														v-model="startFlag"
														@click="rdoOn()"
														label="帳號可用性"
														rules="required"
													/>
													<label class="btn btn-outline-success" for="rdoOn">
														<span class="icon">
															<i class="bi bi-check-circle"></i>
															<span class="text">啟用</span>
														</span>
													</label>
													<input
														type="radio"
														name="startFlag"
														class="btn-check"
														id="rdoOff"
														autocomplete="off"
														value="N"
														v-model="startFlag"
														@click="rdoOff()"
														label="帳號可用性"
														rules="required"
													/>
													<label class="btn btn-outline-danger" for="rdoOff">
														<span class="icon">
															<i class="bi bi-x-circle"></i>
															<span class="text">關閉</span>
														</span>
													</label>
													<div style="height: 25px">
														<span class="text-danger" v-show="errors.startFlag">{{ errors.startFlag }}</span>
													</div>
												</div>
											</Form>
										</div>
									</td>
								</tr>
								<tr>
									<th class="wd-15p">使用者姓名：</th>
									<td class="wd-35p">
										<span class="JQdata-show tx-16" v-if="editUserInfo != null">{{ editUserInfo.userName }}</span>
									</td>
									<th class="wd-15p">所屬單位：</th>
									<td class="wd-35p">
										<span class="JQdata-show tx-16" v-if="editUserInfo != null">{{ editUserInfo.branName }}</span>
									</td>
								</tr>
								<tr>
									<th class="wd-15p">角色設定：</th>
									<td class="wd-35p">
										<span class="JQdata-show d-inline-block" v-if="editUserInfo != null">
											<div class="input-group">
												<input
													type="text"
													id="roleBranName"
													name="roleBranName"
													value=""
													class="form-control"
													readonly
													v-model="branName"
												/>
												<button
													id="chooseBranchId"
													type="button"
													class="btn btn-info"
													:disabled="!isHeadOffice"
													@click="getBranMenu"
												>
													選擇分行(單位)
												</button>
												<button type="button" class="btn btn-white" id="clean" @click="clearForm">清空</button>
											</div>

											<ul class="list-group list-inline-tags" id="job">
												<li class="list-group-item" v-for="(item, index) in userBranPositions" :key="index">
													<a href="javascript:void(0)">
														{{ item.branName }}-&gt;{{ item.roleName }}
														<span
															class="img-delete JQ-delet"
															data-bs-toggle="tooltip"
															@click="deletePos(item)"
															title="刪除"
														></span>
													</a>
												</li>
											</ul>
										</span>
										<button
											id="choosePositionId"
											type="button"
											class="btn btn-info"
											:disabled="!isHeadOffice"
											@click="getRoleMenu"
										>
											選擇角色
										</button>
									</td>
									<th class="wd-15p">資料建立日期：</th>
									<td class="wd-35p">
										<span class="JQdata-show tx-16" v-if="editUserInfo != null">{{ editUserInfo.createDt }}</span>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
			<div class="tx-note">請輸入使用者代碼，按查詢會自動帶出使用者資訊</div>
			<div class="text-end">
				<input name="Submit" type="button" id="btn" class="btn btn-primary btn-lg btn-glow" @click="audit" value="提交審核" />
			</div>
		</div>
		<!--Tab內容 end-->

		<!-- Bran Modal -->
		<modal ref="modal" :before-close="closeModal">
			<template v-slot:content="props">
				<div class="modal-dialog modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title" id="selectModal">{{ modalTitle }}</h4>
							<button type="button" class="btn-close" @click="props.close()" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<table class="table table-bordered">
								<tbody>
									<tr>
										<td>
											<button type="button" class="btn btn-info btn-glow" @click="expandBtn">全部展開</button>
											<button type="button" class="btn btn-info btn-glow" @click="collapsedBtn">全部收合</button>
										</td>
									</tr>
									<tr>
										<td>
											<div id="biTreeIdNew"></div>
											<bi-tree v-if="treeData.data" ref="tree" :treeData="treeData"></bi-tree>
											<div>
												<div id="treeview-noborder"></div>
											</div>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
						<div class="modal-footer">
							<button @click="props.close()" type="button" class="btn btn-white">關閉視窗</button>
						</div>
					</div>
				</div>
			</template>
		</modal>
		<!-- Modal 1 End -->
	</div>
</template>

<script>
import { Form, Field } from 'vee-validate';
import modal from '@/views/components/model.vue';
import biTree from '@/views/components/biTree.vue';

export default {
	components: {
		Form,
		Field,
		modal,
		biTree
	},
	props: {
		editAccount: {
			type: Object,
			default: {}
		}
	},
	data: function () {
		return {
			//Api 用參數
			userCode: null,
			buCode: null,
			branCode: null,
			startFlag: 'Y',
			originStartFlag: null,
			userCodeZeroLength: 6,
			deletePosCodes: [],
			posCode: null,
			branName: null,
			roleName: null,

			//畫面顯示用參數
			modalTitle: null,

			//Api邏輯判斷用參數
			editUserInfo: null,
			origUserBranPositions: [],

			//主要顯示資料
			userBranPositions: [],
			isHeadOffice: true,

			treeData: {}
		};
	},
	watch: {
		editAccount: {
			handler(newVal, oldVal) {
				if (this.$_.isBlank(newVal.userCode)) {
					return;
				}
				this.userCode = newVal.userCode;
				this.getEditUserInfo();
			},
			immediate: true,
			deep: true
		}
	},
	methods: {
		openModal() {
			this.$refs.modal.open();
		},
		closeModal() {
			this.$refs.modal.close();
		},

		getEditUserInfo: async function () {
			this.origUserBranPositions = [];
			let ret = await this.$api.getEditUserInfoApi(this.userCode);
			this.editUserInfo = ret.data;
			this.startFlag = ret.data.startFlag;
			this.originStartFlag = ret.data.startFlag;
			this.intUserPosBranInfo(this.userCode);
		},
		handleUserCodeInput: function () {
			if (this.userCode.length > this.userCodeZeroLength) {
				this.userCode = this.userCode.slice(-1 * this.userCodeZeroLength); // 只保留最後 N 位數字
			}
			this.userCode = this.userCode.padStart(this.userCodeZeroLength, '0');
		},
		expandBtn: function () {
			this.$refs.tree.expandAllNodes(true);
		},
		collapsedBtn: function () {
			this.$refs.tree.expandAllNodes(false);
		},
		intUserPosBranInfo: async function (userCode) {
			let ret = await this.$api.getUserBranPosInfoApi(userCode);
			this.userBranPositions = ret.data;
			this.userBranPositions.forEach((varItem) => {
				this.origUserBranPositions.push(varItem);
			});
		},
		getBranMenu: async function () {
			this.modalTitle = '選擇分行(單位)';
			let ret = await this.$api.getBranMenuApi();

			this.branMenuData = ret.data;
			this.branMenuData = this.genTreeLabel(this.branMenuData);

			this.treeData = {
				data: this.branMenuData,
				tailLinks: [
					{
						type: 'anchor',
						onClick: (id, node) => {
							let name = node.querySelector('.bi-tree-text')?.textContent || '';
							let idArr = this.$_.split(id, '_', 2);
							this.buCode = idArr[0];
							this.branCode = idArr[1];
							this.branName = name;
							this.closeModal();
						},
						text: '選擇',
						toApply: true
					}
				]
			};

			this.openModal();
		},
		genTreeLabel: function (data) {
			data.forEach((item) => {
				item.text = item.branName;
				if (!this.$_.isEmpty(item.nodes)) {
					this.genTreeLabel(item.nodes);
				}
			});

			return data;
		},
		getRoleMenu: async function () {
			if (this.$_.isEmpty(this.branCode)) {
				this.$bi.alert('請先選擇單位。');
				return;
			}

			this.modalTitle = '選擇角色';

			let ret = await this.$api.getPosBranMenuApi(this.branCode, this.buCode);

			if (this.$_.isBlank(ret.data) || this.$_.isEmpty(ret.data)) {
				this.$bi.alert('無可選擇的職位。');
				return;
			}

			this.roleMenuData = ret.data;

			this.treeData = {
				data: this.roleMenuData,
				tailLinks: [
					{
						type: 'anchor',
						onClick: (id, node) => {
							const name = node.querySelector('.bi-tree-text')?.textContent || '';
							let branPosData = { posCode: null, roleName: null, branName: null };
							branPosData.posCode = id;

							let nameArr = this.$_.split(name, '_', 3);
							branPosData.branName = nameArr[0];
							branPosData.roleName = nameArr[1];
							if (nameArr[2]) {
								branPosData.roleName = branPosData.roleName + '_' + nameArr[2];
							}

							let isValidate = true;
							this.userBranPositions.forEach((item) => {
								if (item.posCode == branPosData.posCode) {
									this.$bi.alert('此角色已重複。');
									isValidate = false;
									return;
								}
							});

							if (isValidate) {
								this.userBranPositions.push(branPosData);
							}
							this.closeModal();
						},
						text: '選擇',
						toApply: true
					}
				]
			};

			this.openModal();
		},
		clearForm: function () {
			this.branName = null;
			this.branCode = null;
			this.buCode = null;
			this.posCode = null;
		},
		deletePos: function (target) {
			if (!this.isHeadOffice) return;

			this.userBranPositions.forEach(function (item, index, arr) {
				if (item.branCode == target.branCode && item.posCode == target.posCode) {
					arr.splice(index, 1);
				}
			});
		},
		audit: function () {
			let posCodes = [];
			let originalPosCodes = [];

			for (let i = 0; i < this.userBranPositions.length; i++) {
				posCodes.push(this.userBranPositions[i].posCode);
			}

			for (let i = 0; i < this.origUserBranPositions.length; i++) {
				originalPosCodes.push(this.origUserBranPositions[i].posCode);
			}

			if (this.$_.isEqual(posCodes, originalPosCodes) && this.$_.isEqual(this.originStartFlag, this.startFlag)) {
				this.$bi.alert('尚未修改，不可提交審核。');
				return;
			}

			this.findDeletePosCodes(posCodes);
			this.$refs.queryForm.validate().then(async (pass) => {
				if (pass.valid) {
					let ret = await this.$api.postUserAccountApi(
						this.userCode,
						this.buCode,
						this.branCode,
						posCodes,
						this.deletePosCodes,
						this.startFlag
					);

					this.$_.handleWkfResp(ret.data, false);
					setTimeout(() => {
						this.$router.push('/adm/userAccount');
					}, 5000);
				}
			});
		},
		findDeletePosCodes: function (posCodes) {
			this.deletePosCodes = [];
			let origPosCodes = [];
			for (let i = 0; i < this.origUserBranPositions.length; i++) {
				origPosCodes.push(this.origUserBranPositions[i].posCode);
			}

			for (let i = 0; i < origPosCodes.length; i++) {
				if (!posCodes.includes(origPosCodes[i])) {
					this.deletePosCodes.push(origPosCodes[i]);
				}
			}
		},
		rdoOn: function () {
			this.startFlag = 'Y';
		},
		rdoOff: function () {
			this.startFlag = 'N';
		}
	}
};
</script>
