<template>
	<dynamicTitle />
	<div>
		<div class="row">
			<div class="col-12">
				<vue-bi-tabs :menu-code="'M41-02'" @change-tab="changeTab" :tab-name-decorator="showNameDecorator" ref="tab">
					<template #default="{ id }">
						<component :is="id" :selected-type-code="selectedTypeCode"></component>
					</template>
				</vue-bi-tabs>
			</div>
		</div>
	</div>
</template>
<script>
import dynamicTitle from '@/views/components/dynamicTitle.vue';
import vueBiTabs from '@/views/components/biTabs.vue';
import vueDocSales from './include/docSales.vue';
export default {
	components: {
		vueBiTabs,
		dynamicTitle,
		vueDocSales
	},
	data: function () {
		return {
			docMktMainCnt: [],
			tabCodeMainTypeMap: {
				'M41-020': 'DMTME04', // 行銷活動
				'M41-021': 'DMTME02', // 新商品研討會
				'M41-022': 'DMTME01', // 市場研討會
				'M41-023': 'DMTME05', // 專案活動
				'M41-024': 'DMTME03' // 高資產客戶專屬
			},
			selectedTypeCode: null
		};
	},
	mounted: function () {
		var self = this;
		self.getDocMktMainCnt();
	},
	methods: {
		async getDocMktMainCnt() {
			var self = this;
			const ret = await self.$api.getDocMktMainCntApi();
			if (!ret.data?.length > 0) return;
			self.docMktMainCnt = ret.data;
			self.selectedTypeCode = self.tabCodeMainTypeMap[self.$refs.tab.selectedCode];
		},
		changeTab: function (tabCode) {
			var self = this;
			self.selectedTypeCode = self.tabCodeMainTypeMap[tabCode];
		},
		showNameDecorator: function (tab) {
			var self = this;
			var docOutLookMainCnt = self.docMktMainCnt.filter((t) => t.typeCode === self.tabCodeMainTypeMap[tab.code]);
			var newName = tab.name;
			if (docOutLookMainCnt && docOutLookMainCnt.length > 0) {
				newName = newName + ' (' + (docOutLookMainCnt[0].cnt || '0') + ')';
			}
			return newName;
		}
	}
};
</script>
