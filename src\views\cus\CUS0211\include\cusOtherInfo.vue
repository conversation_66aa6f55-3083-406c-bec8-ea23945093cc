<template>
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<div :id="'dataFormDiv' + que.quesectionId" v-for="que in quesection">
				<div class="card card-table">
					<div class="card-header">
						<h4>{{ que.quesectionName }}</h4>
					</div>
					<div class="table-responsive">
						<table class="table table-bordered">
							<tbody>
								<tr v-for="queItem in que.queitem">
									<th colspan="2" class="wd-15p">{{ queItem.queitemName }}</th>
									<td>
										<div v-for="queItemSel in queItem.itemsel" class="form-check-inline">
											<input
												class="form-check-input"
												v-if="queItem.queitemType == 'CHECK'"
												type="checkbox"
												:id="'extData' + queItem.queitemId"
												maxlength="100"
												:name="'extData' + queItem.queitemId"
												:value="queItemSel.queitemselId"
												v-model="queItem.checkedItemSel"
												@click="checkTextAble($event, queItemSel.queitemselId)"
												:disabled="!auth"
											/>
											<input
												class="form-check-input"
												v-if="queItem.queitemType == 'RADIO'"
												type="radio"
												:id="'extData' + queItem.queitemId"
												maxlength="100"
												:name="'extData' + queItem.queitemId"
												:value="queItemSel.queitemselId"
												v-model="queItem.checkedRadioItem"
												@click="checkTextAble($event, queItemSel.queitemselId)"
												:disabled="!auth"
											/>
											&nbsp;
											<label v-if="queItem.queitemType != 'TEXT'">{{ queItemSel.queitemselName }}</label
											>&nbsp;&nbsp;
											<input
												v-if="queItemSel.inputYn === 'Y' && queItem.queitemType !== 'TEXT'"
												style="width: 100px"
												class="form-control"
												:maxlength="queItemSel.inputCols"
												:id="'ansText' + queItemSel.queitemselId"
												:name="'ansText' + queItemSel.queitemselId"
												size="40"
												type="text"
												v-model="queItemSel.ansText"
												:disabled="initTextAble(queItem, queItemSel.queitemselId)"
											/>
											<input
												v-if="queItem.queitemType === 'TEXT'"
												class="form-control"
												:maxlength="queItemSel.inputCols"
												:id="'ansText' + queItemSel.queitemselId"
												:name="'ansText' + queItemSel.queitemselId"
												size="40"
												type="text"
												v-model="queItemSel.ansText"
											/>
										</div>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
			<div id="extDataLogForm">
				<div class="card card-table">
					<div class="card-header">
						<h4>異動紀錄</h4>
					</div>
					<div class="table-responsive">
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th colspan="2" class="wd-15p">最近更新日期</th>
									<td>
										<span v-if="extDataLog">{{ $filters.formatDate(extDataLog.createDt) }}</span>
									</td>
								</tr>
								<tr>
									<th colspan="2" class="wd-15p">更新人員</th>
									<td>
										<span v-if="extDataLog">{{ extDataLog.userCode }} {{ extDataLog.userName }}</span>
									</td>
								</tr>
								<tr>
									<th colspan="2" class="wd-15p">CACODE</th>
									<td>
										<span v-if="extDataLog">{{ extDataLog.aoCode }} {{ extDataLog.aoName }}</span>
									</td>
								</tr>
								<tr>
									<th colspan="2" class="wd-15p">所屬單位</th>
									<td>
										<span v-if="extDataLog">{{ extDataLog.branCode }} {{ extDataLog.branName }}</span>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
			<div class="text-end mt-3" v-if="auth">
				<input name="Submit" type="button" id="btn" class="btn btn-primary btn-lg btn-glow" value="儲存" @click="updateExtDataAns()" />
			</div>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		cusCode: null
	},
	data: function () {
		return {
			quesection: [],
			extDataAnswers: [],
			extDataLog: null,
			authYn: null
		};
	},
	mounted: function () {
		var self = this;
		self.getQuesectionMenu();
		self.getExtDataLog();
		self.chkCustomerAuth();
	},
	computed: {
		auth: function () {
			return this.authYn === 'Y';
		}
	},
	methods: {
		getQuesectionMenu: async function () {
			var self = this;
			const ret = await self.$api.getExtDataItemApi();
			self.quesection = ret.data;
			self.getExtDataAnswers();
		},
		getExtDataAnswers: async function () {
			var self = this;
			const ret = await self.$api.getExtDataAnswersApi({
				cusCode: self.cusCode
			});
			self.extDataAnswers = ret.data;
			self.setExtDataAns();
		},
		getExtDataLog: async function () {
			var self = this;
			const ret = await self.$api.getExtDataLogApi({ cusCode: self.cusCode });
			self.extDataLog = ret.data.length > 0 ? ret.data[0] : {};
		},
		setExtDataAns: function () {
			var self = this;
			var ansMap = _.groupBy(self.extDataAnswers, 'queitemId');

			self.quesection.forEach(function (quesection) {
				quesection.queitem.forEach(function (queItem) {
					if (!queItem.checkedItemSel) {
						queItem.checkedItemSel = [];
					}

					var ans = ansMap[queItem.queitemId];

					if (ans != null) {
						if (queItem.queitemType === 'CHECK') {
							ans.forEach(function (ansItem) {
								queItem.checkedItemSel.push(ansItem.queitemselId);
							});
						} else if (queItem.queitemType === 'RADIO') {
							queItem.checkedRadioItem = ans[0].queitemselId;
						} else if (queItem.queitemType === 'TEXT') {
							queItem.itemsel[0].ansText = ans[0].ansText;
						}

						if (queItem.queitemType === 'CHECK' || queItem.queitemType === 'RADIO') {
							var selMap = _.groupBy(ans, 'queitemselId');

							queItem.itemsel.forEach(function (itemsel) {
								if (itemsel.inputYn === 'Y' && selMap[itemsel.queitemselId] != null) {
									itemsel.ansText = selMap[itemsel.queitemselId][0].ansText;
								}
							});
						}
					}
				});
			});
		},
		updateExtDataAns: async function () {
			var self = this;
			var extDataAns = [];

			await self.quesection.forEach(async function (quesection) {
				quesection.queitem.forEach(function (queItem) {
					var reqQueitem = {
						queitemId: queItem.queitemId,
						queitemsel: []
					};
					var anaTextObjMap = {};
					queItem.itemsel.forEach(function (queItemSel) {
						if (queItemSel.inputYn === 'Y') {
							var anaTextObj = {};
							anaTextObj.queItemSelId = queItemSel.queitemselId;
							anaTextObj.ansText = queItemSel.ansText;
							anaTextObjMap[queItemSel.queitemselId] = anaTextObj;
						}
					});

					if (queItem.queitemType === 'CHECK') {
						queItem.checkedItemSel.forEach(function (ans) {
							var reqQueitemsel = {};
							reqQueitemsel.queitemselId = ans;

							if (anaTextObjMap[ans] != null) {
								reqQueitemsel.ansText = anaTextObjMap[ans].ansText;
							}

							reqQueitem.queitemsel.push(reqQueitemsel);
						});
					}

					if (queItem.queitemType === 'RADIO' && !_.isBlank(queItem.checkedRadioItem)) {
						var reqQueitemsel = {};
						reqQueitemsel.queitemselId = queItem.checkedRadioItem;

						if (anaTextObjMap[reqQueitemsel.queitemselId] != null) {
							reqQueitemsel.ansText = anaTextObjMap[reqQueitemsel.queitemselId].ansText;
						}

						reqQueitem.queitemsel.push(reqQueitemsel);
					}

					if (queItem.queitemType === 'TEXT') {
						queItem.itemsel.forEach(function (itemsel) {
							var reqQueitemsel = {
								ansText: itemsel.ansText,
								queitemselId: itemsel.queitemselId
							};
							reqQueitem.queitemsel.push(reqQueitemsel);
						});
					}

					extDataAns.push(reqQueitem);
				});
			});
			const ret = await self.$api.updateExtDataAnsApi({
				cusCode: self.cusCode,
				queitem: extDataAns
			});
			self.getExtDataLog();
			self.$bi.alert('更新成功');
		},
		checkTextAble: function (event, queItemSelId) {
			$('#ansText' + queItemSelId).prop('disabled', true);
			if (event.target.checked) {
				$('#ansText' + queItemSelId).prop('disabled', false);
			} else {
				$('#ansText' + queItemSelId).val('');
			}
		},
		initTextAble: function (queItem, queItemSelId) {
			var self = this;
			//判斷是否為經管PBC
			if (!self.authYn) {
				//不是直接退出
				return true;
			}

			if (queItem.queitemType == 'CHECK' && queItem.checkedItemSel.includes(queItemSelId)) {
				return false;
			} else if (queItem.queitemType == 'RADIO' && queItem.checkedRadioItem == queItemSelId) {
				return false;
			} else if (queItem.queitemType == 'TEXT') {
				return false;
			} else {
				return true;
			}
		},
		chkCustomerAuth: async function () {
			var self = this;

			const resp = await self.$api.chkCustomerAuthApi({
				cusCode: self.cusCode,
				progCode: 'ACUS_003'
			});
			self.authYn = resp.data.authYn;
		}
	}
};
</script>
