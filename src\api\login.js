import request from '@/utils/request';

const apiPath = import.meta.env.VITE_API_URL_V1;

// Login.vue
export function recaptchaVerifiedApi(token) {
	return request({
		url: apiPath + '/captcha/verify',
		method: 'post',
		data: {
			token
		}
	});
}

export function loginApi(tenantNumber, username, password) {
	return request({
		url: apiPath + '/login',
		data: {
			tenantNumber,
			username,
			password
		}
	});
}

// SelectPos.vue
export function getUserRolesApi() {
	return request({
		url: apiPath + '/login/user/positions',
		method: 'get'
	});
}

// login authenticate

export function authenticate(posCode) {
	return request({
		url: apiPath + '/login/user/authenticate',
		method: 'post',
		data: {
			posCode
		}
	});
}
