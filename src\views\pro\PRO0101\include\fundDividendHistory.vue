<template>
	<div class="col-12" v-if="dividends && dividends.length > 0">
		<h4>配息資料線圖</h4>
		<!-- <vue-fund-column-line-chart ref-div="dividendColumnLine" div-class="chart-size" :chart-config="chartConfig" :chart-data="chartData"></vue-fund-column-line-chart> -->
		<table width="100%" class="table table-bordered">
			<tr>
				<th>除息日</th>
				<th>發放日</th>
				<th>息值或比例</th>
				<th>年化配息率(%)</th>
				<th>幣別</th>
			</tr>
			<tr v-for="(item, i) in _.orderBy(dividends, ['xdDate'], ['desc'])">
				<td>{{ $filters.formatDate(item.xdDate) }}</td>
				<td>{{ $filters.formatDate(item.paymentDate) }}</td>
				<td>{{ $filters.defaultValue($filters.formatNum(item.paymentLc, '0,0.[00]'), '--') }}</td>
				<td style="color: #e7505a">{{ $filters.defaultValue($filters.formatNum(item.annDivLc, '0,0.[00]'), '--') }}</td>
				<td>{{ localCurrencyName }}</td>
			</tr>
		</table>
	</div>
</template>
<script>
import _ from 'lodash';
export default {
	props: {
		fundCode: String,
		localCurrencyName: String
	},
	data: function () {
		return {
			chartConfig: {
				axisName: ['配息金額', '配息率'],
				series: [
					{
						seriesValue: 'y1',
						seriesName: '配息金額/單位數',
						seriesType: 'line',
						tooltipText: '配息金額: [bold]{valueY}[/]'
					},
					{
						seriesValue: 'y2',
						seriesName: '配息率',
						tooltipText: '配息率: [bold]{valueY}%[/]'
					}
				],
				seriesFieldX: 'x',
				labels: {
					truncate: true,
					maxWidth: 200,
					rotation: -45,
					horizontalCenter: 'right',
					verticalCenter: 'middle'
				}
			},
			dividends: []
		};
	},
	watch: {
		fundCode: {
			handler: function (newVal, oldVal) {
				this.getDividends();
			}
		}
	},
	computed: {
		chartData: function () {
			return _.map(this.dividends, function (item) {
				return {
					x: item.paymentDate,
					y1: item.paymentLc,
					y2: item.annDivLc
				};
			});
		}
	},
	mounted: function () {
		var self = this;
		self.getDividends();
	},
	methods: {
		getDividends: async function () {
			var self = this;
			const ret = await getDividendsApi({
				fundCode: self.fundCode
			});
			self.dividends = _.orderBy(ret.data, ['xdDate']);
		}
	}
};
</script>
