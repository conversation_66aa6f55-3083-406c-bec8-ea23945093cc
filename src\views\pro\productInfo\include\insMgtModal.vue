<template>
	<!-- Modal 2 保險-->
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">人身保險</h4>
				<button type="button" class="btn-close" @click.prevent="close()" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="card card-prointro shadow-none mb-3">
					<div class="pro_left">
						<div class="pro_name">
							<div class="avatar avatar-md avatar-insurance"></div>
							<h4>
								<span>商品名稱</span> <br />{{ proInfo.proName }} <br /><span class="tx-black">{{ proInfo.proEName }}</span>
							</h4>
						</div>
					</div>
					<div class="pro_class">
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6>
								<span>商品代碼</span>
								<br />{{ proInfo.bankProCode }}
							</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>資產類別</span><br />{{ proInfo.assetcatName }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品主類</span><br />{{ proInfo.pfcatName }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品次類</span><br />{{ proInfo.proTypeName }}</h6>
						</div>
					</div>
				</div>

				<div class="tab-nav-pills">
					<ul class="nav nav-pills justify-content-center" role="tablist">
						<li class="nav-item"><a class="nav-link active" href="#Sectionins1" data-bs-toggle="pill">商品基本資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionins2" data-bs-toggle="pill">商品共同資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionins3" data-bs-toggle="pill">商品附加資料</a></li>
						<!-- <li class="nav-item"><a class="nav-link" href="#Sectionins4" data-bs-toggle="pill">投資標的資訊</a>
						</li> -->
					</ul>
					<div class="tab-content">
						<div class="tab-pane fade show active" id="Sectionins1">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>保險商品資訊</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>保險商品代號</th>
											<td class="wd-30p">{{ proInfo.bankProCode }}</td>
											<th>保險商品類別</th>
											<td class="wd-30p">{{ proInfo.insInfo.insType }}</td>
										</tr>
										<tr>
											<th>保險公司名稱</th>
											<td class="wd-30p">{{ proInfo.insInfo.inscmpName }}</td>
											<th>主/附約</th>
											<td class="wd-30p">{{ proInfo.insInfo.baseRider === 'Y' ? '主約' : '附約' }}</td>
										</tr>
										<tr>
											<th>商品幣別</th>
											<td class="wd-30p">{{ proInfo.curCode }}</td>
											<th>商品風險等級</th>
											<td class="wd-30p">
												<span v-if="proInfo.riskCode === '1'">RR1</span>
												<span v-else-if="proInfo.riskCode === '2'">RR2</span>
												<span v-else-if="proInfo.riskCode === '3'">RR3</span>
												<span v-else-if="proInfo.riskCode === '4'">RR4</span>
												<span v-else-if="proInfo.riskCode === '5'">RR5</span>
											</td>
										</tr>
										<tr>
											<th>繳費年期類型（首期）</th>
											<td>{{ proInfo.insInfo.payTermName }}</td>
											<th>繳費年期類型(數值)</th>
											<td>{{ proInfo.insInfo.payTerm }}</td>
										</tr>
										<tr>
											<th>保障年期類型</th>
											<td>{{ proInfo.insInfo.insTermName }}</td>
											<th>保障年期類型(數值)</th>
											<td>{{ proInfo.insInfo.insTerm }}</td>
										</tr>
										<tr>
											<th>承保年齡(起)</th>
											<td>{{ proInfo.insInfo.ageMin }}</td>
											<th>承保年齡(迄)</th>
											<td>{{ proInfo.insInfo.ageMax }}</td>
										</tr>
										<tr>
											<th>保險商品年期</th>
											<td class="wd-30p">{{ proInfo.insInfo.insTermName }}</td>
											<th>商品期間</th>
											<td class="wd-30p">{{ proInfo.insInfo.payTermName }}</td>
										</tr>
										<tr>
											<th>銷售起始日</th>
											<td class="wd-30p">{{ proInfo.insInfo.stdDt }}</td>
											<th>銷售結束日</th>
											<td class="wd-30p">{{ proInfo.insInfo.endDate }}</td>
										</tr>
										<tr>
											<th>商品屬性</th>
											<td class="wd-30p">
												<span v-if="proInfo.protypeCode === 'I01'">儲蓄</span>
												<span v-else-if="proInfo.protypeCode === 'I02'">保障</span>
												<span v-else-if="proInfo.protypeCode === 'I03'">投資</span>
												<span v-else-if="proInfo.protypeCode === 'I04'">房貸</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div class="tab-pane fade" id="Sectionins2">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>銷售相關資料</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th width="20%">銷售地區</th>
											<td width="30%" v-if="proInfo.allYn == 'Y'">全行</td>
											<td width="30%" v-else></td>
											<th width="20%">限PI申購</th>
											<td width="30%">
												{{ proInfo.profInvestorYn }}
											</td>
										</tr>
										<tr>
											<th width="20%">銷售對象</th>
											<td width="30%" colspan="3">
												{{ proInfo.targetCusBu }}
											</td>
										</tr>
										<tr>
											<th><span>理財需求</span></th>
											<td colspan="3">
												<div class="form-check form-check-inline" v-for="(item, index) in finReqCodeMenu">
													<input
														class="form-check-input"
														name="finReqCodes"
														:disabled="actionType == 'EDIT' ? false : true"
														:id="'finReqCodes_' + index"
														v-model="finReqCodes"
														:value="item.codeValue"
														type="checkbox"
													/>
													<label class="form-check-label" :for="'finReqCodes_' + index">{{ item.codeName }}</label>
												</div>
											</td>
										</tr>
										<tr>
											<th>商品標籤</th>
											<td disabled="disabled" colspan="3">
												{{ proInfo.selprocatNames }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div class="tab-pane fade" id="Sectionins3">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>其他設定</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>商品投資標的</span></th>
											<td class="wd-80p">
												<span>{{ proInfo.sectorCode }}</span>
											</td>
										</tr>
										<tr>
											<th><span>商品投資地區</span></th>
											<td>
												<span>{{ proInfo.geoFocusCode }}</span>
											</td>
										</tr>
										<tr>
											<th><span>比較基準設定</span></th>
											<td>
												<span>{{ proInfo.benchmarkCode }}</span>
											</td>
										</tr>
										<tr>
											<th><span>商品簡介</span></th>
											<td>
												<textarea
													class="form-control"
													cols="80"
													rows="4"
													size="200"
													maxlength="200"
													v-model="proInfo.memo"
													:readonly="actionType !== 'EDIT'"
												></textarea>
												<div class="tx-note" v-if="proInfo.memo">{{ 200 - proInfo.memo.length }} 個字可輸入</div>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>保單條款</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_C"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['C']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="insUploadFileC"
																@change="triggerFile($event, 'C')"
																accept="application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.wordprocessingml.document"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('C', url['C'])">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['C'] && uploadFiles['C'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['C'].url" target="_blank">{{ uploadFiles['C'].url }}</a
													><br v-if="uploadFiles['C']" />
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['C'] && uploadFiles['C'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['C'])"
														>{{ uploadFiles['C'].showName }}</span
													>
													<span
														v-show="uploadFiles['C'] && uploadFiles['C'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('C', uploadFiles['C'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
										<tr>
											<th>DM</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_F"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['F']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="insUploadFileF"
																@change="triggerFile($event, 'F')"
																accept=".xlsx,.xls"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('F', url['F'])">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['F'] && uploadFiles['F'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['F'].url" target="_blank">{{ uploadFiles['F'].url }}</a
													><br v-if="uploadFiles['F']" />
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['F'] && uploadFiles['F'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['F'])"
														>{{ uploadFiles['F'].showName }}</span
													>
													<span
														v-show="uploadFiles['F'] && uploadFiles['F'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('F', uploadFiles['F'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
										<tr>
											<th>其他</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_G"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['G']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="insUploadFileG"
																@change="triggerFile($event, 'G')"
																accept="application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.wordprocessingml.document"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('G', url['G'])">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['G'] && uploadFiles['G'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['G'].url" target="_blank">{{ uploadFiles['G'].url }}</a
													><br v-if="uploadFiles['G']" />
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['G'] && uploadFiles['G'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['G'])"
														>{{ uploadFiles['G'].showName }}</span
													>
													<span
														v-show="uploadFiles['G'] && uploadFiles['G'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('G', uploadFiles['G'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>其他相關附件</h4>
								</div>
							</div>
							<table class="table table-RWD table-bordered table-horizontal-RWD">
								<tr>
									<td>
										<span v-for="(item, index) in otherFileList">
											<a
												v-if="index === otherFileList.length - 1"
												v-show="item.show"
												href="#"
												class="tx-link"
												@click="downloadOtherFile(item.docFileId)"
												>{{ $filters.defaultValue(item.showName, '--') }}</a
											>
											<a v-else href="#" class="tx-link" v-show="item.show" @click="downloadOtherFile(item.docFileId)"
												>{{ $filters.defaultValue(item.showName, '--') }}、</a
											>
										</span>
									</td>
								</tr>
							</table>
							<div class="tx-note">其他相關附件為文件管理內所上傳與本商品相關的文件。</div>
						</div>
						<div class="tab-pane fade" id="Sectionins4">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<div class="card-header">
										<h4>投資標的資訊</h4>
									</div>
									<table class="table table-RWD table-bordered" id="invTrgTbl">
										<thead>
											<tr>
												<th width="15%">Lipper Code</th>
												<th width="15%">銀行標的代碼</th>
												<th width="15%">保險公司商品代碼</th>
												<th width="35%">標的名稱</th>
												<th width="10%">國內基金</th>
												<th width="10%">風險屬性</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="item in insInvTargetList">
												<td data-th="Lipper Code">
													{{ $filters.defaultValue(item.lipperId, '--') }}
												</td>
												<td data-th="銀行標的代碼">{{ $filters.defaultValue(item.invtgtCode, '--') }}</td>
												<td data-th="保險公司商品代碼">
													{{ $filters.defaultValue(item.inscmpProCode, '--') }}
												</td>
												<td data-th="標的名稱">{{ $filters.defaultValue(item.invTgtName, '--') }}</td>
												<td data-th="國內基金"><span v-if="item.localYn == 'Y'">境內</span><span v-else>境外</span></td>
												<td data-th="風險屬性">{{ $filters.defaultValue(item.riskName, '--') }}</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>

					<div class="modal-footer">
						<input id="modalCloseButton" type="button" @click.prevent="close()" class="btn btn-white" value="關閉" />
						<input
							type="button"
							class="btn btn-primary"
							value="傳送主管審核"
							v-if="actionType == 'EDIT'"
							@click="
								updateProduct();
								close();
							"
						/>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- Modal 2 End -->
</template>
<script>
import moment from 'moment';
import _ from 'lodash';
export default {
	props: {
		actionType: String,
		gotoPage: Function,
		finReqCodeMenu: Array,
		downloadFile: Function,
		downloadOtherFile: Function,
		close: Function
	},
	data: function () {
		const now = moment();
		return {
			proInfo: {
				fundInfo: {},
				etfInfo: {},
				bondInfo: {},
				spInfo: {},
				insInfo: {},
				dciInfo: {},
				secInfo: {}
			},
			insInvTargetList: [],
			finReqCodes: [],
			proFileList: [], // 相關附件檔案清單
			otherFileList: [], // 其他相關附件
			commInfo: {
				proFiles: []
			},
			proCode: '',
			pfcatCode: '',
			selectYnList: [],

			//File 用參數
			url: [],
			uploadFile: {}, //上傳檔案
			uploadFiles: [] // 已上傳檔案陣列
		};
	},
	watch: {},
	mounted: function () {},
	methods: {
		getProInfo: function (bankProCode, pfcatCode, eventId) {
			var self = this;
			self.resetModalVaule();
			if (eventId) {
				self.doViewProLog(bankProCode, pfcatCode, eventId); // //審核資料
			} else {
				self.getProductInfo(bankProCode, pfcatCode); // 基本資料 共用資料
				self.getProductCommInfo(bankProCode, pfcatCode); // 附加資料
			}
		},
		getProductInfo: function (bankProCode, pfcatCode, callback) {
			var self = this;
			self.$api
				.getProductInfoApi({
					proCode: bankProCode,
					pfcatCode: pfcatCode
				})
				.then(function (ret) {
					if (_.isNil(ret.data)) {
						ret.data = {};
						self.$bi.alert('資料不存在');
						return;
					}
					if (_.isNil(ret.data.insInfo)) {
						ret.data.insInfo = {};
					}

					self.proInfo = ret.data;
					self.proCode = bankProCode;
					self.pfcatCode = pfcatCode;

					var selectYnList = [];
					self.$api
						.getAdmCodeDetail({
							codeType: 'SELECT_YN'
						})
						.then(function (ret) {
							selectYnList = ret.data;

							if (!_.isEmpty(selectYnList)) {
								if (!_.isUndefined(self.proInfo.profInvestorYn)) {
									var profInvestorYnObjs = _.filter(selectYnList, {
										codeValue: self.proInfo.profInvestorYn
									});
									self.proInfo.profInvestorYn = profInvestorYnObjs[0].codeName;
								}
							}
						});

					if (!_.isUndefined(self.proInfo.targetCusBu)) {
						var targetCusBuList = [];
						self.$api
							.getAdmCodeDetail({
								codeType: 'CUS_BU'
							})
							.then(function (ret) {
								targetCusBuList = ret.data;
								var targetCusBuObjs = _.filter(targetCusBuList, {
									codeValue: self.proInfo.targetCusBu
								});
								self.proInfo.targetCusBu = targetCusBuObjs[0]?.codeValue;
								self.proInfo.targetCusBuName = targetCusBuObjs[0]?.codeName;
							});
					}

					// 理財需求
					if (!_.isUndefined(ret.data.finReqCode)) {
						self.finReqCodes = ret.data.finReqCode.split(',');
					}

					if (!_.isUndefined(self.proInfo.selprocatNames)) {
						var selprocatNames = self.proInfo.selprocatNames.replaceAll(',', '、');
						self.proInfo.selprocatNames = selprocatNames;
					}
					callback && callback();
				});
		},
		getProductCommInfo: function (bankProCode, pfcatCode) {
			var self = this;
			// 商品附加資料
			this.$api
				.getProductsCommInfo({
					proCode: bankProCode,
					pfcatCode: pfcatCode
				})
				.then(function (ret) {
					if (!_.isNil(ret.data)) {
						if (ret.data.proDocs) {
							self.otherFileList = ret.data.proDocs; // 其他相關附件
							self.otherFileList.forEach(function (item) {
								// 其他相關附件 檔案顯示時間範圍
								item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
							});
						}

						self.proFileList = ret.data.proFiles;
						if (!_.isNil(self.proFileList)) {
							self.uploadFiles['C'] = self.proFileList.filter((proFile) => proFile.fileType === 'C')[0];
							self.uploadFiles['F'] = self.proFileList.filter((proFile) => proFile.fileType === 'F')[0];
							self.uploadFiles['G'] = self.proFileList.filter((proFile) => proFile.fileType === 'G')[0];
							self.url['C'] = self.uploadFiles['C'] ? self.uploadFiles['C'].url : null;
							self.url['F'] = self.uploadFiles['F'] ? self.uploadFiles['F'].url : null;
							self.url['G'] = self.uploadFiles['G'] ? self.uploadFiles['G'].url : null;
						}
					}
				});
		},
		updateProduct: function () {
			var self = this;
			self.commInfo.finReqCode = self.finReqCodes.join(',');

			Object.keys(self.url).forEach((key) => {
				// 檢查是否只有輸入url 沒有上傳檔案的type
				if (self.url[key] !== null) {
					// 有輸入url
					let typeInclude = self.proFileList.some((obj) => obj.fileType === key); // 找出是否有存在fileList
					if (!typeInclude) {
						var proFile = {};
						proFile.fileType = key;
						self.proFileList.push(proFile);
					}
				}
			});

			self.proFileList.forEach((e) => {
				e.createDt = null; // 移除日期避免轉型錯誤
				e.url = self.url[e.fileType];
			});
			self.commInfo = self.proInfo;
			self.commInfo.proFiles = self.proFileList;

			var formData = new FormData();
			// json model
			formData.append('model', JSON.stringify(self.commInfo));

			// upload file
			for (const key in self.uploadFiles) {
				let item = self.uploadFiles[key];
				if (item) {
					formData.append('files', item);
				}
			}

			this.$api.patchProductApi(formData).then(function (ret) {
				self.$bi.alert('提交審核成功。');
				self.gotoPage(0);
				self.resetModalVaule();
			});
		},
		resetModalVaule: function () {
			var self = this;
			$('[type="file"]').val(null);
			self.url = [];
			self.proFile = [];
			self.uploadFile = {};
			self.uploadFiles = [];
		},
		doViewProLog: function (proCode, pfcatCode, eventId) {
			//審核資料
			var self = this;
			self.resetModalVaule();
			if (proCode) {
				const callback = () => {
					self.getProductCommLogInfo(eventId);
				};

				self.getProductInfo(proCode, pfcatCode, callback);
			}
		},
		getProductCommLogInfo: function (eventId) {
			var self = this;
			this.$api
				.getProductLogApi({
					eventId: eventId
				})
				.then(function (ret) {
					if (!_.isNil(ret.data)) {
						// 取得維護資料帶入
						// 附加資料
						self.proInfo.memo = ret.data.memo; // 備註

						if (ret.data.proDocs) {
							self.otherFileList = ret.data.proDocs; // 其他相關附件
							self.otherFileList.forEach(function (item) {
								// 其他相關附件 檔案顯示時間範圍
								item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
							});
						}

						var proFileList = ret.data.proFiles;
						if (!_.isNil(proFileList)) {
							self.uploadFiles['C'] = proFileList.filter((proFile) => proFile.fileType === 'C')[0];
							self.uploadFiles['F'] = proFileList.filter((proFile) => proFile.fileType === 'F')[0];
							self.uploadFiles['G'] = proFileList.filter((proFile) => proFile.fileType === 'G')[0];
						}
					}
				});
		},
		triggerFile: function (event, fileType) {
			var self = this;
			self.uploadFile[fileType] = event.target.files[0];
			self.uploadFile[fileType].showName = event.target.files[0].name;
		},
		doUploadFile: function (fileType) {
			var self = this;
			if (self.uploadFile[fileType]) {
				if (self.uploadFile[fileType].size > 10485760) {
					self.$bi.alert('檔案大小不得超過10MB！');
					return;
				}
				var proFile = {};

				proFile.fileName = self.uploadFile[fileType].name;
				proFile.showName = self.uploadFile[fileType].name;

				proFile.contentType = self.uploadFile[fileType].type;
				proFile.fileSize = self.uploadFile[fileType].size;
				proFile.fileType = fileType;

				if (!self.proFileList || self.proFileList.length <= 0) {
					self.proFileList.push(proFile);
				} else {
					// 有資料先刪除就檔案再新增
					self.proFileList.forEach((e, index) => {
						if (e.fileType === fileType) {
							self.proFileList.splice(index, 1);
						}
					});
					self.proFileList.push(proFile);
				}

				self.uploadFiles[fileType] = null; // 先將原先檔案清除
				self.uploadFiles[fileType] = self.uploadFile[fileType];

				$('#insUploadFile' + fileType).val(''); // 清空上傳區域檔案
				self.commInfo.isUpdateProFiles = true;
				self.uploadFile[fileType] = null;
			}
		},
		deleteFiles: function (fileType, proFileId) {
			var self = this;
			if (self.proFileList.length > 0) {
				self.proFileList.forEach((e, index, arr) => {
					if (e.proFileId === proFileId) {
						arr.splice(index, 1);
					}
				});
			}

			self.uploadFiles[fileType] = null;
		}
	} // methods end
};
</script>
