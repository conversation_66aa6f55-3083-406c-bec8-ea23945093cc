/**
 * Loading mask
 */
.loading {
    left: 50%;
    top: 50%;
    position: absolute;
    margin-left: -45px;
    margin-top: -45px;
    border-bottom: 3px solid transparent;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-top: 5px solid #d8c5a2;
    border-radius: 100%;
    height: 90px;
    width: 90px;
    -webkit-animation: spin .6s infinite linear;
    -moz-animation: spin .6s infinite linear;
    -ms-animation: spin .6s infinite linear;
    -o-animation: spin .6s infinite linear;
    animation: spin .6s infinite linear;
}
.loading-logo {
    left: 50%;
    top: 50%;
    position: absolute;
    margin-left: -45px;
    margin-top: -45px;
    border-radius: 100%;
    box-shadow: inset 0 0 20px 1px #e3dfdf;
    height: 90px;
    width: 90px;
    background: url(../../images/loading-icon.gif) no-repeat center center;
    background-size: 60px;
}
@keyframes spin {
    from {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(359deg);
        -moz-transform: rotate(359deg);
        -o-transform: rotate(359deg);
        -ms-transform: rotate(359deg);
        transform: rotate(359deg);
    }
}
@-moz-keyframes spin {
    from {
        -moz-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -moz-transform: rotate(359deg);
        transform: rotate(359deg);
    }
}
@-webkit-keyframes spin {
    from {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg);
    }
}
@-ms-keyframes spin {
    from {
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -ms-transform: rotate(359deg);
        transform: rotate(359deg);
    }
}
@-o-keyframes spin {
    from {
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -o-transform: rotate(359deg);
        transform: rotate(359deg);
    }
}
.preloader {
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 19999;
    top: 0;
    background: #fff;
    background: rgba(255,255,255,.65);
}
