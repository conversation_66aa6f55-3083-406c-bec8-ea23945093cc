<template>
	<span>
		<img v-if="score == 1" :src="getImgURL('fund', 'lipper_1.gif')" alt="" style="width: 28px" />
		<img v-if="score == 2" :src="getImgURL('fund', 'lipper_2.gif')" alt="" style="width: 28px" />
		<img v-if="score == 3" :src="getImgURL('fund', 'lipper_3.gif')" alt="" style="width: 28px" />
		<img v-if="score == 4" :src="getImgURL('fund', 'lipper_4.gif')" alt="" style="width: 28px" />
		<img v-if="score == 5" :src="getImgURL('fund', 'lipper_5.gif')" alt="" style="width: 28px" />
		<span v-if="score == 0">--</span>
	</span>
</template>
<script>
import { getImgURL } from '@/utils/imgURL.js';
import _ from 'lodash';

export default {
	props: {
		lipperScores: Object,
		scoreCode: Object
	},
	data: function () {
		return {};
	},
	watch: {},
	computed: {
		score: function () {
			var lipperScore = _.find(this.lipperScores, { lipperScoreCode: this.scoreCode });
			return lipperScore ? lipperScore.score : 0;
		}
	},
	created: function () { },
	mounted: function () { },
	methods: { getImgURL }
};
</script>
