<template>
	<div class="container-fluid">
		<!--頁面內容 start-->
		<div class="row">
			<div class="col-12">
				<vue-bi-tabs :menu-code="'M30-00'">
					<template #default="{ id }">
						<vue-pro-all
							v-if="id === 'vue-pro-all'"
							:fund-modal-handler="fundModalHandler"
							:etf-modal-handler="etfModalHandler"
							:bond-modal-handler="bondModalHandler"
							:pfd-modal-handler="pfdModalHandler"
							:sp-modal-handler="spModalHandler"
							:ins-modal-handler="insModalHandler"
							:dci-modal-handler="dciModalHandler"
							:sec-modal-handler="secModalHandler"
							:cur-option="curOption"
							:risk-menu="riskMenu"
							:favorites-handler="favoritesHandler"
							:source="source"
							:bank-pdt-code="bankPdtCode"
							:pdt-name="pdtName"
						>
						</vue-pro-all>
						<vue-pro-fund
							v-if="id === 'vue-pro-fund'"
							:fund-modal-handler="fundModalHandler"
							:cur-option="curOption"
							:risk-menu="riskMenu"
							:favorites-handler="favoritesHandler"
						>
						</vue-pro-fund>
						<vue-pro-etf
							v-if="id === 'vue-pro-etf'"
							:etf-modal-handler="etfModalHandler"
							:cur-option="curOption"
							:risk-menu="riskMenu"
							:favorites-handler="favoritesHandler"
						>
						</vue-pro-etf>
						<vue-pro-bond
							v-if="id === 'vue-pro-bond'"
							:bond-modal-handler="bondModalHandler"
							:cur-option="curOption"
							:risk-menu="riskMenu"
							:favorites-handler="favoritesHandler"
						>
						</vue-pro-bond>
						<vue-pro-pfd
							v-if="id === 'vue-pro-pfd'"
							:pfd-modal-handler="pfdModalHandler"
							:cur-option="curOption"
							:risk-menu="riskMenu"
							:favorites-handler="favoritesHandler"
						>
						</vue-pro-pfd>
						<vue-pro-sp
							v-if="id === 'vue-pro-sp'"
							:sp-modal-handler="spModalHandler"
							:cur-option="curOption"
							:risk-menu="riskMenu"
							:favorites-handler="favoritesHandler"
						>
						</vue-pro-sp>
						<vue-pro-ins
							v-if="id === 'vue-pro-ins'"
							:ins-modal-handler="insModalHandler"
							:cur-option="curOption"
							:risk-menu="riskMenu"
							:favorites-handler="favoritesHandler"
						>
						</vue-pro-ins>
						<!-- <vue-pro-dci v-if="id === 'vue-pro-dci'" :dci-modal-handler="dciModalHandler" :cur-option="curOption"
              :risk-menu="riskMenu" :favorites-handler="favoritesHandler">
            </vue-pro-dci> -->
						<vue-pro-sec
							v-if="id === 'vue-pro-sec'"
							:sec-modal-handler="secModalHandler"
							:cur-option="curOption"
							:risk-menu="riskMenu"
							:favorites-handler="favoritesHandler"
						>
						</vue-pro-sec>
					</template>
				</vue-bi-tabs>
			</div>

			<vue-modal :is-open="isOpenModal['fund']" :before-close="closeModal('fund')">
				<template v-slot:content="props">
					<vue-fund-modal
						ref="fundModalRef"
						:is-open-fund-modal="isOpenModal['fund']"
						:fin-req-code-menu="finReqCodeMenu"
						:download-file="downloadFile"
						:download-other-file="downloadOtherFile"
						:close="props.close"
					></vue-fund-modal>
				</template>
			</vue-modal>
			<vue-modal :is-open="isOpenModal['etf']" :before-close="closeModal('etf')">
				<template v-slot:content="props">
					<vue-etf-modal
						ref="etfModalRef"
						:is-open-etf-modal="isOpenModal['etf']"
						:fin-req-code-menu="finReqCodeMenu"
						:pro-price-range-menu="proPriceRangeMenu"
						:download-file="downloadFile"
						:download-other-file="downloadOtherFile"
						:close="props.close"
					></vue-etf-modal>
				</template>
			</vue-modal>
			<vue-modal :is-open="isOpenModal['bond']" :before-close="closeModal('bond')">
				<template v-slot:content="props">
					<vue-bond-modal
						ref="bondModalRef"
						:is-open-bond-modal="isOpenModal['bond']"
						:fin-req-code-menu="finReqCodeMenu"
						:pro-price-range-menu="proPriceRangeMenu"
						:download-file="downloadFile"
						:download-other-file="downloadOtherFile"
						:close="props.close"
					></vue-bond-modal>
				</template>
			</vue-modal>
			<vue-modal :is-open="isOpenModal['pfd']" :before-close="closeModal('pfd')">
				<template v-slot:content="props">
					<vue-pfd-modal
						ref="pfdModalRef"
						:is-open-pfd-modal="isOpenModal['pfd']"
						:fin-req-code-menu="finReqCodeMenu"
						:close="props.close"
					></vue-pfd-modal>
				</template>
			</vue-modal>
			<vue-modal :is-open="isOpenModal['sp']" :before-close="closeModal('sp')">
				<template v-slot:content="props">
					<vue-sp-modal
						ref="spModalRef"
						:is-open-structured-product-modal="isOpenModal['sp']"
						:fin-req-code-menu="finReqCodeMenu"
						:pro-price-range-menu="proPriceRangeMenu"
						:download-file="downloadFile"
						:download-other-file="downloadOtherFile"
						:close="props.close"
					></vue-sp-modal>
				</template>
			</vue-modal>
			<vue-modal :is-open="isOpenModal['ins']" :before-close="closeModal('ins')">
				<template v-slot:content="props">
					<vue-ins-modal
						ref="insModalRef"
						:is-open-ins-modal="isOpenModal['ins']"
						:fin-req-code-menu="finReqCodeMenu"
						:download-file="downloadFile"
						:download-other-file="downloadOtherFile"
						:close="props.close"
					></vue-ins-modal>
				</template>
			</vue-modal>
			<!-- <vue-modal :is-open="isOpenModal['dci']" :before-close="closeModal('dci')">
        <template v-slot:content="props">
          <vue-dci-modal ref="dciModalRef" :is-open-dci-modal="isOpenModal['dci']" :fin-req-code-menu="finReqCodeMenu"
            :pro-price-range-menu="proPriceRangeMenu" :download-file="downloadFile"
            :download-other-file="downloadOtherFile" :close="props.close"></vue-dci-modal>
        </template>
      </vue-modal> -->
			<vue-modal :is-open="isOpenModal['sec']" :before-close="closeModal('sec')">
				<template v-slot:content="props">
					<vue-sec-modal
						ref="secModalRef"
						:is-open-sec-modal="isOpenModal['sec']"
						:fin-req-code-menu="finReqCodeMenu"
						:pro-price-range-menu="proPriceRangeMenu"
						:download-file="downloadFile"
						:download-other-file="downloadOtherFile"
						:close="props.close"
					></vue-sec-modal>
				</template>
			</vue-modal>
		</div>
	</div>
	<!--頁面內容 end-->
</template>
<script>
import Swal from 'sweetalert2';

import biTabs from '@/views/components/biTabs.vue';
import productSearchAll from './include/productSearchAll.vue';
import vueModal from '@/views/components/model.vue';
import vueProFund from './include/productSearchFund.vue';
import vueProEtf from './include/productSearchEtf.vue';
import vueProBond from './include/productSearchBond.vue';
import vueProPfd from './include/productSearchPfd.vue';
import vueProSp from './include/productSearchSp.vue';
import vueProIns from './include/productSearchIns.vue';
import vueProDci from './include/productSearchDci.vue';
import vueProSec from './include/productSearchSec.vue';

import vueFundModal from './include/fundModal.vue';
import vueEtfModal from './include/etfModal.vue';
import vueBondModal from './include/bondModal.vue';
import vuePfdModal from './include/pfdModal.vue';
import vueSpModal from './include/spModal.vue';
import vueInsModal from './include/insModal.vue';
import vueDciModal from './include/dciModal.vue';
import vueSecModal from './include/secModal.vue';

export default {
	components: {
		'vue-bi-tabs': biTabs,
		'vue-pro-all': productSearchAll,
		vueModal,
		vueProFund,
		vueProEtf,
		vueProBond,
		vueProPfd,
		vueProSp,
		vueProIns,
		vueProDci,
		vueProSec,
		vueFundModal,
		vueEtfModal,
		vueBondModal,
		vuePfdModal,
		vueSpModal,
		vueInsModal,
		vueDciModal,
		vueSecModal
	},
	data: function () {
		return {
			tabCode: 'ALL',
			menuTab: [], // 項目權限
			curOption: [], // 幣別
			riskMenu: [], // 風險等級
			finReqCodeMenu: [], // 各Tab-商品共同資料-理財需求選項,
			proPriceRangeMenu: [], // 各Tab-價格/淨值顯示區間,
			source: false, // 從潛力客戶搜尋來,
			bankPdtCode: null, // 從首頁搜尋來,
			pdtName: null, // 從首頁搜尋來,
			isOpenModal: {
				fund: false,
				etf: false,
				bond: false,
				pfd: false,
				sp: false,
				ins: false,
				dci: false,
				sec: false
			}
		};
	},
	created: function () {
		var self = this;
		if (self.$route?.params?.bankPdtCode) {
			self.bankPdtCode = self.$route.params.bankPdtCode;
		}
		if (self.$route?.params?.pdtName) {
			self.pdtName = self.$route.params.pdtName;
		}
	},
	beforeMount: function () {
		this.tabCode = 'ALL';
	},
	mounted: async function () {
		var self = this;
		const curData = await self.$api.groupProCurrenciesMenuApi();
		curData.data.forEach(function (item) {
			var obj = {
				value: item.curCode,
				name: item.curName + item.curCode
			};
			self.curOption.push(obj);
		});
		const riskData = await self.$api.getRiskMenuApi();
		self.riskMenu = riskData.data;
		self.getFinReqCodeMenu();
		self.getProPriceRangeMenu();
		if (self.$route.proListSource == 'PRO_POT_SEARCH_RST') {
			self.source = true;
		} else {
			self.source = false;
			// 刪除使用者各功能來源的商品名單
			await this.$api.deleteProPotSearchRstApi();
		}
	},
	methods: {
		// 更新幣別前端
		refCurSel: function () {
			// $('#curMenuAll').selectpicker('refresh');
		},

		closeModal: function (modalName) {
			this.isOpenModal[modalName] = false;
		},
		fundModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.$refs.fundModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.fundModalRef.changeTab('section1');
			self.isOpenModal['fund'] = true;
		},
		etfModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.$refs.etfModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.etfModalRef.getEtfDetail(proCode); // 商品資訊/基金資料
			self.$refs.etfModalRef.getEtfStockHold(proCode); // 商品資訊/ETF持股
			self.$refs.etfModalRef.getEtfPrice(proCode); // 商品資訊/價格分析資料
			self.$refs.etfModalRef.getPricesChartData(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.etfModalRef.getEtfProfileNameMenu(proCode); // 商品資訊/績效分析
			self.$refs.etfModalRef.getEtfProfileBenchmarksMenu(); // 商品資訊/績效分析
			self.isOpenModal['etf'] = true;
		},
		bondModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.$refs.bondModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.bondModalRef.getBondPriceAna(proCode);
			self.$refs.bondModalRef.getPricesChartData(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.bondModalRef.getBondPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal['bond'] = true;
		},
		pfdModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.$refs.pfdModalRef.getProInfo(proCode, pfcatCode);
			//			self.$refs.pfdModalRef.getEtfDetail(proCode); // 商品資訊/基金資料
			self.isOpenModal['pfd'] = true;
		},
		spModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.isOpenModal['sp'] = true;
			self.$refs.spModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.spModalRef.getSpPriceAna(proCode); // 商品資訊/淨值分析資料
			self.$refs.spModalRef.getSpNets(proCode, 'Y', -1.0); // 商品資訊/淨值分析圖表
			self.$refs.spModalRef.getSpPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
		},
		insModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.$refs.insModalRef.getProInfo(proCode, pfcatCode);
			self.isOpenModal['ins'] = true;
		},
		dciModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.$refs.dciModalRef.getProInfo(proCode, pfcatCode); //商品基本資料
			self.$refs.dciModalRef.getDciPriceAna(proCode, 'M', -1.0); // 商品資訊/價格分析資料
			self.$refs.dciModalRef.getDciNets(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.dciModalRef.getDciPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal['dci'] = true;
		},
		secModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.isOpenModal['sec'] = true;
			self.$refs.secModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.secModalRef.getSecPriceAna(proCode); // 商品資訊/淨值分析資料
			self.$refs.secModalRef.getSecNets(proCode, 'Y', -1.0); // 商品資訊/淨值分析圖表
			self.$refs.secModalRef.getSecPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
		},
		favoritesHandler: function (proCode, pfcatCode) {
			var self = this;
			self.$api
				.addFavoriteApi({
					proCode: proCode,
					pfcatCode: pfcatCode
				})
				.then(function (ret) {
					Swal.fire({
						icon: 'success',
						text: '已加入至我的最愛',
						showCloseButton: true,
						confirmButtonText: '確認',
						buttonsStyling: false,
						customClass: {
							confirmButton: 'btn btn-success'
						}
					});
				});
		},
		downloadFile: function (proFile) {
			var self = this;
			var proFileId = proFile.proFileId;
			var fileName = proFile.showName;
			this.$api.downloadProFileApi({ proFileId }).then(function (data) {
				var link = document.createElement('a');
				var url = URL.createObjectURL(data);
				link.download = fileName;
				link.href = url;
				document.body.appendChild(link);
				link.click();
				link.remove();
				setTimeout(() => URL.revokeObjectURL(url), 1000);
			});
		},
		downloadOtherFile: function (fileId) {
			this.$api.downloadOtherFileApi({ fileId });
		},
		// 取得各Tab-商品共同資料-理財需求選項
		getFinReqCodeMenu: async function () {
			var self = this;
			const res = await self.$api.getAdmCodeDetail({ codeType: 'FIN_REQ_CODE' });
			self.finReqCodeMenu = res.data;
		},
		// 取得各Tab-價格/淨值顯示區間
		getProPriceRangeMenu: async function () {
			var self = this;
			const res = await self.$api.getProPriceRangeMenuApi();
			self.proPriceRangeMenu = res.data;
		}
	} // methods end
};
</script>
