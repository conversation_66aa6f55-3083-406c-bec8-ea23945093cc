<template>
	<dynamic-title></dynamic-title>
	<div>
		<div class="row">
			<div class="col-12">
				<div class="card card-form-collapse">
					<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formsearch1">
						<h4>公告訊息分類設定</h4>
						<span class="tx-square-bracket">為必填欄位</span>
					</div>

					<div class="card-body collapse show" id="formsearch1">
						<vue-form v-slot="{ errors }" ref="queryForm">
							<div class="form-row">
								<div class="form-group col-lg-4">
									<label class="form-label">分類名稱</label>
									<vue-field
										name="mainCatName"
										class="form-control"
										type="text"
										label="分類名稱"
										v-model="mainCatName"
										:class="{ 'is-invalid': errors.mainCatName }"
										id="mainCatName"
										rules="required"
									>
									</vue-field>
									<div>
										<span class="text-danger" v-show="errors.mainCatName">
											{{ errors.mainCatName }}
										</span>
									</div>
								</div>
								<div class="form-group col-lg-4">
									<label class="form-label">分類階層</label>
									<div v-for="(item, index) in selGmCatType" class="form-check form-check-inline">
										<vue-field
											class="form-check-input"
											type="radio"
											v-model="catType"
											:value="item.codeValue"
											name="selGmCatType"
											:id="`selGmCatType_${index}`"
											:class="{ 'is-invalid': errors.selGmCatType }"
											rules="required"
											label="分類階層"
											:disabled="disableFlag"
										></vue-field>
										<label :for="`selGmCatType_${index}`">{{ item.codeName }}</label>
									</div>
									<div>
										<span class="text-danger" v-show="errors.selGmCatType">
											{{ errors.selGmCatType }}
										</span>
									</div>
								</div>
								<div class="form-group col-lg-4">
									<template v-if="catType == 'S'">
										<label class="form-label">所屬主分類</label>
										<vue-field
											as="select"
											name="msgMainCat"
											class="form-select"
											:class="{ 'is-invalid': errors.msgMainCat }"
											v-model="msgMainCat"
											rules="required"
											label="所屬主分類"
											:disabled="disableFlag"
										>
											<option :value="null">請選擇</option>
											<option v-for="item in selMsgMainCat" :value="item.catCode">{{ item.catName }}</option>
										</vue-field>
										<div>
											<span class="text-danger" v-show="errors.msgMainCat">
												{{ errors.msgMainCat }}
											</span>
										</div>
									</template>
								</div>
							</div>
							<div v-if="modifyType == 'S'" class="form-footer">
								<a class="btn btn-primary" @click.prevent="saveCat">儲存</a>
							</div>
							<div v-if="modifyType == 'M'" class="form-footer">
								<button class="btn btn-primary" @click.prevent="modifyCat">修改</button>&nbsp;
								<button class="btn btn-primary margin-left10" @click.prevent="cancel">取消修改</button>
							</div>
						</vue-form>
					</div>
				</div>

				<div class="card- card-form">
					<div class="card-header">
						<h4>分類列表</h4>
					</div>
					<table class="table table-RWD table-horizontal-RWD table-bordered">
						<thead>
							<tr>
								<th width="5%"></th>
								<th width="30%">分類名稱</th>
								<th width="20%">維護者</th>
								<th width="20%">維護日</th>
								<th width="10%" class="text-center">執行</th>
							</tr>
						</thead>
						<tbody>
							<template v-for="(mainCat, mainIndex) in selMsgMainCat">
								<tr>
									<td class="btn-minus">
										<button
											class="btn"
											type="button"
											data-bs-toggle="collapse"
											:data-bs-target="`#collapse_${mainIndex}`"
											aria-expanded="false"
											:aria-controls="`collapse_${mainIndex}`"
											@click="toggleBtn(mainIndex)"
										>
											<i :class="{ 'bi bi-dash-lg': !isActive(mainIndex), 'bi bi-plus-lg': isActive(mainIndex) }"></i>
										</button>
									</td>
									<td data-th="分類名稱">
										{{ mainCat.catName }}
									</td>
									<td data-th="維護者">
										{{ mainCat.editUser }}
									</td>
									<td data-th="維護日">
										{{ mainCat.editDate }}
									</td>
									<td data-th="執行">
										<button
											type="button"
											class="btn btn-info btn-icon"
											data-bs-toggle="tooltip"
											title="編輯"
											@click="modifyPage(mainCat.catCode, mainCat.catName, 'M')"
										>
											<i class="fa-solid fa-pen"></i>
										</button>
										<button
											type="button"
											class="btn btn-danger btn-icon"
											data-bs-toggle="tooltip"
											title="刪除"
											@click="deleteCat(mainCat.catCode, 'M')"
										>
											<i class="fa-solid fa-trash"></i>
										</button>
									</td>
								</tr>
								<tr v-for="subCat in mainCat.selMsgSubCat" class="collapse show" aria-expanded="true" :id="`collapse_${mainIndex}`">
									<td></td>
									<td data-th="分類名稱">{{ subCat.catName }}</td>
									<td data-th="維護者">{{ subCat.editUser }}</td>
									<td data-th="維護日">{{ subCat.editDate }}</td>
									<td data-th="執行">
										<button
											type="button"
											class="btn btn-info btn-icon"
											data-bs-toggle="tooltip"
											title="編輯"
											@click="modifyPage(subCat.catCode, subCat.catName, 'S', mainCat.catCode)"
										>
											<i class="fa-solid fa-pen"></i>
										</button>
										<button
											type="button"
											class="btn btn-danger btn-icon"
											data-bs-toggle="tooltip"
											title="刪除"
											@click="deleteCat(subCat.catCode, 'S', subCat.mainCatCode)"
										>
											<i class="fa-solid fa-trash"></i>
										</button>
									</td>
								</tr>
							</template>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<!--頁面內容 end-->
	</div>
</template>
<script>
import _ from 'lodash';
import { Field, Form } from 'vee-validate';
import dynamicTitle from '@/views/components/dynamicTitle.vue';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		dynamicTitle
	},
	data: function () {
		return {
			//畫面顯示用參數
			selGmCatType: [], // 分類階層選項
			selMsgMainCat: [], // 所屬主分類下拉選單
			modifyType: 'S',
			disableFlag: false,

			mainCatName: '', // 分類名稱輸入欄位
			catType: null, // 分類階層選擇欄位
			msgMainCat: '', // 所屬主分類選擇欄位
			catCode: ''
		};
	},
	filters: {},
	beforeMount: function () {},
	created: function () {},
	mounted: async function () {
		console.log('BbsMgtCat mounted');
		var self = this;
		await self.getAdmCodeDetail('GM_CAT_TYPE');
		await self.getMessageCat('M', null);
		console.log('分類階層:', self.selGmCatType, ',資料:', self.selMsgMainCat);
		_.forEach(self.selMsgMainCat, async function (item) {
			var catType = 'S';
			var mainCatCode = item.catCode;
			const ret = await self.$api.getGenMessageCat({
				catType: catType,
				mainCatCode: mainCatCode
			});
			if (ret.data) {
				item['selMsgSubCat'] = ret.data;
			}
		});
		this.$forceUpdate();
	},
	methods: {
		getAdmCodeDetail: async function (codeType) {
			var self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType
			});
			self.selGmCatType = ret.data;
			this.$forceUpdate();
		},
		getMessageCat: async function (catType, mainCatCode) {
			var self = this;
			const ret = await this.$api.getGenMessageCat({
				catType: catType,
				mainCatCode: mainCatCode
			});
			self.selMsgMainCat = ret.data;
		},
		toggleBtn: function (index) {
			var self = this;
			self.selMsgMainCat[index].active = !self.selMsgMainCat[index].active;
		},
		isActive: function (index) {
			var self = this;
			return self.selMsgMainCat[index].active;
		},
		saveCat: function () {
			var self = this;
			self.$refs.queryForm.validate().then(async function (pass) {
				if (pass.valid) {
					if (self.catType === 'M') {
						var duplicateMainCat = _.filter(self.selMsgMainCat, function (mainCat) {
							return mainCat.catName == self.mainCatName;
						});
						if (!_.isEmpty(duplicateMainCat)) {
							self.$swal.fire({
								icon: 'error',
								text: '不可新增相同名稱的分類',
								showCloseButton: true,
								confirmButtonText: '確認',
								buttonStyling: false, // remove default button style
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							});
							return;
						}
					} else if (self.catType === 'S') {
						var mainCatBelonged = _.find(self.selMsgMainCat, function (mainCat) {
							return mainCat.catCode == self.msgMainCat;
						});
						var duplicateSubCat = _.filter(mainCatBelonged.selMsgSubCat, function (subCat) {
							return subCat.catName == self.mainCatName;
						});
						if (!_.isEmpty(duplicateSubCat)) {
							self.$swal.fire({
								icon: 'error',
								text: '不可新增相同名稱的分類',
								showCloseButton: true,
								confirmButtonText: '確認',
								buttonStyling: false, // remove default button style
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							});
							return;
						}
					}
					// 新增
					const ret = await self.$api.postGenMessageCat({
						catName: self.mainCatName,
						catType: self.catType,
						mainCatCode: self.msgMainCat
					});
					self.$router.push('/gen/bbsMgtCat');
				}
			});
		},
		modifyPage: function (catCode, catName, catType, mainCatCode) {
			var self = this;
			self.modifyType = 'M';
			self.disableFlag = true;
			self.catType = catType;
			self.mainCatName = catName;
			self.catCode = catCode;
			if (catType === 'S') {
				var parentCat = _.find(self.selMsgMainCat, function (mainCat) {
					return mainCat.catCode === mainCatCode;
				});
				self.msgMainCat = parentCat.catCode;
			}
		},
		deleteCat: async function (catCode, catType, mainCatCode) {
			var self = this;
			var countMessageData = {};
			var deleteable = true;
			var msg = '';
			if (catType === 'M') {
				countMessageData = {
					catType: 'M',
					mainCatCode: catCode
				};
				msg = '已有文件公告訊息歸類於此主分類下，不可刪除。';
			} else if (catType === 'S') {
				countMessageData = {
					catType: catType,
					subCatCode: mainCatCode
				};
				msg = '已有文件公告訊息歸類於此次分類下，不可刪除。';
			}

			const ret = await self.$api.getGenCountMessage(countMessageData);
			if (ret.data) {
				if (ret.data.cnt > 0) {
					self.$swal.fire({
						icon: 'error',
						text: msg,
						showCloseButton: true,
						confirmButtonText: '確認',
						buttonStyling: false, // remove default button style
						customClass: {
							confirmButton: 'btn btn-danger'
						}
					});
					deleteable = false;
				}
			}

			if (catType === 'M') {
				const ret = await self.$api.getGenMessageCat({
					catType: 'S',
					mainCatCode: catCode
				});
				if (!_.isEmpty(ret.data)) {
					await self.$swal.fire({
						icon: 'error',
						text: '此主分類底下已設有次分類，不可刪除。',
						showCloseButton: true,
						confirmButtonText: '確認',
						buttonStyling: false, // remove default button style
						customClass: {
							confirmButton: 'btn btn-danger'
						}
					});
					deleteable = false;
				}
			}

			var deleteMessageCatData = {};
			if (catType === 'M') {
				deleteMessageCatData = {
					catType: catType,
					mainCatCode: catCode
				};
			} else if (catType === 'S') {
				deleteMessageCatData = {
					catType: catType,
					subCatCode: catCode
				};
			}
			if (deleteable) {
				await self.$bi.confirm('確定要刪除此筆資料嗎?', {
					event: {
						confirmOk: async function () {
							const ret = await self.$api.deleteGenMessageCat(deleteMessageCatData);
							self.$router.push('/gen/bbsMgtCat');
						}
					}
				});
			}
		},
		modifyCat: function () {
			var self = this;
			self.$refs.queryForm.validate().then(async function (pass) {
				if (pass.valid) {
					if (self.catType === 'M') {
						var duplicateMainCat = _.filter(self.selMsgMainCat, function (mainCat) {
							return mainCat.catName == self.mainCatName;
						});
						if (!_.isEmpty(duplicateMainCat)) {
							self.$swal.fire({
								icon: 'error',
								text: '不可新增相同名稱的主分類',
								showCloseButton: true,
								confirmButtonText: '確認',
								buttonStyling: false, // remove default button style
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							});
							return;
						}
					} else if (self.catType === 'S') {
						var mainCatBelonged = _.find(self.selMsgMainCat, function (mainCat) {
							return mainCat.catCode == self.msgMainCat;
						});
						var duplicateSubCat = _.filter(mainCatBelonged.selMsgSubCat, function (subCat) {
							return subCat.catName == self.mainCatName;
						});
						if (!_.isEmpty(duplicateSubCat)) {
							self.$swal.fire({
								icon: 'error',
								text: '不可新增相同名稱的次分類',
								showCloseButton: true,
								confirmButtonText: '確認',
								buttonStyling: false, // remove default button style
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							});
							return;
						}
					}

					// 維護
					var patchMessageCatData = {};
					if (self.catType === 'M') {
						patchMessageCatData = {
							catName: self.mainCatName,
							catType: self.catType,
							mainCatCode: self.catCode
						};
					} else if (self.catType === 'S') {
						patchMessageCatData = {
							catName: self.mainCatName,
							catType: self.catType,
							mainCatCode: self.msgMainCat,
							subCatCode: self.catCode
						};
					}
					const ret = await self.$api.patchGenMessageCat(patchMessageCatData);
					if (ret.status == 200) {
						self.$swal.fire({
							icon: 'success',
							text: '更新成功',
							showCloseButton: true,
							confirmButtonText: '確認',
							buttonStyling: false, // remove default button style
							customClass: {
								confirmButton: 'btn btn-success'
							}
						});
					}
					self.$router.push('/gen/bbsMgtCat');
				}
			});
		},
		cancel: function () {
			var self = this;
			self.modifyType = 'S';
			self.disableFlag = false;
			self.catType = null;
			self.mainCatName = null;
			self.msgMainCat = null;
			self.catCode = null;
		}
	}
};
</script>
