<template>
	<div>
		<vue-modal :is-open="modalStates.modal1" :before-close="() => closeModal('modal1')">
			<template v-slot:content="props">
				<div class="modal-dialog modal-dialog-centered modal-lg">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">聯繫</h4>
							<button type="button" class="btn-close" @click.prevent="closeModal('modal1')" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<div class="alert alert-info" role="alert">
								[ {{ tdRecTask.cusName }} ] {{ tdRecTask.nextRemindDt }}, {{ tdRecTask.nextRemindTime }}
							</div>
							<div class="card card-form">
								<div class="card-header">
									<h4>聯繫紀錄</h4>
								</div>
								<table class="biv-table table table-bordered table-RWD table-horizontal-RWD">
									<tbody>
										<tr>
											<th class="w20">聯繫日期</th>
											<td class="w80">{{ tdRecTask.nextRemindDt }}</td>
										</tr>
										<tr>
											<th>聯繫時間</th>
											<td>{{ tdRecTask.nextRemindTime }}</td>
										</tr>
										<tr>
											<th>主旨</th>
											<td>{{ tdRecTask.title }}</td>
										</tr>
										<tr>
											<th>處理方式</th>
											<td>{{ tdRecTask.visitAprName }}</td>
										</tr>
										<tr>
											<th>處理內容</th>
											<td>{{ tdRecTask.content }}</td>
										</tr>
										<tr>
											<th>聯絡狀況</th>
											<td>{{ tdRecTask.contStatName }}</td>
										</tr>
										<tr>
											<th>處理後續</th>
											<td>{{ tdRecTask.contProcName }}</td>
										</tr>
										<tr>
											<th>是否結案</th>
											<td>
												<span v-if="tdRecTask.status === 'CLOSE'">已結案</span>
												<span v-if="tdRecTask.status === 'UNPRC'">未結案</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div class="modal-footer">
							<button name="btnClose" type="button" class="btn btn-white" @click.prevent="closeModal('modal1')"
								aria-label="Close">
								關閉
							</button>
							<button name="btnDelete" type="button" class="btn btn-danger" @click="deleteTdRec()">刪除</button>
							<button name="btnModify" type="button" class="btn btn-primary" @click.prevent="doUpdate()">修改</button>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>

		<!-- Modal 聯繫紀錄維護 -->
		<vue-modal :is-open="modalStates.modal2" :before-close="() => closeModal('modal2')">
			<template v-slot:content="props">
				<div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">聯繫</h4>
							<button type="button" class="btn-close" @click.prevent="closeModal('modal2')" aria-label="Close"></button>
						</div>
						<div class="modal-body overflow-scroll">
							<div class="card-clientCard">
								<div class="card shadow-none mb-3">
									<table>
										<tbody>
											<tr>
												<td width="10%" class="clientCard-icon">
													<div class="avatar avatar-male">
														<img :src="getImgURL('avatar', 'man-1.png')" class="rounded-circle bg-info" />
														<!-- <img th:src="@{/images/avatar/man-3.png}" class="rounded-circle bg-info" v-if="gender =='F'">-->
													</div>
													<h5 class="mb-0">{{ cusInfo.cusName || '--' }}</h5>
												</td>
												<td width="90%">
													<div class="caption tx-black">
														<sapn class=" ">最近通聯日期：{{ $filters.formatDate(cusInfo.lastConnectionDt) || '--' }}</sapn>
													</div>
													<div class="row">
														<div class="col-lg-4">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-clipboard-data mg-xl-e-5-f"></i>投資屬性：{{
																		cusInfo.rankName || '--'
																	}}
																</li>
																<li><i class="bi bi-gift mg-xl-e-5-f"></i>生日：{{ cusInfo.birth || '--' }}</li>
																<li>
																	<i class="bi bi-envelope mg-xl-e-5-f"></i>電子郵件：{{ cusInfo.email || '--' }}
																</li>
															</ul>
														</div>
														<div class="col-lg-3">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-house-door mg-xl-e-5-f"></i>聯絡電話(住)：{{
																		cusInfo.phoneH || '--'
																	}}
																</li>
																<li>
																	<i class="bi bi-building mg-xl-e-5-f"></i>聯繫電話(公司)：{{
																		cusInfo.phoneO || '--'
																	}}
																</li>
																<li>
																	<i class="bi bi-telephone mg-xl-e-5-f"></i>聯絡電話(行動)：{{
																		cusInfo.phoneM || '--'
																	}}
																</li>
															</ul>
														</div>
														<div class="col-lg-5">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-journal-medical mg-xl-e-5-f"></i>未成年辦理財富管理業務與投資商品同意書：
																	<span :class="cusInfo.childInvYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ cusInfo.childInvYn === 'Y' ? '是' : '否' }}
																	</span>
																</li>
																<li>
																	<i class="bi bi-journal-text mg-xl-e-5-f"></i>特定金錢信託客戶投資有價證券推介同意書：
																	<span :class="cusInfo.specRecommYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ cusInfo.specRecommYn === 'Y' ? '是' : '否' }}
																	</span>
																</li>
																<li>
																	<i class="bi bi-journal-bookmark mg-xl-e-5-f"></i>財富特定客戶(不得主動推介)：
																	<span :class="cusInfo.specCusYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ cusInfo.specCusYn === 'Y' ? '是' : '否' }}
																	</span>
																</li>
															</ul>
														</div>
													</div>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
							<div class="row">
								<div class="col-12 mt-3">
									<div class="card card-form shadow-none">
										<div class="card-header">
											<h4>聯繫紀錄</h4>
											<span class="tx-square-bracket">為必填欄位</span>
										</div>
										<div class="card-content">
											<vue-form v-slot="{ errors, validate }" ref="tdConnTask">
												<table class="biv-table table table-RWD table-borderless">
													<tbody>
														<tr>
															<th>
																<label class="form-label tx-require">聯繫日期</label>
															</th>
															<td>
																<div class="input-group">
																	<vue-field type="date" name="contDate" class="form-control" v-model="contDate"
																		:class="{ 'is-invalid': errors.contDate }" label="聯繫日期"
																		rules="required"></vue-field>
																	<div>
																		<span class="text-danger" v-show="errors.contDate">{{
																			errors.contDate
																		}}</span>
																	</div>
																</div>
															</td>
															<th>
																<label class="form-label">聯繫時間</label>
															</th>
															<td>
																<div class="input-group">
																	<vue-field as="select" class="form-select" id="contHour" name="contHour"
																		v-model="contHour" :class="{ 'is-invalid': errors.contHour }" rules="required"
																		label="聯繫時間">
																		<option value="00">00</option>
																		<option value="01">01</option>
																		<option value="02">02</option>
																		<option value="03">03</option>
																		<option value="04">04</option>
																		<option value="05">05</option>
																		<option value="06">06</option>
																		<option value="07">07</option>
																		<option value="08">08</option>
																		<option value="09">09</option>
																		<option value="10">10</option>
																		<option value="11">11</option>
																		<option value="12">12</option>
																		<option value="13">13</option>
																		<option value="14">14</option>
																		<option value="15">15</option>
																		<option value="16">16</option>
																		<option value="17">17</option>
																		<option value="18">18</option>
																		<option value="19">19</option>
																		<option value="20">20</option>
																		<option value="21">21</option>
																		<option value="22">22</option>
																		<option value="23">23</option>
																	</vue-field>
																	<span class="input-group-text">時</span>
																	<div>
																		<span class="text-danger" v-show="errors.contHour">{{
																			errors.contHour
																		}}</span>
																	</div>
																	<vue-field as="select" class="form-select" id="contMin" name="contMin"
																		v-model="contMin" :class="{ 'is-invalid': errors.contMin }" rules="required"
																		label="聯繫時間">
																		<option selected="selected" value="00">00</option>
																		<option v-for="minute in selectMinutes" :value="minute">{{ minute }}</option>
																	</vue-field>
																	<span class="input-group-text">分</span>
																	<div>
																		<span class="text-danger" v-show="errors.contMin">{{ errors.contMin }}</span>
																	</div>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">主旨</label>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<vue-field class="form-control" name="contTitle" type="text" size="30"
																		value="contTitle" v-model="contTitle" :class="{ 'is-invalid': errors.contTitle }"
																		label="主旨" rules="required"></vue-field>
																	<div style="height: 3px">
																		<span class="text-danger" v-show="errors.contTitle">{{
																			errors.contTitle
																		}}</span>
																	</div>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">處理方式</label>
															</th>
															<td colspan="3">
																<vue-field as="select" class="form-select" id="contVisitAprCode" name="contVisitAprCode"
																	v-model="contVisitAprCode" :class="{ 'is-invalid': errors.contVisitAprCode }"
																	rules="required" label="處理方式">
																	<option disabled selected value="">請選擇</option>
																	<option v-for="visitType in visitAprMenu" :value="visitType.codeValue">
																		{{ visitType.codeName }}
																	</option>
																</vue-field>
																<div style="height: 3px">
																	<span class="text-danger" v-show="errors.contVisitAprCode">{{
																		errors.contVisitAprCode
																	}}</span>
																</div>
															</td>
														</tr>

														<tr>
															<th>
																<label class="form-label tx-require">處理內容</label>
															</th>
															<td colspan="3">
																<vue-field as="textarea" class="form-control" id="contContent" name="contContent"
																	rows="5" cols="50" size="400" v-model="contContent"
																	:class="{ 'is-invalid': errors.contContent }" rules="required"
																	label="處理內容"></vue-field>
																<div style="height: 3px">
																	<span class="text-danger" v-show="errors.contContent">{{
																		errors.contContent
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">聯絡狀況</label>
															</th>
															<td colspan="3">
																<vue-field as="select" class="form-select" id="contStatCode" name="contStatCode"
																	v-model="contStatCode" :class="{ 'is-invalid': errors.contStatCode }" rules="required"
																	label="聯絡狀況">
																	<option disabled selected value="">請選擇</option>
																	<option v-for="contStat in contStatMenu" :value="contStat.codeValue">
																		{{ contStat.codeName }}
																	</option>
																</vue-field>
																<div style="height: 3px">
																	<span class="text-danger" v-show="errors.contStatCode">{{
																		errors.contStatCode
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">後續處理</label>
															</th>
															<td colspan="3">
																<vue-field as="select" class="form-select" id="contProcCode" name="contProcCode"
																	v-model="contProcCode" :class="{ 'is-invalid': errors.contProcCode }" rules="required"
																	label="後續處理">
																	<option disabled selected value="">請選擇</option>
																	<option v-for="contProc in contProcMenu" :value="contProc.codeValue">
																		{{ contProc.codeName }}
																	</option>
																</vue-field>
																<div style="height: 3px">
																	<span class="text-danger" v-show="errors.contProcCode">{{
																		errors.contProcCode
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">是否結案</label>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<div class="form-check form-check-inline">
																		<vue-field type="radio" name="doneYn" class="form-check-input" id="doneYn_YY"
																			value="Y" v-model="doneYn" :class="{ 'is-invalid': errors.doneYn }"
																			rules="required" label="是否結案">
																		</vue-field>
																		<label class="form-check-label">已結案</label>
																	</div>
																	<div class="form-check form-check-inline">
																		<vue-field type="radio" name="doneYn" class="form-check-input" id="doneYn_NN"
																			value="N" v-model="doneYn" :class="{ 'is-invalid': errors.doneYn }"
																			rules="required" label="是否結案">
																		</vue-field>
																		<label class="form-check-label">未結案</label>
																	</div>
																	<div style="height: 3px">
																		<span class="text-danger" v-show="errors.doneYn">{{ errors.doneYn }}</span>
																	</div>
																</div>
															</td>
														</tr>
													</tbody>
												</table>
											</vue-form>
										</div>
									</div>
								</div>
							</div>
						</div>

						<div class="modal-footer" id="modifyTaskFooter">
							<input class="btn btn-white" id="apptEditModalCloseButton" type="button" value="關閉"
								@click.prevent="closeModal('modal2')" />
							<input class="btn btn-primary" id="btnSave" type="button" value="儲存" @click="updateTdRec()" />
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- Modal end -->
	</div>
</template>
<script>
import _ from 'lodash';
import moment from 'moment';
import { Field, Form } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import { getImgURL } from '@/utils/imgURL.js';

export default {
	components: {
		vueModal,
		'vue-form': Form,
		'vue-field': Field
	},
	props: {
		recCode: String,
		getCalendarTasks: Function,
		getSelectReuseWord: Function,
		userInfo: Object
	},
	data: function () {
		var minutes = [];
		for (var i = 1; i < 60; i++) {
			minutes.push(String(i).padStart(2, '0'));
		}
		return {
			modalStates: {
				modal1: false,
				modal2: false
			},
			tdRecTask: {},
			selectMinutes: minutes,

			// 選單
			visitAprMenu: [], // 處理方式選單
			contStatMenu: [], // 聯絡狀況選單
			contProcMenu: [], // 後續處理選單

			selectReuseWord: [],
			showReuseWordSetting: false,

			// 客戶資訊
			cusInfo: {
				cusCode: null,
				cusName: null,
				birth: null,
				email: null,
				phoneH: '',
				phoneO: '',
				phoneM: '',
				rankName: null,
				childInvYn: null,
				specRecommYn: null,
				specCusYn: null,
				logsCreateDt: null
			},

			// 編輯用參數
			contCusCode: null, // cusCode

			contDate: null, // 聯繫日期
			contHour: '00', // 聯繫時間(時)
			contMin: '00', // 聯繫時間(分)
			contTitle: null, // 主旨
			contVisitAprCode: null, // 處理方式
			contContent: null, // 處理內容
			contStatCode: null, // 聯絡狀況
			contProcCode: null, // 後續處理
			doneYn: null, // 是否結案

			reuseWord: null, // 常用聯繫內容
			wobReuseWords: []
		};
	},
	watch: {
		recCode: function (val) {
			var self = this;
			if (self.recCode) {
				self.getTdRec();
			}
		}
	},
	methods: {
		getImgURL,
		closeModal: function (modalName) {
			this.modalStates[modalName] = false;
		},
		openModal: function (modalName) {
			this.modalStates[modalName] = true;
		},
		// 取得客戶資料
		getCusInfo: function () {
			var self = this;
			self.$api
				.getCustomer({
					cusCode: self.cusInfo.cusCode
				})
				.then(function (ret) {
					if (!ret.data) {
						self.$bi.alert('查無顧客，請確認是否為久未往來顧客。');
						self.clearValues();
						return;
					}
					self.cusInfo.cusName = ret.data.cusName;
					self.cusInfo.birth = ret.data.birth;
					self.cusInfo.email = ret.data.email;
					self.cusInfo.rankName = ret.data.rankName;
					self.cusInfo.childInvYn = ret.data.childInvYn;
					self.cusInfo.specRecommYn = ret.data.specRecommYn;
					self.cusInfo.specCusYn = ret.data.specCusYn;
					self.cusInfo.lastConnectionDt = ret.data.logCreateDt;

					// 將聯絡方式分類
					ret.data.contactInfoList.forEach(function (item) {
						switch (item.contactType) {
							case 'E': // email
								self.cusInfo.email = item.email;
								break;
							case 'H': // 住家電話
								self.cusInfo.phoneH = item.phone1;
								break;
							case 'O': // 公司電話
								self.cusInfo.phoneO = item.phone1;
								break;
							case 'M': // 手機
								self.cusInfo.phoneM = item.phone1;
								break;
						}
					});
				});
		},
		// 取得「聯繫紀錄」
		getTdRec: function () {
			var self = this;
			self.$api
				.getTdConnRecApi({
					recCode: self.recCode
				})
				.then(function (ret) {
					self.tdRecTask = ret.data;
					self.cusInfo.cusCode = self.tdRecTask.cusCode;
					self.cusInfo.birth = _.formatDate(ret.data.birth);
					self.contactDt = ret.data.contactDt.replaceAll('/', '-');
				});
		},
		// 刪除「聯繫紀錄」
		deleteTdRec: function () {
			var self = this;
			self.$bi.confirm('確定要刪除此筆資料嗎?', {
				event: {
					confirmOk: function () {
						self.$api
							.deleteTdRecApi({
								recCode: self.recCode
							})
							.then(function (ret) {
								self.$bi.alert('刪除成功。');
								self.modalStates.modal1 = false;
								if (self.getCalendarTasks) {
									self.getCalendarTasks();
								}
							});
					}
				}
			});
		},
		// 修改「聯繫紀錄」
		doUpdate: function () {
			var self = this;
			self.getCusInfo();
			self.getVisitAprMenu();
			self.getContStatMenu();
			self.getContProcMenu();
			self.modalStates.modal2 = true;

			self.contDate = self.tdRecTask.nextRemindDt.replaceAll('/', '-');
			self.contCusCode = self.tdRecTask.cusCode;
			self.contTitle = self.tdRecTask.title;
			self.contContent = self.tdRecTask.content;

			self.contVisitAprCode = self.tdRecTask.visitAprCode;
			self.contStatCode = self.tdRecTask.contStatCode;
			self.contProcCode = self.tdRecTask.contProcCode;
			self.doneYn = self.tdRecTask.status === 'CLOSE' ? 'Y' : self.tdRecTask.status === 'UNPRC' ? 'N' : null;
			var contTime = self.tdRecTask.nextRemindTime.split(':');
			self.contHour = contTime[0];
			self.contMin = contTime[1];
		},
		// 更新「聯繫紀錄」
		updateTdRec: function () {
			var self = this;
			self.$refs.tdConnTask.validate().then(function (pass) {
				if (pass.valid) {
					var content = _.isNil(self.contContent) ? '' : self.contContent;

					self.$api
						.patchTdConnRecApi({
							recCode: self.recCode,
							cusCode: self.contCusCode,
							nextRemindDt: moment(self.contDate).format('YYYY-MM-DD'),
							nextRemindTime: self.contHour + ':' + self.contMin,
							title: self.contTitle,
							visitAprCode: self.contVisitAprCode,
							content: content,
							contStatCode: self.contStatCode,
							contProcCode: self.contProcCode,
							doneYn: self.doneYn
						})
						.then(function (ret) {
							self.$bi.alert('更新成功');
							self.modalStates.modal2 = false;
							if (self.getCalendarTasks) {
								self.getCalendarTasks();
							}
							self.getTdRec();
						});
				}
			});
		},
		// 取得處理方式選單
		getVisitAprMenu: function () {
			var self = this;
			self.$api
				.getCodeDetailApi({
					codeType: 'VISIT_APR_CODE'
				})
				.then(function (ret) {
					self.visitAprMenu = ret.data;
				});
		},
		// 取得聯絡狀況選單
		getContStatMenu: function () {
			var self = this;
			self.$api
				.getCodeDetailApi({
					codeType: 'CONT_STAT_CODE'
				})
				.then(function (ret) {
					self.contStatMenu = ret.data;
				});
		},
		// 取得後續處理選單
		getContProcMenu: function () {
			var self = this;
			self.$api
				.getCodeDetailApi({
					codeType: 'CONT_PROC_CODE'
				})
				.then(function (ret) {
					self.contProcMenu = ret.data;
				});
		},
		setShowReuseWordSetting: function () {
			var self = this;
			self.showReuseWordSetting = !self.showReuseWordSetting;
		},
		insertReuseWord: function (index) {
			var self = this;
			var words = self.wobReuseWords[index].words;
			var wordsId = self.wobReuseWords[index].wordsId;

			self.$api
				.postWobReuseWordsApi({
					wordsId: wordsId,
					words: words
				})
				.then(function (ret) {
					self.getSelectReuseWord();
				});
		},
		appendReuseWord: function () {
			var self = this;

			if (!self.contContent) {
				self.contContent = '';
			}

			if (self.reuseWord != null) {
				self.contContent = self.contContent + self.reuseWord;
			}
		},
		getSelectContactReuseWord: function () {
			var self = this;
			self.$api.getWobReuseWordsApi().then(function (ret) {
				var result = ret.data;
				var needAppend = 5 - result.length;
				if (result.length < 5) {
					for (var i = 0; i < needAppend; i++) {
						result.push({ words: '', wordsId: ret.data.length + i + 1 });
					}
				}
				self.selectReuseWord = result;
				self.wobReuseWords = result;
			});
		}
	}
};
</script>
