/**
 * Copyright (c) 2025 - SoftBI Corporation Limited.
 * All rights reserved.
 *
 * <AUTHOR>
 * @since 1.0.0
 * @Description Ajax object without jQuery
 */

import { _ } from 'lodash';
import Swal from 'sweetalert2';
import { useLoading } from 'vue-loading-overlay';
const $loading = useLoading({
	container: null,
	loader: 'bars',
	color: '#3459e6'
});

export const biAjax = {
	// Prototype extension
	prototype: function () {
		if (!String.prototype.format) {
			String.prototype.format = function () {
				let args = arguments;
				return this.replace(/\{(\d+)\}/g, function () {
					return args[arguments[1]];
				});
			};
		}
		if (!Object.assign) {
			Object.defineProperty(Object, 'assign', {
				enumerable: false,
				configurable: true,
				writable: true,
				value: function (target) {
					if (target === undefined || target === null) throw new TypeError('Cannot convert first argument to object');
					let to = Object(target);
					for (let i = 1; i < arguments.length; i++) {
						let nextSource = arguments[i];
						if (nextSource === undefined || nextSource === null) continue;
						nextSource = Object(nextSource);

						let keysArray = Object.keys(nextSource);
						for (let nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex++) {
							let nextKey = keysArray[nextIndex];
							let desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);
							if (desc !== undefined && desc.enumerable) to[nextKey] = nextSource[nextKey];
						}
					}
					return to;
				}
			});
		}
		if (!String.prototype.startsWith) {
			String.prototype.startsWith = function (searchString, position) {
				position = position || 0;
				return this.indexOf(searchString, position) === position;
			};
		}
	},

	// Ajax object
	// @param option.retrieve Set when ajax success
	ajax: async function (option) {
		this.prototype();
		const opt = Object.assign(
			{},
			{
				id: _.now() + _.random(0, 99),
				retrieve: null,
				method: option.method,
				dataType: 'json',
				showLoading: true,
				beforeSend: (request, settings) => {
					if (this.loading && this.beforeLoad) {
						this.beforeLoad(request, settings, opt.showLoading);
					}
				},
				complete: (response, status) => {
					if (this.loading && this.afterLoad) {
						this.afterLoad(response, status, opt.showLoading);
					}
				},
				afterLoadCallback: null,
				// vue-loading-overlay
				loading: true,
				loader: null,
				loadCount: 0
			},
			option
		);
		// Set contentType when use request payload
		if (opt.contentType && opt.contentType.toUpperCase() == 'application/json'.toUpperCase() && opt.data != null) {
			opt.data = JSON.stringify(opt.data);
		}
		// vue-loading-overlay
		function beforeLoad() {
			if (!opt.showLoading) return;
			opt.loadCount++;
			if (!opt.loader) {
				opt.loader = $loading.show({
					container: null,
					loader: 'spinner',
					color: '#3459e6'
				});
			}
		}
		function afterLoad() {
			if (!opt.showLoading) return;

			opt.loadCount = Math.max(0, opt.loadCount - 1);

			if (opt.loadCount === 0 && opt.loader) {
				const loader = document.querySelector('body > .vld-container');
				opt.loader.hide();
				opt.loader = null;
				if (loader) loader.remove();
			}

			if (opt.afterLoadCallback) {
				opt.afterLoadCallback();
			}
		}

		try {
			beforeLoad();

			const response = await fetch(opt.url, {
				method: opt.method,
				headers: {
					'Content-Type': opt.contentType || 'application/json'
				},
				body: opt.method.toUpperCase() !== 'GET' && opt.data ? opt.data : undefined
			});
			const data = await response.json();

			// 錯誤處理
			if (data.status === 200 && data.statusMsg) {
				Swal.fire({
					title: 'error',
					text: data.statusMsg,
					icon: 'error'
				});
				return Promise.reject();
			} else if (data.status && data.status !== 200) {
				Swal.fire({
					title: 'error',
					text: '未預期的錯誤，請洽系統管理員',
					icon: 'error'
				});
				return Promise.reject();
			}

			// Set retrieve in success
			if (opt.retrieve != null) {
				Object.assign(opt.retrieve, data.data);
			}
			afterLoad();
			return data;
		} catch (error) {
			Swal.fire({
				title: 'error',
				text: '網絡錯誤，請稍後再試',
				icon: 'error'
			});
			afterLoad();
			return Promise.reject(error);
		}
	},

	// Ajax object with json content type
	ajaxJson: function (opt) {
		opt = Object.assign({ contentType: 'application/json' }, opt);
		return this.ajax(opt);
	},

	// base64 encoding for files
	encodeBase64: function (file) {
		return new Promise((resolve, reject) => {
			let reader = new FileReader();
			reader.readAsDataURL(file);
			reader.onload = () => resolve(reader.result.replace(/^.*,/, ''));
			reader.onerror = reject;
		});
	},

	/**
	 * Ajax error stack
	 *
	 * @param option
	 */
	ajaxStackError: function (option) {
		this.prototype();
		let msgTot = [];
		let opt = Object.assign(
			{
				throwMsg: function (error) {
					console.error(error);
				},
				stack: function (event, response, settings, thrownError) {
					let json = JSON.parse(response);
					return {
						msg: json && json.statusMsg ? json.statusMsg : '連線逾時，請稍後再試',
						trace: json ? json.trace : ''
					};
				},
				statusCallback: {}
			},
			option
		);

		const originalFetch = window.fetch;
		window.fetch = async function (...args) {
			msgTot = [];
			try {
				const response = await originalFetch(...args);
				return response;
			} catch (error) {
				const statusCallback = opt.statusCallback[error.status];
				if (statusCallback) {
					statusCallback(error);
				} else {
					msgTot.push(opt.stack(null, error, {}, error.message));
				}

				if (msgTot.length > 0) {
					opt.throwMsg(msgTot);
				}
				throw error;
			}
		};

		window.addEventListener('load', () => {
			if (msgTot.length > 0) {
				opt.throwMsg(msgTot);
			}
		});
	}
};
