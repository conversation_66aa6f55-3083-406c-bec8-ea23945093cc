<template>
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<!--頁面內容 基金fund start-->
			<div class="tab-nav-tabs my-3">
				<ul class="nav nav-tabs nav-justified">
					<li class="nav-item">
						<a class="nav-link" :class="{ active: activeTab == 'common' }" data-bs-toggle="tab"
							@click="changeTab('common')">一般篩選</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" :class="{ active: activeTab == 'fast' }" data-bs-toggle="tab"
							@click="changeTab('fast')">快速篩選</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" :class="{ active: activeTab == 'weighted' }" data-bs-toggle="tab"
							@click="changeTab('weighted')">加權條件篩選</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" :class="{ active: activeTab == 'advanced' }" data-bs-toggle="tab"
							@click="changeTab('advanced')">進階搜尋</a>
					</li>
				</ul>

				<div class="tab-content">
					<div class="tab-pane fade show" :class="{ 'active show': activeTab == 'common' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
								<h4>查詢條件</h4>
								<span class="tx-square-bracket">為必填欄位</span>
							</div>
							<div class="collapse show" id="collapseListGroup1">
								<div class="card-body">
									<!-- <form> -->

									<div class="form-row">
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> 商品代號 </label>
											<input class="form-control" id="prod_bank_pro_code" maxlength="20" v-model="bankProCode" size="25"
												type="text" value="" />
										</div>

										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> 商品名稱</label>
											<input class="form-control" id="prod_pro_name" maxlength="20" v-model="proName" size="45"
												type="text" value="" />
										</div>

										<div class="form-group col-12 col-lg-4">
											<label class="form-label">計價幣別</label>
											<select class="selectpicker form-control" ref="curMenuFund" id="curMenuFund" multiple
												title="請選擇幣別" v-model="curObjs" data-style="btn-white">
												<option value="">全部</option>
												<option v-for="(item, index) in curOption" :key="index" :value="item.value">
													{{ $filters.defaultValue(item.name, '--') }}
												</option>
											</select>
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-4">
											<label class="form-label">風險等級</label>
											<div class="form-check-group">
												<div class="form-check form-check-inline" v-for="(item, index) in riskMenu">
													<input type="checkbox" class="form-check-input" name="riskCodes" v-model="riskCodes"
														:id="'riskGrade-' + index" :value="item.riskCode" />
													<label :for="'riskGrade-' + index" class="form-check-label">{{ item.riskName }}</label>
												</div>
											</div>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label tx-require">境內外基金</label>
											<div v-for="item in localMenu" class="form-check form-check-inline">
												<input class="form-check-input" :id="'genLocalYn' + item.codeValue" v-model="genLocalYn"
													type="radio" :value="item.codeValue" name="genLocalYn" />
												<label class="form-check-label" :for="'genLocalYn' + item.codeValue">{{
													$filters.defaultValue(item.codeName, '--')
												}}</label>
											</div>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label tx-require">基金類型</label>
											<select class="form-select" id="proType" name="proType" title="請選擇類型" v-model="proTypeCode"
												data-style="btn-white">
												<option value="">全部</option>
												<option v-for="item in proTypeMenu" :value="item.proTypeCode">
													{{ $filters.defaultValue(item.proTypeName, '--') }}
												</option>
											</select>
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-4">
											<label class="form-label tx-require">Lipper評級</label>
											<div class="input-group">
												<select name="lipperRankType" class="form-select" v-model="lipperPoint">
													<option value="" selected>全部</option>
													<option value="TOTRETOV ">整體回報</option>
													<option value="CONSRETOV">穩定回報</option>
													<option value="CAPPRESOV">保本回報</option>
												</select>
											</div>
											<div class="input-group-text">指標 &nbsp;排名</div>
											<div class="input-group">
												<select name="lipperRank" class="form-select" v-model="lipperRank">
													<option value="" selected>全部</option>
													<option value="1">1</option>
													<option value="2">2</option>
													<option value="3">3</option>
													<option value="4">4</option>
													<option value="5">5</option>
												</select>
											</div>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label tx-require">配息頻率</label>
											<select name="select" class="form-select" v-model="intFreqUnitType">
												<option value="">全部</option>
												<option v-for="intFreqUnit in intFreqUnitMenu" :value="intFreqUnit.codeValue">
													{{ $filters.defaultValue(intFreqUnit.codeName, '--') }}
												</option>
											</select>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label tx-require">配息率</label>
											<div v-for="item in intRateRankMenu" class="form-check form-check-inline">
												<input class="form-check-input" :id="'intRateRank' + item.codeValue" v-model="intRateRank"
													type="radio" :value="item.codeValue" name="intRateRank" />
												<label class="form-check-label" :for="'intRateRank' + item.codeValue">{{
													$filters.defaultValue(item.codeName, '--')
												}}</label>
											</div>
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-4">
											<label class="form-label">申購手續費</label>
											<div v-for="item in backEndLoadMenu" class="form-check form-check-inline">
												<input class="form-check-input" :id="'backEndLoad' + item.codeValue" v-model="backEndLoadYn"
													type="radio" :value="item.codeValue" name="backEndLoadYn" />
												<label class="form-check-label" :for="'backEndLoad' + item.codeValue">{{
													$filters.defaultValue(item.codeName, '--')
												}}</label>
											</div>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label">報酬率(%)</label>
											<div class="input-group">
												<select name="local" class="form-select" v-model="statCode">
													<option v-for="item in statCodeMenu" :value="item.codeValue">
														{{ $filters.defaultValue(item.codeName, '--') }}
													</option>
												</select>
												<input type="number" class="form-control" size="5" id="minDValue" v-model="minDValue" />
												<div class="input-group-text">~</div>
												<input type="number" class="form-control" size="5" id="maxDValue" v-model="maxDValue" />
											</div>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label">ISINCODE</label>
											<input class="form-control" id="isinCode" maxlength="20" v-model="isinCode" size="45"
												type="text" />
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-12">
											<label class="form-label"> 基金公司</label>
											<div class="input-group">
												<div class="form-check-group">
													<div class="form-check form-check-inline">
														<input class="form-check-input" id="fund_regionin" type="radio" name="nation"
															v-model="nation" value="Y" @change="fundCmpChange" />
														<label class="form-check-label" for="fund_regionin">國內</label>
													</div>
													<div class="form-check form-check-inline">
														<input class="form-check-input" id="fund_regionout" type="radio" name="nation"
															v-model="nation" value="N" @change="fundCmpChange" />
														<label class="form-check-label" for="fund_regionout">國外</label>
													</div>
												</div>
												<button type="button" class="btn btn-primary" @click="groupFundCmpModalHandler(nation)">
													選擇基金公司
												</button>
												<div v-for="item in fundCmpItem">
													<span class="form-check-label"> {{ $filters.defaultValue(item.compName, '--') }}</span>
													<a href="#" @click="deleteFundCmpItem(item.compCode)"><img
															:src="getImgURL('icon', 'i-cancel.png')" /></a>
												</div>
											</div>
										</div>
									</div>
									<div class="form-row">
										<div class="form-group col-12 col-lg-12">
											<label class="form-label">投資地區</label>
											<div class="input-group">
												<button type="button" class="btn btn-primary" @click="groupGeoFocusModalHandler()">
													選擇投資地區
												</button>
												<vue-modal :is-open="isOpenGeoFocusModal" :before-close="isOpenGeoFocusModal = false">
													<template v-slot:content="props">
														<vue-group-geofocus-modal :close="props.close" ref="groupGeoFocusModalRef"
															id="groupGeoFocusModal" :issuer-prop="geoFocusItem"
															@selected="selectedGeoFocus"></vue-group-geofocus-modal>
													</template>
												</vue-modal>
												<div v-for="item in geoFocusItem">
													<span class="form-check-label"> {{ $filters.defaultValue(item.name, '--') }}</span>
													<a href="#" @click="deleteGeoFocusItem(item.geoFocusCode)"><img
															:src="getImgURL('icon', 'i-cancel.png')" /></a>
												</div>
											</div>
										</div>
									</div>
									<!-- </form> -->
								</div>

								<div class="form-footer">
									<a class="btn btn-primary" @click.prevent="gotoPage(0)">查詢</a>
								</div>
							</div>
						</div>
					</div>

					<div class="tab-pane fade" :class="{ 'active show': activeTab == 'fast' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2">
								<h4>查詢條件</h4>
								<span class="tx-square-bracket">為必填欄位</span>
							</div>
							<div class="collapse show" id="collapseListGroup2">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-12">
											<label class="form-label tx-require"> 篩選條件 </label>

											<div class="form-check-group">
												<div class="form-check form-check-inline" v-for="item in fundFastMenu"
													@change="fastChange(item.codeValue)">
													<input class="form-check-input" :id="'fast' + item.codeValue" name="fastCode"
														v-model="fastCode" :value="item.codeValue" type="radio" />
													<label class="form-check-label" :for="'fast' + item.codeValue">{{
														$filters.defaultValue(item.codeName, '--')
													}}</label>
												</div>
											</div>
										</div>
									</div>
									<div class="form-row">
										<div class="form-group col-12 col-lg-6" id="rangeFixedTrFund" style="display: none">
											<label class="form-label tx-require"> 顯示區間</label>
											<select class="form-select" id="prod_protype_code" v-model="timeRange">
												<option v-for="item in timeRangeMenu" :value="item">
													{{ $filters.defaultValue(item.termName, '--') }}
												</option>
											</select>
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-6" id="proPerfTimeTrFund" style="display: none">
											<label class="form-label tx-require"> 標的績效 </label>
											<select class="form-select" id="vfAstStat_stat_code" v-model="perf">
												<option v-for="item in perfMenu" :value="item.codeValue">
													{{ $filters.defaultValue(item.codeName, '--') }}
												</option>
											</select>
										</div>
									</div>
									<div class="form-row">
										<div class="form-group col-12 col-lg-6" id="maxRowIdTrFund" style="display: none">
											<label class="form-label tx-require"> 顯示資料筆數</label>
											<select class="form-select" id="maxRowId" v-model="rowNumber">
												<option value="">全部</option>
												<option v-for="item in rowNumerMenu" :value="item.rangeFixed">
													{{ $filters.defaultValue(item.termName, '--') }}
												</option>
											</select>
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-6" id="fundCmpTrFund" style="display: none">
											<button type="button" class="btn btn-primary" data-bs-toggle="modal"
												data-bs-target="#groupFundCmpTypeModalRef" @click="groupFundCmpTypeModalHandler">
												選擇基金類別
											</button>
											<div class="form-check form-check-inline" v-for="item in fundCmpItem">
												<span class="form-check-label"> {{ $filters.defaultValue(item.compName, '--') }}</span>
												<a href="#" @click="deleteFundCmpItem(item.globalClassCode)"><img
														:src="getImgURL('icon', 'i-cancel.png')" /></a>
											</div>
										</div>
									</div>

									<div class="form-footer">
										<a class="btn btn-primary" @click.prevent="gotoFastPage(0)">查詢</a>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="tab-pane fade" :class="{ 'active show': activeTab == 'weighted' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup3">
								<h4>查詢條件</h4>
								<span class="tx-square-bracket">為必填欄位</span>
							</div>
							<div class="collapse show" id="collapseListGroup3">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-6">
											<label class="form-label">基金類別</label>
											<button type="button" class="btn btn-primary" data-bs-toggle="modal"
												data-bs-target="#groupFundCmpTypeModalRef" @click="groupFundCmpTypeModalHandler">
												選擇基金類別
											</button>
											<div class="form-check form-check-inline" v-for="item in fundCmpItem">
												<span class="form-check-label"> {{ $filters.defaultValue(item.compName, '--') }}</span>
												<a href="#" @click="deleteFundCmpItem(item.globalClassCode)"><img
														:src="getImgURL('icon', 'i-cancel.png')" /></a>
											</div>
										</div>

										<div class="form-group col-12 col-lg-6">
											<label class="form-label tx-require"> 績效幣別 </label>
											<div class="form-check form-check-inline">
												<input class="form-check-input" name="curCode" v-model="curCode" id="curCode1" type="radio"
													value="TWD" />
												<label class="form-check-label" for="curCode1">台幣</label>
											</div>
											<div class="form-check form-check-inline">
												<input class="form-check-input" name="curCode" v-model="curCode" id="curCode2" type="radio"
													value="USD" />
												<label class="form-check-label" for="curCode2">原幣</label>
											</div>
										</div>

										<div class="form-group col-12 col-lg-4">
											<label class="form-label tx-require"> 顯示資料筆數</label>
											<select class="form-select" id="maxRowId" v-model="rowNumber">
												<option value="">全部</option>
												<option v-for="item in rowNumerMenu" :value="item.rangeFixed">
													{{ $filters.defaultValue(item.termName, '--') }}
												</option>
											</select>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="card card-table">
							<div class="card-header">
								<h4>比較條件權重設定</h4>
							</div>
							<div class="table-responsive">
								<table class="table table-bordered table-blue">
									<thead>
										<tr>
											<th><span class="txtStar">風險/績效</span><a href="#"></a></th>
											<th><span class="txtStar">權重</span>(%)<a href="#"></a></th>
											<th>&nbsp;</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>
												<select class="form-select" id="searchCodeName" name="searchCodeName" title="請選擇風險/績效類型"
													v-model="searchCodeName" data-style="btn-white">
													<option value="">全部</option>
													<option v-for="item in proWeightedTypeMenu" :value="item.codeValue">
														{{ $filters.defaultValue(item.codeName, '--') }}
													</option>
												</select>
											</td>
											<td><input type="input" class="form-control" size="20" v-model="searchValue" /> 權重%</td>

											<td>
												<button type="button" class="btn btn-info btn-icon" data-bs-toggle="tooltip" title="新增"
													@click="newRank()">
													<i class="fa-solid fa-plus"></i>
												</button>
											</td>
										</tr>
										<tr v-for="item in proSearchesMaps">
											<td>{{ $filters.defaultValue(item.searchName, '--') }}</td>
											<td>{{ item.searchValue }}</td>
											<td>
												<button type="button" class="btn btn-danger btn-icon" data-bs-toggle="tooltip" title=""
													data-bs-original-title="刪除" @click="deleteWeighted(item.searchCodeName)">
													<i class="fa-solid fa-trash"></i>
												</button>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="text-end mt-3">
							<input type="button" class="btn btn-primary" value="歷史查詢條件" @click="openHisSearch()" />
							<input type="button" class="btn btn-small btn-primary" value="查詢" @click="getRankPage(0)" />
							<input v-if="showButton" type="button" class="btn btn-small btn-primary" value="儲存條件" @click="
								saveCondition();
							isOpenModal3 = true;
							" />
						</div>
					</div>

					<!-- Modal 2 歷史查詢條件 -->
					<vue-modal :is-open="isOpenModal2" :before-close="isOpenModal2 = false">
						<template v-slot:content="props">
							<div class="modal-dialog modal-xl">
								<div class="modal-content">
									<div class="modal-header">
										<h4 class="modal-title">歷史查詢條件</h4>
										<button type="button" class="btn-close" aria-label="Close" @click.prevent="props.close()"></button>
									</div>
									<div class="modal-body">
										<div class="card card-table">
											<div class="card-header">
												<h4>查詢列表</h4>
												<div dev-include-html="../../main/include/nav-pages.html"></div>
											</div>
											<div class="table-responsive">
												<table class="table table-bordered table-blue">
													<thead>
														<tr>
															<th>查詢名稱</th>
															<th>備註</th>
															<th>建立時間</th>
															<th>執行</th>
														</tr>
													</thead>
													<tbody v-for="item in proSearches">
														<tr id="row1">
															<td>{{ $filters.defaultValue(item.searchName, '--') }}</td>
															<td>{{ $filters.defaultValue(item.memo, '--') }}</td>
															<td>{{ $filters.defaultValue(item.createDt, '--') }}</td>
															<td>
																<button type="button" class="btn btn-info btn-icon" data-bs-toggle="tooltip" title="編輯"
																	@click="hisEdit(item)">
																	<i class="fa-solid fa-pen"></i>
																</button>
																<button type="button" class="btn btn-danger btn-icon" data-bs-toggle="tooltip"
																	title="刪除" @click="hisDelete(item)">
																	<i class="fa-solid fa-trash"></i>
																</button>
															</td>
														</tr>
													</tbody>
												</table>
											</div>
										</div>
									</div>
									<div class="modal-footer">
										<input type="button" @click.prevent="props.close()" class="btn btn-white" value="關閉" />
									</div>
								</div>
							</div>
						</template>
					</vue-modal>
					<!-- modal 2 end -->

					<!-- modal 3 儲存條件 -->
					<vue-modal :is-open="isOpenModal3" :before-close="isOpenModal3 = false">
						<template v-slot:content="props">
							<div class="modal-dialog modal-xl">
								<div class="modal-content">
									<div class="modal-header">
										<h4 class="modal-title">儲存條件</h4>
										<button type="button" class="btn-close" aria-label="Close" @click.prevent="props.close()"></button>
									</div>
									<div class="modal-body">
										<div class="card card-table">
											<div class="table-responsive">
												<table class="table table-bordered table-blue">
													<tbody>
														<tr>
															<th class="w20 tx-require">查詢名稱</th>
															<td><input type="text" class="form-control" v-model="searchName" /></td>
														</tr>
														<tr>
															<th class="w20 tx-require">備註</th>
															<td>
																<textarea name="" id="" cols="50" rows="5" class="form-control"
																	v-model="memo"></textarea>
															</td>
														</tr>
													</tbody>
												</table>
											</div>
										</div>
									</div>
									<div class="modal-footer">
										<input type="button" class="btn btn-primary" value="儲存" @click.prevent="
											conditionsSave();
										props.close();
										" />
										<input type="button" @click.prevent="props.close()" class="btn btn-white" value="關閉" />
									</div>
								</div>
							</div>
						</template>
					</vue-modal>
					<!-- modal 3 end -->

					<div class="tab-pane fade" :class="{ 'active show': activeTab == 'advanced' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup4">
								<h4>查詢條件</h4>
							</div>
							<div class="collapse show" id="collapseListGroup4">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-6">
											<label class="form-label">篩選條件</label>
											<div class="form-check form-check-inline" @click="getfundTypeMenu('LOCAL')">
												<input class="form-check-input" name="fundType" v-model="fundSaleType" id="fundType1"
													type="radio" value="LOCAL" />
												<label class="form-check-label" for="fundType1">本行基金</label>
											</div>
											<div class="form-check form-check-inline" @click="getfundTypeMenu('ALL')">
												<input class="form-check-input" name="fundType" v-model="fundSaleType" id="fundType2"
													type="radio" value="ALL" />
												<label class="form-check-label" for="fundType2">全資料庫基金</label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>

						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup4-1">
								<h4>基金資料</h4>
							</div>
							<div class="collapse show" id="collapseListGroup4-1">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-6">
											<label class="form-label"> 本行基金代碼 </label>
											<input class="form-control" type="text" v-model="bankProCodes" />
										</div>
										<div class="form-group col-12 col-lg-6">
											<span class="tx-square-bracket">請以逗點(,)分開代碼</span>
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> 基金名稱 </label>
											<input class="form-control" type="text" v-model="fundCname" maxlength="20" />
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> 基金英文名稱 </label>
											<input class="form-control" type="text" v-model="fundName" maxlength="20" />
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> 基金公司 </label>
											<div class="input-group">
												<div class="form-check form-check-inline">
													<input class="form-check-input" id="localYn1" name="localYn" v-model="localYn" type="radio"
														value="Y" @click="getCmpCodeMenu('Y')" /><label class="form-check-label"
														for="localYn1">國內</label>
												</div>
												<div class="form-check form-check-inline">
													<input class="form-check-input" id="localYn2" name="localYn" v-model="localYn" type="radio"
														value="N" @click="getCmpCodeMenu('N')" /><label class="form-check-label"
														for="localYn2">境外</label>
												</div>
												<select name="local" class="form-select" v-model="compCode">
													<option value="">全部</option>
													<option v-for="item in cmpCodeMenu" :value="item.companyCode">
														{{ $filters.defaultValue(item.cname, '--') }}
													</option>
												</select>
											</div>
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> 計價幣別</label>
											<select class="form-control" id="fundCurMenu" ref="fundCurMenu" multiple title="請選擇幣別"
												v-model="curObjs" data-style="btn-white">
												<option value="">全部</option>
												<option v-for="(item, index) in curOption" :key="index" :value="item.value">
													{{ $filters.defaultValue(item.name, '--') }}
												</option>
											</select>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> 基金類別 </label>
											<select name="TypeCode" class="form-select" v-model="investmentTypeCode">
												<option value="">全部</option>
												<option v-for="item in investmentTypeMenu" :key="index" :value="item.investmentTypeCode">
													{{ $filters.defaultValue(item.name, '--') }}
												</option>
											</select>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> 投資地區 </label>
											<select name="GeoFocus" class="form-select" v-model="geoFocusCode">
												<option value="">全部</option>
												<option v-for="item in geoFocusMenu" :key="index" :value="item.geoFocusCode">
													{{ $filters.defaultValue(item.name, '--') }}
												</option>
											</select>
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-6">
											<label class="form-label mg-e-60"> 投資標的-Lipper全球分類 </label>
											<select name="GlobalClass" class="form-select" v-model="globalClassCode">
												<option value="">全部</option>
												<option v-for="item in globalClassMenu" :key="index" :value="item.globalClassCode">
													{{ $filters.defaultValue(item.name, '--') }}
												</option>
											</select>
										</div>

										<div class="form-group col-12 col-lg-6">
											<label class="form-label mg-e-60"> 投資標的-Lipper區域分類 </label>
											<select name="LocalClass" class="form-select" v-model="localClassCode">
												<option value="">全部</option>
												<option v-for="item in localClassMenu" :key="index" :value="item.localClassCode">
													{{ $filters.defaultValue(item.name, '--') }}
												</option>
											</select>
										</div>
									</div>

									<div class="form-footer">
										<a class="btn btn-primary" @click.prevent="getAdvancePage(0, 'info')">查詢</a>
									</div>
								</div>
							</div>
						</div>

						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup4-2">
								<h4>基金風險</h4>
							</div>
							<div class="collapse show" id="collapseListGroup4-2">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> 依標準差 </label>
											<select name="volatility" class="form-select" v-model="stdValueType">
												<option value="ALL">全部</option>
												<option value="X1">x &lt;= 2</option>
												<option value="X2">2 &lt; x &lt;= 6</option>
												<option value="X3">6 &lt; x</option>
											</select>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> 依Beta值 </label>
											<select name="beta" class="form-select" v-model="betaValueType">
												<option value="ALL">全部</option>
												<option value="X1">x &lt;= 0</option>
												<option value="X2">0 &lt; x &lt;= 1</option>
												<option value="X3">1 &lt; x</option>
											</select>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> 依相關係數 </label>
											<select name="beta" class="form-select" v-model="corValueType">
												<option value="ALL">全部</option>
												<option value="X1">x &lt;= 0.5</option>
												<option value="X2">0.5 &lt; x &lt;= 0.8</option>
												<option value="X3">0.8 &lt; x</option>
											</select>
										</div>
									</div>

									<div class="form-footer">
										<a class="btn btn-primary" @click.prevent="getAdvancePage(0, 'basic')">查詢</a>
									</div>
								</div>
							</div>
						</div>

						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup4-3">
								<h4>基金績效</h4>
							</div>
							<div class="collapse show" id="collapseListGroup4-3">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-6">
											<label class="form-label"> 依年度績效 </label>
											<div class="input-group">
												<select class="form-select" id="year" v-model="assetStatPctCode">
													<option v-for="item in yearList" :value="item.value">
														{{ $filters.formatNumber(item.name, '--') }}
													</option>
												</select>

												<div class="input-group-text">年</div>
												<div class="input-group-text">績效</div>

												<select name="yearPerfmType" class="form-select" v-model="annualPerfType">
													<option value="ALL">全部</option>
													<option value="X1">x &lt;= 0%</option>
													<option value="X2" selected>0 &lt; x &lt;= 30%</option>
													<option value="X3">30% &lt; x &lt;= 100%</option>
													<option value="X4">100% &lt; x</option>
												</select>
											</div>
										</div>
										<div class="form-group col-12 col-lg-6">
											<label class="form-label"> 依累積績效 </label>
											<div class="input-group">
												<select name="accPerfm" class="form-select" v-model="perfRangeType">
													<option value="ALL">全部</option>
													<option value="PCTLTD">成立至今</option>
													<option value="PCTYTD">年初至今</option>
													<option value="PCT1MTD">1月</option>
													<option value="PCT3MTD">3月</option>
													<option value="PCT6MTD">6月</option>
													<option value="PCT9MTD">9月</option>
													<option value="PCT1YTD">1年</option>
													<option value="PCT2YTD">2年</option>
													<option value="PCT3YTD">3年</option>
													<option value="PCT5YTD">5年</option>
												</select>
												<div class="input-group-text">期間</div>
												<div class="input-group-text">績效</div>

												<select name="accPerfmType" class="form-select" v-model="perfType">
													<option value="ALL">全部</option>
													<option value="X1">x &lt;= 0%</option>
													<option value="X2">0 &lt; x &lt;= 30%</option>
													<option value="X3">30% &lt; x &lt;= 100%</option>
													<option value="X4">100% &lt; x</option>
												</select>
											</div>
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> Lipper排名 </label>
											<div class="input-group">
												<select name="lipperRankType" class="form-select" v-model="lipperPointer">
													<option value="ALL">全部</option>
													<option value="TOTRETOV ">整體回報</option>
													<option value="CONSRETOV">穩定回報</option>
													<option value="CAPPRESOV">保守回報</option>
												</select>
											</div>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> 指標 排名</label>
											<div class="input-group">
												<select name="lipperRankType" class="form-select" v-model="rank">
													<option value="ALL">全部</option>
													<option value="ONE">1</option>
													<option value="TWO">2</option>
													<option value="THREE">3</option>
													<option value="FOUR">4</option>
													<option value="FIVE">5</option>
												</select>
											</div>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> Sharpe 值 </label>
											<select name="sharpe" class="form-select" v-model="sharpeValueType">
												<option value="ALL">全部</option>
												<option value="X1">x &lt;= 0</option>
												<option value="X2">0 &lt; x &lt;= 1</option>
												<option value="X3">1 &lt; x</option>
											</select>
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-6">
											<label class="form-label mg-e-10">單筆投資(10,000)回報金額(1年) </label>
											<div class="d-inline-block">
												<select name="investRange" class="form-select" v-model="investRange">
													<option value="ALL">全部</option>
													<option value="R1">10,000 &lt;= x &lt; 12,000</option>
													<option value="R2">12,000 &lt;= x &lt; 15,000</option>
													<option value="R3">15,000 &lt;= x</option>
												</select>
											</div>
										</div>
										<div class="form-group col-12 col-lg-6">
											<label class="form-label mg-e-10"> 定期定額(1000)回報金額(1年)</label>
											<div class="d-inline-block">
												<select name="dcaRange" class="form-select" v-model="dcaRange">
													<option value="ALL">全部</option>
													<option value="R1">x &lt;= 20,000</option>
													<option value="R2">20,000 &lt; x &lt;= 30,000</option>
													<option value="R3">30,000 &lt; x</option>
												</select>
											</div>
										</div>
									</div>

									<div class="form-footer">
										<a class="btn btn-primary" @click.prevent="getAdvancePage(0, 'performance')">查詢</a>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div id="searchResult" v-if="pageData.content.length > 0">
				<div class="tab-nav-line">
					<ul class="nav nav-line"
						style="background-color: transparent; margin-top: 10px; margin-bottom: 20px; border-bottom: 2px solid #e2e2e2">
						<li class="nav-item">
							<a href="#pie_chart1" data-bs-toggle="tab" class="nav-link"
								:class="{ active: selectedTab === 'pie_chart1' }" @click.prevent="setSelectedTab('pie_chart1')">基本資料</a>
						</li>
						<li class="nav-item">
							<a href="#pie_chart2" data-bs-toggle="tab" class="nav-link"
								:class="{ active: selectedTab === 'pie_chart2' }" @click.prevent="setSelectedTab('pie_chart2')">報酬率</a>
						</li>
						<li class="nav-item">
							<a href="#pie_chart3" data-bs-toggle="tab" class="nav-link"
								:class="{ active: selectedTab === 'pie_chart3' }" @click.prevent="setSelectedTab('pie_chart3')">其他</a>
						</li>
					</ul>
					<!-- 一般篩選與進階搜尋共用查詢結果(分頁重查呼叫不同資料來源) -->
					<div class="tab-content">
						<div role="tabpanel" class="tab-pane active" id="pie_chart1">
							<div class="card card-table">
								<div class="card-header">
									<h4>查詢結果</h4>
									<div style="display: flex">
										<vue-pagination :pageable="pageData"
											:goto-page="activeTab == 'common' ? gotoPage : getAdvancePage"></vue-pagination>
										<button type="button" class="btn btn-info ms-2"
											@click="performancesCompareModelHandler()">績效比較圖</button>
									</div>
								</div>
								<div class="table-responsive">
									<table class="table table-RWD table-bordered text-center">
										<thead>
											<tr>
												<th class="wd-100 text-start">加入比較</th>
												<th>商品代號<a href="#" class="icon-sort" @click="sort('BANK_PRO_CODE')"></a></th>
												<th class="10% text-start">
													商品中文名稱<a href="#" class="icon-sort" @click="sort('PRO_NAME')"></a>
												</th>
												<th>基金類型<a href="#" class="icon-sort" @click="sort('PROTYPE_NAME')"></a></th>
												<th>計價幣別<a href="#" class="icon-sort" @click="sort('CUR_CODE')"></a></th>
												<th>風險等級<a href="#" class="icon-sort" @click="sort('RISK_NAME')"></a></th>
												<th>淨值<a href="#" class="icon-sort" @click="sort('PRICE_LC')"></a></th>
												<th>淨值日期<a href="#" class="icon-sort" @click="sort('D_DATE')"></a></th>
												<th>前一日漲跌幅<a href="#" class="icon-sort" @click="sort('PRICE_FLUC_LC')"></a></th>
												<th class="text-center" width="120">執行</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="(item, index) in pageData.content">
												<td data-th="加入比較" class="text-start text-center">
													<input class="form-check-input text-center" v-model="selectedItems[item.lipperId]"
														:id="'id-' + item.bankProCode" type="checkbox" :disabled="!item.lipperId" />
													<label class="form-check-label" :for="'id-' + item.bankProCode"></label>
												</td>
												<td data-th="商品代號">
													<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
												</td>
												<td class="text-start" data-th="商品中文名稱">
													<span v-if="item.proCode != null && item.pfcatCode != null && item.proName != null">
														<a class="tx-link" @click="fundModalHandler(item.proCode, item.pfcatCode)">{{
															$filters.defaultValue(item.proName, '--')
														}}</a>
													</span>
													<span v-else>
														{{ item.proName != null ? item.proName : '--' }}
													</span>
												</td>
												<td class="text-end" data-th="基金類型">
													<span>{{ $filters.defaultValue(item.proTypeName, '--') }}</span>
												</td>
												<td class="text-end" data-th="計價幣別">
													<span>{{ $filters.defaultValue(item.curCode, '--') }}</span>
												</td>
												<td class="text-end" data-th="風險等級">
													<span>{{ $filters.defaultValue(item.riskName, '--') }}</span>
												</td>
												<td class="text-end" data-th="淨值">
													<span>{{ $filters.formatNumber(item.priceLc, '0,0.00' || '--') }}</span>
												</td>
												<td class="text-end" data-th="淨值日期">
													<span>{{ $filters.defaultValue(item.ddate, '--') }}</span>
												</td>
												<td class="text-end" data-th="前一日漲跌幅">
													<span>{{ $filters.formatNumber(item.priceFlucLc, '0,0.00' || '--') }}</span>
												</td>
												<td data-th="執行">
													<span v-if="item.proCode">
														<button v-if="activeTab === 'fast' && fastCode === '06'" type="button"
															class="btn btn-primary" title="移除我的最愛" @click="remove(item.proCode)">
															移除最愛
														</button>
														<button v-else type="button" class="btn btn-dark btn-icon" data-bs-toggle="tooltip"
															title="加入我的最愛" @click="favoritesHandler(item.proCode, item.pfcatCode)">
															<i class="bi bi-heart text-danger"></i>
														</button>
													</span>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
						<div role="tabpanel" class="tab-pane" id="pie_chart2">
							<div class="form-check-group">
								<div class="form-check form-check-inline">
									<input class="form-check-input" id="rateO" name="intRateRank" v-model="rateCurType" type="radio"
										value="O" />
									<label class="form-check-label" for="rateO">報酬率顯示(原幣)</label>

									<input class="form-check-input ms-2" id="rateT" name="intRateRank" v-model="rateCurType" type="radio"
										value="T" />
									<label class="form-check-label ms-2" for="rateT">報酬率顯示(台幣)</label>
								</div>
							</div>
							<div class="card card-table">
								<div class="card-header">
									<h4>查詢結果</h4>
									<div style="display: flex">
										<vue-pagination :pageable="pageData"
											:goto-page="activeTab == 'common' ? gotoPage : getAdvancePage"></vue-pagination>
										<button type="button" class="btn btn-info ms-2"
											@click="performancesCompareModelHandler()">績效比較圖</button>
									</div>
								</div>
								<div class="table-responsive">
									<table class="table table-RWD table-bordered text-center">
										<thead>
											<tr>
												<th rowspan="2" class="wd-100 text-start">選取</th>
												<th rowspan="2">商品代號<a href="#" class="icon-sort" @click="sort('BANK_PRO_CODE')"></a></th>
												<th rowspan="2" class="10% text-start">
													商品中文名稱<a href="#" class="icon-sort" @click="sort('PRO_NAME')"></a>
												</th>
												<th colspan="8">
													<span>{{ $filters.defaultValue(rateCurType === 'O' ? '原幣報酬率' : '台幣報酬率', '--') }}</span>
												</th>
											</tr>
											<tr>
												<th>
													報酬率-1日<a href="#" class="icon-sort"
														@click="sort(rateCurType === 'O' ? 'RETURN_FC' : 'RETURN_LC')"></a>
												</th>
												<th>
													報酬率-1個月<a href="#" class="icon-sort"
														@click="sort(rateCurType === 'O' ? 'FC_1M_RETURN' : 'LC_1M_RETURN')"></a>
												</th>
												<th>
													報酬率-3個月<a href="#" class="icon-sort"
														@click="sort(rateCurType === 'O' ? 'FC_3M_RETURN' : 'LC_3M_RETURN')"></a>
												</th>
												<th>
													報酬率-6個月<a href="#" class="icon-sort"
														@click="sort(rateCurType === 'O' ? 'FC_6M_RETURN' : 'LC_6M_RETURN')"></a>
												</th>
												<th>
													報酬率-1年<a href="#" class="icon-sort"
														@click="sort(rateCurType === 'O' ? 'FC_1Y_RETURN' : 'LC_1Y_RETURN')"></a>
												</th>
												<th>
													報酬率-3年<a href="#" class="icon-sort"
														@click="sort(rateCurType === 'O' ? 'FC_3Y_RETURN' : 'LC_3Y_RETURN')"></a>
												</th>
												<th>
													報酬率-今年以來<a href="#" class="icon-sort"
														@click="sort(rateCurType === 'O' ? 'FC_YTD_RETURN' : 'LC_YTD_RETURN')"></a>
												</th>
												<th class="text-center" width="120">執行</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="item in pageData.content">
												<td data-th="加入比較" class="text-start text-center">
													<input class="form-check-input text-center" v-model="selectedItems[item.lipperId]"
														:id="'id-' + item.bankProCode" type="checkbox" :disabled="!item.lipperId" />
													<label class="form-check-label" :for="'id-' + item.bankProCode"></label>
												</td>
												<td data-th="商品代號">
													<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
												</td>
												<td class="text-start" data-th="商品中文名稱">
													<span>
														<a class="tx-link" @click="fundModalHandler(item.proCode, item.pfcatCode)">{{
															$filters.defaultValue(item.proName, '--')
														}}</a>
													</span>
												</td>
												<td class="text-end" data-th="報酬率-1日">
													<span>{{
														$filters.defaultValue(
															rateCurType === 'O'
																? $filters.formatPct(item.returnFc)
																: $filters.formatPct(item.returnLc),
															'--'
														)
													}}
														%
													</span>
												</td>
												<td class="text-end" data-th="報酬率-1個月">
													<span>{{
														$filters.defaultValue(
															rateCurType === 'O'
																? $filters.formatPct(item.fc1mReturn)
																: $filters.formatPct(item.lc1mReturn),
															'--'
														)
													}}
														%
													</span>
												</td>
												<td class="text-end" data-th="報酬率-3個月">
													<span>{{
														$filters.defaultValue(
															rateCurType === 'O'
																? $filters.formatPct(item.fc3mReturn)
																: $filters.formatPct(item.lc3mReturn),
															'--'
														)
													}}
														%
													</span>
												</td>
												<td class="text-end" data-th="報酬率-6個月">
													<span>{{
														$filters.defaultValue(
															rateCurType === 'O'
																? $filters.formatPct(item.fc6mReturn)
																: $filters.formatPct(item.lc6mReturn),
															'--'
														)
													}}
														%
													</span>
												</td>
												<td class="text-end" data-th="報酬率-1年">
													<span>{{
														$filters.defaultValue(
															rateCurType === 'O'
																? $filters.formatPct(item.fc1yReturn)
																: $filters.formatPct(item.lc1yReturn),
															'--'
														)
													}}
														%
													</span>
												</td>
												<td class="text-end" data-th="報酬率-3年">
													<span>{{
														$filters.defaultValue(
															rateCurType === 'O'
																? $filters.formatPct(item.fc3yReturn)
																: $filters.formatPct(item.lc3yReturn),
															'--'
														)
													}}
														%
													</span>
												</td>
												<td class="text-end" data-th="報酬率-今年以來">
													<span>{{
														$filters.defaultValue(
															rateCurType === 'O'
																? $filters.formatPct(item.fcYtdReturn)
																: $filters.formatPct(item.lcYtdReturn),
															'--'
														)
													}}
														%
													</span>
												</td>
												<td data-th="執行">
													<button v-if="activeTab === 'fast' && fastCode === '06'" type="button" class="btn btn-primary"
														title="移除我的最愛" @click="remove(item.proCode)">
														移除最愛
													</button>
													<button v-else type="button" class="btn btn-dark btn-icon" data-bs-toggle="tooltip"
														title="加入我的最愛" @click="favoritesHandler(item.proCode, item.pfcatCode)">
														<i class="bi bi-heart text-danger"></i>
													</button>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
						<div role="tabpanel" class="tab-pane" id="pie_chart3">
							<div class="card card-table">
								<div class="card-header">
									<h4>查詢結果</h4>
									<div style="display: flex">
										<vue-pagination :pageable="pageData"
											:goto-page="activeTab == 'common' ? gotoPage : getAdvancePage"></vue-pagination>
										<button type="button" class="btn btn-info ms-2"
											@click="performancesCompareModelHandler()">績效比較圖</button>
									</div>
								</div>
								<div class="table-responsive">
									<table class="table table-RWD table-bordered text-center">
										<thead>
											<tr>
												<th class="wd-100 text-start">選取</th>
												<th>商品代號<a href="#" class="icon-sort" @click="sort('BANK_PRO_CODE')"></a></th>
												<th class="10% text-start">
													商品中文名稱<a href="#" class="icon-sort" @click="sort('PRO_NAME')"></a>
												</th>
												<th>年化標準差<a href="#" class="icon-sort" @click="sort('VALUE_USD_STD')"></a></th>
												<th>Sharpe<a href="#" class="icon-sort" @click="sort('VALUE_USD_SHP')"></a></th>
												<th>Beta<a href="#" class="icon-sort" @click="sort('VALUE_USD_BET')"></a></th>
												<th>Lipper整體總評級<br />3年</th>
												<th>Lipper穩定回報<br />3年</th>
												<th>Lipper保本能力<br />3年</th>
												<th class="text-center" width="120">執行</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="item in pageData.content">
												<td data-th="加入比較" class="text-start text-center">
													<input class="form-check-input text-center" v-model="selectedItems[item.lipperId]"
														:id="'id-' + item.bankProCode" type="checkbox" :disabled="!item.lipperId" />
													<label class="form-check-label" :for="'id-' + item.bankProCode"></label>
												</td>
												<td data-th="商品代號">
													<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
												</td>
												<td class="text-start" data-th="商品中文名稱">
													<span>
														<a class="tx-link" @click="fundModalHandler(item.proCode, item.pfcatCode)">{{
															$filters.defaultValue(item.proName, '--')
														}}</a>
													</span>
												</td>
												<td class="text-end" data-th="年化標準差">
													<span>{{ $filters.defaultValue($filters.formatPct(item.valueUsdStd), '--') }}%</span>
												</td>
												<td class="text-end" data-th="Sharpe">
													<span>{{ $filters.defaultValue($filters.formatPct(item.valueUsdShp), '--') }}%</span>
												</td>
												<td class="text-end" data-th="Beta">
													<span>{{ $filters.defaultValue($filters.formatPct(item.valueUsdBet), '--') }}%</span>
												</td>
												<td class="text-end" data-th="Lipper整體總評級3年">
													<span>{{ $filters.formatNumber(item.scoreTot, '--') }}</span>
												</td>
												<td class="text-end" data-th="Lipper穩定回報3年">
													<span>{{ $filters.formatNumber(item.scoreCon, '--') }}</span>
												</td>
												<td class="text-end" data-th="Lipper保本能力3年">
													<span>{{ $filters.formatNumber(item.scoreCap, '--') }}</span>
												</td>
												<td data-th="執行">
													<button v-if="activeTab === 'fast' && fastCode === '06'" type="button" class="btn btn-primary"
														title="移除我的最愛" @click="remove(item.proCode)">
														移除最愛
													</button>
													<button v-else type="button" class="btn btn-dark btn-icon" data-bs-toggle="tooltip"
														title="加入我的最愛" @click="favoritesHandler(item.proCode, item.pfcatCode)">
														<i class="bi bi-heart text-danger"></i>
													</button>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- 加權條件篩選 查詢結果  -->
			<div id="searchResult" v-if="rankPageData.length > 0">
				<div class="tab-nav-line">
					<div class="tab-content">
						<div role="tabpanel" class="tab-pane active">
							<div class="card card-table">
								<div class="card-header">
									<h4>查詢結果</h4>
									<div style="display: flex">
										<vue-pagination :pageable="rankPageData" :goto-page="getRankPageData"></vue-pagination>
									</div>
								</div>
								<div class="table-responsive">
									<table class="table table-RWD table-bordered text-center">
										<thead>
											<tr>
												<th>商品代號</th>
												<th class="10% text-start">商品中文名稱</th>
												<th v-for="rank in proSearchesMaps">{{ rank.searchName }}</th>
												<th>加權分數</th>
												<th class="text-center" width="120">執行</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="(item, index) in rankPageData">
												<td data-th="商品代號">
													<span>{{ $filters.defaultValue(item.BANK_PRO_CODE, '--') }}</span>
												</td>
												<td class="text-start" data-th="商品中文名稱">
													<span>
														<a class="tx-link" @click="fundModalHandler(item.PRO_CODE, item.PFCAT_CODE, $event)">{{
															$filters.defaultValue(item.PRO_NAME, '--')
														}}</a>
													</span>
												</td>
												<td v-for="rank in proSearchesMaps" class="text-end" :data-th="rank.searchName">
													{{ getRankValue(index, rank.searchCodeName) }}
												</td>
												<td data-th="加權分數">
													<span>{{ $filters.formatNumber(item.SCORE, '--') }}</span>
												</td>
												<td data-th="執行">
													<button type="button" class="btn btn-dark btn-icon" data-bs-toggle="tooltip" title="觀察"
														@click="favoritesHandler(item.PRO_CODE, item.PFCAT_CODE)">
														<i class="bi bi-heart"></i>
													</button>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<vue-modal :is-open="isOpenCompareModal" :before-close="isOpenCompareModal = false">
				<template v-slot:content="props">
					<vue-performances-compare-modal :close="props.close" ref="performancesCompareModalRef"
						id="performancesCompareModal">
					</vue-performances-compare-modal>
				</template>
			</vue-modal>
		</div>
	</div>
	<vue-modal :is-open="isOpenCmpTypeModal" :before-close="isOpenCmpTypeModal = false">
		<template v-slot:content="props">
			<vue-group-fundcmp-modal :close="props.close" ref="groupFundCmpTypeModalRef" id="groupFundCmpTypeModal"
				:issuer-prop="fundCmpItem" @selected="selectedFundCmp"></vue-group-fundcmp-modal>
		</template>
	</vue-modal>
</template>
<script>
import _ from 'lodash';
import vueGroupFundcmpModal from './groupFundCmpModal.vue';
import vueGroupGeofocusModal from './groupGeoFocusModal.vue';
import vueModal from '@/views/components/model.vue';
import pagination from '@/views/components/pagination.vue';
import performancesCompareModal from './performancesCompareModal.vue';
import Modal from 'bootstrap/js/dist/modal.js';
import { getImgURL } from '@/utils/imgURL.js';

export default {
	components: {
		'vue-pagination': pagination,
		vueModal,
		vueGroupFundcmpModal,
		vueGroupGeofocusModal,
		'vue-performances-compare-modal': performancesCompareModal
	},
	props: {
		fundModalHandler: Function,
		favoritesHandler: Function,
		curOption: {
			type: Array,
			default: () => []
		},
		riskMenu: Array
	},
	data: function () {
		return {
			groupGeoFocusModalRef: {},
			groupFundCmpTypeModalRef: {},
			proWeightedTypeMenu: [],

			activeTab: 'common',
			selectedTab: 'pie_chart1',
			bankProCode: null, // 商品代碼
			proName: null, // 商品名稱
			selectedItems: {}, // 加入比較選項
			curObjs: [], // 計價幣別
			riskCodes: [], //風險等級
			genLocalYn: '', // 境內外基金
			proTypeCode: '', // 基金類型
			intFreqUnitType: '', //配息頻率
			intRateRank: '', // 配息率
			backEndLoadYn: '', // 申購手續費
			isinCode: '',
			lipperPoint: '', // Lipper評級-指標
			lipperRank: '', // Lipper評級-排名
			fundCmpItem: [], // 基金公司
			geoFocusItem: [], // 投資地區
			statCode: 'PCT1TD', // 報酬率
			maxDValue: null, // 報酬率迄
			minDValue: null, // 報酬率起

			nation: '', //國內還國外基金公司，預設國內

			fundFastMenu: [], // 快速篩選條件
			timeRangeMenu: [], // 顯示區間
			rowNumerMenu: [], // 顯示資料筆數
			perfMenu: [], // 標的績效選項
			globalClassCodeMenu: [], // 前10筆基金類別選項
			globalOtherCodeMenu: [], // 其餘基金類別選項

			fastCode: '03', //快速篩選
			timeRange: null, // 快速 顯示區間
			rowNumber: '', // 快速 顯示資料筆數
			perf: null, // 標的績效

			//商品查詢 畫面
			localMenu: [], // 境內外基金選項
			proTypeMenu: [], // 基金類型下拉
			intFreqUnitMenu: [], // 配息頻率下拉
			intRateRankMenu: [], // 配息率選項
			backEndLoadMenu: [], // 申購手續費選項
			statCodeMenu: [], // 報酬率選項

			// 比較條件權重設定
			searchCodeName: '', // 風險/績效
			searchValue: '', //權重%
			proSearchesMaps: [], // 風險/績效陣列
			proSearches: [], //歷史查詢條件列表
			showButton: false, // 儲存條件按鈕

			//進階搜尋
			fundSaleType: 'LOCAL', //基金類別
			bankProCodes: null, // 本行基金代碼
			fundName: null, // 基金英文名稱
			fundCname: null, // 基金名稱
			localYn: 'Y', // 基金公司-國內/國外
			compCode: '', // 基金公司代碼
			curCode: '', // 計價幣別代碼
			investmentTypeCode: '', //基金類別
			geoFocusCode: '', // 投資地區
			globalClassCode: '', //投資標的-Lipper全球分類
			localClassCode: '', // 投資標的-Lipper區域分類
			stdValueType: 'ALL', // 標準差
			betaValueType: 'ALL', // Beta
			corValueType: 'ALL', // 相關係數
			assetStatPctCode: 'PCT1YT0', // 依年度績效代碼
			annualPerfType: 'ALL', // 年度績效-績效
			perfRangeType: 'ALL', // 累積績效-期間代碼
			perfType: 'ALL', // 累積績效-績效
			lipperPointer: 'ALL', // 排名-指標代碼
			rank: 'ALL', // 排名
			sharpeValueType: 'ALL', // Sharpe值
			investRange: 'ALL', // 單筆投資(10,000)回報金額(1年)
			dcaRange: 'ALL', // 定期定額(1000)回報金額(1年)
			cmpCodeMenu: [], // 基金公司
			curMenu: [], // 全資料庫基金 幣別選擇
			curCodeData: [], // 全資料庫基金 幣別選擇
			investmentTypeMenu: [], // 基金類別選單
			geoFocusMenu: [], // 投資地區選單
			globalClassMenu: [], // 投資標的-Lipper全球分類選單
			localClassMenu: [], // 投資標的-Lipper區域分類選單
			advanceType: null, // 進接搜尋類別

			// 加權條件篩選 儲存條件
			searchName: null,
			memo: null,
			isOpenModal2: false, // 歷史查詢條件model
			isOpenModal3: false, // 儲存條件model

			//查詢條件
			rateCurType: 'O', //(報酬率) 報酬率幣別顯示
			proCodes: [], // 商品代碼
			checkboxs: [], // 加入比較選項
			lipperIds: [], // 績效比較商品代碼
			pageData: {
				content: []
			},
			rankPageData: [],
			pageable: {
				page: 0,
				size: 20,
				sort: ['PRO_CODE'],
				direction: 'ASC'
			},
			isOpenCmpTypeModal: false,
			isOpenCompareModal: false,
			isOpenGeoFocusModal: false
		};
	},
	watch: {
		getImgURL,
		selectedItems: {
			handler(newValues) {
				this.lipperIds = Object.keys(newValues).filter((lipperId) => newValues[lipperId]);
			},
			deep: true
		},
		activeTab(newVal, oldVal) {
			var self = this;
			self.fastCode = '03';
			self.fastChange(self.fastCode);
			self.curObjs = [];
			$(this.$refs.curMenuFund).selectpicker('deselectAll');
			$(this.$refs.fundCurMenu).selectpicker('deselectAll');
		},
		selectedTab(newTab, oldTab) {
			var self = this;
			self.lipperIds = [];
			self.selectedItems = {};
		},
		curObjs(newVal, oldVal) {
			var self = this;
			if (newVal[0] !== oldVal[0]) {
				if (newVal[0] === '') {
					$(this.$refs.curMenuFund).selectpicker('selectAll');
					$(this.$refs.fundCurMenu).selectpicker('selectAll');
				} else if (oldVal[0] === '' && newVal[0] !== '') {
					$(this.$refs.curMenuFund).selectpicker('deselectAll');
					$(this.$refs.fundCurMenu).selectpicker('deselectAll');
				}
			}
		}
	},
	mounted: async function () {
		// 確保在 Vue 完成渲染後再初始化 Modal
		this.$nextTick(() => {
			this.groupGeoFocusModalRef = new Modal(this.$refs.groupGeoFocusModalRef); // 在 Vue 渲染完成後初始化
			this.groupFundCmpTypeModalRef = new Modal(this.$refs.groupFundCmpTypeModalRef); // 在 Vue 渲染完成後初始化
		});
		var self = this;
		$(this.$refs.curMenuFund).selectpicker('refresh');
		$(this.$refs.fundCurMenu).selectpicker('refresh');
		self.getLocalMenu();
		self.getProTypeMenu(); // 取得基金類型選項
		self.getIntFreqUnitTypeMenu(); //取得配息頻率選項
		self.getIntRateRankMenu(); //取得配息率選項
		self.getBackEndLoadMenu(); // 取得申購手續費選項
		self.getFundFastMenu(); // 取得快速篩選條件選項
		self.getTimeRangeMenu(); // 取得顯示區間選項
		self.getRowNumerMenu(); // 取得顯示資料筆數選項
		self.getPerfMenu(); // 取得標的績效選項
		self.getProWeightedTypeMenu(); // 取得風險/績效選單選項
		self.getCurMenu(); // 取得進階搜尋預設計價幣別資料
		self.getInvestmentTypeMenu(); // 取得基金類別選單
		self.getGeoFocusMenu(); // 投資地區選單
		self.getGlobalClassMenu(); // 取得投資標的-Lipper全球分類選單
		self.getLocalClassMenu(); // 取得投資標的-Lipper區域分類
		self.getCmpCodeMenu('Y'); // 基金公司選單選項
		self.getStatCodeMenu(); // 報酬率選項
		self.fastChange(self.fastCode);
	},
	computed: {
		yearList() {
			let item = [];
			let nowYear = new Date().getFullYear();
			item.push({ value: 'PCT1YT0', name: nowYear - 1 });
			item.push({ value: 'PCT1YT1', name: nowYear - 2 });
			item.push({ value: 'PCT1YT2', name: nowYear - 3 });
			item.push({ value: 'PCT1YT3', name: nowYear - 4 });
			item.push({ value: 'PCT1YT4', name: nowYear - 5 });
			return item;
		}
	},
	methods: {
		// 條件Tab切換
		changeTab: function (tabName) {
			var self = this;
			self.activeTab = tabName;
			self.fundCmpChange();
			self.pageData = {
				// 清空查詢資料
				content: []
			};
			self.rankPageData = [];
			self.lipperIds = [];
			self.selectedItems = {};
		},
		// 取得境內外基金選項
		getLocalMenu: async function () {
			console.log('取得境內外基金選項');
			var self = this;
			self.localMenu = [{ codeValue: '', codeName: '不限' }];
			var localYnList = [];
			const res = await this.$api.getAdmCodeDetail({ codeType: 'LOCAL_YN' });
			localYnList = res.data;
			self.localMenu.push(...localYnList);
			console.log('境內外基金選項', self.localMenu);
			// Array.prototype.push.apply(self.localMenu, localYnList);
		},
		// 取得基金類型選項
		getProTypeMenu: async function () {
			var self = this;
			const res = await this.$api.getProTypeListApi({ pfcatCode: 'FUND' });
			self.proTypeMenu = res.data;
		},
		// 取得配息頻率選項
		getIntFreqUnitTypeMenu: async function () {
			var self = this;
			const res = await this.$api.getAdmCodeDetail({ codeType: 'INT_FREQ_UNITTYPE' });
			self.intFreqUnitMenu = res.data;
		},
		// 取得配息率選項
		getIntRateRankMenu: async function () {
			var self = this;
			self.intRateRankMenu = [{ codeValue: '', codeName: '不限' }];
			var intRateRankList = [];
			const res = await this.$api.getAdmCodeDetail({ codeType: 'INT_RATE_RANK' });
			intRateRankList = res.data;
			Array.prototype.push.apply(self.intRateRankMenu, intRateRankList);
		},
		getBackEndLoadMenu: async function () {
			var self = this;
			const res = this.$api.getAdmCodeDetail({ codeType: 'BACK_END_LOAD_YN' });
			self.backEndLoadMenu = res.data;
		},
		// 報酬率選項
		getStatCodeMenu: async function () {
			var self = this;
			const res = this.$api.getAdmCodeDetail({ codeType: 'STAT_CODE' });
			self.statCodeMenu = res.data;
		},
		// 取得快速篩選條件選項
		getFundFastMenu: async function () {
			var self = this;
			const res = await this.$api.getFundFastMenuApi();
			self.fundFastMenu = res.data;
		},
		// 取得顯示區間
		getTimeRangeMenu: async function () {
			var self = this;
			const res = await this.$api.getTimeRangeMenuApi();
			self.timeRangeMenu = res.data;
		},
		// 取得顯示資料筆數
		getRowNumerMenu: async function () {
			var self = this;
			const res = await this.$api.getRowNumerMenuApi();
			self.rowNumerMenu = res.data;
		},
		// 績效排行
		getPerfMenu: async function () {
			var self = this;
			const res = await this.$api.getPerfMenuApi();
			self.perfMenu = res.data;
		},
		// 進階搜尋 基金類別選擇全資料庫基金=>計價幣別選單
		getCurMenu: async function () {
			var self = this;
			const res = await this.$api.getCurMenuApi();
			res.data.forEach(function (item) {
				var obj = { value: item.curCode, name: item.curName + ' ' + item.curCode };
				self.curCodeData.push(obj);
			});
		},
		// 進階搜尋 基金類別
		getfundTypeMenu(fundSaleType) {
			var self = this;
			self.curMenu = [];
			if (fundSaleType === 'LOCAL') {
				//本行基金
				self.curMenu = self.curOption;
			} else {
				// 全資料庫基金
				self.curMenu = self.curCodeData;
			}
			self.curObjs = [];
			$(this.$refs.fundCurMenu).selectpicker('deselectAll'); // 重整選單
		},
		getInvestmentTypeMenu: async function () {
			// 基金類別下拉
			var self = this;
			const res = await this.$api.getInvestmentTypeMenuApi({ local: 'zh_TW' });
			self.investmentTypeMenu = res.data;
		},
		getGeoFocusMenu: async function () {
			// 投資地區下拉
			var self = this;
			const res = await this.$api.getGeoFocusMenuApi({ local: 'zh_TW' });
			self.geoFocusMenu = res.data;
		},
		getGlobalClassMenu: async function () {
			var self = this;
			var globalClassList = [];
			var globalClassOtherList = [];
			const res = await this.$api.getGlobalClassCodeMenuApi();
			globalClassList = res.data;
			const resOther = await this.$api.getGlobalClassCodeOtherMenuApi();
			globalClassOtherList = resOther.data;
			self.globalClassMenu = [...globalClassList, ...globalClassOtherList];
			self.globalClassMenu.sort((a, b) => a.globalClassCode.localeCompare(b.globalClassCode));
		},
		getLocalClassMenu: async function () {
			var self = this;
			const res = await this.$api.getLocalClassMenuApi();
			self.localClassMenu = res.data;
		},
		// 進階搜尋 基金公司
		getCmpCodeMenu: async function (cmpCode) {
			var self = this;
			var url = '';
			let res = null;
			if (cmpCode === 'Y') {
				res = await this.$api.getLocalFundCompanies();
			} else {
				res = await this.$api.getForeignFundCompanies();
			}

			self.cmpCodeMenu = res.data;
			self.compCode = '';
		},
		// 快速查詢切換
		fastChange(fastCode) {
			var self = this;
			self.timeRange = {}; // 快速 顯示區間
			self.rowNumber = ''; // 快速 顯示資料筆數
			self.perf = '';
			self.pageData = {
				// 清空查詢資料
				content: []
			};
			self.pageData = {
				// 清空查詢資料
				content: []
			};
			// 超人氣
			if (fastCode === '05') {
				$('#proPerfTimeTrFund').hide();
				$('#fundCmpTrFund').hide();
				$('#rangeFixedTrFund').show();
				$('#maxRowIdTrFund').show();
				self.timeRange = self.timeRangeMenu[0];
				// 績效排行
			} else if (fastCode === '07') {
				self.perf = 'PCTYTD';
				$('#rangeFixedTrFund').hide();
				$('#fundCmpTrFund').hide();
				$('#maxRowIdTrFund').show();
				$('#proPerfTimeTrFund').show();
			} else if (fastCode === '08') {
				// 最高配息率商品
				$('#maxRowIdTrFund').show();
				$('#rangeFixedTrFund').hide();
				$('#proPerfTimeTrFund').hide();
				$('#fundCmpTrFund').hide();
				// 4433篩選
			} else if (fastCode === '10') {
				$('#fundCmpTrFund').show();
				$('#maxRowIdTrFund').hide();
				$('#rangeFixedTrFund').hide();
				$('#proPerfTimeTrFund').hide();
				self.rowNumber = '10'; // 快速 顯示資料筆數
			} else {
				$('#maxRowIdTrFund').hide();
				$('#rangeFixedTrFund').hide();
				$('#proPerfTimeTrFund').hide();
				$('#fundCmpTrFund').hide();
				self.rowNumber = '10'; // 快速 顯示資料筆數
			}
		},
		// 取得商品資料查詢(信託-基金)-風險/績效選單
		getProWeightedTypeMenu: async function () {
			var self = this;
			const res = await this.$api.getProWeightedTypeMenuApi();
			self.proWeightedTypeMenu = res.data;
			console.log('getProWeight Success!', self.proWeightedTypeMenu);
		},
		// 由查詢結果標題觸發
		sort: function (columnName) {
			var self = this;
			if (this.pageable.sort !== columnName) {
				this.pageable.sort = columnName;
				this.pageable.direction = 'DESC';
			} else {
				this.pageable.direction = this.pageable.direction == 'ASC' ? 'DESC' : 'ASC';
			}

			if (self.activeTab === 'common') {
				this.gotoPage(0);
			} else if (self.activeTab === 'fast') {
				this.gotoFastPage(0);
			} else if (self.activeTab === 'advanced') {
				this.getAdvancePage(0, self.advanceType);
			}
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (page) {
			var self = this;

			var url = '';

			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;

			// 取德compCodeList
			var compCodeList = self.fundCmpItem.map(function (item) {
				return item.compCode;
			});

			var geoFocusCodeList = self.geoFocusItem.map(function (item) {
				return item.geoFocusCode;
			});

			const payload = {
				bankProCode: self.bankProCode,
				proName: self.proName,
				curCodes: self.curObjs,
				riskCodes: self.riskCodes,
				protypeCode: self.proTypeCode,
				localYn: self.genLocalYn,
				intFreqUnitType: self.intFreqUnitType,
				intRateRank: self.intRateRank,
				backEndLoadYn: self.backEndLoadYn,
				isinCode: self.isinCode,
				lipperPointer: self.lipperPoint,
				lipperRank: self.lipperRank,
				compCode: compCodeList,
				geoFocusCodes: geoFocusCodeList,
				statCode: self.statCode,
				minDValue: self.minDValue,
				maxDValue: self.maxDValue
			};
			const res = await this.$api.getFundProducts(payload, url);
			self.pageData = res.data;
			self.pageData.content.forEach((data) => {
				self.checkboxs.push(false);
			});
		},
		// 快速篩選
		gotoFastPage: function (page) {
			this.pageable.page = page;
			this.getFastPageData(page);
		},
		getFastPageData: async function (page) {
			var self = this;

			//			var rangeType = '';
			//			var rangeFixed = '';
			//			if (self.fastCode === '05') {
			//				// 超人氣
			//				var timeRangeObjs = _.filter(self.timeRangeMenu, {
			//					rangeType: self.timeRange
			//				});
			//				rangeType = timeRangeObjs[0].rangeType;
			//				rangeFixed = timeRangeObjs[0].rangeFixed;
			////				self.rowNumber = '10'; // 快速 顯示資料筆數
			//			}

			let lipperClassCode = [];
			self.fundCmpItem.forEach((data) => {
				lipperClassCode.push(data.compCode);
			});

			var url = '';
			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;

			const payload = {
				filterCodeValue: self.fastCode,
				timeRangeType: self.timeRange.rangeType, // 顯示區間類型
				timeRangeFixed: self.timeRange.rangeFixed, // 顯示區間數值
				rowNumberFixed: self.rowNumber,
				perfTimeCode: self.perf, // 標的績效
				lipperClassCodes: lipperClassCode // 基金公司
			};
			const res = await this.$api.getFastPageDataApi(payload, url);
			self.pageData = res.data;
			self.pageData.content.forEach((data) => {
				self.checkboxs.push(false);
			});
		},
		// 加權條件篩選
		getRankPage: function (page) {
			var self = this;
			let errorMsg = '';
			self.pageable.page = page;
			if (_.isEmpty(self.curCode)) {
				errorMsg = '請選擇績效幣別 <br>';
			}
			if (self.proSearchesMaps.length == 0) {
				errorMsg += '請至少加入一筆比較權重條件設定 <br>';
			} else {
				let sum = 0;
				self.proSearchesMaps.forEach((data) => {
					sum += parseInt(data.searchValue);
				});
				if (sum != 100) {
					errorMsg = '權重加總需為100% <br>';
				} else if (sum > 100) {
					errorMsg = '權重加總[' + sum + ']已超過100% <br>';
				}
			}
			if (errorMsg != '') {
				self.popUpMeassage(errorMsg);
			} else {
				self.getRankPageData();
				self.showButton = true;
			}
		},
		popUpMeassage: function (msg) {
			this.$swal.fire({
				icon: 'warning',
				html: msg,
				showCloseButton: true,
				confirmButtonText: '確認',
				buttonsStyling: false, // remove default button style
				customClass: {
					confirmButton: 'btn btn-danger'
				}
			});
		},
		// 儲存條件
		saveCondition() {
			var self = this;
			let errorMsg = '';
			if (self.proSearchesMaps.length == 0) {
				errorMsg += '請至少加入一筆比較權重條件設定 <br>';
			} else {
				let sum = 0;
				self.proSearchesMaps.forEach((data) => {
					sum += parseInt(data.searchValue);
				});
				if (sum != 100) {
					errorMsg = '權重加總需為100% <br>';
				}
			}
			if (errorMsg != '') {
				self.popUpMeassage(errorMsg);
			} else {
				self.isOpenModal3 = true;
			}
		},
		// 加權條件篩選
		getRankPageData: async function () {
			var self = this;
			if (self.rowNumber == '') {
				self.rowNumber = '10'; //顯示資料筆數
			}
			if (self.curCode === '') {
				self.curCode = 'TWD';
			}
			let lipperClassCode = [];
			self.fundCmpItem.forEach((data) => {
				lipperClassCode.push(data.compCode);
			});
			const payload = {
				lipperClassCodes: lipperClassCode, // 基金公司
				perfCurCode: self.curCode, // 績效幣別
				rowNumberFixed: self.rowNumber, // 顯示資料筆數
				proSearchesMaps: self.proSearchesMaps // 條件權重
			};
			const res = await this.$api.getRankPageDataApi(payload);
			self.rankPageData = res.data;
		},
		getRankValue(index, searchCodeName) {
			var self = this;
			return self.rankPageData[index][searchCodeName + '_VALUE'];
		},
		// 開啟歷史查詢條件
		openHisSearch: async function () {
			var self = this;
			const res = await this.$api.getProSearchesApi();
			self.proSearches = res.data;
			self.isOpenModal2 = true;
		},
		//歷史查詢條件 編輯
		hisEdit: async function (item) {
			var self = this;
			const ret = await this.$api.getProSearchesMap({ searchSeq: item.searchSeq });

			self.proSearchesMaps = [];
			ret.data.forEach((data) => {
				let map = {};
				map.searchCodeName = data.searchCode; // 風險/績效 value
				map.searchName = data.searchCodeName; // 風險/績效 名稱
				map.searchValue = data.searchValue; // 權重(%)
				self.proSearchesMaps.push(map);
			});
			self.isOpenModal2 = false;
		},
		//歷史查詢條件 刪除
		hisDelete(item) {
			var self = this;
			this.$bi.confirm('確認刪除?', {
				event: {
					confirmOk: async function () {
						const res = await self.$api.deleteHistorySearchApi({ searchSeq: item.searchSeq });
						self.openHisSearch();
						self.isOpenModal2 = false;
					}
				}
			});
		},
		// 風險/績效 與 權重(%) 增加
		newRank() {
			var self = this;
			let exist = null;
			let map = {};
			if (self.searchCodeName == '') {
				this.$bi.alert('請選擇風險/績效');
				return;
			}
			// 風險/績效
			var weight = self.proWeightedTypeMenu.filter(function (item) {
				console.log('weight filter:', item.codeValue, self.searchCodeName);
				return item.codeValue === self.searchCodeName;
			});
			console.log('Weight', weight);
			map.searchValue = self.searchValue; //權重%
			map.searchName = weight[0].codeName; //風險/績效名稱
			map.searchCodeName = self.searchCodeName; //風險/績效value
			self.proSearchesMaps.forEach((data) => {
				if (data.searchCodeName === self.searchCodeName) {
					exist = true;
				}
			});
			if (self.searchValue == '') {
				this.$bi.alert('請輸入權重(%)');
			} else if (parseInt(self.searchValue) > 100) {
				this.$bi.alert('權重(%)加總[' + self.searchValue + ']已超過100%');
				self.searchValue = null;
			} else if (parseInt(self.searchValue) < 0 || !_.isNumeric(self.searchValue)) {
				// 檢查數值要大於0
				this.$bi.alert('權重(%)必須大於0，請重新輸入');
				self.searchValue = null;
			} else if (exist) {
				this.$bi.alert('風險/績效[' + self.searchCodeName + ']已存在');
				self.searchValue = null;
			} else {
				self.proSearchesMaps.push(map);
				self.searchValue = null;
			}
		},
		// 加權條件篩選 儲存條件
		conditionsSave() {
			var self = this;
			const payload = {
				// searchSeq: '123456', // 查詢條件序號
				searchName: self.searchName, // 查詢條件名稱
				memo: self.memo, // 備註
				proSearchesMaps: self.proSearchesMaps // 條件權重
			};
			const res = this.$api.getProSearchesApi(payload);
			this.$bi.alert('儲存成功');
			var self = this;
			self.proSearchesMaps = [];
			self.searchName = null;
			self.memo = null;
			self.rowNumber = null;
			self.curCode = null;
		},
		// 風險/績效 與 權重(%) 刪除
		deleteWeighted(searchCodeName) {
			var self = this;
			this.$bi.confirm('確認刪除?', {
				event: {
					confirmOk: function () {
						self.proSearchesMaps = self.proSearchesMaps.filter((item) => {
							if (item.searchCodeName === searchCodeName) {
								return false; // 移除刪除項目
							}
							return true; // 保留
						});
						// _.remove(self.proSearchesMaps, (item) => item.searchCodeName === searchCodeName); // 移除刪除項目
					}
				}
			});
		},
		// 進階搜尋
		getAdvancePage: function (page, type) {
			var self = this;
			this.pageable.page = page;

			if (_.isEmpty(type)) {
				type = self.advanceType;
			} else {
				self.advanceType = type;
			}
			this.getAdvancePageData(page, type);
		},
		// 進階搜尋
		getAdvancePageData: async function (page, type) {
			var self = this;
			var url = '';
			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;
			let param = {};
			// 基金資料
			if (type === 'info') {
				var curCodes = [];
				self.curObjs.forEach(function (item) {
					curCodes.push(item);
				});
				param = {
					fundSaleType: self.fundSaleType, //基金類別
					filterCodeValue: self.fastCode,
					timeRangeType: self.timeRange, // 顯示區間類型
					timeRangeFixed: self.rowNumber, // 顯示區間數值
					perfTimeCode: self.perf, // 標的績效
					localYn: self.localYn, // 基金公司-國內/國外
					fundCname: self.fundCname, // 基金名稱
					fundName: self.fundName, // 基金英文名稱
					bankProCodes: self.bankProCodes, // 本行基金代碼
					compCode: self.compCode, // 基金公司代碼
					curCodes: curCodes, // 計價幣別代碼
					investmentTypeCode: self.investmentTypeCode, //基金類別
					globalClassCode: self.globalClassCode, //投資標的-Lipper全球分類
					geoFocusCode: self.geoFocusCode, // 投資地區
					localClassCode: self.localClassCode // 投資標的-Lipper區域分類
				};
				//基金風險
			} else if (type === 'basic') {
				param = {
					fundSaleType: self.fundSaleType, //基金類別
					stdValueType: self.stdValueType, // 標準差
					betaValueType: self.betaValueType, // Beta
					corValueType: self.corValueType // 相關係數
				};
				//基金績效
			} else if (type === 'performance') {
				param = {
					fundSaleType: self.fundSaleType, //基金類別
					assetStatPctCode: self.assetStatPctCode, // 依年度績效代碼
					annualPerfType: self.annualPerfType, // 年度績效-績效
					perfRangeType: self.perfRangeType, // 累積績效-期間代碼
					perfType: self.perfType, // 累積績效-績效
					lipperPointer: self.lipperPointer, // 排名-指標代碼
					rank: self.rank, // 排名
					sharpeValueType: self.sharpeValueType, // Sharpe值
					investRange: self.investRange, // 單筆投資(10,000)回報金額(1年)
					dcaRange: self.dcaRange // 定期定額(1000)回報金額(1年)
				};
			}
			const res = await this.$api.getAdvancePageDataApi(param, url);
			self.pageData = res.data;
			self.pageData.content.forEach((data) => {
				self.checkboxs.push(false);
			});
		},

		//一般篩選 基金公司change
		fundCmpChange() {
			var self = this;
			self.fundCmpItem = [];
			self.$refs.groupFundCmpTypeModalRef.fundcmpPropItem([]);
		},
		//顯示選擇基金公司 model (快速篩選4433篩選、加權條件篩選)
		groupFundCmpTypeModalHandler: function () {
			var self = this;
			self.$refs.groupFundCmpTypeModalRef.groupfundcmpTypeMenu();
			self.$refs.groupFundCmpTypeModalRef.fundcmpPropItem(self.fundCmpItem);
			this.isOpenCmpTypeModal = true;
		},
		// 一般篩選  顯示選擇基金公司 model //不限 國內或國外
		groupFundCmpModalHandler: function (nation) {
			var self = this;
			self.$refs.groupFundCmpTypeModalRef.groupfundcmpsMenu(nation);
			self.$refs.groupFundCmpTypeModalRef.groupfundcmpsOtherMenu(nation);
			self.$refs.groupFundCmpTypeModalRef.fundcmpPropItem(self.fundCmpItem);
			this.isOpenCmpTypeModal = true;
		},
		// 顯示基金公司選擇項目
		selectedFundCmp(fundCmpItem) {
			var self = this;
			self.isOpenCmpTypeModal = false;
			self.fundCmpItem = fundCmpItem; //取得基金公司資料
		},
		//刪除基金公司項目
		deleteFundCmpItem(compCode) {
			var self = this;
			_.remove(self.fundCmpItem, (item) => item.compCode === compCode); // 移除刪除項目
		},
		// 一般篩選  顯示選擇投資地區 model
		groupGeoFocusModalHandler: function () {
			var self = this;
			this.$refs.groupGeoFocusModalRef.geoFocusPropItem(self.geoFocusItem);
			this.isOpenGeoFocusModal = true;
		},
		// 顯示投資地區選擇項目
		selectedGeoFocus(geoFocusItem) {
			var self = this;
			self.isOpenGeoFocusModal = false;
			self.geoFocusItem = geoFocusItem; //取得基金公司資料
		},
		//刪除投資地區項目
		deleteGeoFocusItem(geoFocusCode) {
			var self = this;
			_.remove(self.geoFocusItem, (item) => item.geoFocusCode === geoFocusCode); // 移除刪除項目
		},
		//執行績效比較圖
		performancesCompareModelHandler: function () {
			var self = this;
			if (self.lipperIds.length > 0) {
				if (self.lipperIds.length > 6) {
					this.$bi.alert('最多加入6筆');
				} else {
					this.$refs.performancesCompareModalRef.comparefundPropItem(self.lipperIds, self.proCodes, 'fund');
					this.isOpenCompareModal = true;
				}
			} else {
				this.$bi.alert('至少要勾選一項商品');
			}
		},
		// 刪除我的最愛
		remove: async function (proCode) {
			var self = this;
			const res = await this.$api.deleteFavoriteApi({ proCode: proCode });
			this.$bi.alert('刪除成功');
			self.checkboxs = [];
			self.proCodes = [];
			self.gotoFastPage(0);
		},
		setSelectedTab(tabName) {
			var self = this;
			if (self.selectedTab !== tabName) {
				self.selectedTab = tabName;
			}
		}
	} // methods end
};
</script>
