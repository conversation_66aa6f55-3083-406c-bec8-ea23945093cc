<template>
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<!--頁面內容 結構型商品sp start-->
			<div class="tab-nav-tabs my-3">
				<ul class="nav nav-tabs nav-justified">
					<li class="nav-item">
						<a class="nav-link" :class="{ active: activeTab == 'common' }" data-bs-toggle="tab"
							@click="changeTab('common')">一般篩選</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" :class="{ active: activeTab == 'fast' }" data-bs-toggle="tab"
							@click="changeTab('fast')">快速篩選</a>
					</li>
				</ul>

				<div class="tab-content">
					<div class="tab-pane fade show" id="SectionA" :class="{ 'active show': activeTab == 'common' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
								<h4>查詢條件</h4>
								<span class="tx-square-bracket">為必填欄位</span>
							</div>
							<div class="collapse show" id="collapseListGroup1">
								<div class="card-body">
									<form>
										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 商品代號 </label>
												<input class="form-control" id="prod_bank_pro_code" maxlength="20" v-model="bankProCode"
													size="25" type="text" value="" />
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 商品名稱</label>
												<input class="form-control" id="prod_pro_name" maxlength="20" v-model="proName" size="45"
													type="text" value="" />
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 商品類型</label>
												<select class="form-select" id="proType" name="proType" title="請選擇類型" v-model="proTypeCode"
													data-style="btn-white">
													<option value="">全部</option>
													<option v-for="item in proTypeMenu" :value="item.proTypeCode">
														{{ $filters.defaultValue(item.proTypeName, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 計價幣別</label>
												<select class="selectpicker form-control" id="curMenuSp" multiple title="請選擇幣別"
													v-model="curObjs" data-style="btn-white">
													<option value="">全部</option>
													<option v-for="(item, index) in curOption" :key="index" :value="item.value">
														{{ $filters.defaultValue(item.name, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 風險等級 </label>
												<select class="form-select" id="riskCode" name="riskCode" v-model="riskCode">
													<option value="">全部</option>
													<option v-for="item in riskMenu" :value="item.riskCode">
														{{ $filters.defaultValue(item.riskName, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 保本與否 </label>

												<select class="form-select" id="guarType" v-model="guarType">
													<option value="">全部</option>
													<option v-for="(item, index) in guarTypeMenu" :key="index" :value="item.codeValue">
														{{ $filters.defaultValue(item.codeName, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-6">
												<label class="form-label">配息頻率</label>
												<select name="select" class="form-select" v-model="intFreqUnitType">
													<option value="">全部</option>
													<option v-for="intFreqUnit in intFreqUnitMenu" :value="intFreqUnit.codeValue">
														{{ $filters.defaultValue(intFreqUnit.codeName, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-6">
												<label class="form-label">剩餘年限(天)</label>
												<div class="input-group">
													<input type="text" class="form-control" size="5" v-model="startDay" />
													<div class="input-group-text">~</div>
													<input type="text" class="form-control" size="5" v-model="endDay" />
												</div>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">連結標的</label>
												<button type="button" class="btn btn-primary" @click="groupTargetModalHandler()">選擇連結標的</button>
												<vue-modal :is-open="isOpenTargetModal" :before-close="isOpenTargetModal = false">
													<template v-slot:content="props">
														<vue-group-target-modal ref="groupTargetModalRef" id="groupTargetModal" :close="props.close"
															:target-prop="targetItem" @selected="selectedTarget"></vue-group-target-modal>
													</template>
												</vue-modal>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">發行機構</label>
												<button type="button" class="btn btn-primary" @click="groupIssuerModalHandler()">選擇發行機構</button>
												<vue-modal :is-open="isOpenIssuerModal" :before-close="isOpenIssuerModal = false">
													<template v-slot:content="props">
														<vue-group-issuer-modal ref="groupIssuerModalRef" id="groupIssuerModal" :close="props.close"
															:pfcat-code="'SP'" @selected="selectedIssuer"></vue-group-issuer-modal>
													</template>
												</vue-modal>
											</div>
										</div>

										<div class="form-row d-flex align-items-start">
											<div class="col-4">
												<div style="padding-left: 110px; padding-bottom: 15px" v-for="item in targetItem">
													<span class="form-check-label"> {{ $filters.defaultValue(item.stockCode, '--') }}</span>
													<a href="#" @click="deleteTargetItem(item.stockCode)"><img
															:src="getImgURL('icon', 'i-cancel.png')" /></a>
												</div>
											</div>

											<div class="col-8">
												<div style="padding-left: 115px; padding-bottom: 15px" v-for="item in issuerItem">
													<span class="form-check-label"> {{ $filters.defaultValue(item.issuerName, '--') }}</span>
													<a href="#" @click="deleteIssuerItem(item.issuerCode)"><img
															:src="getImgURL('icon', 'i-cancel.png')" /></a>
												</div>
											</div>
										</div>

										<div class="form-footer">
											<button class="btn btn-primary" @click.prevent="gotoPage(0)">查詢</button>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>

					<div class="tab-pane fade" id="SectionB" :class="{ 'active show': activeTab == 'fast' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2">
								<h4>查詢條件</h4>
								<span class="tx-square-bracket">為必填欄位</span>
							</div>
							<div class="collapse show" id="collapseListGroup2">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-12">
											<label class="form-label tx-require"> 篩選條件 </label>
											<div class="form-check-group" v-for="item in spFastMenu" @change="fastChange(item.codeValue)">
												<input class="form-check-input" :id="'fast' + item.codeValue" name="fastCode" v-model="fastCode"
													:value="item.codeValue" type="radio" />
												<label class="form-check-label" :for="'fast' + item.codeValue">{{
													$filters.defaultValue(item.codeName, '--')
												}}</label>
											</div>
										</div>
										<div class="form-group col-12 col-lg-6" id="rangeFixedTr" style="display: none">
											<label class="form-label tx-require"> 顯示區間</label>

											<select class="form-select" id="prod_protype_code" v-model="timeRange">
												<option v-for="item in timeRangeMenu" :value="item">
													{{ $filters.defaultValue(item.termName, '--') }}
												</option>
											</select>
										</div>

										<div class="form-group col-12 col-lg-6" id="proPerfTimeTr" style="display: none">
											<label class="form-label tx-require">標的績效 </label>

											<select class="form-select" id="vfAstStat_stat_code" v-model="perf">
												<option v-for="item in perfMenu" :value="item.codeValue">
													{{ $filters.defaultValue(item.codeName, '--') }}
												</option>
											</select>
										</div>

										<div class="form-group col-12 col-lg-6" id="maxRowIdTr" style="display: none">
											<label class="form-label tx-require">顯示資料筆數</label>
											<select class="form-select" id="maxRowId" v-model="rowNumber">
												<option value="">全部</option>
												<option v-for="item in rowNumerMenu" :value="item.rangeFixed">
													{{ $filters.defaultValue(item.termName, '--') }}
												</option>
											</select>
										</div>
									</div>
									<div class="form-footer">
										<button class="btn btn-primary" @click.prevent="gotoFastPage(0)">查詢</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div id="searchResult" v-if="pageData.content.length > 0">
				<div class="card card-table">
					<div class="card-header">
						<h4>查詢結果</h4>
						<div style="display: flex">
							<vue-pagination :pageable="pageData" :goto-page="gotoPage"> </vue-pagination><button type="button"
								class="btn btn-info ms-2" @click="performancesCompareModelHandler()">績效比較圖</button>
							<vue-modal :is-open="isOpenCompareModal" :before-close="isOpenCompareModal = false">
								<template v-slot:content="props">
									<vue-performances-compare-modal :close="props.close" ref="performancesCompareModalRef"
										id="performancesCompareModal"></vue-performances-compare-modal>
								</template>
							</vue-modal>
						</div>
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-bordered text-center">
							<thead>
								<tr>
									<th class="wd-100 text-start">加入比較</th>
									<th>商品代號</th>
									<th class="10% text-start">商品中文名稱</th>
									<th>計價幣別</th>
									<th>風險等級</th>
									<th>參考贖回價</th>
									<th>參考淨值</th>
									<th>配息頻率</th>
									<th>到期日</th>
									<th>連結標的</th>
									<th class="text-center" width="120">執行</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="(item, index) in pageData.content">
									<td data-th="加入比較" class="text-start text-center">
										<input class="form-check-input text-center" v-model="selectedItems[item.proCode]"
											:id="'id-' + item.bankProCode" type="checkbox" />
										<label class="form-check-label" :for="'id-' + item.bankProCode"></label>
									</td>
									<td data-th="商品代號">
										<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
									</td>
									<td class="text-start" data-th="商品中文名稱">
										<span>
											<a class="tx-link" @click="spModalHandler(item.proCode, item.pfcatCode)">{{
												$filters.defaultValue(item.proName, '--')
											}}</a>
										</span>
									</td>
									<td class="text-end" data-th="計價幣別">
										<span>{{ $filters.defaultValue(item.curCode, '--') }}</span>
									</td>
									<td class="text-end" data-th="風險等級">
										<span>{{ $filters.defaultValue(item.riskName, '--') }}</span>
									</td>
									<td class="text-end" data-th="參考贖回價">
										<span>{{ $filters.formatNumber(item.sprice, '--') }}</span>
									</td>
									<td class="text-end" data-th="參考淨值">
										<span>{{ $filters.formatNumber(item.aprice, '--') }}</span>
									</td>
									<td data-th="配息頻率">
										<span>{{ $filters.defaultValue(item.intFreqName, '--') }}</span>
									</td>
									<td data-th="到期日">
										<span>{{ $filters.defaultValue(item.expireDt ? item.expireDt : '9999/12/31', '--') }}</span>
									</td>
									<td data-th="連結標的">
										<span><button type="button" class="btn btn-dark btn-icon" data-bs-toggle="tooltip" title="連結標的"
												@click="tragetHandler(item.proCode)">
												<i class="bi bi-search"></i></button></span>
									</td>
									<td class="text-center" data-th="執行">
										<button v-if="activeTab === 'fast' && fastCode === '06'" type="button" class="btn btn-primary"
											title="移除我的最愛" @click="remove(item.proCode)">
											移除最愛
										</button>
										<button v-else type="button" class="btn btn-dark btn-icon" data-bs-toggle="tooltip" title="加入我的最愛"
											@click="favoritesHandler(item.proCode, item.pfcatCode)">
											<i class="bi bi-heart text-danger"></i>
										</button>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="tx-note">
					<ol>
						<li><span>資料日期：</span></li>
						<li><span>商品是否可申購以交易系統為主</span></li>
					</ol>
				</div>
				<vue-modal :is-open="isOpenViewModal" :before-close="isOpenViewModal = false">
					<template v-slot:content="props">
						<vue-target-view-modal :close="props.close" ref="targetViewModalRef"></vue-target-view-modal>
					</template>
				</vue-modal>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import pagination from '@/views/components/pagination.vue';
import vuePerformancesCompareModal from './performancesCompareModal.vue';
import vueModal from '@/views/components/model.vue';
import vueGroupIssuerModal from './groupIssuerModal.vue';
import vueGroupTargetModal from './groupTargetModal.vue';
import vueTargetViewModal from './targetViewModal.vue';
import { getImgURL } from '@/utils/imgURL.js';
export default {
	components: {
		'vue-pagination': pagination,
		vuePerformancesCompareModal,
		vueModal,
		vueGroupIssuerModal,
		vueGroupTargetModal,
		vueTargetViewModal
	},
	props: {
		spModalHandler: Function,
		favoritesHandler: Function,
		curOption: Array,
		riskMenu: Array
	},
	data: function () {
		return {
			perf: 'PCTYTD',
			perfMenu: [],
			activeTab: 'common',
			bankProCode: null,
			proName: null,
			proTypeCode: '', //商品類型
			curObjs: [],
			riskCode: '',
			guarType: '',
			intFreqUnitType: '',
			proTypeMenu: [], // 商品類型選單
			guarTypeMenu: [], // 保本與否下拉
			intFreqUnitMenu: [], // 配息頻率下拉
			targetItem: [], // 連結標的
			startDay: null, // 剩餘年限(天)-起
			endDay: null, // 剩餘年限(天)-迄
			issuerItem: [], //發行機構

			fastCode: '05', // 快速 篩選條件
			timeRange: null, // 快速 顯示區間
			rowNumber: '10.000000', // 快速 顯示資料筆數

			spFastMenu: [], //快速篩選條件
			timeRangeMenu: [], // 顯示區間
			rowNumerMenu: [], // 顯示資料筆數
			proCodes: [], // 商品代碼
			selectedItems: {}, // 加入比較選項

			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: ['PRO_CODE'],
				direction: 'ASC'
			},
			isOpenTargetModal: false,
			isOpenIssuerModal: false,
			isOpenCompareModal: false,
			isOpenViewModal: false
		};
	},
	watch: {
		selectedItems: {
			handler(newValues) {
				this.proCodes = Object.keys(newValues).filter((proCode) => newValues[proCode]);
			},
			deep: true
		},
		activeTab(newVal, oldVal) {
			var self = this;
			self.fastCode = '05';
			self.fastChange(self.fastCode);
		},
		curObjs(newVal, oldVal) {
			var self = this;
			if (newVal[0] !== oldVal[0]) {
				if (newVal[0] === '') {
					$('#curMenuSp').selectpicker('selectAll');
				} else if (oldVal[0] === '' && newVal[0] !== '') {
					$('#curMenuSp').selectpicker('deselectAll');
				}
			}
		}
	},
	mounted: function () {
		var self = this;
		$('#curMenuSp').selectpicker('refresh');
		self.getProTypeMenu(); // 取得商品類型選單
		self.getGuarTypeMenu(); // // 取得保本與否選單
		self.getIntFreqUnitTypeMenu(); // 配息頻率
		self.getspFastMenu(); // 取得快速篩選條件選項
		self.getTimeRangeMenu(); // 取得顯示區間選項
		self.getRowNumerMenu(); // 取得顯示資料筆數選項
		self.fastChange(self.fastCode);
	},
	methods: {
		getImgURL,
		// 條件Tab切換
		changeTab: function (tabName) {
			var self = this;
			self.activeTab = tabName;
			self.pageData = {
				// 清空查詢資料
				content: []
			};
		},
		async getProTypeMenu() {
			// 商品類型選單
			var self = this;
			const r = await this.$api.getProTypeListApi({
				pfcatCode: 'SP'
			});
			self.proTypeMenu = r.data;
		},
		// 保本與否選單
		async getGuarTypeMenu() {
			var self = this;
			const ret = await this.$api.getAdmCodeDetail({
				codeType: 'GUAR_TYPE'
			});
			self.guarTypeMenu = ret.data;
		},
		//顯示連結標的 model
		groupTargetModalHandler: function () {
			var self = this;
			this.$refs.groupTargetModalRef.targetPropItem(self.targetItem);
			this.isOpenTargetModal = true;
		},
		// 顯示連結標的選擇項目
		selectedTarget(targetItem) {
			var self = this;
			this.isOpenTargetModal = false;
			self.targetItem = targetItem; //取得連結標的資料
		},
		// 刪除 連結標的擇項目
		deleteTargetItem(targetCode) {
			var self = this;
			_.remove(self.targetItem, (item) => item.stockCode === targetCode); // 移除刪除項目
		},
		//顯示發行機構 model
		groupIssuerModalHandler: function () {
			var self = this;
			this.$refs.groupIssuerModalRef.issuerPropItem(self.issuerItem);
			this.isOpenIssuerModal = true;
		},
		// 顯示發行機構選擇項目
		selectedIssuer(issuerItem) {
			var self = this;
			this.isOpenIssuerModal = false;
			self.issuerItem = issuerItem; //取得發行機構資料
		},
		// 刪除 發行機構選擇項目
		deleteIssuerItem(issuerCode) {
			var self = this;
			_.remove(self.issuerItem, (item) => item.issuerCode === issuerCode); // 移除刪除項目
		},
		// 連結標的檢視
		tragetHandler: function (proCode) {
			this.$refs.targetViewModalRef.getTargetDataList(proCode);
			this.isOpenViewModal = true;
		},
		// 取得配息頻率選項
		getIntFreqUnitTypeMenu: async function () {
			var self = this;
			const ret = await this.$api.getAdmCodeDetail({
				codeType: 'INT_FREQ_UNITTYPE'
			});

			self.intFreqUnitMenu = ret.data;
		},
		// 取得顯示區間
		getTimeRangeMenu: async function () {
			var self = this;
			const ret = await this.$api.getTimeRangeMenuApi();
			self.timeRangeMenu = ret.data;
		},
		// 取得顯示資料筆數
		getRowNumerMenu: async function () {
			var self = this;
			const ret = await this.$api.getRowNumerMenuApi();
			self.rowNumerMenu = ret.data;
		},
		// 取得快速篩選條件選項
		getspFastMenu: async function () {
			var self = this;
			const ret = await this.$api.getSpFastFilterMenuApi();
			self.spFastMenu = ret.data;
		},
		// 快速查詢切換
		fastChange(fastCode) {
			var self = this;
			self.timeRange = {}; // 快速 顯示區間
			self.rowNumber = ''; // 快速 顯示資料筆數
			self.pageData = {
				// 清空查詢資料
				content: []
			};
			if (fastCode === '05') {
				// 超人氣
				$('#proPerfTimeTr').hide();
				$('#rangeFixedTr').show();
				$('#maxRowIdTr').show();
				self.timeRange = self.timeRangeMenu[0];
			} else if (fastCode === '07') {
				// 績效排行
				$('#rangeFixedTr').hide();
				$('#maxRowIdTr').show();
				$('#proPerfTimeTr').show();
				self.rowNumber = '10'; // 快速 顯示資料筆數
			} else if (fastCode === '08') {
				// 最高配息率商品
				$('#maxRowIdTr').show();
				$('#rangeFixedTr').hide();
				$('#proPerfTimeTr').hide();
				self.rowNumber = '10'; // 快速 顯示資料筆數
			} else {
				$('#maxRowIdTr').hide();
				$('#rangeFixedTr').hide();
				$('#proPerfTimeTr').hide();
			}
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (page) {
			var self = this;
			self.pageable.page = page;
			var url = '';

			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;

			var targetCodes = [];
			self.targetItem.forEach(function (item) {
				targetCodes.push(item.isinCode);
			});

			var issuerCodes = [];
			self.issuerItem.forEach(function (item) {
				issuerCodes.push(item.issuerCode);
			});

			const ret = await this.$api.getSpProductsApi(
				{
					bankProCode: self.bankProCode, // 商品代號
					proName: self.proName, //商品名稱
					protypeCode: self.proTypeCode, //商品類型
					curCodes: self.curObjs, // 計價幣別
					riskCode: self.riskCode, //風險等級
					principalGuarYn: self.guarType, // 保本與否
					targetCodes: targetCodes, // 連結標的
					intFreqUnitType: self.intFreqUnitType, // 配息頻率
					remainDayMin: self.startDay, // 剩餘年限(天)-起
					remainDayMax: self.endDay, // 剩餘年限(天)-迄
					issuerCodes: issuerCodes // 發行機構
				},
				url
			);
			self.pageData = ret.data;
		},
		//執行績效比較圖
		performancesCompareModelHandler: function () {
			var self = this;
			if (self.proCodes.length > 0) {
				if (self.proCodes.length > 6) {
					this.$bi.alert('最多加入6筆');
				} else {
					this.$refs.performancesCompareModalRef.comparePropItem(self.proCodes, 'sp');
					this.isOpenCompareModal = true;
				}
			} else {
				this.$bi.alert('至少要勾選一項商品');
			}
		},
		gotoFastPage: function (page) {
			this.pageable.page = page;
			this.getFastPageData(page);
		},
		getFastPageData: async function (page) {
			var self = this;
			this.pageable.page = page;

			var url = '';
			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;

			const ret = await this.$api.getSpProductsFilterQueryApi(
				{
					filterCodeValue: self.fastCode,
					timeRangeType: self.timeRange.rangeType, // 顯示區間類型
					timeRangeFixed: self.timeRange.rangeFixed, // 顯示區間數值
					rowNumberFixed: self.rowNumber
				},
				url
			);
			self.pageData = ret.data;
		},
		// 刪除我的最愛
		async remove(proCode) {
			await this.$api.deleteFavoriteApi({ proCode });
			this.$bi.alert('刪除成功');
			this.proCodes = [];
			this.gotoFastPage(0);
		}
	} // methods end
};
</script>
