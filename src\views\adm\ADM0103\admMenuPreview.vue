<template>
	<dynamic-title></dynamic-title>
	<div class="card card-form-collapse">
		<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formsearch1">
			<h4>查詢條件</h4>
			<span class="tx-square-bracket">為必填欄位</span>
		</div>

		<div class="card-body collapse show" id="formsearch1">
			<Form v-slot="{ errors, validate }" ref="queryForm">
				<div class="form-row">
					<div class="form-group col-lg-6">
						<label class="form-label">選擇系統角色</label>
						<select
							name="roleCode"
							id="roleCode"
							class="form-select"
							v-model="roleObj"
							label="系統角色"
							rules="required"
							:class="{ 'is-invalid': errors.roleCode }"
						>
							<option :value="{ roleCode: null, roleName: null }">請選擇</option>
							<option v-for="roleMenu in admRoleData" :value="roleMenu">[{{ roleMenu.roleCode }}]{{ roleMenu.roleName }}</option>
						</select>
					</div>
				</div>

				<!-- <div style="height: 25px">
					<span class="text-danger" v-show="errors.roleCode">{{errors.roleCode}}</span>
				</div> -->

				<div class="form-footer">
					<button class="btn btn-primary" id="queryBtnId" @click.prevent="getRoleMenuTree()">查詢</button>
					<button class="btn btn-primary" @click.prevent="exportMenuData()">Excel下載</button>
				</div>
			</Form>
		</div>
	</div>

	<p class="tx-note mb-4">點選Excel下載功能,系統會產生所有角色以及所對應的權限至Excel,因檔案內容較大請耐心等候下載</p>

	<div class="searchResult" id="searchResult" v-if="showSearchResult">
		<div class="card card-table">
			<div class="card-header">
				<h4>功能選單預覽</h4>
			</div>
			<table class="table">
				<tbody>
					<tr>
						<th>系統角色：{{ roleName }}</th>
					</tr>
				</tbody>
				<tbody>
					<tr>
						<td>
							<button type="button" class="btn btn-info" id="expandBtn" @click="expandBtn()">全部展開</button>
							<button type="button" class="btn btn-info" id="collapseBtn" @click="collapsedBtn()">全部收合</button>
						</td>
					</tr>
					<tr class="bg-white">
						<td>
							<bi-tree ref="tree" :treeData="treeData" :generateCheckbox="false"></bi-tree>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</template>

<script>
import dynamicTitle from '@/views/components/dynamicTitle.vue';
import { Form, Field } from 'vee-validate';
import biTree from '@/views/components/biTree.vue';

export default {
	components: {
		dynamicTitle,
		Form,
		Field,
		biTree
	},
	data: function () {
		return {
			//Api 用參數
			roleObj: {
				roleCode: null,
				roleName: null
			},
			roleName: null,
			//主要顯示資料
			admRoleData: [],

			treeData: {},
			showSearchResult: false
		};
	},
	mounted: function () {
		this.getRoleMenuDatas();
	},
	methods: {
		getRoleMenuDatas: async function () {
			let ret = await this.$api.getRoleMenuApi();
			this.admRoleData = ret.data;
		},
		getRoleMenuTree: function () {
			this.$refs.queryForm.validate().then(async (pass) => {
				if (pass.valid) {
					if (!this.roleObj.roleCode) {
						this.$bi.alert('系統角色為必填。');
						return;
					}

					let ret = await this.$api.getRoleMenuTreeApi(this.roleObj.roleCode);
					this.treeData = ret.data;
					this.roleName = this.roleObj.roleName;
					this.showSearchResult = true;
				}
			});
		},
		exportMenuData: function () {
			if (!this.roleObj.roleCode) {
				this.$bi.alert('系統角色為必填。');
				return;
			}

			var url = import.meta.env.VITE_API_URL_V1 + '/adm/roleMenuTree/export?roleCode=' + this.roleObj.roleCode;
			var fileName = 'MenuPreview.xls';
			var xhr = new XMLHttpRequest();
			xhr.open('GET', url, true);
			xhr.responseType = 'blob';
			xhr.onload = function () {
				if (this.status === 200) {
					var blob = this.response;
					var reader = new FileReader();
					reader.readAsDataURL(blob);
					reader.onload = function (e) {
						var a = document.createElement('a');
						a.download = fileName;
						a.href = e.target.result;
						$('body').append(a);
						a.click();
						$(a).remove();
					};
				}
			};
			xhr.send();
		},
		expandBtn: function () {
			this.$refs.tree.expandAllNodes(true);
		},
		collapsedBtn: function () {
			this.$refs.tree.expandAllNodes(false);
		}
	}
};
</script>
