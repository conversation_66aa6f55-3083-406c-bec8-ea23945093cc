<template>
	<dynamic-title></dynamic-title>
	<div>
		<div class="row">
			<div class="col-12">
				<div class="card card-form">
					<div class="card-body">
						<div class="row g-3 align-items-end">
							<div class="col-md-6">
								<label class="form-label"> 事件通知類別</label>
								<select name="tdCat1Code" id="wobTdItem" class="form-select" v-model="tdCat1Code">
									<option value="">全部</option>
									<option v-for="menu in tdItemCat1Menu" :value="menu.tdCat1Code">{{ menu.tdCat1Name }}</option>
								</select>
							</div>
							<div class="col-md-2">
								<button class="btn btn-primary btn-glow btn-search" @click="getTdItems">查詢</button>
							</div>
						</div>
					</div>
				</div>

				<div id="searchResult" v-if="showYn">
					<div class="card card-table">
						<div class="card-header">
							<h4>事件通知列表</h4>
						</div>
						<div class="table-responsive">
							<table class="table table-RWD table-hover text-center">
								<thead>
									<tr>
										<th>事件通知類別</th>
										<th class="text-start">事件類別</th>
										<th>執行狀態</th>
										<th>維護人員</th>
										<th>異動日期</th>
										<th>提前幾天<br />發送事件</th>
										<th>提醒天數</th>
										<th>狀態變更</th>
									</tr>
								</thead>
								<tr v-for="item in tdItems">
									<td data-th="事件通知類別">{{ item.tdCat1Name }}</td>
									<td data-th="事件類別" class="text-start">{{ item.itemName }}</td>
									<td data-th="執行狀態">
										<span v-if="item.activeYn == 'Y'">正常執行</span>
										<span v-if="item.activeYn == 'N'">已停止</span>
									</td>
									<td data-th="維護人員">{{ item.modifyBy }} {{ item.userName }}</td>
									<td data-th="審核狀態">{{ item.modifyDt }}</td>
									<td data-th="提前幾天 發送事件">
										<input
											type="number"
											class="form-control wd-75"
											maxlength="10"
											v-model="item.earlySendDay"
											@input="dataChanged(item)"
											:disabled="isReview(item)"
										/>
									</td>
									<td data-th="提醒天數">
										<input
											type="number"
											class="form-control wd-75"
											maxlength="10"
											v-model="item.expProcDay"
											@input="dataChanged(item)"
											:disabled="isReview(item)"
										/>
									</td>
									<td data-th="狀態變更">
										<div class="form-check form-check-inline">
											<input
												class="form-check-input"
												type="radio"
												:name="item.itemCode + 'R'"
												:value="'Y'"
												v-model="item.activeYn"
												@change="dataChanged(item)"
												:disabled="isReview(item)"
											/>
											<label class="form-check-label">執行</label>
										</div>
										<div class="form-check form-check-inline">
											<input
												class="form-check-input"
												type="radio"
												:name="item.itemCode + 'R'"
												:value="'N'"
												v-model="item.activeYn"
												@change="dataChanged(item)"
												:disabled="isReview(item)"
											/>
											<label class="form-check-label">停用</label>
										</div>
									</td>
								</tr>
							</table>
						</div>
					</div>
					<p class="tx-note">若設定「提前幾天發送事件」欄位，須為可提前知道發送日期的事件才會有效。</p>
					<div class="text-end">
						<input
							name=""
							type="button"
							class="btn btn-primary btn-lg btn-glow"
							value="儲存"
							@click="saveFromUpdate()"
							id="incomeAnchor"
						/>
					</div>
				</div>
			</div>
		</div>
		<!--頁面內容 end-->
	</div>
</template>
<script>
import Swal from 'sweetalert2';
import dynamicTitle from '@/views/components/dynamicTitle.vue';

export default {
	components: {
		dynamicTitle
	},
	data: function () {
		return {
			showYn: false,
			//API 用參數
			tdCat1Code: '',
			//下拉選單
			tdItemCat1Menu: [],
			//API邏輯判斷用參數
			changedTdItems: [],
			//主要顯示資料
			tdItems: []
		};
	},
	mounted: function () {
		var self = this;
		self.getTdItemCat1Menu();
		// self.getTdItems();
	},
	methods: {
		getTdItemCat1Menu: function () {
			var self = this;
			self.$api.getTdItemCat1MenuApi().then(function (ret) {
				self.tdItemCat1Menu = ret.data;
			});
		},
		getTdItems: function () {
			var self = this;
			self.$api
				.getTdItemsApi({
					tdCat1Code: self.tdCat1Code
				})
				.then(function (ret) {
					self.tdItems = ret.data;
					self.showYn = true;
				});
		},
		dataChanged: function (changedItem) {
			var self = this;
			self.changedTdItems.forEach(function (item, index, object) {
				if (item.itemCode == changedItem.itemCode) {
					object.splice(index, 1);
				}
			});
			self.changedTdItems.push(changedItem);
		},
		isReview: function (item) {
			var self = this;
			if (item.status == 'P') {
				return true;
			}
			return false;
		},
		saveFromUpdate: function () {
			var self = this;
			var isPassValidate = true;
			self.changedTdItems.forEach(function (item, index, object) {
				if (item.earlySendDay < 0 || item.earlySendDay > 2000 || item.expProcDay < 0 || item.expProcDay > 2000) {
					Swal.fire({
						icon: 'error',
						text: '請在資料筆數欄位中輸入1~2000數字。',
						showCloseButton: true,
						confirmButtonText: '確認',
						buttonStyling: false,
						customClass: {
							confirmButton: 'btn btn-danger'
						}
					});
					isPassValidate = false;
					return;
				}
			});

			if (!isPassValidate) {
				return;
			}
			self.$api.patchTdItemsApi(self.changedTdItems).then(function (ret) {
				Swal.fire({
					icon: 'success',
					text: '儲存成功。',
					showCloseButton: true,
					confirmButtonText: '確認',
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-success'
					}
				});
				self.getTdItems();
			});
		}
	}
};
</script>
