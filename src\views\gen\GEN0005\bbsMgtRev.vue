<template>
	<div class="container-fluid">
		<div class="row">
			<div class="col-12">
				<div class="card card-table-list">
					<div class="card-header">
						<h4>待審核清單</h4>
					</div>
					<!-- 待審核清單 -->
					<table class="table table-RWD table-list text-center">
						<thead>
							<tr>
								<th>提交日期</th>
								<th>提交人員</th>
								<th v-for="varItem in varItems">{{ varItem.varItemName }}</th>
								<th>申請明細</th>
								<th class="wd-100">審核狀態</th>
								<th class="wd-10p">其他說明</th>
							</tr>
						</thead>
						<tbody>
							<template v-for="item in wkfEvents">
								<tr>
									<td data-th="提交日期">
										<span>{{ item.createDt }}</span>
									</td>
									<td data-th="提交人員">
										<span>{{ item.createBy }} {{ item.userName }}</span>
									</td>

									<td v-if="isEmptyItemData(item)" v-for="(itemData, index) in item.itemDatas" :key="index">
										<span>{{ itemData.varItemValue }}</span>
									</td>
									<!-- 無值輸出空白 避免排版錯誤 -->
									<td v-else v-for="varItem in varItems">
										<span></span>
									</td>

									<td data-th="申請明細">
										<button type="button" class="btn btn-dark btn-icon" @click="getDetail(item.eventId)">
											<i class="bi bi-search"></i>
										</button>
									</td>
									<td data-th="審核狀態" class="text-start">
										<ul class="list-unstyled mb-0" v-for="(checkItem, index) in item.wkfEngineFlows">
											<li>
												<div class="form-check">
													<input
														class="form-check-input"
														type="radio"
														:id="item.eventId + index"
														:name="item.eventId"
														v-model="item.status"
														:value="checkItem.actionStatus"
													/>
													<label class="form-check-label" :for="item.eventId + index">{{ checkItem.actionName }}</label>
												</div>
											</li>
										</ul>
									</td>
									<td data-th="其他說明">
										<textarea class="form-control" rows="3" :disabled="item.status != 'R'" v-model="item.desc"></textarea>
									</td>
								</tr>
							</template>
						</tbody>
					</table>
				</div>
			</div>
			<div class="col-12 mt-3 text-end">
				<input class="btn btn-primary btn-lg" type="button" @click="audit()" value="審核完成" />
			</div>
		</div>

		<!--頁面內容 end-->

		<vue-modal :is-open="isOpenModal" :before-close="closeModal">
			<template v-slot:content="props">
				<div :class="modalClass">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">公告訊息</h4>
							<button type="button" :class="buttonClass" @click="changeModalSize()"><i class="bi bi-arrows-fullscreen"></i></button>
							<button type="button" class="btn-close" @click.prevent="props.close()"></button>
						</div>
						<div class="modal-body">
							<table class="table table-bordered">
								<caption>
									公告分類
								</caption>
								<tbody>
									<tr>
										<th>訊息類別</th>
										<td>{{ selMsgLog.msgName }}</td>
									</tr>
								</tbody>
							</table>

							<table class="table table-bordered">
								<caption>
									公告內容
								</caption>
								<tbody>
									<tr>
										<th class="wd-15p">重要性</th>
										<td>{{ subTypeName(selMsgLog.importantYn) }}</td>
									</tr>
									<tr>
										<th>主分類</th>
										<td>{{ selMsgLog.mainCatName }}</td>
									</tr>
									<tr>
										<th>次分類</th>
										<td>{{ selMsgLog.subCatName }}</td>
									</tr>
									<tr>
										<th>有效日期</th>
										<td>{{ selMsgLog.validBgnDt }} ~ {{ selMsgLog.validEndDt }}</td>
									</tr>
									<tr>
										<th><span>公告標題</span></th>
										<td>{{ selMsgLog.msgTitle }}</td>
									</tr>
									<tr>
										<th>公告內容</th>
										<td>{{ selMsgLog.msgContent }}</td>
									</tr>
									<tr>
										<th>首頁是否顯示</th>
										<td>{{ subTypeName(selMsgLog.showYn) }}</td>
									</tr>
									<tr>
										<th>上傳檔案</th>
										<td>
											<ul class="list-group list-inline-tags" v-for="file in selMsgLog.fileList">
												<li class="list-group-item">
													<a href="#" @click="viewFile(file)">
														<span>{{ file.showName }}</span>
													</a>
												</li>
											</ul>
										</td>
									</tr>
									<tr>
										<th>連結</th>
										<td>
											<a :href="selMsgLog.favoriteLink" style="word-break: break-all">{{ selMsgLog.favoriteLink }}</a>
										</td>
									</tr>
								</tbody>
							</table>

							<table class="table table-bordered">
								<caption>
									維護資訊
								</caption>
								<tbody>
									<tr>
										<th>建立人員</th>
										<td>{{ selMsgLog.createUser }}</td>
									</tr>
									<tr>
										<th>建立人員分機</th>
										<td>{{ selMsgLog.createUserExt }}</td>
									</tr>
									<tr>
										<th>建立日期</th>
										<td>{{ selMsgLog.createDt }}</td>
									</tr>
									<tr>
										<th>最後維護人員</th>
										<td>{{ selMsgLog.modifyUser }}</td>
									</tr>
									<tr>
										<th>維護人員分機</th>
										<td>{{ selMsgLog.modifyUserExt }}</td>
									</tr>
									<tr>
										<th>最後維護日期</th>
										<td>{{ selMsgLog.modifyDt }}</td>
									</tr>
								</tbody>
							</table>
						</div>
						<div class="modal-footer">
							<button @click.prevent="props.close()" type="button" class="btn btn-white">關閉視窗</button>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
	</div>
</template>
<script>
import vueModal from '@/views/components/model.vue';
import _ from 'lodash';
export default {
	components: {
		vueModal
	},
	data: function () {
		return {
			//主要顯示資料
			selOptionYn: [], // convert option name
			wkfEvents: [],
			varItems: [],
			isOpenModal: null,
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',
			buttonClass: 'btn-expand',
			selMsgLog: {}
		};
	},
	mounted: async function () {
		var self = this;
		self.selOptionYn = await self.getAdmCodeDetail('OPTION_YN');
		self.getVarItems();
		self.getWkfEvents();
	},
	methods: {
		getAdmCodeDetail: async function (codeType) {
			var self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: codeType
			});
			return ret.data;
		},
		getVarItems: async function () {
			var self = this;
			const ret = await self.$api.getVarItemsApi(self.$route.params.wfgId);
			self.varItems = ret.data;
		},
		getWkfEvents: async function () {
			var self = this;

			const ret = await self.$api.getWkfEventsApi(self.$route.params.wfgId);
			self.wkfEvents = ret.data;
			console.log('self.wkfEvents: ', self.wkfEvents);
		},
		getDetail: async function (eventId) {
			console.log('eventId: ', eventId);
			var self = this;
			const ret = await self.$api.postMsgLogApi({
				eventId
			});
			if (ret.data) {
				self.selMsgLog = ret.data;
				console.log('self.selMsgLog: ', self.selMsgLog);
				self.isOpenModal = true;
			}
		},
		changeModalSize: function () {
			var self = this;
			if (self.modalClass === 'modal-dialog modal-lg modal-dialog-centered') {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered fullscreen';
				self.buttonClass = 'btn-expand mini';
			} else {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered';
				self.buttonClass = 'btn-expand';
			}
		},
		subTypeName: function (codeValue) {
			var self = this;
			if (!_.isBlank(self.selOptionYn) && !_.isBlank(codeValue)) {
				return _.find(self.selOptionYn, { codeValue: codeValue }).codeName;
			} else {
				return codeValue;
			}
		},
		viewFile: function (file) {
			var self = this;
			var url = self.config.apiPath + '/com/fileView?fileType=GenFilesTemp&fileId=' + file.msgFileId;

			const a = document.createElement('a');
			a.href = url;
			a.download = file.showName;
			a.click();

			URL.revokeObjectURL(url); // 釋放 URL
		},
		isEmptyItemData: function (item) {
			return !_.isEmpty(item.itemDatas);
		},
		audit: async function () {
			var self = this;
			var wkfEvents = self.wkfEvents;
			var updateRoleMenuLogs = [];

			for (var i = 0; i < wkfEvents.length; i++) {
				var wkfEvent = wkfEvents[i];
				if (wkfEvent.status == 'A' || wkfEvent.status == 'R') {
					if (wkfEvent.status == 'R' && _.isBlank(wkfEvent.desc)) {
						this.$bi.alert('請輸入退回原因。');
						return;
					}
					updateRoleMenuLogs.push(wkfEvent);
				}
			}

			if (_.isEmpty(updateRoleMenuLogs)) {
				this.$bi.alert('無可審核資料。');
				return;
			}

			var finishCnt = 0;
			for (var i = 0; i < updateRoleMenuLogs.length; i++) {
				var roleMenuLog = updateRoleMenuLogs[i];
				const ret = await self.$api.patchAuditApi({
					eventId: roleMenuLog.eventId,
					actionCode: _.filter(roleMenuLog.wkfEngineFlows, ['actionStatus', roleMenuLog.status])[0].actionCode,
					desc: roleMenuLog.desc
				});
				_.handleWkfResp(ret, true);
				finishCnt++;

				if (finishCnt == updateRoleMenuLogs.length) {
					Swal.fire({
						icon: 'success',
						text: '審核完成。',
						showCloseButton: true,
						confirmButtonText: '確認',
						buttonsStyling: false,
						customClass: {
							confirmButton: 'btn btn-success'
						}
					}).then(function () {
						window.location.reload();
					});
				}
			}
		},
		closeModal: function () {
			this.isOpenModal = false;
		}
	}
};
</script>
