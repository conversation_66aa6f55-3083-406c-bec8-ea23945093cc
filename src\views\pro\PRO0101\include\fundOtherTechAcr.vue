<template>
	<div>
		<p class="font-md bold m-t-15">
			年化累積報酬率（毎月月末更新）
			<select class="select" v-model="selectedCurrencyCode">
				<option value="0">新台幣</option>
				<option value="1">美金</option>
			</select>
		</p>
		<div class="row">
			<div class="col-md-12 col-lg-6">
				<vue-fund-column-chart
					:chart-id="techsAcrChartId"
					:prop-chart-data="techsAcrChartData"
					:prop-selected-tech="techsAcrMenu[selectedCurrencyCode]"
				></vue-fund-column-chart>
			</div>
			<div class="col-md-12 col-lg-6">
				<div class="table-responsive">
					<table width="100%" class="table table-condensed text-right">
						<thead>
							<tr>
								<th width="30%">&nbsp;</th>
								<th>{{ $filters.defaultValue(fundInfo && fundInfo.fundEnName, '--') }}</th>
								<th>{{ $filters.defaultValue(bmName, '--') }}</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td class="text-center">3個月</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getTech(list[0].statCode), '%'), '--')"></p>
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getBmTech(list[0].statCode), '%'), '--')"></p>
								</td>
							</tr>
							<tr>
								<td class="text-center">6個月</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getTech(list[1].statCode), '%'), '--')"></p>
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getBmTech(list[1].statCode), '%'), '--')"></p>
								</td>
							</tr>
							<tr>
								<td class="text-center">1年</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getTech(list[2].statCode), '%'), '--')"></p>
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getBmTech(list[2].statCode), '%'), '--')"></p>
								</td>
							</tr>
							<tr>
								<td class="text-center">2年</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getTech(list[3].statCode), '%'), '--')"></p>
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getBmTech(list[3].statCode), '%'), '--')"></p>
								</td>
							</tr>
							<tr>
								<td class="text-center">3年</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getTech(list[4].statCode), '%'), '--')"></p>
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getBmTech(list[4].statCode), '%'), '--')"></p>
								</td>
							</tr>
							<tr>
								<td class="text-center">5年</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getTech(list[5].statCode), '%'), '--')"></p>
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getBmTech(list[5].statCode), '%'), '--')"></p>
								</td>
							</tr>
							<tr>
								<td class="text-center">10年</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getTech(list[6].statCode), '%'), '--')"></p>
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getBmTech(list[6].statCode), '%'), '--')"></p>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
export default {
	props: {
		fundInfo: Object,
		twdTechs: Array,
		twdBmTechs: Array,
		bmName: String
	},
	data: function () {
		return {
			selectedCurrencyCode: '0',
			techsAcrChartId: 'techsAcrChartId',
			list: [
				{
					name: '3個月',
					statCode: 'ACR3M'
				},
				{
					name: '6個月',
					statCode: 'ACR6M'
				},
				{
					name: '1年',
					statCode: 'ACR1Y'
				},
				{
					name: '2年',
					statCode: 'ACR2Y'
				},
				{
					name: '3年',
					statCode: 'ACR3Y'
				},
				{
					name: '5年',
					statCode: 'ACR5Y'
				},
				{
					name: '10年',
					statCode: 'ACR10Y'
				}
			],
			usdTechs: [],
			usdBmTechs: []
		};
	},
	watch: {},
	mounted: function () {
		this.$nextTick(function () {
			this.getUsdTechs();
			this.getUsdBmTechs();
		});
	},
	computed: {
		techsAcrMenu: function () {
			var self = this;
			return [
				{
					name: '年化累積報酬',
					valueList: [
						{ name: '基金', value: 'twdAcr' },
						{ name: '對應指數' + self.bmName, value: 'twdAcrBm' }
					],
					tooltipText: '{categoryX}:{valueY}%'
				},
				{
					name: '年化累積報酬',
					valueList: [
						{ name: '基金', value: 'usdAcr' },
						{ name: '對應指數' + self.bmName, value: 'usdAcrBm' }
					],
					tooltipText: '{categoryX}:{valueY}%'
				}
			];
		},
		techsAcrChartData: function () {
			return [
				{
					year: '3個月',
					twdAcr: this.getTwdTech('ACR3M'),
					twdAcrBm: this.getTwdBmTech('ACR3M'),
					usdAcr: this.getUsdTech('ACR3M'),
					usdAcrBm: this.getUsdBmTech('ACR3M')
				},
				{
					year: '6個月',
					twdAcr: this.getTwdTech('ACR6M'),
					twdAcrBm: this.getTwdBmTech('ACR6M'),
					usdAcr: this.getUsdTech('ACR6M'),
					usdAcrBm: this.getUsdBmTech('ACR6M')
				},
				{
					year: '1年',
					twdAcr: this.getTwdTech('ACR1Y'),
					twdAcrBm: this.getTwdBmTech('ACR1Y'),
					usdAcr: this.getUsdTech('ACR1Y'),
					usdAcrBm: this.getUsdBmTech('ACR1Y')
				},
				{
					year: '2年',
					twdAcr: this.getTwdTech('ACR2Y'),
					twdAcrBm: this.getTwdBmTech('ACR2Y'),
					usdAcr: this.getUsdTech('ACR2Y'),
					usdAcrBm: this.getUsdBmTech('ACR2Y')
				},
				{
					year: '3年',
					twdAcr: this.getTwdTech('ACR3Y'),
					twdAcrBm: this.getTwdBmTech('ACR3Y'),
					usdAcr: this.getUsdTech('ACR3Y'),
					usdAcrBm: this.getUsdBmTech('ACR3Y')
				},
				{
					year: '5年',
					twdAcr: this.getTwdTech('ACR5Y'),
					twdAcrBm: this.getTwdBmTech('ACR5Y'),
					usdAcr: this.getUsdTech('ACR5Y'),
					usdAcrBm: this.getUsdBmTech('ACR5Y')
				},
				{
					year: '10年',
					twdAcr: this.getTwdTech('ACR10Y'),
					twdAcrBm: this.getTwdBmTech('ACR10Y'),
					usdAcr: this.getUsdTech('ACR10Y'),
					usdAcrBm: this.getUsdBmTech('ACR10Y')
				}
			];
		}
	},
	methods: {
		getUsdTechs: async function () {
			var self = this;
			const ret = await this.$api.getTechsApi({
				proCode: self.fundInfo.fundCode,
				techCurrencyCode: 'USD'
			});
			self.usdTechs = ret.data;
		},
		getUsdBmTechs: async function () {
			var self = this;
			var managerBmCode = self.fundInfo.managerBmCode;
			var analysisBmCode = self.fundInfo.analysisBmCode;
			var bmCode;

			if (managerBmCode && managerBmCode != '11000006' && managerBmCode != '11000000') {
				// self.bmName = self.fundInfo.managerBmName;
				bmCode = managerBmCode;
			} else if (analysisBmCode) {
				// self.bmName = self.fundInfo.analysisBmName;
				bmCode = analysisBmCode;
			}
			const ret = await this.$api.getTechsApi({
				proCode: bmCode,
				techCurrencyCode: 'USD'
			});
			self.usdBmTechs = ret.data;
		},
		getTwdTech: function (statCode) {
			var tech = _.find(this.twdTechs, { statCode: statCode });
			return tech ? tech.dvalue : null;
		},
		getTwdBmTech: function (statCode) {
			var tech = _.find(this.twdBmTechs, { statCode: statCode });
			return tech ? tech.dvalue : null;
		},
		getUsdTech: function (statCode) {
			var tech = _.find(this.usdTechs, { statCode: statCode });
			return tech ? tech.dvalue : null;
		},
		getUsdBmTech: function (statCode) {
			var tech = _.find(this.usdBmTechs, { statCode: statCode });
			return tech ? tech.dvalue : null;
		},
		getTech: function (statCode) {
			var self = this;
			if (self.selectedCurrencyCode == '0') {
				// 台幣
				var tech = _.find(this.twdTechs, { statCode: statCode });
				return tech ? tech.dvalue : null;
			} else {
				// 美元
				var tech = _.find(this.usdTechs, { statCode: statCode });
				return tech ? tech.dvalue : null;
			}
		},
		getBmTech: function (statCode) {
			var self = this;
			if (self.selectedCurrencyCode == '0') {
				// 台幣
				var tech = _.find(this.twdBmTechs, { statCode: statCode });
				return tech ? tech.dvalue : null;
			} else {
				// 美元
				var tech = _.find(this.usdBmTechs, { statCode: statCode });
				return tech ? tech.dvalue : null;
			}
		}
	}
};
</script>
