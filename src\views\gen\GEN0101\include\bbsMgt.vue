<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form">
			<div class="card-header">
				<h4>公告設定</h4>
				<span class="tx-square-bracket">為必填欄位</span>
			</div>
			<vue-form v-slot="{ errors, validate }" ref="queryForm">
				<div class="form-row">
					<table class="table table-bordered">
						<tbody>
							<tr>
								<th class="tx-require">訊息類別</th>
								<td>
									<template v-for="selMessage in selMessageMap">
										<div class="form-check form-check-inline">
											<vue-field
												:id="'selMessageMap_' + selMessage.msgCode"
												name="msgCode"
												type="radio"
												class="form-check-input has-validation"
												:value="selMessage.msgCode"
												v-model="selMsg.msgCode"
												label="訊息類別"
												:class="{ 'is-invalid': errors.msgCode }"
												rules="required"
											>
											</vue-field>
											<label class="form-check-label" :for="'selMessageMap_' + selMessage.msgCode" style="margin-right: 3px">{{
												selMessage.msgName
											}}</label>
										</div>
									</template>
									<span class="text-danger" v-show="errors.msgCode">{{ errors.msgCode }}</span>
								</td>
							</tr>
							<tr>
								<th class="tx-require">重要性</th>
								<td>
									<template v-for="selImportent in selImportentYn">
										<div class="form-check form-check-inline">
											<vue-field
												:id="'selImportent_' + selImportent.codeValue"
												name="importantYn"
												type="radio"
												class="form-check-input has-validation"
												:value="selImportent.codeValue"
												v-model="selMsg.importantYn"
												label="重要性"
												:class="{ 'is-invalid': errors.importantYn }"
												rules="required"
											>
											</vue-field>
											<label
												class="form-check-label"
												:for="'selImportent_' + selImportent.codeValue"
												style="margin-right: 3px"
												>{{ selImportent.codeName }}</label
											>
										</div>
									</template>
									<span class="text-danger" v-show="errors.importantYn">{{ errors.importantYn }}</span>
								</td>
							</tr>
							<tr>
								<th>主分類</th>
								<td>
									<select v-model="selMsg.mainCatCode" name="mainCatCode" class="form-select">
										<option :value="null">請選擇</option>
										<option :value="item.catCode" v-for="item in selMsgMainCat">
											{{ item.catName }}
										</option>
									</select>
								</td>
							</tr>
							<tr>
								<th>次分類</th>
								<td>
									<select v-model="selMsg.subCatCode" name="subCatCode" class="form-select" :disabled="!selMsg.mainCatCode">
										<option :value="null">請選擇</option>
										<option :value="item.catCode" v-for="item in selMsgSubCat">
											{{ item.catName }}
										</option>
									</select>
								</td>
							</tr>
							<tr>
								<th class="tx-require">有效日期</th>
								<td>
									<div class="input-group input-date">
										<vue-field
											type="date"
											id="validBgnDt"
											name="validBgnDt"
											v-model="selMsg.validBgnDt"
											size="13"
											label="有效日期起"
											class="form-control"
											maxlength="10"
											rules="required"
											:max="selMsg.validEndDt"
											:class="{ 'is-invalid': errors.validBgnDt }"
										></vue-field>
										<span class="input-group-text">~</span>
										<vue-field
											type="date"
											id="validEndDt"
											name="validEndDt"
											v-model="selMsg.validEndDt"
											size="13"
											label="有效日期迄"
											class="form-control"
											maxlength="10"
											rules="required"
											:min="selMsg.validBgnDt"
											:class="{ 'is-invalid': errors.validEndDt }"
										></vue-field>
									</div>
									<span class="text-danger" v-show="errors.validBgnDt || errors.validEndDt"
										>{{ errors.validBgnDt }} {{ errors.validEndDt }}</span
									>
								</td>
							</tr>
							<tr>
								<th class="tx-require">公告標題</th>
								<td>
									<vue-field
										id="msgTitle"
										name="msgTitle"
										label="公告標題"
										type="text"
										class="form-control form-input"
										v-model="selMsg.msgTitle"
										:class="{ 'is-invalid': errors.msgTitle }"
										rules="required|max: 50"
									>
									</vue-field>
									<span class="text-danger" v-show="errors.msgTitle">{{ errors.msgTitle }}</span>
								</td>
							</tr>
							<tr>
								<th>公告內容</th>
								<td>
									<vue-field
										name="msgContent"
										label="公告內容"
										id="msgContent"
										as="textarea"
										rows="8"
										class="textarea form-control"
										v-model="selMsg.msgContent"
										:class="{ 'is-invalid': errors.msgContent }"
										rules="max: 1000"
									></vue-field>
									<div class="tx-note">1000 個字可輸入</div>
									<span class="text-danger" v-show="errors.msgContent">
										{{ errors.msgContent }}
									</span>
								</td>
							</tr>
							<tr>
								<th class="tx-require">首頁是否顯示</th>
								<td>
									<template v-for="selOption in selOptionYn">
										<div class="form-check form-check-inline">
											<vue-field
												:id="'showYn_' + selOption.codeValue"
												name="showYn"
												type="radio"
												class="form-check-input"
												:value="selOption.codeValue"
												:disabled="$_.includes(initialMsgCode, selMsg.msgCode)"
												v-model="selMsg.showYn"
												label="首頁是否顯示"
												:class="{ 'is-invalid': errors.showYn }"
												rules="required"
											>
											</vue-field>
											<label class="form-check-label" :for="'showYn_' + selOption.codeValue" style="margin-right: 3px">{{
												selOption.codeName
											}}</label>
										</div>
									</template>
									<span class="text-danger" v-show="errors.msgCode">{{ errors.showYn }}</span>
								</td>
							</tr>
							<tr>
								<th>上傳檔案</th>
								<td>
									<vue-form v-slot="{ errors, validate }" class="col-lg-12" ref="fileForm">
										<div class="row g-2">
											<div class="input-group">
												<vue-field
													name="uploadFile"
													type="file"
													class="form-control"
													label="附件"
													@change="handleChange($event)"
													ref="uploadFile"
													:class="{ 'is-invalid': errors.uploadFile }"
													:rules="{
														required_file: true,
														mbSize: 15,
														extMgt: validExts,
														validateName: symbols,
														isOverSize: fileCntObj
													}"
													accept=".doc, .docx, .pdf, .xlsx, .txt"
												>
												</vue-field>
												<button class="btn btn-info btn-glow" type="button" name="'tempFile" size="30" @click="addFile">
													上傳
												</button>
											</div>
										</div>
										<ul class="list-group list-inline-tags mt-2">
											<li class="list-group-item" v-for="file in files">
												<a href="#" @click="previewFile(file.fileNo)">
													<span>{{ file.showName }}</span>
													<span
														class="img-delete JQ-delet"
														data-bs-toggle="tooltip"
														title="刪除"
														@click.stop="deleteFile(file.fileId)"
													></span>
												</a>
											</li>
										</ul>
										<div style="height: 25px">
											<span class="text-danger" v-show="errors.uploadFile">{{
												$filters.defaultValue(errors.uploadFile, '--')
											}}</span>
										</div>
									</vue-form>
								</td>
							</tr>
							<tr>
								<th>連結URL</th>
								<td>
									<input class="form-control" v-model="selMsg.favoriteLink" type="text" maxlength="100" placeholder="連結URL" />
								</td>
							</tr>
							<tr>
								<th>建立人員分機:</th>
								<td>
									<input
										class="form-control"
										v-model="selMsg.createUserExt"
										type="text"
										maxlength="100"
										placeholder="建立人員分機"
										:readOnly="selMsg.actionCode != 'A'"
									/>
								</td>
							</tr>
							<tr>
								<th>維護人員分機:</th>
								<td>
									<input
										class="form-control"
										v-model="selMsg.modifyUserExt"
										type="text"
										maxlength="100"
										placeholder="維護人員分機"
										:readOnly="selMsg.actionCode != 'M'"
									/>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
				<div class="form-footer">
					<button class="btn btn-primary btn-glow btn-save" @click.prevent="save">儲存送審</button>
				</div>
			</vue-form>
		</div>
	</div>
</template>
<script>
import { Field, Form } from 'vee-validate';
import _ from 'lodash';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	data: function () {
		return {
			msgId: null,
			validExts: ['doc', 'xlsx', 'docx', 'pdf', 'xlsx', 'txt'],
			symbols: ['/', ':', '*', '?', '"', '<', '>', '|'],
			selMessageMap: [], // 公告類別資料
			selMsgMainCat: [], // 主分類資料
			selMsgSubCat: [], // 次分類資料
			selImportentYn: [], // 重要性參數
			selOptionYn: [], // 重要性參數
			initialMsgCode: ['MSG21', 'MSG26'], // 跑馬燈、強制閱讀為預設
			selMsg: {
				actionCode: 'A',
				msgCode: null,
				importantYn: 'Y',
				mainCatCode: null,
				subCatCode: null,
				validBgnDt: null,
				validEndDt: null,
				msgTitle: null,
				msgContent: null,
				showYn: 'Y',
				favoriteLink: null,
				createUserExt: null,
				modifyUserExt: null
			},
			//檔案處裡
			fileTemp: null,
			uploadFiles: [],
			files: [],
			fileCnt: 0,
			maxFileCount: 30,
			errors: {
				uploadFile: null
			}
		};
	},
	computed: {
		userInfo: function () {
			return his.$store.getters['userInfo/info'];
		},
		fileCntObj: function () {
			return {
				fieldCnt: this.fileCnt,
				maxFileCount: this.maxFileCount
			};
		}
	},
	mounted: async function () {
		var self = this;
		$.when(
			self.getMessageMap(),
			(self.selMsgMainCat = await self.getMessageCat('M', null)),
			(self.selImportentYn = await self.getAdmCodeDetail('GM_IMPORTANT_YN')),
			(self.selOptionYn = await self.getAdmCodeDetail('OPTION_YN'))
		).then(function () {
			self.msgId = self.$route.params.msgId;
			if (!_.isNil(self.msgId)) {
				self.getMessage(self.msgId);
			}
		});
	},
	watch: {
		'selMsg.mainCatCode': async function (newVal, oldVal) {
			var self = this;
			if (newVal) {
				self.selMsgSubCat = await self.getMessageCat('S', newVal);
			} else {
				self.selMsg.subCatCode = null;
			}
		},
		'selMsg.msgCode': function (newVal, oldVal) {
			var self = this;
			if (newVal && _.includes(self.initialMsgCode, newVal)) {
				self.selMsg.showYn = 'Y';
			}
		}
	},
	methods: {
		getAdmCodeDetail: async function (codeType) {
			var self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType
			});
			return ret.data;
		},
		getMessageMap: async function (selMessageMap) {
			var self = this;
			const ret = await self.$api.getMessageMapApi();
			if (ret.data) {
				self.selMessageMap = ret.data;
				self.selMsg.msgCode = _.first(self.selMessageMap).msgCode;
			}
		},
		getMessageCat: async function (catType, mainCatCode) {
			var self = this;
			var data = { catType: catType, mainCatCode: mainCatCode };
			let reqData = _.omitBy(data, (value) => _.isNil(value) || value === '');
			const ret = await self.$api.getGenMessageCat(reqData);
			return ret.data;
		},
		getMessage: async function (msgId) {
			var self = this;
			const ret = await self.$api.getGenMessageApi({
				msgId: msgId
			});
			if (ret.data) {
				self.selMsg = _.clone(ret.data);
				self.selMsg.validBgnDt = moment(ret.data.validBgnDt).format('yyyy-MM-DD');
				self.selMsg.validEndDt = moment(ret.data.validEndDt).format('yyyy-MM-DD');
				self.selMsg.actionCode = 'M';
				self.files = _.clone(ret.data.fileList);
				_.forEach(self.files, function (file) {
					file.fileNo = file.msgFileId;
				});
			}
		},
		//檔案處裡
		handleChange: function (event) {
			var self = this;
			self.fileTemp = event.target.files[0];
			self.fileCnt = self.files.length;
		},
		addFile: function () {
			var self = this;
			$.when(self.$refs.fileForm.validate()).then(function (result) {
				if (result.valid) {
					if (self.files.length >= self.maxFileCount) {
						return;
					}

					var fileInfo = {
						fileNo: self.generatorId('file'),
						groupId: null,
						showName: self.fileTemp.name,
						fileName: null,
						contentType: self.fileTemp.contentType,
						filePath: null,
						file: self.fileTemp
					};
					self.files.push(fileInfo);
					self.$refs.fileForm.resetForm();
					self.$refs.uploadFile.$el.value = null;
				}
			});
		},
		previewFile: function (targetFileId) {
			var self = this;
			var index = self.files.findIndex((f) => f.fileNo === targetFileId);
			var fileInfo = self.files[index];
			var url;
			// 預覽待上傳檔案
			if (fileInfo.file) {
				url = URL.createObjectURL(fileInfo.file);
				var previewWindow = window.open(url, '_blank');
				previewWindow.document.title = fileInfo.showName;
				previewWindow.addEventListener('beforeunload', () => {
					URL.revokeObjectURL(url);
				});
				// 預覽伺服器檔案
			} else {
				self.$api.previewServerDoc({
					fileId: targetFileId,
					fileTitle: fileInfo.showName
				});
			}
		},
		deleteFile: function (targetFileId) {
			var self = this;
			var index = self.files.findIndex((f) => f.fileId === targetFileId);
			if (index != -1) {
				self.files.splice(index, 1);
			}
		},
		save: function () {
			var self = this;
			self.$refs.queryForm.validate().then(function (pass) {
				if (pass.valid) {
					self.bbmMaintainSave();
				}
			});
		},
		bbmMaintainSave: async function () {
			var self = this;
			const ret = await self.$api.getBbmMaintainSaveApi();
			if (ret.data && ret.data.length > 0) {
				var bbma = ret.data[0];
				if (bbma.wfgRoleKind !== 'rule4' && bbma.wfgsRole !== self.userInfo.roleCode) {
					this.$bi.alert('無使用權限，請洽系統管理人員！');
					return;
				} else {
					self.postMsgLog();
				}
			}
		},
		postMsgLog: async function () {
			var self = this;
			var data = _.clone(self.selMsg);
			data.msgId = self.msgId || '';
			data.fileObject = _.reduce(
				self.files,
				function (result, file) {
					var fileInfo = _.cloneDeep(file);
					fileInfo.msgFileId = file.fileNo;
					result.push(fileInfo);
					return result;
				},
				[]
			);
			var formData = new FormData();
			var jsonStr = JSON.stringify(data);
			formData.append('json', new Blob([jsonStr], { type: 'application/json' }));
			_.forEach(self.files, function (fileInfo) {
				if (fileInfo.file) {
					formData.append('fileNo', fileInfo.fileNo);
					formData.append('fileObject', fileInfo.file);
				}
			});
			var confirmText = self.msgId ? '編輯' : '新增';

			const ret = await self.$api.postMsgLogApi(formData);
			var confirmStatus = 'success';
			if (typeof ret.data.msgId === 'undefined' || ret.data.msgId === null) {
				confirmText = confirmText + '失敗，請聯絡管理員';
				confirmStatus = 'error';
			} else {
				confirmText = confirmText + '成功';
			}
			self.$swal
				.fire({
					icon: confirmStatus,
					text: confirmText,
					showCloseButton: true,
					confirmButtonText: '確認',
					buttonsStyling: false,
					customClass: {
						confirmButton: 'btn btn-success'
					}
				})
				.then(function (ret) {
					self.$router.push('/gen/bbsMgt');
				});
		},
		generatorId: function (name) {
			return name + '-' + _.now() + _.random(0, 99);
		}
	}
};
</script>
