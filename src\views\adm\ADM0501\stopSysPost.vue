<template>
	<dynamic-title></dynamic-title>
	<div>
		<div class="row">
			<div class="col-12">
				<div class="card card-form">
					<div class="card-header">
						<h4>請輸入下列資料</h4>
						<span class="tx-square-bracket">為必填欄位</span>
					</div>
					<vue-form v-slot="{ errors }" ref="queryForm">
						<div class="card-body">
							<div class="row g-3 align-items-end">
								<div class="col-lg-6">
									<label class="form-label tx-require">停機日期(起)</label>
									<div class="input-group">
										<span class="input-group-text">日期</span>
										<vue-field
											:class="{ 'is-invalid': errors.startDate }"
											name="startDate"
											type="date"
											class="form-control wd-30p-f"
											v-model="startDate"
											label="停機日期(起)-日期"
											rules="required"
										></vue-field>
										<span class="input-group-text">時間</span>
										<vue-field
											as="select"
											:class="{ 'is-invalid': errors.startHr }"
											name="startHr"
											class="form-select"
											v-model="startHr"
											label="停機日期(起)-時間"
											rules="required"
										>
											<option value="00">00</option>
											<option value="01">01</option>
											<option value="02">02</option>
											<option value="03">03</option>
											<option value="04">04</option>
											<option value="05">05</option>
											<option value="06">06</option>
											<option value="07">07</option>
											<option value="08">08</option>
											<option value="09">09</option>
											<option value="10">10</option>
											<option value="11">11</option>
											<option value="12">12</option>
											<option value="13">13</option>
											<option value="14">14</option>
											<option value="15">15</option>
											<option value="16">16</option>
											<option value="17">17</option>
											<option value="18">18</option>
											<option value="19">19</option>
											<option value="20">20</option>
											<option value="21">21</option>
											<option value="22">22</option>
											<option value="23">23</option>
										</vue-field>
										<vue-field
											as="select"
											name="startMin"
											id="startMin"
											class="form-select"
											v-model="startMin"
											label="停機日期(起)-分鐘"
											rules="required"
											:class="{ 'is-invalid': errors.startMin }"
										>
											<option value="00">00</option>
											<option value="05">05</option>
											<option value="10">10</option>
											<option value="15">15</option>
											<option value="20">20</option>
											<option value="25">25</option>
											<option value="30">30</option>
											<option value="35">35</option>
											<option value="40">40</option>
											<option value="45">45</option>
											<option value="50">50</option>
											<option value="55">55</option>
										</vue-field>
									</div>
									<div style="height: 25px">
										<span class="text-danger" v-show="errors.startDate">{{ errors.startDate }}</span>
										<span class="text-danger" v-show="errors.startHr">{{ errors.startHr }}</span>
										<span class="text-danger" v-show="errors.startMin">{{ errors.startMin }}</span>
									</div>
								</div>
								<div class="col-lg-6">
									<label class="form-label tx-require">停機日期(迄)</label>
									<div class="input-group">
										<span class="input-group-text">日期</span>
										<vue-field
											:class="{ 'is-invalid': errors.endDate }"
											name="endDate"
											type="date"
											id="endDate"
											class="form-control wd-30p-f"
											v-model="endDate"
											label="停機日期(迄)-日期"
											rules="required"
										></vue-field>
										<span class="input-group-text">時間</span>
										<vue-field
											as="select"
											:class="{ 'is-invalid': errors.endHour }"
											name="endHour"
											id="endHour"
											class="form-select"
											v-model="endHr"
											label="停機日期(迄)-時間"
											rules="required"
										>
											<option value="00">00</option>
											<option value="01">01</option>
											<option value="02">02</option>
											<option value="03">03</option>
											<option value="04">04</option>
											<option value="05">05</option>
											<option value="06">06</option>
											<option value="07">07</option>
											<option value="08">08</option>
											<option value="09">09</option>
											<option value="10">10</option>
											<option value="11">11</option>
											<option value="12">12</option>
											<option value="13">13</option>
											<option value="14">14</option>
											<option value="15">15</option>
											<option value="16">16</option>
											<option value="17">17</option>
											<option value="18">18</option>
											<option value="19">19</option>
											<option value="20">20</option>
											<option value="21">21</option>
											<option value="22">22</option>
											<option value="23">23</option>
										</vue-field>
										<vue-field
											as="select"
											name="endMin"
											id="endMin"
											class="form-select"
											v-model="endMin"
											label="停機日期(迄)-分鐘"
											rules="required"
											:class="{ 'is-invalid': errors.endMin }"
										>
											<option value="00">00</option>
											<option value="05">05</option>
											<option value="10">10</option>
											<option value="15">15</option>
											<option value="20">20</option>
											<option value="25">25</option>
											<option value="30">30</option>
											<option value="35">35</option>
											<option value="40">40</option>
											<option value="45">45</option>
											<option value="50">50</option>
											<option value="55">55</option>
										</vue-field>
									</div>
									<div style="height: 25px">
										<span class="text-danger" v-show="errors.endDate">{{ errors.endDate }}</span>
										<span class="text-danger" v-show="errors.endHour">{{ errors.endHour }}</span>
										<span class="text-danger" v-show="errors.endMin">{{ errors.endMin }}</span>
									</div>
								</div>
								<div class="col-lg-9">
									<label class="form-label tx-require">停機說明</label>
									<vue-field
										as="textarea"
										:class="{ 'is-invalid': errors.shutdownDesc }"
										name="shutdownDesc"
										rows="3"
										id="shutdownDesc"
										class="textarea form-control"
										v-model="shutdownDesc"
										label="停機說明"
										:rules="{ required: true, max: 200 }"
									></vue-field>
									<div class="tx-note">200 個字可輸入</div>
									<span class="text-danger" style="height: 3px" v-show="errors.shutdownDesc">
										{{ errors.shutdownDesc }}
									</span>
								</div>
								<div class="col-lg-3 mb-lg-5">
									<button class="btn btn-primary btn-glow btn-save" v-if="shutdownId == null" @click.prevent="insertShutdownInfo">
										儲存
									</button>
									<button class="btn btn-secondary btn-glow btn-cancle" v-if="shutdownId != null" @click.prevent="clearForm()">
										取消修改
									</button>
									<button class="btn btn-primary btn-glow btn-modify" v-if="shutdownId != null" @click.prevent="updateShutdownInfo">
										修改
									</button>
								</div>
							</div>
						</div>
					</vue-form>
				</div>

				<div class="card card-table">
					<div class="card-header">
						<h4>停機公告列表</h4>
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-hover text-center">
							<thead>
								<tr>
									<th>停機日期(起)</th>
									<th>停機日期(迄)</th>
									<th class="text-start wd-40p">停機說明</th>
									<th>執行</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="item in shutdownInfo">
									<td data-th="停機日期(起)">{{ item.startDt }}</td>
									<td data-th="停機日期(迄)">{{ item.endDt }}</td>
									<td data-th="停機說明" class="text-start">{{ item.shutdownDesc }}</td>
									<td data-th="執行">
										<button
											v-show="isStartDateFuture(item.endDt)"
											type="button"
											class="btn btn-info btn-glow btn-icon btn-edit"
											data-bs-toggle="tooltip"
											data-bs-original-title="編輯"
											@click="doUpdate(item)"
										>
											<i class="bi bi-pen"></i>
										</button>
										<button
											v-show="isStartDateFuture(item.endDt)"
											type="button"
											class="btn btn-danger btn-glow btn-icon"
											data-bs-toggle="tooltip"
											data-bs-original-title="刪除"
											@click="deleteShutdownInfo(item)"
										>
											<i class="bi bi-trash"></i>
										</button>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>

		<!--頁面內容 end-->
	</div>
</template>
<script>
import Swal from 'sweetalert2';
import { Field, Form } from 'vee-validate';
import dynamicTitle from '@/views/components/dynamicTitle.vue';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		dynamicTitle
	},
	data: function () {
		return {
			//API 用參數
			shutdownId: null,
			startDt: null,
			startDate: null,
			startHr: null,
			startMin: null,
			endDt: null,
			endDate: null,
			endHr: null,
			endMin: null,
			//畫面顯示用參數
			shutdownDesc: null,
			//主要顯示資料
			shutdownInfo: []
		};
	},
	mounted: function () {
		var self = this;
		self.getShutdownInfo();
	},
	methods: {
		//init function
		getShutdownInfo: function () {
			var self = this;
			self.$api.getShutdownInfoApi().then(function (ret) {
				self.shutdownInfo = ret.data;
			});
		},
		doUpdate: function (item) {
			var self = this;
			self.shutdownId = item.shutdownId;
			self.startDt = new Date(item.startDt);
			self.endDt = new Date(item.endDt);
			self.shutdownDesc = item.shutdownDesc;

			self.startDate =
				self.startDt.getFullYear() +
				'-' +
				self.paddingLeft(self.startDt.getMonth() + 1, 2) +
				'-' +
				self.paddingLeft(self.startDt.getDate(), 2);
			self.startHr = self.paddingLeft(self.startDt.getHours(), 2);
			self.startMin = self.paddingLeft(self.startDt.getMinutes(), 2);

			self.endDate =
				self.endDt.getFullYear() + '-' + self.paddingLeft(self.endDt.getMonth() + 1, 2) + '-' + self.paddingLeft(self.endDt.getDate(), 2);
			self.endHr = self.paddingLeft(self.endDt.getHours(), 2);
			self.endMin = self.paddingLeft(self.endDt.getMinutes(), 2);
		},
		clearForm: function () {
			var self = this;
			self.shutdownId = null;
			self.startDt = null;
			self.startDate = null;
			self.startHr = null;
			self.startMin = null;
			self.endDt = null;
			self.endDate = null;
			self.endHr = null;
			self.endMin = null;
			self.shutdownDesc = null;
			self.$refs.queryForm.resetForm();
		},
		insertShutdownInfo: function () {
			var self = this;
			self.$refs.queryForm.validate().then(function (pass) {
				if (pass.valid) {
					self.buildQueryStr();
					self.$api
						.postShutdownInfoApi({
							startDt: self.startDt,
							endDt: self.endDt,
							shutdownDesc: self.shutdownDesc
						})
						.then(function (ret) {
							Swal.fire({
								icon: 'success',
								text: '新增成功。',
								showCloseButton: true,
								confirmButtonText: '確認',
								buttonStyling: false,
								customClass: {
									confirmButton: 'btn btn-success'
								}
							});
							self.clearForm();
							self.getShutdownInfo();
						});
				}
			});
		},
		updateShutdownInfo: function () {
			var self = this;

			self.$refs.queryForm.validate().then(function (pass) {
				if (pass.valid) {
					self.buildQueryStr();
					self.$api
						.patchShutdownInfoApi({
							shutdownId: self.shutdownId,
							startDt: self.startDt,
							endDt: self.endDt,
							shutdownDesc: self.shutdownDesc
						})
						.then(function (ret) {
							Swal.fire({
								icon: 'success',
								text: '修改成功。',
								showCloseButton: true,
								confirmButtonText: '確認',
								buttonStyling: false,
								customClass: {
									confirmButton: 'btn btn-success'
								}
							});
							self.clearForm();
							self.getShutdownInfo();
						});
				}
			});
		},
		deleteShutdownInfo: async function (item) {
			var self = this;
			var result = await self.$bi.confirm('確定要刪除此筆資料嗎?');
			if (result.isConfirmed) {
				self.$api
					.deleteShutdownInfoApi({
						shutdownId: item.shutdownId
					})
					.then(function (ret) {
						Swal.fire({
							icon: 'success',
							text: '刪除成功。',
							showCloseButton: true,
							confirmButtonText: '確認',
							buttonStyling: false,
							customClass: {
								confirmButton: 'btn btn-success'
							}
						});
						self.clearForm();
						self.getShutdownInfo();
					});
			}
		},
		buildQueryStr: function () {
			var self = this;
			if (!self.startMin) {
				self.startMin = '00';
			}

			if (!self.endMin) {
				self.endMin = '00';
			}
			self.startDt = self.startDate + ' ' + self.startHr + ':' + self.startMin;
			self.endDt = self.endDate + ' ' + self.endHr + ':' + self.endMin;
		},
		paddingLeft: function (str, length) {
			var self = this;
			str = str + '';
			if (str.length >= length) {
				return str;
			} else {
				return self.paddingLeft('0' + str, length);
			}
		},
		isStartDateFuture: function (startDate) {
			return new Date(startDate) > new Date();
		}
	}
};
</script>
