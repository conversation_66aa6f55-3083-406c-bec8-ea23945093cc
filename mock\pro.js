import SearchProductMockData from './mockData/pro/SearchProductMockData.json';
import groupProCurrenciesMenuMockData from './mockData/pro/groupProCurrenciesMenuMockData.json';
import getAssetcatsMenuMockDataMockData from './mockData/pro/getAssetcatsMenuMockData.json';
import getProPfcatsMenuMockData from './mockData/pro/getProPfcatsMenuMockData.json';
import getFastFilterMenuMockData from './mockData/pro/getFastFilterMenuMockData.json';
import getTimeRangeMenuMockData from './mockData/pro/getTimeRangeMenuMockData.json';
import getRowNumerMenuMockData from './mockData/pro/getRowNumerMenuMockData.json';
import getPerfMenuMockData from './mockData/pro/getPerfMenuMockData.json';
import getRiskMenuApiMockData from './mockData/pro/getRiskMenuApiMockData.json';
import getProPriceRangeMenuMockData from './mockData/pro/getProPriceRangeMenuMockData';
// 商品資料查詢/維護-商品類型
export function getProPfcatsMenuApi() {
	return getProPfcatsMenuMockData;
}

// 商品資料查詢 - 一般查詢/查詢結果
export function searchProductsApi() {
	return SearchProductMockData;
}

// 商品資料查詢 - 快速查詢/查詢結果
export function fastSearchProductsApi({ fastCode, rangeType, rangeFixed, perfTime, rowNumber, queryString }) {}

// 商品資料查詢 - 加入觀察
export function addFavoriteApi({ proCode, pfcatCode }) {}

// 商品資料查詢 - 取消觀察/刪除我的最愛
export function deleteFavoriteApi({ proCode }) {}

// 取得檔案預覽資訊
export function downloadOtherFileApi({ fileId }) {}

// 風險等級選單
export function getRiskMenuApi() {
	return getRiskMenuApiMockData;
}

// 取得資產類別
export function getAssetcatsMenuApi() {
	return getAssetcatsMenuMockDataMockData;
}
// 取得快速篩選選單
export function getFastFilterMenuApi() {
	return getFastFilterMenuMockData;
}

// 取得顯示區間
export function getTimeRangeMenuApi() {
	return getTimeRangeMenuMockData;
}
// 取得顯示資料筆數
export function getRowNumerMenuApi() {
	return getRowNumerMenuMockData;
}

// 取得標的績效
export function getPerfMenuApi() {
	return getPerfMenuMockData;
}

// 配息頻率選單
export function getIntFreqUnitTypeMenuApi() {}

// 幣別-常用幣別選單
export function groupProCurrenciesMenuApi() {
	return groupProCurrenciesMenuMockData;
}

// 信託分析ETF-績效表現(歷史績效走勢圖)-顯示區間列表
export function getProPriceRangeMenuApi() {
	return getProPriceRangeMenuMockData;
}

// 商品資料維護-查詢結果
export function editProductsApi({ pfcatCode, principalGuarYn, bankProCode, proName, riskCode, intFreqUnitType, curCodes }, pageable) {}

// pro 檔案檢視
export function downloadProFileApi({ proFileId, eventId }) {}

// 商品資料查詢(全商品)-商品檢視-商品基本資料
export function getProductInfoApi({ proCode, pfcatCode }) {}

// 商品資料維護-傳送主管審核
export function patchProductApi(formData) {}

// 商品資料審核-商品檢視
export function getProductLogApi({ eventId }) {}

// 商品資料查詢(全商品)-商品檢視-商品附加資料(全部)
export function getProductsCommInfo({ proCode, pfcatCode }) {
	return request({
		url: apiPath + '/pro/productsCommInfo',
		method: 'get',
		params: {
			proCode,
			pfcatCode
		}
	});
}

// 發行機構選單
export function getGroupIssuersMenuApi({ pfcatCode }) {}

export function getBondPriceAnaApi({ proCodes, freqType, freqFixed }) {}

// 取得基金類型選項
export function getProTypeListApi({ pfcatCode }) {}

// 取得快速篩選條件選項
export function getFundFastMenuApi() {}

// 進階搜尋 基金類別選擇全資料庫基金=>計價幣別選單
export function getCurMenuApi() {}

// 基金類別下拉
export function getInvestmentTypeMenuApi({ local }) {}

// 投資地區下拉
export function getGeoFocusMenuApi({ local }) {}

export function getGlobalClassCodeMenuApi() {}

export function getGlobalClassCodeOtherMenuApi() {}

export function getLocalClassMenuApi() {}

// 進階搜尋 基金公司
export function getLocalFundCompanies() {}

export function getForeignFundCompanies() {}

// 取得商品資料查詢(信託-基金)-風險/績效選單
export function getProWeightedTypeMenuApi() {}

export function getFundProducts(payload, queryString) {}

export function getFastPageDataApi(payload, queryString) {}

export function getRankPageDataApi(payload) {}

export function getProSearchesApi() {}

// 歷史查詢條件刪除
export function deleteHistorySearchApi({ searchSeq }) {}

// 加權條件篩選 儲存條件
export function postProSearchesApi({ searchName, memo, proSearchesMaps }) {}

// 信託-基金 進階搜尋
export function getAdvancePageDataApi(payload, queryString) {}

export function getBondIssuersMenuApi() {}
// 選擇債券-發行機構選單
export function getBondIssuersApi() {}

// 取得保證機構選單
export function getBondGuaranteesApi() {}

// 取得快速篩選條件選項
export function getBondFastMenuApi() {}

// 取得債券商品列表
export function getBondProductsApi(payload, queryString = '') {}

// 取得快速篩選債券商品列表
export function getBondFastProductsApi(payload, queryString = '') {}

// 刪除我的最愛
export function deleteBondFavoriteApi({ proCode }) {}
export function getGlobalFundsApi({ lipperIds }) {}

export function getFundInfo({ proCode }) {}

//歷史查詢條件 編輯
export function getProSearchesMap({ searchSeq }) {}

export function getFundCompany({ proCode }) {}

export function getLipperScoreApi({ proCode }) {}

export function getFundInfoFundSizeLatest2Api({ proCode }) {}

export function getTechsApi({ proCode, techCurrencyCode }) {}

export function getPreMonthEndPriceApi({ prCode, beginDate, endDate }) {}

// 刪除使用者各功能來源的商品名單
export function deleteProPotSearchRstApi() {}

export function getSdMenu() {}

export function getEtfFastMenuApi() {}

export function getEtfProductsApi(payload, queryString) {}

export function getEtfFastPageDataApi(payload, queryString) {}
export function getFundProfileBenchmarksMenuApi({ lipperIds }) {}

export function getFundPerformanceClassMajorMenuApi({ lipperIds }) {}
export function getEtfProfileNameMenuApi() {}

export function getEtfProfileBenchmarksMenuApi() {}

export function getEtfPerformanceClassMajorMenuApi() {}

export function getProductByPfcatCodeApi({ pfcatCode, issuerCode }) {}

export function comparePropItemApi({ proCodes, url }) {}

// 績效比較圖-已加入商品
export function observedFundsApi({ lipperIds }) {}

// -商品歷史績效走勢圖
export function fundRunChartApi({ lipperIds }) {}

export function getObservedEtfsApi({ lipperIds }) {}

export function getPerformanceRunChartApi({ proCodes, freqType, freqFixed }) {}

export function getEtfDetailApi({ proCode }) {}

export function getEtfStockHoldApi({ proCode }) {}

export function getEtfPriceApi({ prodCode }) {}

export function getPricesChartDataApi({ prodCode, freqType, freqFixeds }) {}

export function getEtfPerformancesApi({ proCodes, freqType, freqFixed }) {}

export function getEtfPerformanceStatsApi({ proCode }) {}

export function getEtfIntRateApi({ proCode }) {}

export function getGroupProExchangesMenuApi() {}

export function getGroupFundCmpsMenuApi({ localYn, fuseYn }) {}

export function getFundListApi({ fundCode }) {}

export function getGlobalFundListApi({ fundCode, globalGlassCode }) {}

export function get2x2PerfsApi({ searchData }) {}

export function getFundCodeApi({ fundCode }) {}

export function getDividendsApi({ fundCode }) {}

export function getBenchmarksApi() {}

export function getFundCompaniesApi() {}

export function getFundsApi({ companyCode }) {}

export function getFundSizeApi({ fundCode, beginDate, endDate }) {}

export function getPctsApi({ beginDate, endDate, techCurrencyCode, assetCodes }) {}

export function getPerfRankApi({ techCurrencyCode, fundCode, statCode, fundPerfRank, fundPerfRankCode }) {}

export function getMonthPctsApi({ fundCode, beginDate, endDate, techCurrencyCode }) {}

export function getAllocsApi(fundCode) {}

export function getObservedProductsApi({ proCodes }) {}

export function getPfdFastMenuApi() {}

export function getPfdProductsApi(payload, queryString) {}

export function getPfdFastPageDataApi(payload, queryString) {}

export function groupTargetMenuApi({ stockCode }) {}

export function getTargetViewDataListApi({ proCode }) {}

// 取得快速篩選條件選項
export function getSpFastFilterMenuApi() {}

export function getSpProductsApi(payload, queryString) {}
export function getSpProductsFilterQueryApi(payload, queryString) {}
// 取得價格分析資料
export function getPriceAnaApi({ proCodes }) {}

// 取得快速篩選條件選項
export function getInsFastFilterMenuApi() {}

// 取得保險商品列表
export function getInsProductsApi(payload, queryString) {}

// 取得保險商品快速篩選查詢
export function getInsProductsFilterQueryApi(payload, queryString) {}

export function getOtherInsCompanies() {}

export function getGroupInsCompanies() {}

// DCI 快速篩選選單
export function getDciFastFilterMenuApi() {}

// DCI 商品查詢
export function getDciProductsApi() {}

// DCI 商品快速查詢
export function getDciProductsFilterQueryApi() {}

export function getSecFastMenuApi() {}
export function getSecProductsApi() {}

export function getSecProductsFilterQueryApi() {}

export function getNewShelfProductList() {}

export function getProSelected() {}

export function getNewProPfcatsMenuApi() {}

export function deleteShelfProduct() {}

export function getSelectProPfcatsMenuApi() {}
export function getSelProPfcatsMenuApi() {}
export function getPrdSearchSelected() {}
export function getPrdSearchSelectedDetail() {}
export function getProFastSelected() {}

export function getProSelectedCombs() {}

export function getProSelectedLog() {}

export function getIssuersMenu() {}
export function deleteProSelectedLog() {}

export function postProSelected() {}
