<template>
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">信託-ETF</h4>
				<button type="button" class="btn-close" @click.prevent="close()" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="card card-prointro shadow-none mb-3">
					<div class="pro_left">
						<div class="pro_name">
							<div class="avatar avatar-md avatar-fund"></div>
							<h4><span>商品名稱</span> <br />{{ proInfo.proName }}</h4>
						</div>
						<h4 class="pro_value">
							<span>最新淨值</span>
							<br /><span>{{ proInfo.aprice }}</span> <br /><span>{{ proInfo.priceDt }}</span>
						</h4>
						<h4 class="pro_value">
							<span>最新市價</span>
							<br /><span>{{ proInfo.sprice }}</span> <br /><span>{{ proInfo.priceDt }}</span>
						</h4>
					</div>
					<div class="pro_class">
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品代碼</span> <br />{{ proInfo.bankProCode }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6>
								<span>資產類別 <br /></span>{{ proInfo.assetcatName }}
							</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品主類</span> <br />{{ proInfo.pfcatName }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品次類</span><br />{{ proInfo.proTypeName }}</h6>
						</div>
					</div>
				</div>
				<div class="tab-nav-pills">
					<ul class="nav nav-pills justify-content-center" role="tablist">
						<li class="nav-item"><a class="nav-link active" href="#Sectionetf1" data-bs-toggle="pill">商品基本資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionetf2" data-bs-toggle="pill">商品共同資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionetf3" data-bs-toggle="pill">商品附加資料</a></li>
					</ul>
					<div class="tab-content">
						<div class="tab-pane fade show active" id="Sectionetf1">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>ETF商品資訊</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>交易所名稱</th>
											<td class="wd-30p">
												{{ proInfo.etfInfo.exchangeName }}
											</td>
											<th>交易所代碼</th>
											<td class="wd-30p">
												{{ proInfo.etfInfo.exchangeCode }}
											</td>
										</tr>
										<tr>
											<th>ETF類型</th>
											<td>{{ proInfo.etfInfo.proTypeName }}</td>
											<th>風險等級</th>
											<td>{{ proInfo.etfInfo.riskName }}</td>
										</tr>
										<tr>
											<th>計價幣別</th>
											<td>{{ proInfo.etfInfo.curCode }}</td>
											<th>是否可銷售</th>
											<td>{{ proInfo.etfInfo.buyYn }}</td>
										</tr>
										<tr>
											<th>國際代碼</th>
											<td>{{ proInfo.etfInfo.isinCode }}</td>
											<th>交易單位</th>
											<td>{{ proInfo.etfInfo.txUnit }}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionetf2">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>銷售相關資料</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>交易所下單碼</span></th>
											<td class="wd-80p" colspan="3">
												<span>{{ proInfo.brokerCode }}</span>
											</td>
										</tr>
										<tr>
											<th width="20%">銷售地區</th>
											<td width="30%" v-if="proInfo.allYn == 'Y'">全行</td>
											<td width="30%" v-else></td>
											<th width="20%">限PI申購</th>
											<td width="30%">
												{{ proInfo.profInvestorYn }}
											</td>
										</tr>
										<tr>
											<th><span>銷售對象</span></th>
											<td class="wd-30p" colspan="3">{{ proInfo.targetCusBuName }}</td>
										</tr>
										<tr>
											<th>是否開放申購</th>
											<td>{{ proInfo.buyYn }}</td>
											<th>是否開放贖回</th>
											<td>{{ proInfo.sellYn }}</td>
										</tr>
										<tr>
											<th><span>波動類型</span></th>
											<td>
												<span v-if="actionType !== 'EDIT'">{{ proInfo.volatilityTypeName }}</span>
												<template v-for="(item, index) in volatilityTypeList" v-if="actionType === 'EDIT'">
													<div class="form-check form-check-inline">
														<input
															class="form-check-input"
															type="radio"
															:id="'volatilityType_' + index"
															name="volatilityType"
															v-model="proInfo.volatilityType"
															:value="item.codeValue"
														/>
														<label class="form-check-label" :for="'volatilityType_' + index">{{ item.codeName }}</label>
													</div>
												</template>
											</td>
											<th><span>配息頻率</span></th>
											<td>
												<span v-if="actionType !== 'EDIT'">{{ proInfo.intFreqUnitypeName }}</span>
												<select
													class="form-select"
													name="intFreqUnitype"
													id="intFreqUnitype"
													v-if="actionType === 'EDIT'"
													v-model="proInfo.intFreqUnitype"
													rules="required"
												>
													<option v-for="item in intFreqUnitypeList" :value="item.codeValue">
														{{ item.codeName }}
													</option>
												</select>
											</td>
										</tr>
										<tr>
											<th><span>保本要求</span></th>
											<td colspan="3">
												<span v-if="actionType !== 'EDIT'">{{ proInfo.principalGuarYn }}</span>
												<span v-else>
													<div v-for="item in principalGuarMenu" class="form-check form-check-inline">
														<input
															class="form-check-input"
															:id="'principalGuar' + item.codeValue"
															v-model="proInfo.principalGuarYn"
															type="radio"
															:value="item.codeValue"
															name="fastCode"
														/>
														<label class="form-check-label" :for="'principalGuar' + item.codeValue">{{
															$filters.defaultValue(item.codeName, '--')
														}}</label>
													</div>
												</span>
											</td>
										</tr>
										<tr v-if="false">
											<th><span>配息率</span></th>
											<td colspan="3">
												{{ proInfo.intRate * 100 }}%<span v-if="proInfo.intRate * 100 > 5">(高配息率)</span
												><span v-else>(一般配息率)</span>
											</td>
										</tr>
										<tr>
											<th><span>理財需求</span></th>
											<td colspan="3">
												<div class="form-check form-check-inline" v-for="(item, index) in finReqCodeMenu">
													<input
														class="form-check-input"
														name="finReqCodes"
														:disabled="actionType == 'EDIT' ? false : true"
														:id="'finReqCodes_' + index"
														v-model="finReqCodes"
														:value="item.codeValue"
														type="checkbox"
													/>
													<label class="form-check-label" :for="'finReqCodes_' + index">{{ item.codeName }}</label>
												</div>
											</td>
										</tr>
										<tr>
											<th>商品標籤</th>
											<td disabled="disabled" colspan="3">
												{{ proInfo.selprocatNames }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionetf3">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>其他設定</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>商品投資標的</span></th>
											<td class="wd-80p">
												<span>{{ proInfo.sectorName }}</span>
											</td>
										</tr>
										<tr>
											<th><span>商品投資地區</span></th>
											<td>
												<span>{{ proInfo.geoFocusName }}</span>
											</td>
										</tr>
										<tr>
											<th><span>比較基準設定</span></th>
											<td>
												<span>{{ proInfo.benchmarkName }}</span>
											</td>
										</tr>
										<tr>
											<th><span>備註</span></th>
											<td>
												<textarea
													class="form-control"
													cols="80"
													rows="4"
													size="200"
													maxlength="200"
													v-model="proInfo.memo"
													:readonly="actionType !== 'EDIT'"
												></textarea>
												<div class="tx-note" v-if="proInfo.memo">{{ 200 - proInfo.memo.length }} 個字可輸入</div>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>公開說明書</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_B"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['B']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="etfUploadFileB"
																@change="triggerFile($event, 'B')"
																accept="application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.wordprocessingml.document"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('B')">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['B'] && uploadFiles['B'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['B'].url" target="_blank">{{ uploadFiles['B'].url }}</a
													><br v-if="uploadFiles['B']" />
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['B'] && uploadFiles['B'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['B'])"
														>{{ uploadFiles['B'].showName }}</span
													>
													<span
														v-show="uploadFiles['B'] && uploadFiles['B'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('B', uploadFiles['B'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
										<tr>
											<th>投資人須知</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_D"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['D']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="etfUploadFileD"
																@change="triggerFile($event, 'D')"
																accept="application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.wordprocessingml.document"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('D')">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['D'] && uploadFiles['D'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['D'].url" target="_blank">{{ uploadFiles['D'].url }}</a
													><br v-if="uploadFiles['D']" />
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['D'] && uploadFiles['D'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['D'])"
														>{{ uploadFiles['D'].showName }}</span
													>
													<span
														v-show="uploadFiles['D'] && uploadFiles['D'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('D', uploadFiles['D'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
										<tr>
											<th>基金月報</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_E"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['E']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="etfUploadFileE"
																@change="triggerFile($event, 'E')"
																accept="application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.wordprocessingml.document"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('E')">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['E'] && uploadFiles['E'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['E'].url" target="_blank">{{ uploadFiles['E'].url }}</a
													><br v-if="uploadFiles['E']" />
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['E'] && uploadFiles['E'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['E'])"
														>{{ uploadFiles['E'].showName }}</span
													>
													<span
														v-show="uploadFiles['E'] && uploadFiles['E'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('E', uploadFiles['E'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
										<tr>
											<th>DM</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_F"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['F']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="etfUploadFileF"
																@change="triggerFile($event, 'F')"
																accept="application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.wordprocessingml.document"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('F')">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['F'] && uploadFiles['F'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['F'].url" target="_blank">{{ uploadFiles['F'].url }}</a
													><br v-if="uploadFiles['F']" />
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['F'] && uploadFiles['F'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['F'])"
														>{{ uploadFiles['F'].showName }}</span
													>
													<span
														v-show="uploadFiles['F'] && uploadFiles['F'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('F', uploadFiles['F'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
										<tr>
											<th>其他</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_G"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['G']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="etfUploadFileG"
																@change="triggerFile($event, 'G')"
																accept=".xlsx,.xls"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('G')">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['G'] && uploadFiles['G'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['G'].url" target="_blank">{{ uploadFiles['G'].url }}</a
													><br v-if="uploadFiles['G']" />
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['G'] && uploadFiles['G'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['G'])"
														>{{ uploadFiles['G'].showName }}</span
													>
													<span
														v-show="uploadFiles['G'] && uploadFiles['G'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('G', uploadFiles['G'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>其他相關附件</h4>
								</div>
							</div>
							<table class="table table-RWD table-bordered table-horizontal-RWD">
								<tr>
									<td>
										<span v-for="(item, index) in otherFileList">
											<a
												v-if="index === otherFileList.length - 1"
												v-show="item.show"
												href="#"
												class="tx-link"
												@click="downloadOtherFile(item.docFileId)"
												>{{ $filters.defaultValue(item.showName, '--') }}</a
											>
											<a v-else href="#" class="tx-link" v-show="item.show" @click="downloadOtherFile(item.docFileId)"
												>{{ $filters.defaultValue(item.showName, '--') }}、</a
											>
										</span>
									</td>
								</tr>
							</table>
							<div class="tx-note">其他相關附件為文件管理內所上傳與本商品相關的文件。</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<input id="modalCloseButton" type="button" @click.prevent="close()" class="btn btn-white" value="關閉" />
				<input
					type="button"
					class="btn btn-primary"
					value="傳送主管審核"
					v-if="actionType == 'EDIT'"
					@click="
						updateProduct();
						close();
					"
				/>
			</div>
		</div>
	</div>
</template>
<script>
import moment from 'moment';
import _ from 'lodash';
import { biModule } from '@/utils/bi/module.js';

export default {
	props: {
		actionType: String,
		benchmark: Object,
		gotoPage: Function,
		finReqCodeMenu: Array,
		downloadFile: Function,
		downloadOtherFile: Function,
		close: Function
	},
	data: function () {
		const now = moment();
		return {
			proInfo: {
				fundInfo: {},
				etfInfo: {},
				bondInfo: {},
				spInfo: {},
				insInfo: {},
				dciInfo: {},
				secInfo: {}
			},
			volatilityTypeList: [],
			finReqCodes: [],
			proFileList: [], // 相關附件檔案清單
			otherFileList: [], // 其他相關附件
			commInfo: {
				proFiles: []
			},
			proCode: '',
			pfcatCode: '',
			selectYnList: [],
			intFreqUnitypeList: [],
			principalGuarMenu: [],

			//File 用參數
			url: [],
			uploadFile: {}, //上傳檔案
			uploadFiles: [], // 已上傳檔案陣列

			benchmarkName: null,
			benchmarkRiskValue: null,
			benchmarkAvgintPerct: null
		};
	},
	watch: {},
	mounted: function () {},
	methods: {
		async getIntFreqUnitypeList() {
			let self = this;
			let ret = await self.$api.getAdmCodeDetail({ codeType: 'INT_FREQ_UNITTYPE' });
		},
		getProInfo: function (bankProCode, pfcatCode, eventId) {
			let self = this;
			self.resetModalVaule();
			Promise.all([self.getIntFreqUnitypeList()]).then(() => {
				if (eventId) {
					self.doViewProLog(bankProCode, pfcatCode, eventId); // //審核資料
				} else {
					self.getProductInfo(bankProCode, pfcatCode); // 基本資料 共用資料
					self.getProductCommInfo(bankProCode, pfcatCode); // 附加資料
				}
			});
		},
		getProductInfo: async function (bankProCode, pfcatCode, callback) {
			let self = this;

			let productInfoRet = await self.$api.getProductInfoApi({
				proCode: bankProCode,
				pfcatCode: pfcatCode
			});

			if (_.isNil(productInfoRet.data)) {
				productInfoRet.data = {};
				biModule.alert('資料不存在');
				return;
			}
			if (_.isNil(productInfoRet.data.etfInfo)) {
				productInfoRet.data.etfInfo = {};
			}

			self.proInfo = productInfoRet.data;
			self.proCode = bankProCode;
			self.pfcatCode = pfcatCode;

			let selectYnRet = await self.$api.getAdmCodeDetail({ codeType: 'SELECT_YN' });
			self.selectYnList = selectYnRet.data;
			if (!_.isUndefined(self.proInfo.etfInfo.buyYn)) {
				let buyYnObjs = _.filter(self.selectYnList, {
					codeValue: self.proInfo.etfInfo.buyYn
				});
				self.proInfo.etfInfo.buyYn = buyYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.buyYn)) {
				let buyYnObjs = _.filter(self.selectYnList, {
					codeValue: self.proInfo.buyYn
				});
				self.proInfo.buyYn = buyYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.sellYn)) {
				let sellYnObjs = _.filter(self.selectYnList, {
					codeValue: self.proInfo.sellYn
				});
				self.proInfo.sellYn = sellYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.profInvestorYn)) {
				let profInvestorYnObjs = _.filter(self.selectYnList, {
					codeValue: self.proInfo.profInvestorYn
				});
				self.proInfo.profInvestorYn = profInvestorYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.targetCusBu)) {
				let targetCusBuList = [];
				let cusBuRet = await self.$api.getAdmCodeDetail({ codeType: 'CUS_BU' });
				targetCusBuList = cusBuRet.data;
				let targetCusBuObjs = _.filter(targetCusBuList, {
					codeValue: self.proInfo.targetCusBu
				});
				self.proInfo.targetCusBu = targetCusBuObjs[0]?.codeValue;
				self.proInfo.targetCusBuName = targetCusBuObjs[0]?.codeName;
			}

			// 波動類型
			let volatilityTypeRet = await self.$api.getAdmCodeDetail({ codeType: 'VOLATILITY_TYPE' });
			self.volatilityTypeList = volatilityTypeRet.data;
			let volatilityTypeObjs = _.filter(self.volatilityTypeList, {
				codeValue: self.proInfo.volatilityType
			});
			// 區分編輯與(檢視、審核)
			if ((volatilityTypeObjs.length > 0) & (self.actionType === 'EDIT')) {
				self.proInfo.volatilityType = volatilityTypeObjs[0].codeValue;
			} else {
				self.proInfo.volatilityTypeName = volatilityTypeObjs[0]?.codeName;
			}

			// 配息頻率
			if (!_.isUndefined(self.proInfo.intFreqUnitype)) {
				let intFreqYnitTypeRet = await self.$api.getAdmCodeDetail({ codeType: 'INT_FREQ_UNITTYPE' });
				self.intFreqUnitypeList = intFreqYnitTypeRet.data;
				let intFreqUnitypeObjs = _.filter(self.intFreqUnitypeList, {
					codeValue: self.proInfo.intFreqUnitype
				});
				if (self.actionType === 'EDIT') {
					self.proInfo.intFreqUnitype = intFreqUnitypeObjs[0].codeValue;
				} else {
					self.proInfo.intFreqUnitypeName = intFreqUnitypeObjs[0].codeName;
				}
			}

			// 保本要求
			let guarYnRet = await self.$api.getAdmCodeDetail({ codeType: 'GUAR_YN' });
			self.principalGuarMenu = guarYnRet.data;
			let principalGuarYnObjs = _.filter(self.principalGuarMenu, {
				codeValue: self.proInfo.principalGuarYn
			});
			// 區分編輯與(檢視、審核)
			if (self.actionType === 'EDIT') {
				self.proInfo.principalGuarYn = principalGuarYnObjs[0].codeValue;
			} else {
				self.proInfo.principalGuarYn = principalGuarYnObjs[0].codeName;
			}

			// 理財需求
			if (!_.isUndefined(productInfoRet.data.finReqCode)) {
				self.finReqCodes = productInfoRet.data.finReqCode.split(',');
			}

			if (!_.isUndefined(self.proInfo.selprocatNames)) {
				let selprocatNames = self.proInfo.selprocatNames.replaceAll(',', '、');
				self.proInfo.selprocatNames = selprocatNames;
			}

			callback && callback();
		},
		getProductCommInfo: async function (bankProCode, pfcatCode) {
			let self = this;
			// 商品附加資料
			let ret = await self.$api.getProductsCommInfo({
				proCode: bankProCode,
				pfcatCode: pfcatCode
			});
			if (!_.isNil(ret.data)) {
				if (ret.data.proDocs) {
					self.otherFileList = ret.data.proDocs; // 其他相關附件
					self.otherFileList.forEach(function (item) {
						// 其他相關附件 檔案顯示時間範圍
						item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
					});
				}

				self.proFileList = ret.data.proFiles;
				if (!_.isNil(self.proFileList)) {
					self.uploadFiles['B'] = self.proFileList.filter((proFile) => proFile.fileType === 'B')[0];
					self.uploadFiles['D'] = self.proFileList.filter((proFile) => proFile.fileType === 'D')[0];
					self.uploadFiles['E'] = self.proFileList.filter((proFile) => proFile.fileType === 'E')[0];
					self.uploadFiles['F'] = self.proFileList.filter((proFile) => proFile.fileType === 'F')[0];
					self.uploadFiles['G'] = self.proFileList.filter((proFile) => proFile.fileType === 'G')[0];
					self.url['B'] = self.uploadFiles['B'] ? self.uploadFiles['B'].url : null;
					self.url['D'] = self.uploadFiles['D'] ? self.uploadFiles['D'].url : null;
					self.url['E'] = self.uploadFiles['E'] ? self.uploadFiles['E'].url : null;
					self.url['F'] = self.uploadFiles['F'] ? self.uploadFiles['F'].url : null;
					self.url['G'] = self.uploadFiles['G'] ? self.uploadFiles['G'].url : null;
				}
			}
		},
		updateProduct: async function () {
			let self = this;

			self.commInfo.finReqCode = self.finReqCodes.join(',');

			Object.keys(self.url).forEach((key) => {
				// 檢查是否只有輸入url 沒有上傳檔案的type
				if (self.url[key] !== null) {
					// 有輸入url
					let typeInclude = self.proFileList.some((obj) => obj.fileType === key); // 找出是否有存在fileList
					if (!typeInclude) {
						let proFile = {};
						proFile.fileType = key;
						self.proFileList.push(proFile);
					}
				}
			});

			self.proFileList.forEach((e) => {
				e.createDt = null; // 移除日期避免轉型錯誤
				e.url = self.url[e.fileType];
			});
			self.commInfo = self.proInfo;
			self.commInfo.proFiles = self.proFileList;
			let formData = new FormData();
			// json model
			formData.append('model', JSON.stringify(self.commInfo));

			// upload file
			for (const key in self.uploadFiles) {
				let item = self.uploadFiles[key];
				if (item) {
					formData.append('files', item);
				}
			}

			let ret = await self.$api.patchProductApi(formData);
			biModule.alert('提交審核成功。');
			self.gotoPage(0);
			self.resetModalVaule();
		},
		resetModalVaule: function () {
			let self = this;
			$('[type="file"]').val(null);
			self.url = [];
			self.proFile = [];
			self.uploadFile = {};
			self.uploadFiles = [];
		},
		doViewProLog: function (proCode, pfcatCode, eventId) {
			//審核資料
			let self = this;
			self.resetModalVaule();
			if (proCode) {
				const callback = () => {
					self.getProductCommLogInfo(eventId);
				};

				self.getProductInfo(proCode, pfcatCode, callback);
			}
		},
		getProductCommLogInfo: async function (eventId) {
			let self = this;
			let ret = await self.$api.getProductLogApi({ eventId: eventId });

			if (!_.isNil(ret.data)) {
				// 取得維護資料帶入
				// 共同資料
				// 波動類型
				if (!_.isUndefined(ret.data.volatilityType)) {
					let volatilityTypeObjs = _.filter(self.volatilityTypeList, {
						codeValue: ret.data.volatilityType
					});
					self.proInfo.volatilityTypeName = volatilityTypeObjs[0].codeName;
				}
				// 配息頻率
				if (!_.isUndefined(ret.data.intFreqUnitype)) {
					let intFreqUnitTypeRet = await self.$api.getAdmCodeDetail({ codeType: 'INT_FREQ_UNITTYPE' });
					self.intFreqUnitypeList = intFreqUnitTypeRet.data;
					let intFreqUnitypeObjs = _.filter(self.intFreqUnitypeList, {
						codeValue: ret.data.intFreqUnitype
					});
					self.proInfo.intFreqUnitypeName = intFreqUnitypeObjs[0].codeName;
				}
				// 保本要求
				if (!_.isUndefined(ret.data.principalGuarYn)) {
					let guarYnRet = await self.$api.getAdmCodeDetail({ codeType: 'GUAR_YN' });
					self.principalGuarMenu = guarYnRet.data;
					let principalGuarYnObjs = _.filter(self.principalGuarMenu, {
						codeValue: ret.data.principalGuarYn
					});
					self.proInfo.principalGuarYn = principalGuarYnObjs[0].codeName;
				}
				//附加資料
				self.proInfo.memo = ret.data.memo; // 備註

				if (ret.data.proDocs) {
					self.otherFileList = ret.data.proDocs; // 其他相關附件
					self.otherFileList.forEach(function (item) {
						// 其他相關附件 檔案顯示時間範圍
						item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
					});
				}

				let proFileList = ret.data.proFiles;
				if (!_.isNil(proFileList)) {
					self.uploadFiles['B'] = proFileList.filter((proFile) => proFile.fileType === 'B')[0];
					self.uploadFiles['D'] = proFileList.filter((proFile) => proFile.fileType === 'D')[0];
					self.uploadFiles['E'] = proFileList.filter((proFile) => proFile.fileType === 'E')[0];
					self.uploadFiles['F'] = proFileList.filter((proFile) => proFile.fileType === 'F')[0];
					self.uploadFiles['G'] = proFileList.filter((proFile) => proFile.fileType === 'G')[0];
				}
			}
		},
		triggerFile: function (event, fileType) {
			let self = this;
			self.uploadFile[fileType] = event.target.files[0];
			self.uploadFile[fileType].showName = event.target.files[0].name;
		},
		doUploadFile: function (fileType) {
			let self = this;
			if (self.uploadFile[fileType]) {
				if (self.uploadFile[fileType].size > 10485760) {
					biModule.alert('檔案大小不得超過10MB！');
					return;
				}
				let proFile = {};

				proFile.fileName = self.uploadFile[fileType].name;
				proFile.showName = self.uploadFile[fileType].name;

				proFile.contentType = self.uploadFile[fileType].type;
				proFile.fileSize = self.uploadFile[fileType].size;
				proFile.fileType = fileType;

				if (!self.proFileList || self.proFileList.length <= 0) {
					self.proFileList.push(proFile);
				} else {
					// 有資料先刪除就檔案再新增
					self.proFileList.forEach((e, index) => {
						if (e.fileType === fileType) {
							self.proFileList.splice(index, 1);
						}
					});
					self.proFileList.push(proFile);
				}
				self.uploadFiles[fileType] = null; // 先將原先檔案清除
				self.uploadFiles[fileType] = self.uploadFile[fileType];

				// TODO: remove jQuery
				$('#etfUploadFile' + fileType).val(''); // 清空上傳區域檔案
				self.commInfo.isUpdateProFiles = true;
				self.uploadFile[fileType] = null;
			}
		},
		deleteFiles: function (fileType, proFileId) {
			let self = this;
			if (self.proFileList.length > 0) {
				self.proFileList.forEach((e, index, arr) => {
					if (e.proFileId === proFileId) {
						arr.splice(index, 1);
					}
				});
			}
			self.uploadFiles[fileType] = null;
		}
	} // methods end
};
</script>
