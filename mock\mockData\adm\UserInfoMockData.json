{"status": 200, "data": {"branCode": "897", "branName": "電子金融部    ", "buCode": "Z", "userCode": "014605", "userName": "何OO", "startFlag": "Y", "createDt": "2025/02/06"}, "timestamp": "2025/04/17", "sqlTracer": [{"data": {"branCode": "897", "branName": "電子金融部    ", "buCode": "Z", "userCode": "014605", "userName": "何OO", "startFlag": "Y", "createDt": "2025/02/06"}, "sqlInfo": " SELECT AU.*, AB.BRAN_NAME  FROM ADM_USERS AU  LEFT JOIN ADM_BRANCHES AB ON AU.BRAN_CODE = AB.BRAN_CODE AND AB.BU_CODE= AU.BU_CODE  WHERE AU.REMOVE_YN = 'N'  AND AU.USER_CODE = :userCode ,class com.bi.pbs.cus.web.model.UsersResp,{userCode=014605}"}, {"data": 0, "sqlInfo": " SELECT COUNT(*) COUNT  FROM ADM_USER_POS_EVENTS  WHERE USER_CODE= :userCode AND STATUS = 'P' ,{userCode=014605}"}]}