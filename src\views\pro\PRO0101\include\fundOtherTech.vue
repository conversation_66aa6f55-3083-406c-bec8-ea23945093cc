<template>
	<div>
		<p class="font-md bold m-t-15">
			基金指標
			<select class="select" v-model="selectedTech">
				<option :value="0">年化標準差</option>
				<option :value="1">年化夏普值</option>
				<option :value="2">年化Alpha</option>
				<option :value="3">Beta值</option>
				<option :value="4">年化資訊比率</option>
			</select>
		</p>
		<div class="row">
			<div class="col-md-12 col-lg-6">
				<!-- 長條圖 -->
				<vue-fund-column-chart
					:chart-id="techsOtherChartId"
					:prop-chart-data="techsOtherChartData"
					:prop-selected-tech="techsOtherMenu[selectedTech]"
				></vue-fund-column-chart>
				<div class="text-center">指數因計算準則只可顯示於年化標準差</div>
			</div>
			<div class="col-md-12 col-lg-6">
				<div class="table-responsive">
					<table width="100%" class="table table-condensed text-right">
						<thead>
							<tr>
								<th width="30%">&nbsp;</th>
								<th>{{ $filters.defaultValue(fundInfo && fundInfo.fundEnName, '--') }}</th>
								<th>{{ $filters.defaultValue(bmName, '--') }}</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td class="text-center">1年</td>
								<td class="text-right">
									<p
										v-html="
											$filters.defaultValue(
												$filters.formatFlucWithView(
													getTech(list[selectedTech][0].statCode),
													_.includes([0, 2], selectedTech) ? '%' : ''
												),
												'--'
											)
										"
									></p>
								</td>
								<td class="text-right">
									<p
										v-html="
											$filters.defaultValue(
												$filters.formatFlucWithView(
													getBmTech(list[selectedTech][0].statCode),
													_.includes([0, 2], selectedTech) ? '%' : ''
												),
												'--'
											)
										"
									></p>
								</td>
							</tr>
							<tr>
								<td class="text-center">3年</td>
								<td class="text-right">
									<p
										v-html="
											$filters.defaultValue(
												$filters.formatFlucWithView(
													getTech(list[selectedTech][1].statCode),
													_.includes([0, 2], selectedTech) ? '%' : ''
												),
												'--'
											)
										"
									></p>
								</td>
								<td class="text-right">
									<p
										v-html="
											$filters.defaultValue(
												$filters.formatFlucWithView(
													getBmTech(list[selectedTech][1].statCode),
													_.includes([0, 2], selectedTech) ? '%' : ''
												),
												'--'
											)
										"
									></p>
								</td>
							</tr>
							<tr>
								<td class="text-center">5年</td>
								<td class="text-right">
									<p
										v-html="
											$filters.defaultValue(
												$filters.formatFlucWithView(
													getTech(list[selectedTech][2].statCode),
													_.includes([0, 2], selectedTech) ? '%' : ''
												),
												'--'
											)
										"
									></p>
								</td>
								<td class="text-right">
									<p
										v-html="
											$filters.defaultValue(
												$filters.formatFlucWithView(
													getBmTech(list[selectedTech][2].statCode),
													_.includes([0, 2], selectedTech) ? '%' : ''
												),
												'--'
											)
										"
									></p>
								</td>
							</tr>
							<tr>
								<td class="text-center">10年</td>
								<td class="text-right">
									<p
										v-html="
											$filters.defaultValue(
												$filters.formatFlucWithView(
													getTech(list[selectedTech][3].statCode),
													_.includes([0, 2], selectedTech) ? '%' : ''
												),
												'--'
											)
										"
									></p>
								</td>
								<td class="text-right">
									<p
										v-html="
											$filters.defaultValue(
												$filters.formatFlucWithView(
													getBmTech(list[selectedTech][3].statCode),
													_.includes([0, 2], selectedTech) ? '%' : ''
												),
												'--'
											)
										"
									></p>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
export default {
	props: {
		fundInfo: Object,
		techs: Array,
		bmTechs: Array,
		bmName: String
	},
	data: function () {
		return {
			selectedTech: 0,
			list: [
				[
					{
						name: '1年',
						statCode: 'ASTD1Y'
					},
					{
						name: '3年',
						statCode: 'ASTD3Y'
					},
					{
						name: '5年',
						statCode: 'ASTD5Y'
					},
					{
						name: '10年',
						statCode: 'ASTD10Y'
					}
				],
				[
					{
						name: '1年',
						statCode: 'ASHP1Y'
					},
					{
						name: '3年',
						statCode: 'ASHP3Y'
					},
					{
						name: '5年',
						statCode: 'ASHP5Y'
					},
					{
						name: '10年',
						statCode: 'ASHP10Y'
					}
				],
				[
					{
						name: '1年',
						statCode: 'AALP1Y'
					},
					{
						name: '3年',
						statCode: 'AALP3Y'
					},
					{
						name: '5年',
						statCode: 'AALP5Y'
					},
					{
						name: '10年',
						statCode: 'AALP10Y'
					}
				],
				[
					{
						name: '1年',
						statCode: 'BET1Y'
					},
					{
						name: '3年',
						statCode: 'BET3Y'
					},
					{
						name: '5年',
						statCode: 'BET5Y'
					},
					{
						name: '10年',
						statCode: 'BET10Y'
					}
				],
				[
					{
						name: '1年',
						statCode: 'AINF1Y'
					},
					{
						name: '3年',
						statCode: 'AINF3Y'
					},
					{
						name: '5年',
						statCode: 'AINF5Y'
					},
					{
						name: '10年',
						statCode: 'AINF10Y'
					}
				]
			],
			techsOtherChartId: 'techsOtherChartId'
		};
	},
	watch: {},
	computed: {
		techsOtherMenu: function () {
			var self = this;
			return [
				{
					name: '年化標準差',
					valueList: [
						{ name: '基金', value: 'astd' },
						{ name: '對應指數' + self.bmName, value: 'astdBm' }
					],
					tooltipText: '{categoryX}:{valueY}%'
				},
				{
					name: '年化夏普值',
					valueList: [
						{ name: '基金', value: 'ashp' },
						{ name: '對應指數' + self.bmName, value: 'ashpBm' }
					],
					tooltipText: '{categoryX}:{valueY}'
				},
				{
					name: '年化Alpha',
					valueList: [
						{ name: '基金', value: 'aalp' },
						{ name: '對應指數' + self.bmName, value: 'aalpBm' }
					],
					tooltipText: '{categoryX}:{valueY}%'
				},
				{
					name: 'Beta值',
					valueList: [
						{ name: '基金', value: 'bet' },
						{ name: '對應指數' + self.bmName, value: 'betBm' }
					],
					tooltipText: '{categoryX}:{valueY}'
				},
				{
					name: '年化資訊比率',
					valueList: [
						{ name: '基金', value: 'ainf' },
						{ name: '對應指數' + self.bmName, value: 'ainfBm' }
					],
					tooltipText: '{categoryX}:{valueY}'
				}
			];
		},
		techsOtherChartData: function () {
			return [
				{
					year: '1年',
					astd: this.getTech('ASTD1Y'),
					astdBm: this.getBmTech('ASTD1Y'),
					ashp: this.getTech('ASHP1Y'),
					ashpBm: this.getBmTech('ASHP1Y'),
					aalp: this.getTech('AALP1Y'),
					bet: this.getTech('BET1Y'),
					betBm: this.getBmTech('BET1Y'),
					ainf: this.getTech('AINF1Y'),
					aalpBm: this.getBmTech('AINF1Y')
				},
				{
					year: '3年',
					astd: this.getTech('ASTD3Y'),
					astdBm: this.getBmTech('ASTD3Y'),
					ashp: this.getTech('ASHP3Y'),
					ashpBm: this.getBmTech('ASHP3Y'),
					aalp: this.getTech('AALP3Y'),
					bet: this.getTech('BET3Y'),
					betBm: this.getBmTech('BET3Y'),
					ainf: this.getTech('AINF3Y'),
					aalpBm: this.getBmTech('AINF3Y')
				},
				{
					year: '5年',
					astd: this.getTech('ASTD5Y'),
					astdBm: this.getBmTech('ASTD5Y'),
					ashp: this.getTech('ASHP5Y'),
					ashpBm: this.getBmTech('ASHP5Y'),
					aalp: this.getTech('AALP5Y'),
					bet: this.getTech('BET5Y'),
					betBm: this.getBmTech('BET5Y'),
					ainf: this.getTech('AINF5Y'),
					aalpBm: this.getBmTech('AINF5Y')
				},
				{
					year: '10年',
					astd: this.getTech('ASTD10Y'),
					astdBm: this.getBmTech('ASTD10Y'),
					ashp: this.getTech('ASHP10Y'),
					ashpBm: this.getBmTech('ASHP10Y'),
					aalp: this.getTech('AALP10Y'),
					bet: this.getTech('BET10Y'),
					betBm: this.getBmTech('BET10Y'),
					ainf: this.getTech('AINF10Y'),
					aalpBm: this.getBmTech('AINF10Y')
				}
			];
		}
	},
	methods: {
		getTech: function (statCode) {
			var tech = _.find(this.techs, { statCode: statCode });
			return tech ? tech.dvalue : null;
		},
		getBmTech: function (statCode) {
			var tech = _.find(this.bmTechs, { statCode: statCode });
			return tech ? tech.dvalue : null;
		}
	}
};
</script>
