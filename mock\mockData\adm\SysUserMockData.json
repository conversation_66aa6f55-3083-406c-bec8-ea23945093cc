{"status": 200, "data": {"content": [{"branCode": "897", "branName": "電子金融部    ", "userCode": "014605", "userName": "何OO", "titleName": "副理", "jobName": "行銷企劃", "userMapId": "UMI20250417001", "createDt": "2025-04-17T15:09:40.38", "createBy": "112790", "createUserName": "簡OO", "status": "P", "eventId": "EVN20250417000006"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "076430", "userName": "許OO", "titleName": "三等專員", "jobName": "業務規劃"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "085731", "userName": "<PERSON>", "titleName": "三等專員", "jobName": "行銷企劃"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "088394", "userName": "陳OO", "titleName": "領組", "jobName": "企業網銀"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "088868", "userName": "張OO", "titleName": "三等襄理", "jobName": "行動支付"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "089773", "userName": "郭OO", "titleName": "經理", "jobName": "業務規劃"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "090409", "userName": "顏OO", "titleName": "二等襄理", "jobName": "行銷企劃"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "091895", "userName": "黃OO", "titleName": "高辦", "jobName": "業務規劃"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "093457", "userName": "楊OO", "titleName": "高辦", "jobName": "電子銀行"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "109062", "userName": "楊OO", "titleName": "三等襄理", "jobName": "企業網銀"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "114400", "userName": "<PERSON>", "titleName": "中辦", "jobName": "行動網銀"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "115267", "userName": "簡OO", "titleName": "副理", "jobName": "電子銀行"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "116213", "userName": "劉OO", "titleName": "中辦", "jobName": "行動網銀"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "118428", "userName": "彭OO", "titleName": "高辦", "jobName": "企業網銀"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "118590", "userName": "謝OO", "titleName": "高辦", "jobName": "電子銀行"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "119560", "userName": "陳OO", "titleName": "領組", "jobName": "跨業行銷"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "124269", "userName": "蔡OO", "titleName": "三等專員", "jobName": "行銷企劃"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "125129", "userName": "<PERSON>OO", "titleName": "二等專員", "jobName": "跨業行銷"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "125363", "userName": "<PERSON>OO", "titleName": "初辦", "jobName": "業務規劃"}, {"branCode": "896", "branName": "法令遵循處    ", "userCode": "125703", "userName": "潘OO", "titleName": "初辦", "jobName": "洗錢防制人員"}], "pageable": {"pageNumber": 0, "pageSize": 20, "sort": {"empty": false, "sorted": true, "unsorted": false}, "offset": 0, "paged": true, "unpaged": false}, "last": false, "totalElements": 2408, "totalPages": 121, "first": true, "size": 20, "number": 0, "sort": {"empty": false, "sorted": true, "unsorted": false}, "numberOfElements": 20, "empty": false}, "timestamp": "2025/04/17", "sqlTracer": [{"data": {"content": [{"branCode": "897", "branName": "電子金融部    ", "userCode": "014605", "userName": "何OO", "titleName": "副理", "jobName": "行銷企劃", "userMapId": "UMI20250417001", "createDt": "2025-04-17T15:09:40.38", "createBy": "112790", "createUserName": "簡OO", "status": "P", "eventId": "EVN20250417000006"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "076430", "userName": "許OO", "titleName": "三等專員", "jobName": "業務規劃"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "085731", "userName": "<PERSON>", "titleName": "三等專員", "jobName": "行銷企劃"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "088394", "userName": "陳OO", "titleName": "領組", "jobName": "企業網銀"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "088868", "userName": "張OO", "titleName": "三等襄理", "jobName": "行動支付"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "089773", "userName": "郭OO", "titleName": "經理", "jobName": "業務規劃"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "090409", "userName": "顏OO", "titleName": "二等襄理", "jobName": "行銷企劃"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "091895", "userName": "黃OO", "titleName": "高辦", "jobName": "業務規劃"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "093457", "userName": "楊OO", "titleName": "高辦", "jobName": "電子銀行"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "109062", "userName": "楊OO", "titleName": "三等襄理", "jobName": "企業網銀"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "114400", "userName": "<PERSON>", "titleName": "中辦", "jobName": "行動網銀"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "115267", "userName": "簡OO", "titleName": "副理", "jobName": "電子銀行"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "116213", "userName": "劉OO", "titleName": "中辦", "jobName": "行動網銀"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "118428", "userName": "彭OO", "titleName": "高辦", "jobName": "企業網銀"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "118590", "userName": "謝OO", "titleName": "高辦", "jobName": "電子銀行"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "119560", "userName": "陳OO", "titleName": "領組", "jobName": "跨業行銷"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "124269", "userName": "蔡OO", "titleName": "三等專員", "jobName": "行銷企劃"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "125129", "userName": "<PERSON>OO", "titleName": "二等專員", "jobName": "跨業行銷"}, {"branCode": "897", "branName": "電子金融部    ", "userCode": "125363", "userName": "<PERSON>OO", "titleName": "初辦", "jobName": "業務規劃"}, {"branCode": "896", "branName": "法令遵循處    ", "userCode": "125703", "userName": "潘OO", "titleName": "初辦", "jobName": "洗錢防制人員"}], "pageable": {"pageNumber": 0, "pageSize": 20, "sort": {"empty": false, "sorted": true, "unsorted": false}, "offset": 0, "paged": true, "unpaged": false}, "last": false, "totalElements": 2408, "totalPages": 121, "first": true, "size": 20, "number": 0, "sort": {"empty": false, "sorted": true, "unsorted": false}, "numberOfElements": 20, "empty": false}, "sqlInfo": " SELECT TOP 100 PERCENT AU.BRAN_CODE, AB.BRAN_NAME, AU.USER_CODE, AU.USER_NAME  , AU.TITLE_NAME,AU.JOB_NAME , AUPE.CREATE_DT, AU1.USER_NAME CREATE_USER_NAME , AUPE.MODIFY_DT, AU2.USER_NAME MODIFY_USER_NAME  , C.STEP_CONTENT  ,AUPE.STATUS, AUPE.USER_MAP_ID, AUPE.CREATE_BY, AUPE.MODIFY_BY, AUPE.EVENT_ID  FROM ADM_USERS AU  LEFT JOIN (SELECT DISTINCT AUPE1.USER_CODE, AUPE1.CREATE_DT, AUPE1.CREATE_BY, AUPE1.MODIFY_DT, AUPE1.MODIFY_BY, AUPE1.STATUS, AUPE1.USER_MAP_ID, AUPE1.EVENT_ID  FROM ADM_USER_POS_EVENTS AUPE1  INNER JOIN (SELECT USER_CODE, MAX(CREATE_DT) CREATE_DT  FROM ADM_USER_POS_EVENTS  GROUP BY USER_CODE  ) AUPE2 ON (AUPE2.CREATE_DT = AUPE1.CREATE_DT AND AUPE2.USER_CODE = AUPE1.USER_CODE)  ) AUPE ON AU.USER_CODE = AUPE.USER_CODE  LEFT JOIN ADM_USERS AU1 ON AUPE.CREATE_BY = AU1.USER_CODE  LEFT JOIN ADM_USERS AU2 ON AU.MODIFY_BY = AU2.USER_CODE  LEFT JOIN ADM_BRANCHES AB ON AU.BRAN_CODE = AB.BRAN_CODE AND AU.BU_CODE = AB.BU_CODE  LEFT JOIN WKF_EVENTS B ON AUPE.EVENT_ID = B.EVENT_ID  LEFT JOIN WKF_EVENT_HISTORY C ON B.WKF_HISTORY_ID = C.WKF_HISTORY_ID  WHERE AU.REMOVE_YN = 'N' ,Page request [number: 0, size 20, sort: BRAN_CODE: DESC],class com.bi.pbs.adm.web.model.SysUserResp,{branCode=, roleCode=, userCode=}"}]}