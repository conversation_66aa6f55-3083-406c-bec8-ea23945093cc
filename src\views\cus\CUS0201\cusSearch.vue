<template>
	<div>
		<div class="row gx-2">
			<div class="d-block d-lg-none mb-3 d-flex justify-content-end" v-show="!showSearchResult">
				<a class="btn btn-lg btn-dark tx-16 tx-bold wd-200 d-flex justify-content-between" type="button" @click="redirect()"
					><span><img src="/src/assets/images/icon/ico-multi.png" />綜合條件客戶查詢</span> <i class="bi bi-shuffle"></i
				></a>
			</div>

			<div class="col-lg-8" v-show="!showSearchResult">
				<vue-cus-single-search title="基本條件查詢" :goto-page="gotoPage" :query-req="queryReq" ref="cusSingleSearch"></vue-cus-single-search>
				<vue-cus-spec-search title="特殊條件查詢" :goto-page="gotoPage" :query-req="queryReq" ref="cusSpecSearch"></vue-cus-spec-search>
			</div>
			<!-- 歷史查詢結果名單 -->
			<vue-cus-search-history
				title="歷史查詢結果名單"
				ref="searchHistoryPage"
				:goto-page="gotoPage"
				:query-req="queryReq"
				v-show="!showSearchResult"
			></vue-cus-search-history>

			<!-- 查詢結果 -->
			<div v-show="showSearchResult">
				<vue-complex-search-result
					title="查詢結果"
					ref="searchPage"
					:query-req="queryReq"
					:set-is-show-search-bar="setShowSearchResult"
					:get-search-history="reCusSearchHistory"
					:set-is-show-title="setIsShowTitle"
				>
				</vue-complex-search-result>
			</div>
		</div>
		<!--頁面內容 end-->
	</div>
</template>
<script>
import vueModal from '@/views/components/model.vue';
import vuePagination from '@/views/components/pagination.vue';
import vueCusSingleSearch from './include/cusSingleSearch.vue';
import vueComplexSearchResult from '../CUS0202/include/complexSearchResult.vue';
import vueCusSpecSearch from './include/cusSpecSearch.vue';
import vueCusSearchHistory from './include/cusSearchHistory.vue';
export default {
	components: {
		vueModal,
		vuePagination,
		vueCusSingleSearch,
		vueComplexSearchResult,
		vueCusSpecSearch,
		vueCusSearchHistory
	},
	data: function () {
		return {
			//API 用參數
			isShowSummary: false,
			resultName: null,
			selectedCusCodes: [],
			groupCode: null,

			//子組件用條件物件
			queryReq: { queryType: null },

			//畫面邏輯判斷用參數
			isShowTitle: true,
			showSearchResult: false,
			showCalResult: false,
			allSelected: false,
			showCusSingleSearchPage: false,
			customFields: [],
			hasCustomFields: false,
			customFieldValues: [],

			//下拉選單
			groupCodeMenu: [],

			//畫面顯示用參數
			calResult: {
				aum: 0,
				trustAum: 0,
				sBal: 0,
				sBalFc: 0,
				fMonth: 0,
				fYtd: 0,
				fLy: 0
			}
		};
	},
	filters: {},
	beforeMount: function () {},
	created: function () {},
	computed: {
		isShowPageTitle: function () {
			return this.isShowTitle;
		},
		//首頁查詢條件
		cusName: function () {
			return this.$route.params?.cusName || null;
		},
		cusCode: function () {
			return this.$route.params?.cusCode || null;
		},
		isHomePageQuery: function () {
			return this.$route.params?.isHomePageQuery || null;
		}
	},
	mounted: function () {
		var self = this;
		self.checkHomePageQuery();
	},
	methods: {
		receiveMessage(val) {
			this.isShowSummary = val;
		},
		checkHomePageQuery: function () {
			var self = this;
			if (self.isHomePageQuery == 'Y') {
				self.queryReq.cusName = self.cusName;
				self.queryReq.cusCode = self.cusCode;
				self.queryReq.queryType = 'SINGLE';
				// 清除URL參數
				history.replaceState({}, document.title, window.location.pathname);
				self.gotoPage(0);
			}
		},
		gotoPage: function (page) {
			this.$refs.searchPage.gotoPage(page);
		},
		reCusSearchHistory: function () {
			this.$refs.searchHistoryPage.getSearchHistory();
		},
		setShowSearchResult: function (val) {
			var self = this;
			self.showSearchResult = val;
			window.scrollTo(0, 0);
		},
		redirect: function () {
			var self = this;
			location.href = self.config.contextPath + '/cus/cusSearch1';
		},
		setIsShowTitle(val) {
			this.isShowTitle = val;
		}
	}
};
</script>
