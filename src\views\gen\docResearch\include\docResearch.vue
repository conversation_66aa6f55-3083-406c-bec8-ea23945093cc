<template>
	<div class="tab-content">
		<div class="tab-pane fade active show">
			<div class="card-header">
				<ul class="nav nav-pills card-header-pills">
					<li v-for="tab in subTabs" @click="selectedSubTabCode = tab.typeCode">
						<a href="javascript:void(0);" class="nav-link" :class="{ active: selectedSubTabCode == tab.typeCode }"
							>{{ tab.typeName }}({{ tab.cnt || 0 }})</a
						>
					</li>
				</ul>
			</div>
			<div class="card card-table mb-3">
				<div class="card-header">
					<h4>文件列表</h4>
					<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
				</div>
				<div class="table-responsive">
					<table class="table table-RWD table-hover table-bordered table-padding">
						<thead>
							<tr>
								<th width="40%">文件標題</th>
								<th width="10%">生效日</th>
								<th width="10%">到期日</th>
								<th width="12%">可否提供給客戶</th>
								<th width="9%">緊急程度</th>
								<th width="9%">檢視</th>
								<th width="10%">相關附件</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="data in pageData.content">
								<td data-th="文件標題">{{ data.docName }}</td>
								<td data-th="生效日">{{ $filters.formatDate(data.validDt) }}</td>
								<td data-th="到期日">{{ $filters.formatDate(data.expireDt) }}</td>
								<td data-th="可否提供給客戶">
									{{ data.showCusYnName === 'Y' ? '可提供給客戶' : '僅供內部使用' }}
								</td>
								<td data-th="緊急程度">{{ data.priorityName }}</td>
								<td data-th="執行" class="text-center">
									<button type="button" class="btn btn-dark btn-icon" @click="viewSelDoc(data.docId)" title="檢視">
										<i class="bi bi-search"></i>
									</button>
								</td>
								<td data-th="相關附件" class="text-center">
									<a v-for="file in data.fileList" href="#" @click="viewFile(file.docFileId)">{{ file.showName }}</a>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>

	<!-- Modal -->
	<vue-modal :is-open="isOpenModal" :before-close="closeModal">
		<template v-slot:content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">文件檢視</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()"><i class="bi bi-arrows-fullscreen"></i></button>
						<button type="button" class="btn-close" @click.prevent="props.close()" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="caption">文件分類</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p">文件類型</th>
									<td>{{ selDoc.docCatName }}</td>
								</tr>
							</tbody>
						</table>
						<div class="caption">文件內容</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p">文件標題</th>
									<td>{{ selDoc.docName }}</td>
								</tr>
								<tr>
									<th>生效日</th>
									<td>{{ $filters.formatDate(selDoc.validDt) }}</td>
								</tr>
								<tr>
									<th>到期日</th>
									<td>{{ $filters.formatDate(selDoc.expireDt) }}</td>
								</tr>
								<tr>
									<th>緊急程度</th>
									<td>{{ selDoc.priorityName }}</td>
								</tr>
								<tr>
									<th>可否提供給客戶</th>
									<td>{{ selDoc.showCusName }}</td>
								</tr>
								<tr>
									<th>摘要</th>
									<td>{{ selDoc.docDesc }}</td>
								</tr>
								<tr>
									<th>附加文檔</th>
									<td>
										<a v-for="file in selDoc.fileInfo" href="#" @click="viewFile(fileId)" class="link-underline">{{
											file.showName
										}}</a
										><br />
									</td>
								</tr>
							</tbody>
						</table>
						<div class="caption">維護資訊</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p">建立人員</th>
									<td>{{ selDoc.createBy }}</td>
								</tr>
								<tr>
									<th>建立日期</th>
									<td>{{ $filters.formatDate(selDoc.createDt) }}</td>
								</tr>
								<tr>
									<th>最後維護人員</th>
									<td>{{ selDoc.modifyBy }}</td>
								</tr>
								<tr>
									<th>最後維護日期</th>
									<td>{{ $filters.formatDate(selDoc.modifyDt) }}</td>
								</tr>
							</tbody>
						</table>
					</div>

					<div class="modal-footer" id="appointmentFooter">
						<input
							name="btnClose"
							class="btn btn-white"
							id="appointmentCloseButton"
							type="button"
							value="關閉"
							@click.prevent="props.close()"
						/>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
	<!-- Modal End -->
</template>
<script>
import vueModal from '@/views/components/model.vue';
import vuePagination from '@/views/components/pagination.vue';
import _ from 'lodash';
export default {
	components: {
		vueModal,
		vuePagination
	},
	props: {
		selectedTypeCode: {
			type: String,
			default: ''
		}
	},
	data: function () {
		return {
			subTabs: [],
			pageData: {
				content: {}
			},
			selDoc: {},
			selectedSubTabCode: null,
			pageable: {
				page: 0,
				size: 10,
				sort: 'DOC_ID',
				direction: 'ASC'
			},
			//Modal
			isOpenModal: null,
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',
			buttonClass: 'btn-expand'
		};
	},
	watch: {
		selectedTypeCode: {
			immediate: true,
			handler: function () {
				var self = this;
				self.getSubTabs();
			}
		},
		selectedSubTabCode(newVal) {
			if (newVal) {
				this.gotoPage(0);
			}
		}
	},
	computed: {
		userInfo: function () {
			return this.$store.getters['userInfo/info'];
		}
	},
	methods: {
		close: function () {
			this.isOpenModal = false;
		},
		async getSubTabs() {
			var self = this;
			const ret = await self.$api.getDocOutLookSubCntApi({
				mainTypeCode
			});
			if (!ret.data?.length > 0) return;
			self.subTabs = ret.data;
			self.selectedSubTabCode = self.subTabs[0]?.typeCode;
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (page) {
			var self = this;
			var url = _.toPageUrl('', page, self.pageable);
			const ret = await self.$api.getDocResearchPageData(
				{
					mainTypeCode: self.selectedTypeCode,
					subTypeCode: self.selectedSubTabCode
				},
				url
			);
			self.pageData = ret.data;
		},
		viewSelDoc: async function (docId) {
			var self = this;

			const ret = await self.$api.getViewSelDoc({
				docCat: 'MKTOUTLOOK',
				docId: docId
			});
			if (!ret.data?.length > 0) return;
			self.selDoc = ret.data[0];
			self.isOpenModal = true;
		},
		viewFile: async function (fileId) {
			var self = this;
			await self.$api.downloadGenOtherFileApi({
				fileId: fileId
			});
		},
		changeModalSize: function () {
			var self = this;
			if (self.modalClass === 'modal-dialog modal-lg modal-dialog-centered') {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered fullscreen';
				self.buttonClass = 'btn-expand mini';
			} else {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered';
				self.buttonClass = 'btn-expand';
			}
		},
		closeModal: function () {
			var self = this;
			self.isOpenModal = false;
		},
		openModal: function () {
			var self = this;
			self.isOpenModal = true;
		}
	}
};
</script>
