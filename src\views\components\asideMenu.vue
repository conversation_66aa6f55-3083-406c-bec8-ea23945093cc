<template>
	<aside class="aside aside-fixed">
		<div class="aside-body">
			<ul class="nav nav-aside nav-crm">
				<template v-for="m0 in menus">
					<li class="nav-label">{{ m0.name }}</li>
					<div>
						<li
							v-for="m1 in m0.nodes"
							class="nav-item"
							:id="m1.code"
							:class="{
								'with-sub': m1.nodes != null,
								show: selectedMenu[m1.code],
								active: isCurrentPath(m1.url)
							}"
							@click.prevent="
								toggleSubMenu(m1.code);
								changeUrl(m1.url);
							"
						>
							<a href="#" class="nav-link">
								<span>{{ m1.name }}</span>
							</a>
							<ul>
								<li v-for="m2 in m1.nodes" :class="{ active: isCurrentPath(m2.url) }">
									<a href="#" @click.prevent="changeUrl(m2.url)"
										><span>{{ m2.name }}</span></a
									>
								</li>
							</ul>
						</li>
					</div>
				</template>
			</ul>
		</div>
	</aside>
</template>

<script>
export default {
	data: function () {
		return {
			selectedMenu: {}
		};
	},
	mounted: async function () {
		await this.$store.dispatch('menus/getMenus');
	},
	computed: {
		menus: function () {
			return this.$store.getters['menus/menus'];
		},
		contentPath: function () {
			return this.$store.getters['menus/contentPath'];
		}
	},
	watch: {
		contentPath: function (newVal) {
			var self = this;
			self.expandCurrentMenu();
		}
	},
	methods: {
		expandCurrentMenu: function () {
			const findAndExpand = (nodes) => {
				if (!nodes) return false;
				for (let node of nodes) {
					if (this.isCurrentPath(node.url)) {
						let menuCode = this.formatMenuCode(node.code);
						this.showSubMenu(menuCode);
						this.scrollToMenu(menuCode);
						return true;
					}
					if (findAndExpand(node.nodes)) {
						return true;
					}
				}
				return false;
			};
			findAndExpand(this.menus);
		},
		formatMenuCode: function (menuCode) {
			return menuCode.includes('-') ? menuCode.split('-')[0] : menuCode;
		},
		toggleSubMenu: function (menuCode) {
			var self = this;
			self.selectedMenu[menuCode] = !self.selectedMenu[menuCode];
			this.hideOtherSubMenu(menuCode);
		},
		hideOtherSubMenu: function (menuCode) {
			var self = this;
			for (var key in self.selectedMenu) {
				if (key != menuCode) {
					self.selectedMenu[key] = false;
				}
			}
		},
		showSubMenu: function (menuCode) {
			var self = this;
			self.selectedMenu[menuCode] = true;
		},
		// 比較傳入的 Api 路徑, 是否與當前網址列路徑相符合
		isCurrentPath: function (targetPath) {
			var self = this;
			if (this.$_.isBlank(self.contentPath)) {
				return false;
			}
			return self.contentPath == targetPath;
		},
		scrollToMenu: function (menuCode) {
			const targetElement = document.getElementById(menuCode);
			if (targetElement) {
				// const offsetTop = targetElement.offsetTop - 100;
				const offsetTop = 0;
				window.scrollTo({
					top: offsetTop,
					behavior: 'smooth'
				});
			}
		},
		changeUrl: function (url) {
			if (!url) return;
			let routerUrl = url;
			const splitUrl = url.split('/');
			const module = splitUrl[1];
			const page = splitUrl[2];
			// 判斷審核頁面，加入wfgId
			if (module == 'wkf') {
				switch (page) {
					case 'admRole':
						routerUrl += '/WFG20121005008';
						break;
					case 'userAccount':
						routerUrl += '/WFG20141112001';
						break;
					case 'mktCamp':
						routerUrl += '/WFG20241118001';
						break;
					case 'proMgt':
						routerUrl += '/WFG20241203001';
						break;
					case 'proNew':
						routerUrl += '/WFG20241203002';
						break;
					case 'proPfcats':
						routerUrl += '/WFG20241203003';
						break;
				}
			} else if (module == 'gen' && page == 'bbsMgtRev') {
				routerUrl += '/WFG20170411001';
			}
			this.$router.push(routerUrl);
			this.$emit('clickSide');
		}
	}
};
</script>
