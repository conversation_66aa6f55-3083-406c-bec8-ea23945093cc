import WFG20141112001_VarItemsMockData from './mockData/wkf/WFG20141112001_VarItemsMockData.json';
import WFG20121005008_VarItemsMockData from './mockData/wkf/WFG20121005008_VarItemsMockData.json';
import WFG20141112001_EventsMockData from './mockData/wkf/WFG20141112001_EventsMockData.json';
import WFG20121005008_EventsMockData from './mockData/wkf/WFG20121005008_EventsMockData.json';
import AuditMockData from './mockData/wkf/AuditMockData.json';
import UserAccountDetailMockData from './mockData/wkf/UserAccountDetailMockData.json';

// wkfProcessor.vue
export function getVarItemsApi(wfgId) {
	switch (wfgId) {
		case 'WFG20141112001':
			return WFG20141112001_VarItemsMockData;
		case 'WFG20121005008':
			return WFG20121005008_VarItemsMockData;
		default:
			console.warn(`沒有 mock 資料對應流程: ${wfgId}`);
			return [];
	}
}

export function getWkfEventsApi(wfgId) {
	switch (wfgId) {
		case 'WFG20141112001':
			return WFG20141112001_EventsMockData;
		case 'WFG20121005008':
			return WFG20121005008_EventsMockData;
		default:
			console.warn(`沒有 mock 資料對應流程: ${wfgId}`);
			return [];
	}
}

export function patchAuditApi() {
	return AuditMockData;
}

// userAccountDetail.vue
export function getUserAccountDetailApi() {
	return UserAccountDetailMockData;
}

// admRoleReviewDetail.vue
// getDetail() 呼叫了與 ADM0101/include/admRoleReviewDetail.vue 的 getDetailApi 一樣的Api
