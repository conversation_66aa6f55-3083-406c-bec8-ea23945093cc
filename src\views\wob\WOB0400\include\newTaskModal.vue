<template>
	<div>
		<!-- newTaskModal 新增 -->
		<vue-modal :is-open="isOpenModal" :before-close="isOpenModal = false">
			<template v-slot:content="props">
				<div class="modal-dialog modal-xl modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">新增工作項目</h4>
							<button type="button" class="btn-expand"><i class="bi bi-arrows-fullscreen"></i></button>
							<button type="button" class="btn-close" @click.prevent="props.close()" aria-label="Close"></button>
						</div>
						<div class="modal-body bg-white p-3">
							<!-- 切換新增工作項目類型-->
							<div class="tab-nav-main">
								<ul class="nav nav-pills">
									<li class="nav-item" v-if="!showSection || showSection == 1">
										<a href="#" class="nav-link" :class="{ active: sectionCode == 1 }"
											@click.prevent="changeSession(1)">個人記事</a>
									</li>
									<!-- <li class="nav-item" v-if="!showSection || showSection == 2"> -->
									<li class="nav-item" v-if="true">
										<a href="#" class="nav-link" :class="{ active: sectionCode == 2 }"
											v-if="userInfo?.roleType === 'HQ'" @click.prevent="changeSession(2)">約訪行程</a>
									</li>
									<!-- <li class="nav-item" v-if="!showSection || showSection == 3"> -->
									<li class="nav-item" v-if="true">
										<a href="#" class="nav-link" :class="{ active: sectionCode == 3 }"
											v-if="userInfo?.roleType === 'HQ'" @click.prevent="changeSession(3)">聯繫紀錄</a>
									</li>
									<!-- <li class="nav-item" v-if="!showSection || showSection == 4"> -->
									<li class="nav-item" v-if="true">
										<a href="#" class="nav-link" :class="{ active: sectionCode == 4 }"
											v-if="userInfo?.roleType === 'HQ'" @click.prevent="changeSession(4)">客戶重要日子</a>
									</li>
								</ul>
							</div>

							<div class="tab-content">
								<div class="tab-pane fade show active" v-if="sectionCode !== 1">
									<table v-if="!propCusCode" class="biv-table table table-RWD table-horizontal-RWD">
										<tbody>
											<tr>
												<th>
													<label v-if="sectionCode == 2" class="form-label tx-require">約訪事件</label>
													<label v-if="sectionCode == 3" class="form-label tx-require">聯繫客戶</label>
													<label v-if="sectionCode == 4" class="form-label tx-require">客戶重要日子</label>
												</th>
												<td>
													<div class="input-group">
														<div class="input-group-text">客戶群組</div>
														<select name="groupCode" class="form-select" v-model="groupCode">
															<option selected disabled value="">請選擇</option>
															<option v-for="apptCusGroup in selectCusGroup" :value="apptCusGroup.groupCode">
																{{ apptCusGroup.groupName }}
															</option>
														</select>
														<div class="input-group-text">客戶</div>
														<select class="form-select" v-model="queryCusCode">
															<option selected disabled value="">請選擇</option>
															<option v-for="apptGroupCustomer in selectCusList" :value="apptGroupCustomer.cusCode">
																{{ apptGroupCustomer.cusName }} &nbsp; {{ apptGroupCustomer.idn }}
															</option>
														</select>
													</div>
													<div class="input-group">
														<div class="input-group-text">客戶ID/統編</div>
														<input class="form-control" type="text" v-model="queryIdn" />
														<button class="btn btn-primary btn-glow" type="button" @click="getCusInfo()">查詢</button>
													</div>
												</td>
											</tr>
										</tbody>
									</table>
									<div class="card-clientCard" v-if="isShowCusInfo">
										<div class="card shadow-none mb-3">
											<table>
												<tbody>
													<tr>
														<td width="10%" class="clientCard-icon">
															<div class="avatar avatar-male">
																<img :src="getImgURL('avatar', 'man-1.png')" class="rounded-circle bg-info" />
																<!-- <img th:src="@{/images/avatar/man-3.png}" class="rounded-circle bg-info" v-if="gender =='F'">-->
															</div>
															<h5 class="mb-0">{{ cusInfo.cusName || '--' }}</h5>
														</td>
														<td width="90%">
															<div class="caption tx-black">
																<span class=" ">最近通聯日期：{{ $filters.formatDate(cusInfo.lastConnectionDt) || '--' }}
																</span>
															</div>
															<div class="row">
																<div class="col-lg-4">
																	<ul class="list-unstyled profile-info-list">
																		<li>
																			<i class="bi bi-clipboard-data mg-xl-e-5-f"></i>投資屬性：{{
																				cusInfo.rankName || '--'
																			}}
																		</li>
																		<li>
																			<i class="bi bi-gift mg-xl-e-5-f"></i>生日：{{ cusInfo.birth || '--' }}
																		</li>
																		<li>
																			<i class="bi bi-envelope mg-xl-e-5-f"></i>電子郵件：{{
																				cusInfo.email || '--'
																			}}
																		</li>
																	</ul>
																</div>
																<div class="col-lg-3">
																	<ul class="list-unstyled profile-info-list">
																		<li>
																			<i class="bi bi-house-door mg-xl-e-5-f"></i>聯絡電話(住)：{{
																				cusInfo.phoneH || '--'
																			}}
																		</li>
																		<li>
																			<i class="bi bi-building mg-xl-e-5-f"></i>聯繫電話(公司)：{{
																				cusInfo.phoneO || '--'
																			}}
																		</li>
																		<li>
																			<i class="bi bi-telephone mg-xl-e-5-f"></i>聯絡電話(行動)：{{
																				cusInfo.phoneM || '--'
																			}}
																		</li>
																	</ul>
																</div>
																<div class="col-lg-5">
																	<ul class="list-unstyled profile-info-list">
																		<li>
																			<i class="bi bi-journal-medical mg-xl-e-5-f"></i>未成年辦理財富管理業務與投資商品同意書：
																			<span :class="cusInfo.childInvYn === 'Y' ? 'tx-red' : 'tx-green'">
																				{{ cusInfo.childInvYn === 'Y' ? '是' : '否' }}
																			</span>
																		</li>
																		<li>
																			<i class="bi bi-journal-text mg-xl-e-5-f"></i>特定金錢信託客戶投資有價證券推介同意書：
																			<span :class="cusInfo.specRecommYn === 'Y' ? 'tx-red' : 'tx-green'">
																				{{ cusInfo.specRecommYn === 'Y' ? '是' : '否' }}
																			</span>
																		</li>
																		<li>
																			<i class="bi bi-journal-bookmark mg-xl-e-5-f"></i>財富特定客戶(不得主動推介)：
																			<span :class="cusInfo.specCusYn === 'Y' ? 'tx-red' : 'tx-green'">
																				{{ cusInfo.specCusYn === 'Y' ? '是' : '否' }}
																			</span>
																		</li>
																	</ul>
																</div>
															</div>
														</td>
													</tr>
												</tbody>
											</table>
										</div>
									</div>
								</div>

								<!-- 個人記事 -->
								<div class="tab-pane fade show active" v-if="sectionCode == 1">
									<!--   <div class="card card-form shadow-none" v-if="cusInfo.cusCode">-->
									<div class="card card-form shadow-none">
										<div class="card-header">
											<h4>個人記事</h4>
											<span class="tx-square-bracket">為必填欄位</span>
										</div>
										<div class="card-content">
											<vue-form v-slot="{ errors, validate }" ref="tdPersonalRec">
												<table class="biv-table table table-RWD table-bordered">
													<tbody>
														<tr>
															<th>
																<label class="form-label tx-require">工作日期</label>
															</th>
															<td>
																<div class="input-group">
																	<vue-field type="date" name="nextRemindDt" class="form-control" v-model="nextRemindDt"
																		rules="required" :class="{ 'is-invalid': errors.nextRemindDt }"
																		label="工作日期"></vue-field>
																	<div>
																		<span class="text-danger" v-show="errors.nextRemindDt">{{
																			errors.nextRemindDt
																		}}</span>
																	</div>
																</div>
															</td>
															<th>
																<label class="form-label tx-require">工作時間</label>
															</th>
															<td>
																<div class="input-group">
																	<vue-field as="select" class="form-select" id="appoHour" name="appoHour"
																		v-model="appoHour" rules="required" :class="{ 'is-invalid': errors.appoHour }"
																		label="工作時間">
																		<option value="00">00</option>
																		<option value="01">01</option>
																		<option value="02">02</option>
																		<option value="03">03</option>
																		<option value="04">04</option>
																		<option value="05">05</option>
																		<option value="06">06</option>
																		<option value="07">07</option>
																		<option value="08">08</option>
																		<option value="09">09</option>
																		<option value="10">10</option>
																		<option value="11">11</option>
																		<option value="12">12</option>
																		<option value="13">13</option>
																		<option value="14">14</option>
																		<option value="15">15</option>
																		<option value="16">16</option>
																		<option value="17">17</option>
																		<option value="18">18</option>
																		<option value="19">19</option>
																		<option value="20">20</option>
																		<option value="21">21</option>
																		<option value="22">22</option>
																		<option value="23">23</option>
																	</vue-field>
																	<span class="input-group-text">時</span>
																	<div>
																		<span class="text-danger" v-show="errors.appoHour">{{
																			errors.appoHour
																		}}</span>
																	</div>
																	<vue-field as="select" class="form-select" id="appoMinute" name="appoMinute"
																		v-model="appoMinute" rules="required" :class="{ 'is-invalid': errors.appoMinute }"
																		label="工作時間">
																		<option selected="selected" value="00">00</option>
																		<option v-for="minute in selectMinutes" :value="minute">{{ minute }}</option>
																	</vue-field>
																	<span class="input-group-text">分</span>
																	<div>
																		<span class="text-danger" v-show="errors.appoMinute">{{
																			errors.appoMinute
																		}}</span>
																	</div>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">到期通知設定</label>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<div class="form-check form-check-inline">
																		<vue-field type="radio" name="apptAdvNceYn" class="form-check-input"
																			id="apptAdvNce_NN" value="N" v-model="advNce"
																			:class="{ 'is-invalid': errors.advNce }" rules="required"
																			label="到期通知設定"></vue-field>
																		<label class="form-check-label">不提前通知</label>
																	</div>
																	<div class="form-check form-check-inline">
																		<vue-field type="radio" name="apptAdvNceYn" class="form-check-input"
																			id="apptAdvNce_YY" value="Y" v-model="advNce"
																			:class="{ 'is-invalid': errors.advNce }" rules="required" label="到期通知設定">
																		</vue-field>
																		<label class="form-check-label">提前</label>
																	</div>

																	<vue-field class="form-control" name="advNceDay" type="text" size="30"
																		value="advNceDay" v-model="advNceDay" :class="{ 'is-invalid': errors.advNceDay }"
																		label="提前通知天/週數" :rules="advNce === 'Y' ? 'required' : ''">
																	</vue-field>
																	<div style="height: 3px">
																		<span class="text-danger" v-show="errors.advNceDay">{{
																			errors.advNceDay
																		}}</span>
																	</div>

																	<vue-field as="select" class="form-select" id="advNcePrd" name="advNcePrd"
																		v-model="advNcePrd" :class="{ 'is-invalid': errors.advNcePrd }"
																		:rules="advNcePrd === 'Y' ? 'required' : ''" label="提前通知週期">
																		<option value="D">日</option>
																		<option value="W">週</option>
																	</vue-field>
																	<div style="height: 3px">
																		<span class="text-danger" v-show="errors.advNcePrd">{{
																			errors.advNcePrd
																		}}</span>
																	</div>

																	<span class="input-group-text">通知</span>
																</div>
															</td>
														</tr>
														<tr>
															<th><label class="form-label tx-require">主旨</label></th>
															<td colspan="3">
																<div class="input-group">
																	<vue-field class="form-control" name="personalWorkTitle" type="text" size="30"
																		value="personalWorkTitle" v-model="personalWorkTitle"
																		:class="{ 'is-invalid': errors.personalWorkTitle }" label="主旨" rules="required">
																	</vue-field>
																	<div style="height: 3px">
																		<span class="text-danger" v-show="errors.personalWorkTitle">{{
																			errors.personalWorkTitle
																		}}</span>
																	</div>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">內容</label>
															</th>
															<td colspan="3">
																<vue-field as="textarea" class="form-control" id="content" name="content" rows="5"
																	cols="50" size="400" v-model="content" :class="{ 'is-invalid': errors.content }"
																	label="內容" rules="required">
																</vue-field>
																<div style="height: 3px">
																	<span class="text-danger" v-show="errors.content">{{ errors.content }}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<span class="form-label">常用句</span>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<select name="reuseWord" class="form-select" id="reuseWord" v-model="reuseWord">
																		<option v-for="selectWords in wobReuseWords" v-show="selectWords.words"
																			:value="selectWords.words">
																			{{ selectWords.words }}
																		</option>
																	</select>
																	<button type="button" class="btn btn-primary" id="setContent"
																		@click="appendReuseWord('content')">
																		加入
																	</button>
																	<input class="form-control" id="words" type="text" size="20" maxlength="20"
																		v-model="newReuseWord" />
																	<button type="button" class="btn btn-primary" id="wordAdd" @click="insertReuseWord()">
																		新增
																	</button>
																	<!--                                                          <button type="button" class="btn btn-primary" id="wordSetting" data-bs-toggle="modal" data-bs-target="#newTaskReuseWordModal" data-bs-dismiss="modal">設定</button>-->
																	<button type="button" class="btn btn-primary" id="wordSetting" @click.prevent="
																		openReuseWordModal();
																	props.close();
																	">
																		設定
																	</button>
																</div>
															</td>
														</tr>
													</tbody>
												</table>
											</vue-form>
										</div>
									</div>
								</div>

								<!-- 約訪紀錄 -->
								<div class="tab-pane fade show active" v-if="sectionCode == 2">
									<div class="card card-form shadow-none">
										<div class="card-header">
											<h4>約訪行程</h4>
											<span class="tx-square-bracket">為必填欄位</span>
										</div>
										<div class="card-content">
											<vue-form v-slot="{ errors, validate }" ref="appointmentTask">
												<table class="biv-table table table-RWD table-borderless">
													<tbody>
														<tr>
															<th>
																<label class="form-label tx-require">約訪日期</label>
															</th>
															<td>
																<div class="input-group">
																	<vue-field type="date" name="visitDate" class="form-control" v-model="visitDate"
																		:class="{ 'is-invalid': errors.visitDate }" label="約訪日期"
																		rules="required"></vue-field>
																	<div>
																		<span class="text-danger" v-show="errors.visitDate">{{
																			errors.visitDate
																		}}</span>
																	</div>
																</div>
															</td>
															<th>
																<label class="form-label tx-require">約訪時間</label>
															</th>
															<td>
																<div class="input-group">
																	<vue-field as="select" class="form-select" id="visitHour" name="visitHour"
																		v-model="visitHour" :class="{ 'is-invalid': errors.visitHour }" rules="required"
																		label="約訪時間">
																		<option value="00">00</option>
																		<option value="01">01</option>
																		<option value="02">02</option>
																		<option value="03">03</option>
																		<option value="04">04</option>
																		<option value="05">05</option>
																		<option value="06">06</option>
																		<option value="07">07</option>
																		<option value="08">08</option>
																		<option value="09">09</option>
																		<option value="10">10</option>
																		<option value="11">11</option>
																		<option value="12">12</option>
																		<option value="13">13</option>
																		<option value="14">14</option>
																		<option value="15">15</option>
																		<option value="16">16</option>
																		<option value="17">17</option>
																		<option value="18">18</option>
																		<option value="19">19</option>
																		<option value="20">20</option>
																		<option value="21">21</option>
																		<option value="22">22</option>
																		<option value="23">23</option>
																	</vue-field>
																	<span class="input-group-text">時</span>
																	<div>
																		<span class="text-danger" v-show="errors.visitHour">{{
																			errors.visitHour
																		}}</span>
																	</div>
																	<vue-field as="select" class="form-select" id="visitMin" name="visitMin"
																		v-model="visitMin" :class="{ 'is-invalid': errors.visitMin }" rules="required"
																		label="約訪時間">
																		<option selected="selected" value="00">00</option>
																		<option v-for="minute in selectMinutes" :value="minute">{{ minute }}</option>
																	</vue-field>
																	<span class="input-group-text">分</span>
																	<div>
																		<span class="text-danger" v-show="errors.visitMin">{{
																			errors.visitMin
																		}}</span>
																	</div>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">到期通知設定</label>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<div class="form-check form-check-inline">
																		<vue-field type="radio" name="apptAdvNceYn" class="form-check-input"
																			id="apptAdvNce_NN" value="N" v-model="apptAdvNce"
																			:class="{ 'is-invalid': errors.apptAdvNce }" rules="required"
																			label="到期通知設定"></vue-field>
																		<label class="form-check-label">不提前通知</label>
																	</div>
																	<div class="form-check form-check-inline">
																		<vue-field type="radio" name="apptAdvNceYn" class="form-check-input"
																			id="apptAdvNce_YY" value="Y" v-model="apptAdvNce"
																			:class="{ 'is-invalid': errors.apptAdvNce }" rules="required" label="到期通知設定">
																		</vue-field>
																		<label class="form-check-label">提前</label>
																	</div>
																	<vue-field class="form-control" name="apptAdvNceDay" type="text" size="30"
																		value="apptAdvNceDay" v-model="apptAdvNceDay"
																		:class="{ 'is-invalid': errors.apptAdvNceDay }" label="提前通知天/週數"
																		:rules="apptAdvNce === 'Y' ? 'required' : ''">
																	</vue-field>
																	<div style="height: 3px">
																		<span class="text-danger" v-show="errors.apptAdvNceDay">{{
																			errors.apptAdvNceDay
																		}}</span>
																	</div>
																	<vue-field as="select" class="form-select" id="apptAdvNcePrd" name="apptAdvNcePrd"
																		v-model="apptAdvNcePrd" :rules="apptAdvNce === 'Y' ? 'required' : ''"
																		:class="{ 'is-invalid': errors.apptAdvNcePrd }" label="到期通知設定">
																		<option value="D">日</option>
																		<option value="W">週</option>
																	</vue-field>
																	<span class="input-group-text">通知</span>
																	<div style="height: 3px">
																		<span class="text-danger" v-show="errors.apptAdvNce">{{
																			errors.apptAdvNce
																		}}</span>
																	</div>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">訪談目的</label>
															</th>
															<td>
																<vue-field as="select" class="form-select" id="apptVisitPurCode" name="apptVisitPurCode"
																	v-model="apptVisitPurCode" :class="{ 'is-invalid': errors.apptVisitPurCode }"
																	rules="required" label="訪談目的">
																	<option disabled selected value="">請選擇</option>
																	<option v-for="visitPurpose in visitPurMenu" :value="visitPurpose.codeValue">
																		{{ visitPurpose.codeName }}
																	</option>
																</vue-field>
																<div style="height: 3px">
																	<span class="text-danger" v-show="errors.apptVisitPurCode">{{
																		errors.apptVisitPurCode
																	}}</span>
																</div>
															</td>
															<th>
																<label class="form-label tx-require">訪談方式</label>
															</th>
															<td>
																<vue-field as="select" class="form-select" id="apptVisitAprCode" name="apptVisitAprCode"
																	v-model="apptVisitAprCode" :class="{ 'is-invalid': errors.apptVisitAprCode }"
																	rules="required" label="類型">
																	<option disabled selected value="">請選擇</option>
																	<option v-for="visitType in visitAprMenu" :value="visitType.codeValue">
																		{{ visitType.codeName }}
																	</option>
																</vue-field>
																<div style="height: 3px">
																	<span class="text-danger" v-show="errors.apptVisitAprCode">{{
																		errors.apptVisitAprCode
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">訪談主旨</label>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<vue-field class="form-control" name="apptTitle" type="text" size="30"
																		value="apptTitle" v-model="apptTitle" :class="{ 'is-invalid': errors.apptTitle }"
																		label="訪談主旨" rules="required"></vue-field>
																	<div style="height: 3px">
																		<span class="text-danger" v-show="errors.apptTitle">{{
																			errors.apptTitle
																		}}</span>
																	</div>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">訪談內容</label>
															</th>
															<td colspan="3">
																<vue-field as="textarea" class="form-control" id="visitContent" name="visitContent"
																	rows="5" cols="50" size="400" v-model="visitContent"
																	:class="{ 'is-invalid': errors.visitContent }" rules="required"
																	label="訪談內容"></vue-field>
																<div style="height: 3px">
																	<span class="text-danger" v-show="errors.visitContent">{{
																		errors.visitContent
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<span class="form-label">常用句</span>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<select name="reuseWord" class="form-select" id="reuseWord" v-model="reuseWord">
																		<option v-for="selectWords in wobReuseWords" v-show="selectWords.words"
																			:value="selectWords.words">
																			{{ selectWords.words }}
																		</option>
																	</select>
																	<button type="button" class="btn btn-primary" id="setContent"
																		@click="appendReuseWord('content')">
																		加入
																	</button>
																	<input class="form-control" id="words" type="text" size="20" maxlength="20"
																		v-model="newReuseWord" />
																	<button type="button" class="btn btn-primary" id="wordAdd" @click="insertReuseWord()">
																		新增
																	</button>
																	<button type="button" class="btn btn-primary" id="wordSetting" @click.prevent="
																		openReuseWordModal();
																	props.close();
																	">
																		設定
																	</button>
																</div>
															</td>
														</tr>
													</tbody>
												</table>
											</vue-form>
										</div>
									</div>
								</div>

								<!-- 聯繫紀錄 -->
								<div class="tab-pane fade show active" v-if="sectionCode == 3">
									<div class="card card-form shadow-none">
										<div class="card-header">
											<h4>聯繫紀錄</h4>
											<span class="tx-square-bracket">為必填欄位</span>
										</div>
										<div class="card-content">
											<vue-form v-slot="{ errors, validate }" ref="tdConnTask">
												<table class="biv-table table table-RWD table-borderless">
													<tbody>
														<tr>
															<th>
																<label class="form-label tx-require">聯繫日期</label>
															</th>
															<td>
																<div class="input-group">
																	<vue-field type="date" name="contDate" class="form-control" v-model="contDate"
																		:class="{ 'is-invalid': errors.contDate }" label="聯繫日期"
																		rules="required"></vue-field>
																	<div>
																		<span class="text-danger" v-show="errors.contDate">{{
																			errors.contDate
																		}}</span>
																	</div>
																</div>
															</td>
															<th>
																<label class="form-label">聯繫時間</label>
															</th>
															<td>
																<div class="input-group">
																	<vue-field as="select" class="form-select" id="contHour" name="contHour"
																		v-model="contHour" :class="{ 'is-invalid': errors.contHour }" rules="required"
																		label="聯繫時間">
																		<option value="00">00</option>
																		<option value="01">01</option>
																		<option value="02">02</option>
																		<option value="03">03</option>
																		<option value="04">04</option>
																		<option value="05">05</option>
																		<option value="06">06</option>
																		<option value="07">07</option>
																		<option value="08">08</option>
																		<option value="09">09</option>
																		<option value="10">10</option>
																		<option value="11">11</option>
																		<option value="12">12</option>
																		<option value="13">13</option>
																		<option value="14">14</option>
																		<option value="15">15</option>
																		<option value="16">16</option>
																		<option value="17">17</option>
																		<option value="18">18</option>
																		<option value="19">19</option>
																		<option value="20">20</option>
																		<option value="21">21</option>
																		<option value="22">22</option>
																		<option value="23">23</option>
																	</vue-field>
																	<span class="input-group-text">時</span>
																	<div>
																		<span class="text-danger" v-show="errors.contHour">{{
																			errors.contHour
																		}}</span>
																	</div>
																	<vue-field as="select" class="form-select" id="contMin" name="contMin"
																		v-model="contMin" :class="{ 'is-invalid': errors.contMin }" rules="required"
																		label="聯繫時間">
																		<option selected="selected" value="00">00</option>
																		<option v-for="minute in selectMinutes" :value="minute">{{ minute }}</option>
																	</vue-field>
																	<span class="input-group-text">分</span>
																	<div>
																		<span class="text-danger" v-show="errors.contMin">{{ errors.contMin }}</span>
																	</div>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">主旨</label>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<vue-field class="form-control" name="contTitle" type="text" size="30"
																		value="contTitle" v-model="contTitle" :class="{ 'is-invalid': errors.contTitle }"
																		label="主旨" rules="required"></vue-field>
																	<div style="height: 3px">
																		<span class="text-danger" v-show="errors.contTitle">{{
																			errors.contTitle
																		}}</span>
																	</div>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">處理方式</label>
															</th>
															<td colspan="3">
																<vue-field as="select" class="form-select" id="contVisitAprCode" name="contVisitAprCode"
																	v-model="contVisitAprCode" :class="{ 'is-invalid': errors.contVisitAprCode }"
																	rules="required" label="處理方式">
																	<option disabled selected value="">請選擇</option>
																	<option v-for="visitType in visitAprMenu" :value="visitType.codeValue">
																		{{ visitType.codeName }}
																	</option>
																</vue-field>
																<div style="height: 3px">
																	<span class="text-danger" v-show="errors.contVisitAprCode">{{
																		errors.contVisitAprCode
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">處理內容</label>
															</th>
															<td colspan="3">
																<vue-field as="textarea" class="form-control" id="contContent" name="contContent"
																	rows="5" cols="50" size="400" v-model="contContent"
																	:class="{ 'is-invalid': errors.contContent }" rules="required"
																	label="處理內容"></vue-field>
																<div style="height: 3px">
																	<span class="text-danger" v-show="errors.contContent">{{
																		errors.contContent
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">聯絡狀況</label>
															</th>
															<td colspan="3">
																<vue-field as="select" class="form-select" id="contStatCode" name="contStatCode"
																	v-model="contStatCode" :class="{ 'is-invalid': errors.contStatCode }" rules="required"
																	label="聯絡狀況">
																	<option disabled selected value="">請選擇</option>
																	<option v-for="contStat in contStatMenu" :value="contStat.codeValue">
																		{{ contStat.codeName }}
																	</option>
																</vue-field>
																<div style="height: 3px">
																	<span class="text-danger" v-show="errors.contStatCode">{{
																		errors.contStatCode
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">後續處理</label>
															</th>
															<td colspan="3">
																<vue-field as="select" class="form-select" id="contProcCode" name="contProcCode"
																	v-model="contProcCode" :class="{ 'is-invalid': errors.contProcCode }" rules="required"
																	label="後續處理">
																	<option disabled selected value="">請選擇</option>
																	<option v-for="contProc in contProcMenu" :value="contProc.codeValue">
																		{{ contProc.codeName }}
																	</option>
																</vue-field>
																<div style="height: 3px">
																	<span class="text-danger" v-show="errors.contProcCode">{{
																		errors.contProcCode
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">是否結案</label>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<div class="form-check form-check-inline">
																		<vue-field type="radio" name="doneYn" class="form-check-input" id="doneYn_YY"
																			value="Y" v-model="doneYn" :class="{ 'is-invalid': errors.doneYn }"
																			rules="required" label="是否結案">
																		</vue-field>
																		<label class="form-check-label">已結案</label>
																	</div>
																	<div class="form-check form-check-inline">
																		<vue-field type="radio" name="doneYn" class="form-check-input" id="doneYn_NN"
																			value="N" v-model="doneYn" :class="{ 'is-invalid': errors.doneYn }"
																			rules="required" label="是否結案">
																		</vue-field>
																		<label class="form-check-label">未結案</label>
																	</div>
																	<div style="height: 3px">
																		<span class="text-danger" v-show="errors.doneYn">{{ errors.doneYn }}</span>
																	</div>
																</div>
															</td>
														</tr>
													</tbody>
												</table>
											</vue-form>
										</div>
									</div>
								</div>

								<!-- 客戶重要日子 -->
								<div class="tab-pane fade show active" v-if="sectionCode == 4">
									<div class="card card-form shadow-none">
										<div class="card-header">
											<h4>客戶重要日子</h4>
											<span class="tx-square-bracket">為必填欄位</span>
										</div>
										<div class="card-content">
											<vue-form v-slot="{ errors, validate }" ref="memoryDateTask">
												<table class="biv-table table table-RWD table-bordered">
													<tbody>
														<tr>
															<th>
																<label class="form-label tx-require">重要日子</label>
															</th>
															<td>
																<div class="input-group">
																	<vue-field type="date" name="memoryDate" class="form-control" v-model="memoryDate"
																		:class="{ 'is-invalid': errors.memoryDate }" label="工作日期" rules="required">
																	</vue-field>
																	<div>
																		<span class="text-danger" v-show="errors.memoryDate">{{
																			errors.memoryDate
																		}}</span>
																	</div>
																</div>
															</td>
														</tr>

														<tr>
															<th><label class="form-label tx-require">內容</label></th>
															<td colspan="3">
																<vue-field as="textarea" class="form-control" id="memContent" name="memContent" rows="5"
																	cols="50" size="400" v-model="memContent" :class="{ 'is-invalid': errors.memContent }"
																	label="內容" rules="required">
																</vue-field>
																<div>
																	<span class="text-danger" v-show="errors.memContent">{{
																		errors.memContent
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">到期通知設定</label>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<div class="form-check form-check-inline">
																		<vue-field type="radio" name="memRemindYn" class="form-check-input"
																			id="memRemindYn_NN" value="N" v-model="memRemindYn"
																			:class="{ 'is-invalid': errors.memRemindYn }" rules="required"
																			label="到期通知設定"></vue-field>
																		<label class="form-check-label">不提前通知</label>
																	</div>
																	<div class="form-check form-check-inline">
																		<vue-field type="radio" name="memRemindYn" class="form-check-input"
																			id="memRemindYn_YY" value="Y" v-model="memRemindYn"
																			:class="{ 'is-invalid': errors.memRemindYn }" rules="required" label="到期通知設定">
																		</vue-field>
																		<label class="form-check-label">提前</label>
																	</div>
																	<vue-field class="form-control" name="memRemindDays" type="text" size="30"
																		value="memRemindDays" v-model="memRemindDays"
																		:rules="memRemindYn === 'Y' ? 'required' : ''"
																		:class="{ 'is-invalid': errors.memRemindDays }" label="提前通知天/週數" rules="required">
																	</vue-field>
																	<div style="height: 3px">
																		<span class="text-danger" v-show="errors.memRemindDays">{{
																			errors.memRemindDays
																		}}</span>
																	</div>
																	<vue-field as="select" class="form-select" id="memRemindPrd" name="memRemindPrd"
																		v-model="memRemindPrd" :rules="memRemindYn === 'Y' ? 'required' : ''"
																		:class="{ 'is-invalid': errors.memRemindPrd }" label="到期通知設定">
																		<option value="D">日</option>
																		<option value="W">週</option>
																	</vue-field>
																	<span class="input-group-text">通知</span>
																	<div style="height: 3px">
																		<span class="text-danger" v-show="errors.memRemindPrd">{{
																			errors.memRemindPrd
																		}}</span>
																	</div>
																</div>
															</td>
														</tr>
													</tbody>
												</table>
											</vue-form>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="modal-footer">
							<button @click.prevent="props.close()" type="button" class="btn btn-white">關閉</button>
							<input name="btnSave" class="btn btn-primary" id="btnSave" type="button" :value="'儲存'" @click.prevent="
								doInsertRec();
							props.close();
							" />
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<vue-modal :is-open="isOpenReuseWordModal" :before-close="isOpenReuseWordModal = false">
			<template v-slot:content="props">
				<vue-cus-reuse-word-modal :close="props.close" :id="'newTaskReuseWordModal'" :wob-reuse-words="wobReuseWords"
					:super-modal-name="'newTaskModal'"></vue-cus-reuse-word-modal>
			</template>
		</vue-modal>
	</div>
</template>

<script>
import { Field, Form } from 'vee-validate';
import moment from 'moment';
import vueModal from '@/views/components/model.vue';
import vueCusReuseWordModal from '@/views/cus/include/reuseWordModal.vue';
import _ from 'lodash';
import { getImgURL } from '@/utils/imgURL.js';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueModal,
		vueCusReuseWordModal
	},
	props: {
		showSection: {
			// 僅可點選帶入sectionCode的tab，若無帶入則顯示所有tab
			type: Number
		},
		propCusCode: String, // 帶入客戶cusCode
		getCalendarTasks: Function,
		getSelectReuseWord: Function
	},
	data: function () {
		var minutes = [];
		for (var i = 1; i < 60; i++) {
			minutes.push(String(i).padStart(2, '0'));
		}
		return {
			isShowCusInfo: false,
			isOpenModal: false,
			isOpenReuseWordModal: false,
			sectionCode: 1,
			loginRoleType: null, // 登入者的角色

			// 客戶群組選擇
			groupCode: '', // 客戶群組
			queryCusCode: '', // 客戶
			queryIdn: null, // 客戶ID/統編
			cusCode: null, // 最後選擇的客戶

			selectVisitPurpose: [],
			selectVisitType: [],
			selectMinutes: minutes,
			showReuseWordSetting: false,
			selectReuseWord: [], // 常用句選擇
			//Api用參數
			idn: null,

			// 個人記事
			nextRemindDt: null, // 工作日期
			appoHour: '00', // 工作時間(時)
			appoMinute: '00', // 工作時間(分)
			advNce: 'N', // 到期通知設定
			advNceDay: null, // 提前通知天/週數
			advNcePrd: 'D', // 提前通知週期
			personalWorkTitle: null, // 主旨
			content: null, // 內容

			// 客戶約訪
			visitDate: null, // 約訪日期
			visitHour: '00', // 約訪時間(時)
			visitMin: '00', // 約訪時間(分)
			apptAdvNce: 'N', // 到期通知設定
			apptAdvNceDay: null, // 提前通知天/週數
			apptAdvNcePrd: 'D', // 提前通知週期
			apptVisitPurCode: '', // 訪談目的
			apptVisitAprCode: '', // 訪談方式
			apptTitle: null, // 訪談主旨
			visitContent: null, // 訪談內容

			//聯繫紀錄
			contDate: null, // 聯繫日期
			contHour: '00', // 聯繫時間(時)
			contMin: '00', // 聯繫時間(分)
			contTitle: null, // 主旨
			contVisitAprCode: '', // 處理方式
			contContent: null, // 處理內容
			contStatCode: '', // 聯絡狀況
			contProcCode: '', // 後續處理
			doneYn: 'N', // 是否結案

			//客戶重要日子
			memoryDate: null, // 重要日子
			memContent: null, // 內容
			memRemindYn: 'N', // 到期通知設定
			memRemindDays: null, // 提前通知天數
			memRemindPrd: 'D', // 提前通知週期

			//常用聯繫內容
			reuseWord: null, // 常用句選擇
			newReuseWord: null, // 新增常用句
			wobReuseWords: [], // 常用句下拉選單

			//下拉選單
			selectCusGroup: null, // 客戶群組選單
			selectCusList: [], // 客戶選單

			visitPurMenu: [], // 訪談目的選單
			visitAprMenu: [], // 訪談方式/處理方式選單
			contStatMenu: [], // 聯絡狀況選單
			contProcMenu: [], // 後續處理選單
			//顯示用參數
			cusInfo: {},

			//檔案處裡
			tempFile: null,
			files: [],
			maxFileCount: 3,
			validExts: ['doc', 'docx', 'pdf', 'xlsx', 'txt'],
			symbols: ['/', ':', '*', '?', '"', '<', '>', '|'],
			modal: null
		};
	},
	watch: {
		userInfo: {
			immediate: true,
			handler: function (newVal, oldVal) {
				var self = this;
				if (newVal) {
					self.loginRoleType = newVal.roleType;
				}
			}
		},
		// 選擇客戶群組
		groupCode: function (val) {
			var self = this;
			self.queryCusCode = '';
			self.selectCusList = [];
			self.isShowCusInfo = false;
			if (self.groupCode) {
				self.getSelectCusList(val);
			}
		},
		// 選擇客戶
		queryCusCode: function (val) {
			var self = this;
			self.isShowCusInfo = false;
			if (val) {
				self.getCusInfo();
			}
		},
		// 以下為帶入單一客戶ID時使用
		showSection: {
			immediate: true,
			handler: function (newVal, oldVal) {
				var self = this;
				if (newVal) {
					self.changeSession(newVal);
				}
			}
		},
		propCusCode: {
			immediate: true,
			handler: function (newVal, oldVal) {
				var self = this;
				if (newVal) {
					self.getCusInfo();
				}
			}
		}
	},
	computed: {
		userInfo: function () {
			return this.$store.getters['userInfo/info'].data;
		}
	},
	mounted: function () {
		var self = this;
		self.getSelectCusGroup();
		self.getVisitPurMenu();
		self.getVisitAprMenu();
		self.getContStatMenu();
		self.getContProcMenu();
	},
	methods: {
		getImgURL,
		show: function (sectionCode) {
			if (!this.propCusCode) {
				this.resetVariables();
			}
			if (!this.getSelectReuseWord) {
				this.getSelectReuseWordSelf();
			}

			if (sectionCode) {
				this.sectionCode = sectionCode;
			}

			this.isOpenModal = true;
		},
		// 切換頁籤
		changeSession: function (val) {
			var self = this;
			self.sectionCode = val;
		},
		// 客戶群組下拉選單
		async getSelectCusGroup() {
			var self = this;
			const ret = await self.$api.getCusGroupMenuApi();
			self.selectCusGroup = ret.data;
		},
		// 客戶下拉選單
		getSelectCusList: async function (groupCode) {
			var self = this;
			const ret = await self.$api.postCusGroupDetail({
				groupCode: groupCode
			});
			if (ret.data.groupDetail) {
				self.selectCusList = ret.data.groupDetail;
			}
		},
		// 取得客戶資訊
		getCusInfo: async function () {
			var self = this;

			if (_.isBlank(self.queryCusCode) && _.isBlank(self.queryIdn) && _.isBlank(self.propCusCode)) {
				self.$bi.alert('請輸入顧客ID/統編。');
				return;
			}

			var queryData = {};
			if (!_.isBlank(self.queryCusCode)) {
				queryData = { cusCode: self.queryCusCode };
				self.queryIdn = null;
			} else {
				queryData = { cusCode: self.queryIdn };
				self.queryCusCode = null;
			}

			if (!_.isBlank(self.propCusCode)) {
				queryData.cusCode = self.propCusCode;
			}

			const resp = await self.$api.getCustomer(queryData);

			if (!resp.data.cusCode || _.isEmpty(resp.data)) {
				self.$bi.alert('查無此客戶。');
				return;
			} else {
				self.resetVariables('ignoreQueryFields');
				self.cusInfo = resp.data;
				self.isShowCusInfo = true;
				// $('#Section' + self.sectionCode).show();
			}
		},
		// 取得訪談目的選單
		getVisitPurMenu: async function () {
			var self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'VISIT_PUR_CODE'
			});
			self.visitPurMenu = ret.data;
		},
		// 取得訪談方式選單
		getVisitAprMenu: async function () {
			var self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'VISIT_APR_CODE'
			});
			self.visitAprMenu = ret.data;
		},
		// 取得聯絡狀況選單
		getContStatMenu: async function () {
			var self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'CONT_STAT_CODE'
			});
			self.contStatMenu = ret.data;
		},
		// 取得後續處理選單
		getContProcMenu: async function () {
			var self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'CONT_PROC_CODE'
			});
			self.contProcMenu = ret.data;
		},
		// 儲存按鈕
		doInsertRec: async function () {
			var self = this;
			if (self.sectionCode !== 1 && _.isBlank(self.cusInfo.cusCode)) {
				console.log('打印資訊=' + self.cusInfo.cusCode);
				self.$bi.alert('請先選擇客戶。');
				return;
			}
			switch (self.sectionCode) {
				case 1: // 個人記事
					self.insertPersonalTask();
					break;
				case 2: // 客戶約訪
					self.insertVisit();
					break;
				case 3: // 聯繫紀錄
					self.insertConnect();
					break;
				case 4: // 客戶重要日子
					self.insertMemory();
					break;
			}
		},
		// 新增工作項目-個人記事-儲存
		insertPersonalTask: async function () {
			var self = this;
			await self.$refs.tdPersonalRec.validate().then(async function (pass) {
				if (pass.valid) {
					var content = _.isNil(self.content) ? '' : self.content;
					var data = {
						nextRemindDt: self.nextRemindDt ? moment(self.nextRemindDt).format('YYYY-MM-DD') : null,
						nextRemindTime: self.appoHour + ':' + self.appoMinute,
						advNce: self.advNce,
						advNceDay: self.advNce === 'Y' ? self.advNceDay : null,
						advNcePrd: self.advNce === 'Y' ? self.advNcePrd : null,
						title: self.personalWorkTitle,
						content: content
					};

					const ret = await self.$api.postPersonalTaskApi(data);
					self.$bi.alert('新增成功');
					self.isOpenModal = +false;
					if (self.getCalendarTasks) {
						self.getCalendarTasks();
					}
				}
			});
		},
		// 新增工作項目-客戶約訪-儲存
		insertVisit: async function () {
			var self = this;

			await self.$refs.appointmentTask.validate().then(async function (pass) {
				if (pass.valid) {
					var content = _.isNil(self.visitContent) ? '' : self.visitContent;

					var data = {
						cusCode: self.cusInfo.cusCode,
						nextRemindDt: self.visitDate ? moment(self.visitDate).format('YYYY-MM-DD') : null,
						nextRemindTime: self.visitHour + ':' + self.visitMin,
						advNce: self.apptAdvNce,
						advNceDay: self.apptAdvNce === 'Y' ? self.apptAdvNceDay : null,
						advNcePrd: self.apptAdvNce === 'Y' ? self.apptAdvNcePrd : null,
						visitAprCode: self.apptVisitAprCode,
						visitPurCode: self.apptVisitPurCode,
						title: self.apptTitle,
						content: content
					};
					const ret = await self.$api.postVisit(data);
					self.$bi.alert('新增成功');
					self.isOpenModal = false;
					if (self.getCalendarTasks) {
						self.getCalendarTasks();
					}
				}
			});
		},
		// 新增工作項目-聯絡紀錄-儲存
		insertConnect: async function () {
			var self = this;
			await self.$refs.tdConnTask.validate().then(async function (pass) {
				if (pass.valid) {
					var content = _.isNil(self.contContent) ? '' : self.contContent;
					const ret = await self.$api.postConnect({
						cusCode: self.cusInfo.cusCode,
						nextRemindDt: self.contDate ? moment(self.contDate).format('YYYY-MM-DD') : null,
						nextRemindTime: self.contHour + ':' + self.contMin,
						title: self.contTitle,
						visitAprCode: self.contVisitAprCode,
						content: content,
						contStatCode: self.contStatCode,
						contProcCode: self.contProcCode,
						doneYn: self.doneYn
					});
					self.$bi.alert('新增成功');
					self.isOpenModal = false;
					if (self.getCalendarTasks) {
						self.getCalendarTasks();
					}
				}
			});
		},
		// 新增工作項目-客戶重要日子-儲存
		insertMemory: async function () {
			var self = this;
			self.$refs.memoryDateTask.validate().then(async function (pass) {
				if (pass.valid) {
					var content = _.isNil(self.memContent) ? '' : self.memContent;

					const ret = await self.$api.postMemoryDateApi({
						cusCode: self.cusInfo.cusCode,
						dateDt: self.memoryDate ? moment(self.memoryDate).format('YYYY-MM-DD') : null,
						note: content,
						remindYn: self.memRemindYn,
						remindDays: self.memRemindYn === 'Y' ? self.memRemindDays : null,
						remindPrd: self.memRemindYn === 'Y' ? self.memRemindPrd : null
					});
					self.$bi.alert('新增成功');
					self.isOpenModal = false;
					if (self.getCalendarTasks) {
						self.getCalendarTasks();
					}
				}
			});
		},
		// 常用句設定-儲存
		setDefaultReuseWords: function () {
			var self = this;
			self.wobReuseWords = [];
			for (var i = 0; i < 10; i++) {
				var tempWordObj = {
					wordsId: i + 1,
					words: null
				};
				self.wobReuseWords.push(tempWordObj);
			}
		},
		// 取得常用句選單
		getReuseWords: async function () {
			var self = this;
			const ret = await self.$api.getReuseWordsApi();
			ret.data.forEach(function (item) {
				var index = item.wordsId - 1;
				self.wobReuseWords[index].words = item.words;
			});
		},
		// 新增常用句
		insertReuseWord: async function (index) {
			var self = this;
			if (self.newReuseWord) {
				var wordsId = null;

				if (self.wobReuseWords.length < 10) {
					wordsId = self.wobReuseWords.length + 1;
				}

				if (_.isNil(wordsId)) {
					self.$bi.alert('常用句已超過設定個數，請由常用句設定頁面進行調整。');
					return;
				}

				const ret = await self.$api.postReuseWordsApi({
					wordsId: wordsId,
					words: self.newReuseWord
				});

				if (self.getSelectReuseWord) {
					self.getSelectReuseWord();
				} else {
					self.getSelectReuseWordSelf();
				}
			}
		},
		// 常用句加入內容
		appendReuseWord: function (targetName) {
			var self = this;

			if (self.reuseWord != null) {
				switch (targetName) {
					case 'content': // 個人記事-內容
						if (!self.content) {
							self.content = '';
						}
						self.content = self.content + self.reuseWord;
						break;
					case 'visitContent': // 客戶約訪-訪談內容
						if (!self.visitContent) {
							self.visitContent = '';
						}
						self.visitContent = self.visitContent + self.reuseWord;
						break;
				}
			}
		},
		// 關閉 model 彈窗 (reset variables)
		doClose: function () {
			var self = this;
			self.reuseWord = null;
			self.newReuseWord = null;
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		closeReuseWordModal: function () {
			this.isOpenReuseWordModal = false;
		},
		openReuseWordModal: function () {
			this.isOpenReuseWordModal = true;
		},
		// 重置變數
		resetVariables: function (ignoreType) {
			var self = this;

			if (ignoreType != 'ignoreQueryFields') {
				self.queryCusCode = '';
				self.queryIdn = null;
				self.groupCode = '';
			}

			self.cusInfo = {};

			self.showReuseWordSetting = false;

			//Api用參數
			self.idn = null;
			//個人工作
			self.nextRemindDt = null;
			self.appoHour = '00';
			self.appoMinute = '00';
			self.personalWorkTitle = null;
			self.advNce = 'N';
			self.advNceDay = null;
			self.advNcePrd = 'D';
			self.content = null;

			//約訪記錄
			self.visitDate = null;
			self.visitHour = '00';
			self.visitMin = '00';
			self.apptVisitPurCode = '';
			self.apptVisitAprCode = '';
			self.apptTitle = null;
			self.visitContent = '';
			self.apptAdvNce = 'N';
			self.apptAdvNceDay = null;
			self.apptAdvNcePrd = 'D';
			self.visitContent = null;

			//聯繫紀錄
			self.contTitle = null;
			self.contVisitAprCode = '';
			self.contDate = null;
			self.contHour = '00';
			self.contMin = '00';
			self.contContent = null;
			self.contStatCode = '';
			self.contProcCode = '';
			self.doneYn = 'N';

			//顧客重要日子
			self.memoryDate = null;
			self.memRemindYn = 'N';
			self.memRemindDays = 1;
			self.memRemindPrd = 'D';
			self.memContent = null;
			//檔案處裡
			self.tempFile = null;
			self.files = [];
		},
		// 顯示常用聯繫紀錄設定頁面
		setShowReuseWordSetting: function () {
			var self = this;
			self.showReuseWordSetting = !self.showReuseWordSetting;
		},
		generatorId: function (name) {
			return name + '-' + _.now() + _.random(0, 99);
		},
		// 常用聯繫內容設定
		getSelectReuseWordSelf: async function () {
			var self = this;
			const ret = await self.$api.getReuseWordsApi();
			var result = ret.data;
			var needAppend = 5 - result.length;
			if (result.length < 5) {
				for (var i = 0; i < needAppend; i++) {
					result.push({ words: '', wordsId: ret.data.length + i + 1 });
				}
			}
			self.selectReuseWord = result;
			self.wobReuseWords = result;
		}
	}
};
</script>

<style scoped>
.width100 {
	width: 100%;
}
</style>
