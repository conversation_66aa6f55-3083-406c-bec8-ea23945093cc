<template>
	<div class="clientDB">
		<div class="row g-3">
			<!-- 基本資料 -->
			<div class="col-lg-5 height-size">
				<div class="card card-table">
					<div class="card-header">
						<h4>基本資料</h4>
					</div>
					<div class="table-scroll ht-300">
						<table class="table">
							<thead class="thead-primary tx-spacing-2">
								<th colspan="2">
									客戶等級：{{ customer.idnEntityTypeName }}({{ customer.graName }}) / 最近聯繫：{{
										$filters.formatDate(customer.contactDate)
									}}
								</th>
							</thead>
							<tbody>
								<tr>
									<th width="30%">聯繫電話</th>
									<td>(公司) {{ customer.phoneO }} (手機) {{ customer.phoneM }} (住家) {{ customer.phoneH }}</td>
								</tr>
								<tr>
									<th>KYC 風險等級(有效日)</th>
									<td>{{ customer.rankName }} ({{ $filters.formatDate(customer.nextRankQueDt) }})</td>
								</tr>
								<tr>
									<th>專業投資人(到期日)</th>
									<td>{{ customer.piYn }} ({{ $filters.formatDate(customer.piDueDt) }})</td>
								</tr>
								<tr>
									<th>帳戶總資產 (AUA)</th>
									<td>{{ $filters.formatAmt(customer.mktAmtAua) }} (等值台幣)</td>
								</tr>
								<tr>
									<th>投資市值(AUM)</th>
									<td>{{ $filters.formatAmt(customer.mktAmtAum) }} (等值台幣)</td>
								</tr>
								<tr>
									<th>聯繫地址</th>
									<td>{{ customer.addC }}</td>
								</tr>
								<tr>
									<th>戶籍地址</th>
									<td>{{ customer.addH }}</td>
								</tr>
								<tr>
									<th>是否可行銷聯絡</th>
									<td>{{ customer.rejectPhoneYn }}</td>
								</tr>
								<tr>
									<th>最近交易日</th>
									<td>{{ $filters.formatDate(customer.transDate) }}</td>
								</tr>
								<tr>
									<th>經管理專/經管分行</th>
									<td>{{ customer.userName }}/{{ customer.branName }}</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>

			<!-- 工作事項 -->
			<div class="col-lg-7 height-size">
				<div class="card card-table card-tabs">
					<div class="card-header">
						<h4>工作事項</h4>
						<ul class="nav nav-pills card-header-pills" v-if="tdListsData && tdListsData.length > 0">
							<li>
								<a href="#tab-w1" class="nav-link active" data-bs-toggle="pill">近2月事件({{ tdListsData[0].totalCount }})</a>
							</li>
						</ul>
					</div>
					<div class="tab-content p-0">
						<div id="tab-w1" class="tab-pane fade show active">
							<div class="table-scroll ht-300">
								<table class="table table-striped table-hover table-RWD">
									<thead>
										<tr>
											<th>類別</th>
											<th>服務紀錄</th>
											<th>事件</th>
											<th>說明</th>
											<th class="text-center">到期日</th>
											<th class="text-center">狀態</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="item in tdListsData">
											<td data-th="類別">{{ item.tdcat1Name }}</td>
											<td data-th="服務紀錄" class="text-center">
												<a class="btn btn-action btn-icon">
													<i class="far fa-comments" data-bs-toggle="tooltip" title="????"></i>
												</a>
											</td>
											<td data-th="事件">{{ item.itemName }}</td>
											<td data-th="說明">{{ item.content }}</td>
											<td data-th="到期日" class="text-center">{{ $filters.formatDate(item.expireDt) }}</td>
											<td data-th="狀態" class="text-center">
												<span class="badge badge-danger wd-60">{{ item.statusName }} </span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="tab-w2" class="tab-pane fade">
							<div class="table-scroll ht-300">
								<table class="table table-striped table-hover table-RWD">
									<thead>
										<tr>
											<th width="15%">客戶</th>
											<th width="8%">起始日</th>
											<th width="8%">結束日</th>
											<th width="8%">類別</th>
											<th width="10%">說明</th>
											<th width="10%">狀態</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td data-th="客戶">
												<div class="input-group">
													<input name="cusIdentity" class="form-control" type="text" size="15" maxlength="20" value="" />
													<button type="button" class="btn btn-dark btn-icon"><i class="bi bi-search"></i></button>
												</div>
											</td>
											<td data-th="起始日">
												<input type="date" name="beginDate" value="" id="beginDate" class="form-control" />
											</td>
											<td data-th="結束日">
												<input type="date" name="beginDate" value="" id="endDate" class="form-control" />
											</td>
											<td data-th="類別">
												<select name="Genre" class="form-select">
													<option value="0">電訪</option>
													<option value="1">面訪</option>
													<option value="2">事件</option>
													<option value="3">商品</option>
													<option value="4">公司</option>
													<option value="5">陪訪</option>
												</select>
											</td>

											<td data-th="說明">
												<input name="cusIdentity" class="form-control" type="text" size="15" maxlength="20" value="" />
											</td>
											<td data-th="狀態">
												<select name="Status" class="form-select">
													<option value="0">未處理</option>
													<option value="1">聯繫不上</option>
													<option value="2">已完成</option>
												</select>
											</td>
										</tr>
									</tbody>
								</table>
								<div class="btn-end">
									<button class="btn btn-primary btn-search">確認</button>
								</div>
							</div>
						</div>

						<div id="tab-w3" class="tab-pane fade">
							<div class="table-scroll ht-300"></div>
						</div>
					</div>
				</div>
			</div>

			<!-- <div class="col-lg-4 height-size2">
					<div class="card card-table">
						<div class="card-header">
							<h4> 資產分布 </h4>
						</div>
						 <div class="tab-content">
							<div id="tab-Inv-1" class="tab-pane fade show active">
								<div class="row align-items-center">
									<div class="col-xl-12 text-center">
										<div id="All-container" class="chart-size"></div>
									</div>
								</div>
							</div>
							<div id="tab-Inv-2" class="tab-pane fade">
								<img src="../../../images/AllContPFReview.gif" class="chart-size" />
							</div>
						</div>
					</div>
				</div> -->

			<div class="col-lg-12">
				<!-- 資產現況 -->
				<div class="card card-table card-tabs">
					<div class="card-header">
						<h4>資產現況</h4>
						<ul class="nav nav-pills card-header-pills">
							<li>
								<a href="#tab-Inv1" class="nav-link active" data-bs-toggle="pill">投資商品配置 </a>
							</li>
							<!--<li>
									<a href="#tab-Inv-2" class="nav-link" data-bs-toggle="pill">投資組合配置</a>
								</li>-->
							<li>
								<a href="#tab-Inv3" class="nav-link" data-bs-toggle="pill">貸款配置</a>
							</li>
						</ul>
					</div>
					<div class="tab-content">
						<div id="tab-Inv1" class="tab-pane fade show active">
							<div class="row align-items-center">
								<div class="col-xl-4 text-center">
									<vue-client-overview-pie-chart
										class="dbchart-container"
										v-if="showFinanceChart"
										:chart-id="financeChartId"
										:prop-chart-data="financeChartData"
									></vue-client-overview-pie-chart>
								</div>
								<div class="col-xl-8">
									<div class="table-responsive">
										<table class="table table-striped table-RWD table-hover">
											<thead>
												<tr>
													<th width="18%">
														<span>商品類別</span>
													</th>
													<th width="18%" class="text-end">
														<span>配置比例</span>
													</th>
													<th width="23%" class="text-end">
														<span>原始投資金額</span>
													</th>
													<th width="23%" class="text-end">
														<span>市值</span>
													</th>
													<th width="18%" class="text-end">
														<span>報酬率</span>
													</th>
												</tr>
											</thead>
											<tbody v-if="assetLoansData && assetLoansData.cusAssetAmountMergeList">
												<tr v-for="item in assetLoansData.cusAssetAmountMergeList">
													<td data-th="商品類別">
														<template v-if="!hasAuth">{{ item.pfcatName }}</template>
														<a v-if="hasAuth" href="#" @click="changeTab('M20-022', item.pfcatCode)">{{
															item.pfcatName
														}}</a>
													</td>
													<td data-th="配置比例" class="text-end">{{ $filters.formatPct(item.alcRate) }}%</td>
													<td data-th="原始投資金額" class="text-end">{{ formatValue(item.sumInvAmtLc, 'I') }}</td>
													<td data-th="市值" class="text-end">{{ formatValue(item.sumMktAmtLc, 'I') }}</td>
													<td data-th="報酬率" class="text-end">{{ $filters.formatPct(item.rtnRate) }}%</td>
												</tr>
											</tbody>
											<tfoot
												v-if="
													assetLoansData &&
													assetLoansData.cusAssetAmountMergeList &&
													assetLoansData.cusAssetAmountMergeList.length > 0
												"
											>
												<tr class="tx-sum bg-total">
													<td data-th="商品類別">合計</td>
													<td data-th="配置比例" class="text-end">100.00%</td>
													<td data-th="原始投資金額" class="text-end">
														{{ formatValue(assetLoansData.sumInvAmtLc, 'I') }}
													</td>
													<td data-th="市值" class="text-end">{{ formatValue(assetLoansData.sumMktAmtLc, 'I') }}</td>
													<td data-th="報酬率" class="text-end">{{ $filters.formatPct(assetLoansData.rtnRate) }}%</td>
													<!--<td data-th="日變動" class="text-end">--</td>
														<td data-th="週變動" class="text-end">--</td>-->
												</tr>
											</tfoot>
										</table>
									</div>
								</div>
							</div>
						</div>
						<div id="tab-Inv3" class="tab-pane fade">
							<div class="row align-items-center">
								<div class="col-xl-4 text-center">
									<vue-client-overview-pie-chart
										class="dbchart-container"
										v-if="showLoanChart"
										:chart-id="loanChartId"
										:prop-chart-data="loanChartData"
									></vue-client-overview-pie-chart>
								</div>
								<div class="col-xl-8">
									<div class="table-responsive">
										<table class="table table-striped table-RWD table-hover">
											<thead>
												<tr>
													<th width="10%">
														<span>種類</span>
													</th>
													<th width="18%" class="text-end">
														<span>比例</span>
													</th>
													<th width="18%" class="text-end">
														<span>初貸金額(折台)</span>
													</th>
													<th width="18%" class="text-end">
														<span>初貸金額</span>
													</th>
													<th width="18%" class="text-end">
														<span>現貸餘額</span>
													</th>
													<th width="18%" class="text-end">
														<span>已還金額</span>
													</th>
												</tr>
											</thead>
											<tbody v-if="assetLoansData && assetLoansData.cusLoansMergeRespList">
												<tr v-for="item in assetLoansData.cusLoansMergeRespList">
													<td data-th="商品類別">
														<template v-if="!hasAuth">{{ item.curName }}</template>
														<a v-if="hasAuth" href="#" @click="changeTab('M20-022', 'LOAN')">{{ item.curName }}</a>
													</td>
													<td data-th="比例" class="text-end">{{ $filters.formatPct(item.alcRate) }}%</td>
													<td data-th="初貸金額(折台)" class="text-end">{{ formatValue(item.sumUmtLc, 'I') }}</td>
													<td data-th="初貸金額" class="text-end">{{ $filters.formatAmt(item.sumUmtFc) }}</td>
													<td data-th="現貸餘額" class="text-end">{{ $filters.formatAmt(item.sumBalFc) }}</td>
													<td data-th="已還金額" class="text-end">{{ $filters.formatAmt(item.sumUseBalFc) }}</td>
												</tr>
											</tbody>
											<tfoot
												v-if="
													assetLoansData &&
													assetLoansData.cusLoansMergeRespList &&
													assetLoansData.cusLoansMergeRespList.length > 0
												"
											>
												<tr class="tx-sum bg-total">
													<td data-th="商品類別">合計</td>
													<td data-th="配置比例" class="text-end">100.00%</td>
													<td data-th="初貸金額(折台)" class="text-end">{{ formatValue(assetLoansData.sumUmtLc, 'I') }}</td>
													<td data-th="初貸金額" class="text-end">{{ $filters.formatAmt(assetLoansData.sumUmtFc) }}</td>
													<td data-th="現貸餘額" class="text-end">{{ $filters.formatAmt(assetLoansData.sumBalFc) }}</td>
													<td data-th="已還金額" class="text-end">{{ $filters.formatAmt(assetLoansData.sumUseBalFc) }}</td>
												</tr>
											</tfoot>
										</table>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- 近12個月資產走勢 -->
				<div class="card card-table">
					<div class="card-header">
						<h4>近12個月資產走勢</h4>
					</div>
					<h5 align="right">資產餘額(新台幣/千元)/資料日期</h5>
					<div class="table-responsive">
						<!-- 近12個月資產走勢 圖表 -->
						<vue-asset-trend-chart ref="assetTrendChartRef" :chart-id="assetTrendId"></vue-asset-trend-chart>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import vueAssetTrendChart from './assetTrendChart.vue';
import vueClientOverviewPieChart from './clientOverviewPieChart.vue';
export default {
	components: {
		vueAssetTrendChart,
		vueClientOverviewPieChart
	},
	props: {
		cusCode: String,
		hasAuth: Boolean,
		customer: Object,
		setMenuCode: Function
	},
	data: function () {
		return {
			assetTrendId: 'assetTrendId', // 近12個月資產走勢圖id
			assetTrendData: [],
			financeChartId: 'financeChartId',
			loanChartId: 'loanChartId',
			financeChartData: [],
			loanChartData: [],
			tdListsData: [], // 工作項目
			assetLoansData: null, // 資產現況和貸款配置
			showFinanceChart: false,
			showLoanChart: false
		};
	},
	watch: {
		userInfo: {
			immediate: true,
			handler: function (newVal, oldVal) {
				var self = this;
				if (newVal) {
					if (newVal.deputyUserName) {
						self.userName = newVal.deputyUserName;
					} else {
						self.userName = newVal.userName;
					}
					self.userRoleName = newVal.roleName;
				}
			}
		}
	},
	mounted: function () {
		var self = this;
		// self.getFinanceChartData();
		// self.getLoanChartData();
		self.near12MonthassetTrend(); // 近12個月資產走勢資料
		self.getTdListsData(); // 工作項目
		self.getassetLoansData(); //
	},
	methods: {
		// DEMO CHART
		getFinanceChartData: function () {
			var self = this;
			self.financeChartData = [
				{ value: 28.21, category: '存款' },
				{ value: 6.7, category: '基金' },
				{ value: 17.07, category: 'ETF' },
				{ value: 6.21, category: '債券' },
				{ value: 9.51, category: 'SI' },
				{ value: 19.29, category: 'DCI' },
				{ value: 10.0, category: '貴金屬' },
				{ value: 3.01, category: '保險' }
			];
			self.showFinanceChart = true;
		},
		getLoanChartData: function () {
			var self = this;
			self.loanChartData = [
				{ value: 43.3, category: '台幣' },
				{ value: 36.7, category: '美元' },
				{ value: 20.0, category: '日圓' }
			];
			self.showLoanChart = true;
		},
		async near12MonthassetTrend() {
			//近12個月資產走勢資料
			var self = this;
			const ret = await self.$api.getAssetTrendApi({
				cusCode: self.cusCode
			});
			for (var i = 0; i < ret.data.length; i++) {
				ret.data[i].datas.forEach((e) => {
					const dataDtString = String(e.dataDt).padStart(6, '0');
					const year = parseInt(dataDtString.substring(0, 4), 10);
					const month = parseInt(dataDtString.substring(4, 6), 10) - 1; // 月份從0開始
					e.date = new Date(year, month, 1).getTime();
				});
			}
			self.assetTrendData = ret.data;
			self.$refs.assetTrendChartRef.initChart(ret.data);
		},
		async getTdListsData() {
			var self = this;
			const ret = await self.$api.getTdListsDataApi({
				cusCode: self.cusCode
			});
			self.tdListsData = ret.data;
		},
		async getassetLoansData() {
			var self = this;
			const ret = await self.$api.getAssetLoansDataApi({
				cusCode: self.cusCode
			});
			self.assetLoansData = ret.data;
			if (self.assetLoansData && self.assetLoansData.cusAssetAmountMergeList && self.assetLoansData.cusAssetAmountMergeList.length > 0) {
				self.assetLoansData.cusAssetAmountMergeList.forEach((e) => {
					self.financeChartData.push({
						value: e.alcRate,
						category: e.pfcatName
					});
				});
				self.showFinanceChart = true;
			}

			if (self.assetLoansData && self.assetLoansData.cusLoansMergeRespList && self.assetLoansData.cusLoansMergeRespList.length > 0) {
				self.assetLoansData.cusLoansMergeRespList.forEach((e) => {
					self.loanChartData.push({
						value: e.alcRate,
						category: e.curName
					});
				});
				self.showLoanChart = true;
			}
		},
		changeTab(menuCode, code) {
			let self = this;
			self.setMenuCode(menuCode, code);
		},
		formatValue(value, type) {
			switch (type) {
				case 'I': // 千分位整數格式
					return this.formatInteger(value);
				case 'D': // 日期格式
					return this.formatDate(value);
				case 'F': // 浮點數轉為千分位整數格式
					return this.formatFloatAsInteger(value);
				case 'S': // 字串格式
					return value;
				// 其他類型的格式化處理可以在這裡繼續擴展
				default:
					return value; // 預設返回原始值
			}
		},
		formatInteger(value) {
			const numericValue = Number(value);
			return isNaN(numericValue) ? 0 : numeral(numericValue).format('0,0');
		},
		formatDate(value) {
			const date = new Date(value);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`; // 手動格式化為 'YYYY-MM-DD'
		},
		formatFloatAsInteger(value) {
			const numericValue = Number(value);
			return isNaN(numericValue) ? 0 : parseFloat(numericValue).toFixed(0); // 或者其他浮點數處理方式
		}
	} // methods end}
};
</script>
