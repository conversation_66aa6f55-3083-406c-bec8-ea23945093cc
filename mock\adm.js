//ADM0110/programSet.vue
import FunctionMenuTreeMockData from './mockData/adm/FunctionMenuTreeMockData.json';
import RoleMenuMockData from './mockData/adm/RoleMenuMockData.json';
// ADM0101/admRole.vue
import SystemRolesMockData from './mockData/adm/SystemRolesMockData.json';
import MenuEnableTreeMockData from './mockData/adm/MenuEnableTreeMockData.json';
// ADM0101/include/admRoleAuthority.vue
import UpdateRolAuthorityMockData from './mockData/adm/UpdateRolAuthorityMockData.json';
import RoleReviewDetailMockData from './mockData/adm/RoleReviewDetailMockData.json';
// ADM0103/admMenuPreview.vue
import RoleMenuTreeMockData from './mockData/adm/RoleMenuTreeMockData.json';
// components/biTabs.vue
import UserAccountMenuTabMockData from './mockData/adm/UserAccountMenuTabMockData.json';
import GroupBranMenuTabMockData from './mockData/adm/GroupBranMenuTabMockData.json';
import FcStepMenuTabMockData from './mockData/adm/FcStepMenuTabMockData.json';
import ProductSearchMenuTabMockData from './mockData/pro/productSearchMenuTabMockData.json';
import GetLoggingMockData from './mockData/adm/GetLoggingMockData.json';
import BbsMgtMenuTabMockData from './mockData/gen/BbsMgtMenuTabMockData.json';
import BbsHeadMenuTabMockData from './mockData/gen/BbsHeadMenuTabMockData.json';
import CusBaseInfoMenuTabMockData from './mockData/cus/CusBaseInfoMenuTabMockData.json';
import InvestAnalysisMenuTabMockData from './mockData/cus/InvestAnalysisMenuTabMockData.json';
// adm/ADM0104/include/userAccountTab.vue
import BranMenuMockData from './mockData/adm/BranMenuMockData.json';
import SysUserMockData from './mockData/adm/SysUserMockData.json';
import UserPosEventMockData from './mockData/adm/UserPosEventMockData.json';
// adm/ADM0104/include/userAccountNewTab.vue
import UserInfoMockData from './mockData/adm/UserInfoMockData.json';
import UserBranPosInfoMockData from './mockData/adm/UserBranPosInfoMockData.json';
import PosBranMenuMockData from './mockData/adm/PosBranMenuMockData.json';

import SELECT_YNMockData from './mockData/adm/AdmCodeDetail/SELECT_YNMockData.json';
import INT_FREQ_UNITTYPEMockData from './mockData/adm/AdmCodeDetail/INT_FREQ_UNITTYPEMockData.json';
import FIN_REQ_CODEMockData from './mockData/adm/AdmCodeDetail/FIN_REQ_CODEMockData.json';
import GM_CAT_TYPEMockData from './mockData/adm/AdmCodeDetail/GM_CAT_TYPEMockData.json';

import getUnderUserDeputiesPageDataApiJson from './mockData/adm/GetUnderUserDeputiesPageDataApiData.json';
import getDeputiesLogApiJson from './mockData/adm/GetDeputiesLogApiData.json';
import getUserAccessCntLogApiJson from './mockData/adm/GetUserAccessCntLogApiData.json';
import getBranAccessCntLogApiJson from './mockData/adm/GetbranAccessCntLogApiData.json';
import getAreaMenuApiJson from './mockData/adm/GetAreaMenuApiData.json';
import getuserAccessCusLogsApiSortLogDtAscJson from './mockData/adm/GetuserAccessCusLogsApiDataSortLogDtAsc.json';
import getuserAccessCusLogsApiSortLogDtDescJson from './mockData/adm/GetuserAccessCusLogsApiDataSortLogDtDesc.json';
import getuserAccessCusLogsApiSortBranCodeAscJson from './mockData/adm/GetuserAccessCusLogsApiDataSortBranCodeAsc.json';
import getuserAccessCusLogsApiSortBranCodeDescJson from './mockData/adm/GetuserAccessCusLogsApiDataSortBranCodeDesc.json';
import getMinorAreaApiJson from './mockData/adm/GetMinorAreaApiData.json'
import getTdItemCat1MenuApiJson from './mockData/adm/GetTdItemCat1MenuApiData.json'
import getTdItemsApiJson from './mockData/adm/GetTdItemsApiData.json'
export function getAdmCodeDetail({ codeType, codeValue, codeName }) {
	switch (codeType) {
		case 'SELECT_YN':
			return SELECT_YNMockData;
		case 'INT_FREQ_UNITTYPE':
			return INT_FREQ_UNITTYPEMockData;
		case 'FIN_REQ_CODE':
			return FIN_REQ_CODEMockData;
		case 'GM_CAT_TYPE':
			return GM_CAT_TYPEMockData;
	}
}
//ADM0110/programSet.vue
export function getFunctionMenuTreeApi() {
	return FunctionMenuTreeMockData;
}

export function getPrograSetApi() {
	return FunctionMenuTreeMockData;
}

export function patchSaveMenuActive() {
	return true;
}

// ADM0101/admRole.vue
export function getRoleMenuApi() {
	return RoleMenuMockData;
}

export function getAdmRolesApi() {
	return SystemRolesMockData;
}

// ADM0101/include/admRoleAuthority.vue
// getAdmRoles() 呼叫了與 ADM0101/admRole.vue 的 getAdmRolesApi 一樣的Api

export function getMenuEnableTreeApi() {
	return MenuEnableTreeMockData;
}

export function postUpdateRolAuthorityApi() {
	return UpdateRolAuthorityMockData;
}

// ADM0101/include/admRoleReviewDetail.vue
export function getDetailApi() {
	return RoleReviewDetailMockData;
}

// ADM0103/admMenuPreview.vue
// getRoleMenuDatas() 呼叫了與 ADM0101/admRole.vue 的 getRoleMenuApi 一樣的Api

export function getRoleMenuTreeApi() {
	return RoleMenuTreeMockData;
}

// components/biTabs.vue
export function getMenuTabApi(menuCode) {
	switch (menuCode) {
		case 'M00-04':
			return UserAccountMenuTabMockData;
		case 'M00-07':
			return GroupBranMenuTabMockData;
		case 'M00-06':
			return FcStepMenuTabMockData;
		case 'M30-00':
			return ProductSearchMenuTabMockData;
		case 'M40-01':
			return BbsMgtMenuTabMockData;
		case 'M40-02':
			return BbsHeadMenuTabMockData;
		case 'M20-052':
			return CusBaseInfoMenuTabMockData;
		case 'M20-058':
			return InvestAnalysisMenuTabMockData;
	}
	return UserAccountMenuTabMockData;
}

export function postLoggingApi() {
	return GetLoggingMockData;
}

// adm/ADM0104/include/userAccountTab.vue
export function getBranMenuApi() {
	return BranMenuMockData;
}
// getRoleMenu() 呼叫了與 ADM0101/admRole.vue 的 getRoleMenuApi 一樣的Api
export function getSysUserApi() {
	return SysUserMockData;
}

export async function getExportExcelApi() {
	const response = await fetch('./mockData/adm/ExportExcelMockData.xlsx');
	const blob = await response.blob();
	return {
		data: blob
	};
}

export function getUserPosEventApi() {
	return UserPosEventMockData;
}

// adm/ADM0104/include/userAccountNewTab.vue
export function getEditUserInfoApi() {
	return UserInfoMockData;
}

export function getUserBranPosInfoApi() {
	return UserBranPosInfoMockData;
}
// getBranMenu() 呼叫了與 ADM0104/include/userAccountTab.vue 的 getBranMenuApi 一樣的Api

export function getPosBranMenuApi() {
	return PosBranMenuMockData;
}

export function postUserAccountApi() {
	return; // todo: 待提供正確的 mockData 內容
}

export function getGroupBranNameApi() {}

export function getBranPageData({ groupCode }) {}

export function getBranExportPageData({ groupCode }) {}

export function postFcBranMapChkApi(formData) {}

export function getAdmRoleByRoleTypeApi({ roleType }) {}

export function postFcBranMapApi() {}

export function getFcBranMapData({ roleCode }, queryString) {
	return Promise.resolve({ data: null });
}

export function getFcBranExportPageData({ roleCode }) {
	return Promise.resolve({ data: null });
}

export function getAreaMenu() {
	return Promise.resolve({ data: getAreaMenuApiJson });
}

export function getUserDeputiesApi() {
	return Promise.resolve({ data: null });
}

export function getDeputiesRmMgrApi() {
	return Promise.resolve({ data: null });
}

export function getUnderUserDeputiesPageDataApi({ groupCode, branCode }, queryString) {
	return Promise.resolve({ data: getUnderUserDeputiesPageDataApiJson });
}

export function getDeputyUserCodeApi({ deputyUserCode }) {
	return Promise.resolve({ data: null });
}

export function postInsertDeputyApi({ userCode, roleMetadata, branCode, deputyUserCode, deputyBranCode, stdDt, endDt }) {
	return Promise.resolve({ data: null });
}

export function getchkValidDeputiesTimeApi({ stdDt, endDt }) {
	return Promise.resolve({ data: null });
}

export function getdoCheckIsBusinessDtApi({ date }) {
	return Promise.resolve({ data: null });
}

export function getcheckDeputyUserCodeApi({ userCode, deputyUserCode }) {
	return Promise.resolve({ data: null });
}

export function deleteUserdeputyApi({ userCode, deputyUserCode }) {
	return Promise.resolve({ data: null });
}

export function getAdmBranchesApi({ parentBranCode, branLvlCode, removeYn, branCode }) {
	return Promise.resolve({ data: null });
}

export function getAdmUsersListApi({ branCode, userCode, parentBranCode, roleCode }) {
	return Promise.resolve({ data: null });
}

export function getUserCodeLengthApi() {
	return Promise.resolve({ data: null });
}

export function getDeputiesLogApi({ parentBranCode, branCode, userCode, stdDt, endDt }) {
	return Promise.resolve({ data: getDeputiesLogApiJson });
}

export function getMinorAreaApi({ buCode, majorCode }) {
	return Promise.resolve({ data: getMinorAreaApiJson });
}

export function getBranchesApi({ buCode, majorCode, minorCode }) {
	return Promise.resolve({ data: null });
}

export function getDeputiesApi({ branCode }) {
	return Promise.resolve({ data: null });
}

export function getShutdownInfoApi() {
	return Promise.resolve({ data: null });
}

export function postShutdownInfoApi({ startDt, endDt, shutdownDesc }) {
	return Promise.resolve({ data: null });
}

export function patchShutdownInfoApi({ shutdownId, startDt, endDt, shutdownDesc }) {
	return Promise.resolve({ data: null });
}

export function deleteShutdownInfoApi({ shutdownId }) {
	return Promise.resolve({ data: null });
}

export function getAllBranchesMenuApi() {
	return Promise.resolve({ data: null });
}

export function getModuleMenuApi() {
	return Promise.resolve({ data: null });
}

export function getBranEmployeeApi({ buCode, branCode }) {
	return Promise.resolve({ data: null });
}

export function getUserMenuApi({ branCode }) {
	return Promise.resolve({ data: null });
}

export function getBranchFunctionMenuApi({ menuCode }) {
	return Promise.resolve({ data: null });
}

export function getUserMenu({ branCode }) {
	return Promise.resolve({ data: null });
}

export function getUserAccessCntLogApi({ branCode, logStartDt, logEndDt, moduleMenuCode, functionMenuCode, userCode }, queryString) {
	return Promise.resolve({ data: getUserAccessCntLogApiJson });
}

export function getCusSaveResultCountApi({ paramType, paramCode }) {
	return Promise.resolve({ data: null });
}

export function getBranAccessCntLogApi({ branCode, logStartDt, logEndDt, moduleMenuCode, functionMenuCode, userCode }, queryString) {
	return Promise.resolve({ data: getBranAccessCntLogApiJson });
}

export function getCusFunctionMenuApi() {
	return Promise.resolve({ data: null });
}

export function getUserAccessCusLogsApi({ branCode, logStartDt, logEndDt, menuCode, userCode, cusCode }, queryString) {
	const params = new URLSearchParams(queryString);
	const sortValue = params.get('sort');
	const columeName = sortValue.split(',')[0];
	const sort = sortValue.split(',')[1];

	if (columeName == 'log_dt') {
		if (sort == 'ASC') {
			return Promise.resolve({ data: getuserAccessCusLogsApiSortLogDtAscJson });
		} else {
			return Promise.resolve({ data: getuserAccessCusLogsApiSortLogDtDescJson });
		}
	} else if (columeName == 'bran_code') {
		if (sort == 'ASC') {
			return Promise.resolve({ data: getuserAccessCusLogsApiSortBranCodeAscJson });
		} else {
			return Promise.resolve({ data: getuserAccessCusLogsApiSortBranCodeDescJson });
		}
	} else {
		return Promise.resolve({ data: getuserAccessCusLogsApiSortLogDtAscJson });
	}
}

export function getUserFunctionMenuApi({ depths, strset }) {
	return Promise.resolve({ data: null });
}

export function getUserRoleMenuApi() {
	return Promise.resolve({ data: null });
}

export function getUserInfoApi({ userCode, path }) {
	return Promise.resolve({ data: null });
}

export function getUserAccessLogsApi({ userCode, logStartDt, logEndDt, m1MenuCode, m2MenuCode, m3MenuCode, m4MenuCode }, queryString) {
	return Promise.resolve({ data: null });
}

export function getCarryOutItemsActionTypeApi({ actionMode }) {
	return Promise.resolve({ data: null });
}

export function getItemsActionTypeMenuApi() {
	return Promise.resolve({ data: null });
}

export function getCarryOutItemsApi({ userCode, progCode, actionTypes, logStdDt, logEndDt }, queryString) {
	return Promise.resolve({ data: null });
}

export function getTdItemCat1MenuApi() {
	return Promise.resolve({ data: getTdItemCat1MenuApiJson });
}

export function getTdItemsApi({ tdCat1Code }) {
	return Promise.resolve({ data: getTdItemsApiJson });
}

export function patchTdItemsApi(changedTdItems) {
	return Promise.resolve({ data: null });
}

export function getRmLvlMenuApi() {
	return Promise.resolve({ data: null });
}

export function getCusGradesSetListsApi() {
	return Promise.resolve({ data: null });
}

export function patchCusGradesSetListsApi({ graCode, rmLvlCode, graName, auaMin, auaMax }) {
	return Promise.resolve({ data: null });
}

export function getGradesApi() {
	return Promise.resolve({ data: null });
}

export function patchGradesApi(changedGradesItems) {
	return Promise.resolve({ data: null });
}

export function getCodeDetailApi({ codeType }) {
	return Promise.resolve({ data: null });
}

export function getHasEnableVerifyOptionApi() {
	return Promise.resolve({ data: null });
}
export function getSearchTdListApi({
	startDate,
	endDate,
	tdCat1Code,
	itemCode,
	cusCode,
	branCode,
	allBranCode,
	userCode,
	allUserCode,
	pageable
}) {
	return Promise.resolve({ data: null });
}