<template>
	<dynamicTitle />
	<div>
		<div class="row">
			<div class="col-12">
				<vue-bi-tabs :menu-code="'M41-01'" @change-tab="changeTab" :tab-name-decorator="showNameDecorator" ref="tab">
					<template #default="{ id }">
						<component :is="id" :selected-type-code="selectedTypeCode"></component>
					</template>
				</vue-bi-tabs>
			</div>
		</div>
	</div>
</template>
<script>
import dynamicTitle from '@/views/components/dynamicTitle.vue';
import vueBiTabs from '@/views/components/biTabs.vue';
import vueDocProDcd from './include/docProDCD.vue';
export default {
	components: {
		vueBiTabs,
		dynamicTitle,
		vueDocProDcd
	},
	data: function () {
		return {
			docProTypeCnt: [],
			tabCodeProTypeMap: {
				'M41-010': 'FUND',
				'M41-011': 'ETF',
				'M41-012': 'FB',
				'M41-013': 'PFD',
				'M41-014': 'SP',
				'M41-015': 'DCD',
				'M41-016': 'INS'
			},
			tabTitle: '',
			selectedTypeCode: null
		};
	},
	mounted: function () {
		this.getDocProTypeCnt();
	},
	methods: {
		async getDocProTypeCnt() {
			var self = this;
			const ret = await self.$api.getDocProTypeCntApi();
			if (!ret.data?.length > 0) return;
			self.docProTypeCnt = ret.data;
			self.selectedTypeCode = self.tabCodeProTypeMap[self.$refs.tab.selectedCode];
			self.$forceUpdate();
		},
		changeTab: function (tabCode) {
			var self = this;
			self.selectedTypeCode = self.tabCodeProTypeMap[tabCode];
		},
		showNameDecorator: function (tab) {
			var self = this;
			var docProTypeCnt = self.docProTypeCnt.filter((t) => t.proTypeCode === self.tabCodeProTypeMap[tab.code]);
			var newName = tab.name;
			if (docProTypeCnt && docProTypeCnt.length > 0) {
				newName = newName + ' (' + (docProTypeCnt[0].cnt || '0') + ')';
			}
			return newName;
		}
	}
};
</script>
