{"status": 200, "data": true, "timestamp": "2025/04/21", "sqlTracer": [{"data": {"userCode": "112790", "userName": "簡OO", "buCode": "Z", "branCode": "891", "branName": "財富管理部    ", "branLvlCode": "20", "roleCode": "49", "roleName": "系統管理幹部", "roleType": "HQ", "posCode": "891_49", "posName": "系統管理幹部", "strset": "000F", "homeType": "TYPE5"}, "sqlInfo": " SELECT AU.USER_CODE, AU.USER_NAME, AP.BU_CODE, AP.BRAN_CODE,  AB.BRAN_NAME, AB.BRAN_LVL_CODE, AR.ROLE_CODE, AR.ROLE_NAME, AR.ROLE_TYPE, AR.HOME_TYPE,  AP.POS_CODE, AP.POS_NAME, AB.STRSET  FROM ADM_USERS AU  LEFT JOIN ADM_USER_POS_MAP AUPM ON AUPM.USER_CODE = AU.USER_CODE  LEFT JOIN ADM_POSITIONS AP ON AP.POS_CODE = AUPM.POS_CODE  LEFT JOIN ADM_BRANCHES AB ON AB.BRAN_CODE = AP.BRAN_CODE AND AB.BU_CODE= AP.BU_CODE  LEFT JOIN ADM_ROLES AR ON AR.ROLE_CODE = AP.ROLE_CODE  WHERE AU.USER_CODE = ? AND AUPM.POS_CODE = ? ,class com.bi.pbs.adm.login.model.UserInfo,[Ljava.lang.Object;@55e4f991"}, {"data": ["891_00", "891_41", "891_49", "891_98", "891_99"], "sqlInfo": "SELECT POS_CODE FROM ADM_USER_POS_MAP WHERE USER_CODE = ?,class java.lang.String,[Ljava.lang.Object;@651bb6c4"}, {"data": ["891"], "sqlInfo": "SELECT DISTINCT BRAN_CODE FROM ADM_POSITIONS WHERE POS_CODE IN (:posCodes) AND VALID_YN = 'N',class java.lang.String,{posCodes=[891_00, 891_41, 891_49, 891_98, 891_99]}"}, {"data": ["41", "49", "98"], "sqlInfo": "SELECT DISTINCT ROLE_CODE FROM ADM_POSITIONS WHERE POS_CODE IN (:posCodes) AND VALID_YN = 'N',class java.lang.String,{posCodes=[891_00, 891_41, 891_49, 891_98, 891_99]}"}, {"data": [], "sqlInfo": "SELECT JOBITEM_CODE FROM ADM_USER_JOBITEM WHERE USER_CODE=:userCode,class java.lang.String,{userCode=112790}"}]}