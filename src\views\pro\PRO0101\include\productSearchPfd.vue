<template>
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<!--頁面內容 基金fund start-->
			<div class="tab-nav-tabs my-3">
				<ul class="nav nav-tabs nav-justified">
					<li class="nav-item">
						<a class="nav-link" :class="{ active: activeTab == 'common' }" data-bs-toggle="tab"
							@click="changeTab('common')">一般篩選</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" :class="{ active: activeTab == 'fast' }" data-bs-toggle="tab"
							@click="changeTab('fast')">快速篩選</a>
					</li>
				</ul>

				<div class="tab-content">
					<div class="tab-pane fade show" id="SectionA" :class="{ 'active show': activeTab == 'common' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
								<h4>查詢條件</h4>
								<span class="tx-square-bracket">為必填欄位</span>
							</div>
							<div class="collapse show" id="collapseListGroup1">
								<div class="card-body">
									<form>
										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 商品代號 </label>
												<input class="form-control" id="prod_bank_pro_code" maxlength="20" v-model="bankProCode"
													size="25" type="text" value="" />
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 商品名稱</label>
												<input class="form-control" id="prod_pro_name" maxlength="20" v-model="proName" size="45"
													type="text" value="" />
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 計價幣別</label>
												<select class="selectpicker form-control" id="curMenuPfd" multiple title="請選擇幣別"
													v-model="curObjs" data-style="btn-white">
													<option value="">全部</option>
													<option v-for="(item, index) in curOption" :key="index" :value="item.value">
														{{ $filters.defaultValue(item.name, '--') }}
													</option>
												</select>
											</div>
										</div>

										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">風險等級</label>
												<div class="form-check-group">
													<div class="form-check form-check-inline" v-for="(item, index) in riskMenu">
														<input type="checkbox" class="form-check-input" name="riskCodes" v-model="riskCodes"
															:id="'riskGrade-' + index" :value="item.riskCode" />
														<label :for="'riskGrade-' + index" class="form-check-label">{{ item.riskName }}</label>
													</div>
												</div>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">ISINCODE</label>
												<input class="form-control" id="isinCode" maxlength="20" v-model="isinCode" size="45"
													type="text" />
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">限PI銷售</label>
												<div v-for="item in profInvestorMenu" class="form-check form-check-inline">
													<input class="form-check-input" :id="'profInvestor' + item.codeValue" v-model="profInvestorYn"
														type="radio" :value="item.codeValue" name="fastCode" />
													<label class="form-check-label" :for="'profInvestor' + item.codeValue">{{
														$filters.defaultValue(item.codeName, '--')
													}}</label>
												</div>
											</div>
										</div>

										<div class="form-row">
											<div class="form-group col-12 col-lg-12">
												<label class="form-label">投資地區</label>
												<div class="input-group">
													<button type="button" class="btn btn-primary" @click="groupGeoFocusModalHandler()">
														選擇投資地區
													</button>
													<vue-modal :is-open="isOpenGeoFocusModal" :before-close="isOpenGeoFocusModal = false">
														<template v-slot:content="props">
															<vue-group-geofocus-modal :close="props.close" ref="groupGeoFocusModalRef"
																id="groupGeoFocusModal" :issuer-prop="geoFocusItem"
																@selected="selectedGeoFocus"></vue-group-geofocus-modal>
														</template>
													</vue-modal>
													<div v-for="item in geoFocusItem">
														<span class="form-check-label"> {{ $filters.defaultValue(item.name, '--') }}</span>
														<a href="#" @click="deleteGeoFocusItem(item.geoFocusCode)"><img
																:src="getImgURL('icon', 'i-cancel.png')" /></a>
													</div>
												</div>
											</div>
										</div>

										<div class="form-footer">
											<a class="btn btn-primary" @click.prevent="gotoPage(0)">查詢</a>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>

					<div class="tab-pane fade" id="SectionB" :class="{ 'active show': activeTab == 'fast' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2">
								<h4>查詢條件</h4>
								<span class="tx-square-bracket">為必填欄位</span>
							</div>
							<div class="collapse show" id="collapseListGroup2">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-12">
											<label class="form-label tx-require"> 篩選條件 </label>

											<div class="form-check-group">
												<div class="form-check form-check-inline" v-for="item in pfdFastMenu"
													@change="fastChange(item.codeValue)">
													<input class="form-check-input" :id="'fast' + item.codeValue" name="fastCode"
														v-model="fastCode" :value="item.codeValue" type="radio" />
													<label class="form-check-label" :for="'fast' + item.codeValue">{{
														$filters.defaultValue(item.codeName, '--')
													}}</label>
												</div>
											</div>
										</div>
									</div>

									<div class="form-footer">
										<a class="btn btn-primary" @click.prevent="gotoFastPage(0)">查詢</a>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div id="searchResult" v-if="pageData.content.length > 0">
				<div class="card card-table">
					<div class="card-header">
						<h4>查詢結果</h4>
						<div style="display: flex">
							<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
						</div>
					</div>
					<div class="table-responsive">
						<table class="table table-bordered table-blue">
							<thead>
								<tr>
									<th>
										商品代號<a v-if="activeTab === 'common'" href="#" class="icon-sort"
											@click="sort('BANK_PRO_CODE')"></a><a v-if="activeTab === 'fast'" href="#" class="icon-sort"
											@click="sortFast('BANK_PRO_CODE')"></a>
									</th>
									<th>
										商品中文名稱<a v-if="activeTab === 'common'" href="#" class="icon-sort" @click="sort('PRO_NAME')"></a><a
											v-if="activeTab === 'fast'" href="#" class="icon-sort" @click="sortFast('PRO_NAME')"></a>
									</th>
									<th>
										風險等級<a v-if="activeTab === 'common'" href="#" class="icon-sort" @click="sort('RISK_NAME')"></a><a
											v-if="activeTab === 'fast'" href="#" class="icon-sort" @click="sortFast('RISK_NAME')"></a>
									</th>
									<th>
										計價幣別<a v-if="activeTab === 'common'" href="#" class="icon-sort" @click="sort('CUR_CODE')"></a><a
											v-if="activeTab === 'fast'" href="#" class="icon-sort" @click="sortFast('CUR_CODE')"></a>
									</th>
									<th>
										參考市值<a v-if="activeTab === 'common'" href="#" class="icon-sort" @click="sort('A_PRICE')"></a><a
											v-if="activeTab === 'fast'" href="#" class="icon-sort" @click="sortFast('A_PRICE')"></a>
									</th>
									<th>
										市值日期<a v-if="activeTab === 'common'" href="#" class="icon-sort" @click="sort('PRICE_DT')"></a><a
											v-if="activeTab === 'fast'" href="#" class="icon-sort" @click="sortFast('PRICE_DT')"></a>
									</th>
									<th>
										銷售對象<br />（是否限PI）<a v-if="activeTab === 'common'" href="#" class="icon-sort"
											@click="sort('PROF_INVESTOR_YN_NAME')"></a><a v-if="activeTab === 'fast'" href="#"
											class="icon-sort" @click="sortFast('PROF_INVESTOR_YN_NAME')"></a>
									</th>
									<th>
										是否可銷售<a v-if="activeTab === 'common'" href="#" class="icon-sort" @click="sort('BUY_YN_NAME')"></a><a
											v-if="activeTab === 'fast'" href="#" class="icon-sort" @click="sortFast('BUY_YN_NAME')"></a>
									</th>
									<th>執行</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="(item, index) in pageData.content">
									<td data-th="商品代號">
										<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
									</td>
									<td class="text-start" data-th="商品中文名稱">
										<span>
											<a class="tx-link" @click="pfdModalHandler(item.proCode, item.pfcatCode)">{{
												$filters.defaultValue(item.proName, '--')
											}}</a>
										</span>
									</td>
									<td data-th="風險等級">
										<span>{{ $filters.defaultValue(item.riskName, '--') }}</span>
									</td>
									<td class="text-center" data-th="計價幣別">
										<span>{{ $filters.defaultValue(item.curCode, '--') }}</span>
									</td>
									<td class="text-center" data-th="參考市值">
										<span>{{ $filters.formatNumber(item.aprice, '--') }}</span>
									</td>
									<td class="text-center" data-th="市值日期">
										<span>{{ $filters.formatDate(item.priceDt, '--') }}</span>
									</td>
									<td class="text-center" data-th="銷售對象<br>（是否限PI）">
										<span>{{ $filters.defaultValue(item.profInvestorYnName, '--') }}</span>
									</td>
									<td class="text-center" data-th="是否可銷售">
										<span>{{ $filters.defaultValue(item.buyYnName, '--') }}</span>
									</td>
									<td class="text-center" data-th="執行">
										<button v-if="activeTab === 'fast' && fastCode === '06'" type="button" class="btn btn-primary"
											title="移除我的最愛" @click="remove(item.proCode)">
											移除最愛
										</button>
										<button v-else type="button" class="btn btn-dark btn-icon" data-bs-toggle="tooltip" title="加入我的最愛"
											@click="favoritesHandler(item.proCode, item.pfcatCode)">
											<i class="bi bi-heart text-danger"></i>
										</button>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import pagination from '@/views/components/pagination.vue';
import vueModal from '@/views/components/model.vue';
import vueGroupGeofocusModal from './groupGeoFocusModal.vue';
import { getImgURL } from '@/utils/imgURL.js';
export default {
	components: {
		'vue-pagination': pagination,
		vueModal,
		vueGroupGeofocusModal
	},
	props: {
		pfdModalHandler: Function,
		favoritesHandler: Function,
		curOption: Array,
		riskMenu: Array
	},
	data: function () {
		return {
			activeTab: 'common',
			bankProCode: null, // 商品代碼
			proName: null, // 商品名稱
			curObjs: [], // 計價幣別
			riskCodes: [], //風險等級
			isinCode: null,
			profInvestorYn: '', // 限PI銷售
			geoFocusItem: [], // 投資地區

			fastCode: '03', //快速篩選
			timeRange: null, // 快速 顯示區間
			rowNumber: '', // 快速 顯示資料筆數
			curCode: 'TWD', // 績效幣別
			perf: null, // 標的績效

			profInvestorMenu: [], // 限PI銷售選項
			pfdFastMenu: [], // 快速篩選選單

			//查詢條件
			rateCurType: 'O', //(報酬率) 報酬率幣別顯示
			selectedItems: {}, // 加入比較選項
			pageData: {
				content: []
			},
			rankPageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: ['PRO_CODE'],
				direction: 'ASC'
			},
			isOpenGeoFocusModal: false
		};
	},
	watch: {
		selectedItems: {
			handler(newValues) {
				this.lipperIds = Object.keys(newValues).filter((lipperId) => newValues[lipperId]);
			},
			deep: true
		},
		activeTab(newVal, oldVal) {
			var self = this;
			self.fastCode = '03';
		},
		selectedTab(newTab, oldTab) {
			var self = this;
			self.lipperIds = [];
			self.selectedItems = {};
		},
		curObjs(newVal, oldVal) {
			var self = this;
			if (newVal[0] !== oldVal[0]) {
				if (newVal[0] === '') {
					$('#curMenuPfd').selectpicker('selectAll');
				} else if (oldVal[0] === '' && newVal[0] !== '') {
					$('#curMenuPfd').selectpicker('deselectAll');
				}
			}
		}
	},
	mounted: function () {
		var self = this;
		$('#curMenuPfd').selectpicker('refresh');
		self.getProfInvestorMenu(); // 限PI銷售選項
		self.getPfdFastMenu(); // 取得快速篩選條件選項
	},
	methods: {
		getImgURL,
		// 條件Tab切換
		changeTab: function (tabName) {
			var self = this;
			self.activeTab = tabName;
			self.pageData = {
				// 清空查詢資料
				content: []
			};
		},
		// 取得限PI銷售選項
		async getProfInvestorMenu() {
			var self = this;
			self.profInvestorMenu = [{ codeValue: '', codeName: '不限' }];
			var selectYnList = [];
			const ret = await this.$api.getAdmCodeDetail({
				codeType: 'SELECT_YN'
			});

			selectYnList = ret.data;
			Array.prototype.push.apply(self.profInvestorMenu, selectYnList);
		},
		// 一般篩選  顯示選擇投資地區 model
		groupGeoFocusModalHandler: function () {
			var self = this;
			this.$refs.groupGeoFocusModalRef.geoFocusPropItem(self.geoFocusItem);
			this.isOpenGeoFocusModal = true;
		},
		// 顯示投資地區選擇項目
		selectedGeoFocus(geoFocusItem) {
			var self = this;
			self.isOpenGeoFocusModal = false;
			self.geoFocusItem = geoFocusItem; //取得基金公司資料
		},
		//刪除投資地區項目
		deleteGeoFocusItem(geoFocusCode) {
			var self = this;
			_.remove(self.geoFocusItem, (item) => item.geoFocusCode === geoFocusCode); // 移除刪除項目
		},
		// 取得快速篩選條件選項
		getPfdFastMenu: async function () {
			var self = this;
			const ret = await this.$api.getPfdFastMenuApi();
			self.pfdFastMenu = ret.data;
		},
		// 刪除我的最愛
		async remove(proCode) {
			var self = this;
			await this.$api.deleteFavoriteApi({
				proCode: proCode
			});
			this.$bi.alert('刪除成功');
			self.checkboxs = [];
			self.proCodes = [];
			self.gotoFastPage(0);
		},
		// 由查詢結果標題觸發
		sort: function (columnName) {
			if (this.pageable.sort !== columnName) {
				this.pageable.sort = columnName;
				this.pageable.direction = 'DESC';
			} else {
				this.pageable.direction = this.pageable.direction == 'ASC' ? 'DESC' : 'ASC';
			}

			this.gotoPage(0);
		},
		// 由快速查詢結果標題觸發
		sortFast: function (columnName) {
			if (this.pageable.sort !== columnName) {
				this.pageable.sort = columnName;
				this.pageable.direction = 'DESC';
			} else {
				this.pageable.direction = this.pageable.direction == 'ASC' ? 'DESC' : 'ASC';
			}

			this.gotoFastPage(0, columnName);
		},
		fastChange(fastCode) {
			// 快速查詢切換
			var self = this;
			self.pageData = {
				// 清空查詢資料
				content: []
			};
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (page) {
			var self = this;
			var url = '';
			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;

			var geoFocusCodeList = self.geoFocusItem.map(function (item) {
				return item.geoFocusCode;
			});
			const ret = await this.$api.getPfdProductsApi(
				{
					bankProCode: self.bankProCode,
					proName: self.proName,
					curCodes: self.curObjs,
					riskCodes: self.riskCodes,
					isinCode: self.isinCode,
					profInvestorYn: self.profInvestorYn,
					geoFocusCodes: geoFocusCodeList
				},
				url
			);
			self.pageData = ret.data;
		},
		// 快速篩選
		gotoFastPage: function (page, sortColumnName) {
			this.pageable.page = page;
			this.getFastPageData(page, sortColumnName);
		},
		getFastPageData: async function (page, sortColumnName) {
			var self = this;

			var url = '';
			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;
			const ret = await this.$api.getPfdFastPageDataApi(
				{
					filterCodeValue: self.fastCode
				},
				url
			);
			self.pageData = ret.data;
		}
	}
};
</script>
<style scoped>
.dropdown.bootstrap-select {
	min-width: 0;
}
</style>
