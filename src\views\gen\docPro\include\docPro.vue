<template>
	<div role="tabpanel" class="tab-pane fade active show">
		<div class="card card-form mb-3">
			<div class="card-header">
				<h4>文件維護</h4>
				<span class="tx-square-bracket">為必填欄位</span>
			</div>
			<vue-form v-slot="{ errors, validate }" ref="queryForm">
				<table class="table table-RWD table-horizontal-RWD table-bordered">
					<tbody>
						<tr>
							<th class="wd-15p tx-require">文件標題</th>
							<td class="wd-85p">
								<vue-field
									type="text"
									name="docTitle"
									label="文件標題"
									class="form-control"
									size="70"
									v-model="docName"
									:class="{ 'is-invalid': errors.docTitle }"
									rules="required"
								></vue-field>
								<span class="text-danger" v-show="errors.docTitle">{{ errors.docTitle }}</span>
							</td>
						</tr>
						<tr>
							<th class="tx-require">生效日</th>
							<td>
								<div class="input-group">
									<vue-field
										type="date"
										name="beginDate"
										label="生效日"
										value=""
										id="beginDate"
										class="form-control"
										v-model="validDate"
										:class="{ 'is-invalid': errors.beginDate }"
										rules="required"
									></vue-field>
									<span class="text-danger" v-show="errors.beginDate">{{ errors.beginDate }}</span>
								</div>
							</td>
						</tr>
						<tr>
							<th class="tx-require">到期日</th>
							<td>
								<div class="input-group">
									<vue-field
										type="date"
										name="endDate"
										label="到期日"
										value=""
										id="endDate"
										class="JQ-datepicker form-control"
										:class="{ 'is-invalid': errors.endDate }"
										rules="required"
										v-model="expireDate"
									></vue-field>
									<span class="text-danger" v-show="errors.endDate">{{ errors.endDate }}</span>
								</div>
							</td>
						</tr>
						<tr>
							<th class="tx-require">緊急程度</th>
							<td>
								<vue-field
									as="select"
									name="mainCatCode"
									id="mainCatCode"
									class="form-select"
									data-inline="true"
									v-model="priority"
									label="緊急程度"
									:class="{ 'is-invalid': errors.endDate }"
									rules="required"
								>
									<option :value="null">請選擇</option>
									<option :value="item.codeValue" v-for="item in selPriority">
										{{ item.codeName }}
									</option>
								</vue-field>
								<span class="text-danger" v-show="errors.mainCatCode">{{ errors.mainCatCode }}</span>
							</td>
						</tr>
						<tr>
							<th class="tx-require">可否提供給客戶</th>
							<td>
								<div class="form-check form-check-inline" v-for="item in selShowCus">
									<vue-field
										class="form-check-input"
										type="radio"
										id="r-9"
										:value="item.codeValue"
										name="selShowCus"
										label="可否提供給客戶"
										v-model="showCusYn"
										:class="{ 'is-invalid': errors.selShowCus }"
										rules="required"
									></vue-field>
									<label class="form-check-label" :for="'r-9'">{{ item.codeName }}</label>
								</div>
								<span class="text-danger" v-show="errors.selShowCus">{{ errors.selShowCus }}</span>
							</td>
						</tr>
						<tr>
							<th>摘要</th>
							<td>
								<textarea class="form-control" maxlength="150" rows="8" v-model="docDesc"></textarea>
								<span class="tx-danger">(150 個字可輸入)</span>
							</td>
						</tr>
						<tr>
							<th>附加檔案</th>
							<td>
								<vue-form v-slot="{ errors, validate }" class="col-lg-12" ref="fileForm">
									<div class="row g-2">
										<div class="input-group">
											<vue-field
												name="uploadFile"
												type="file"
												class="form-control"
												label="附件"
												@change="handleChange($event)"
												ref="uploadFile"
												:class="{ 'is-invalid': errors.uploadFile }"
												:rules="{
													required_file: true,
													mbSize: 15,
													extMgt: validExts,
													validateName: symbols,
													isOverSize: fileCntObj
												}"
												accept=".doc, .docx, .pdf, .xlsx, .txt"
											>
											</vue-field>
											<button class="btn btn-info btn-glow" type="button" name="'tempFile" size="30" @click="addFile">
												上傳
											</button>
										</div>
									</div>
									<ul class="list-group list-inline-tags mt-2">
										<li class="list-group-item" v-for="file in files">
											<a href="#" @click="previewFile(file.fileNo)">
												<span>{{ file.showName }}</span>
												<span
													class="img-delete JQ-delet"
													data-bs-toggle="tooltip"
													title="刪除"
													@click.stop="deleteFile(file.fileId)"
												></span>
											</a>
										</li>
									</ul>
									<div style="height: 25px">
										<span class="text-danger" v-show="errors.uploadFile">{{
											$filters.defaultValue(errors.uploadFile, '--')
										}}</span>
									</div>
								</vue-form>
							</td>
						</tr>
					</tbody>
				</table>

				<div>
					<div class="tx-title mt-3">
						相關設定
						<span class="tx-square-bracket">請勾選和這份文件相關的項目（至少一項）</span>
					</div>
					<table class="table table-RWD table-bordered table-horizontal-RWD">
						<tbody>
							<tr v-for="item in selTypeMessage">
								<th class="wd-100p" v-if="item.codeValue === 'P'">
									<span class="tx-require">
										<input
											class="form-check-input"
											name="productItems"
											:id="item.codeValue"
											:value="item.codeValue"
											type="checkbox"
											v-model="productYn"
										/>
										<label class="form-check-label" :for="item.codeValue">{{ item.codeName }}</label>
									</span>
								</th>
							</tr>
							<tr>
								<td class="wd-100p">
									<div class="input-group">
										<span class="input-group-text">商品名稱 / 商品代碼：</span>
										<input class="btn btn-primary" id="btn-add" type="button" value="商品快速查詢" @click="openModal()" />
									</div>
								</td>
							</tr>
						</tbody>
					</table>
					<div id="proArea">
						<table class="table table-RWD table-bordered table-horizontal-RWD" style="width: 50%">
							<thead>
								<tr>
									<th width="20%">商品代碼</th>
									<th width="70%">商品名稱</th>
									<th width="10%">執行</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="item in productList">
									<td data-th="商品代碼">{{ item.proCode }}</td>
									<td data-th="商品名稱">{{ item.proName }}</td>
									<td data-th="執行" class="text-center">
										<button class="btn btn-danger btn-icon JQ-logDelete" id="btnDelete" type="button" data-original-title="刪除">
											<span class="fa-solid fa-trash" @click="deleteProduct(item.proCode)"></span>
										</button>
									</td>
								</tr>
							</tbody>
						</table>
						<div class="form-check form-check-inline" v-for="item in selProMessage" :key="item.codeValue">
							<input
								class="form-check-input"
								name="balanceItems"
								:id="item.codeValue"
								type="checkbox"
								:value="item.codeValue"
								v-model="selProItem"
							/>
							<label class="form-check-label" :for="item.codeValue">{{ item.codeName }}</label>
						</div>
					</div>

					<table class="table table-RWD table-bordered table-horizontal-RWD">
						<tbody>
							<tr v-for="item in selTypeMessage">
								<th class="wd-100p" v-if="item.codeValue === 'M'">
									<span class="tx-require"
										><input class="form-check-input" type="checkbox" :value="item.codeValue" v-model="mktYn" />{{
											item.codeName
										}}</span
									>
								</th>
							</tr>
							<tr>
								<td class="wd-100p">
									<div class="input-group">
										<span class="input-group-text">主分類：</span>
										<select name="wobMessages.main_cat_code1" class="form-select" id="mainCatCode1" v-model="mainCat">
											<option :value="null">請選擇</option>
											<option v-for="item in selMainCat" :value="item.typeCode">
												{{ item.typeName }}
											</option>
										</select>
										<span class="input-group-text">次分類：</span>
										<select
											name="wobMessages.main_cat_code2"
											class="form-select"
											id="mainCatCode2"
											v-model="subTypeCode"
											:disabled="!mainCat"
										>
											<option :value="null">請選擇</option>
											<option v-for="item in selSubCat" :value="item.typeCode">
												{{ item.typeName }}
											</option>
										</select>
									</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>

				<table class="table table-RWD table-bordered table-horizontal-RWD">
					<tbody>
						<tr v-for="item in selTypeMessage">
							<th class="wd-100p" v-if="item.codeValue === 'R'">
								<span class="tx-require"
									><input class="form-check-input" type="checkbox" :value="item.codeValue" v-model="researchYn" />{{
										item.codeName
									}}</span
								>
							</th>
						</tr>
						<tr>
							<td class="wd-100p">
								<div class="input-group">
									<span class="input-group-text">主分類：</span>
									<select name="wobMessages.main_cat_code1" class="form-select" id="mainCatCode3" v-model="researchMainTypeCode">
										<option :value="null">請選擇</option>
										<option v-for="item in selOutlookMainCat" :value="item.typeCode">
											{{ item.typeName }}
										</option>
									</select>
									<span class="input-group-text">次分類：</span>
									<select
										name="wobMessages.main_cat_code2"
										class="form-select"
										id="mainCatCode4"
										v-model="researchSubTypeCode"
										:disabled="!researchMainTypeCode"
									>
										<option :value="null">請選擇</option>
										<option v-for="item in selOutlookSubCat" :value="item.typeCode">
											{{ item.typeName }}
										</option>
									</select>
								</div>
							</td>
						</tr>
					</tbody>
				</table>

				<table class="table table-RWD table-bordered table-horizontal-RWD">
					<tbody>
						<tr v-for="item in selTypeMessage">
							<th class="wd-100p" v-if="item.codeValue === 'I'">
								<span class="tx-require"
									><input class="form-check-input" type="checkbox" :value="item.codeValue" v-model="noticeYn" />{{
										item.codeName
									}}</span
								>
							</th>
						</tr>
						<tr>
							<td class="wd-100p">
								<input class="btn btn-primary" id="btn-add1" type="button" value="發行機構查詢" @click="openNoticeModal()" />
							</td>
						</tr>
					</tbody>
				</table>
				<div id="noticeArea">
					<table class="table table-RWD table-bordered table-horizontal-RWD" style="width: 50%">
						<thead>
							<tr>
								<th width="20%">發行機構代碼</th>
								<th width="70%">發行機構名稱</th>
								<th width="10%">執行</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="item in noticeList">
								<td data-th="發行機構代碼">{{ item.issuerCode }}</td>
								<td data-th="發行機構名稱">{{ item.issuerName }}</td>
								<td data-th="執行" class="text-center">
									<button class="btn btn-danger btn-icon JQ-logDelete" id="btnDelete1" type="button" data-original-title="刪除">
										<span class="fa-solid fa-trash" @click="deleteNotice(item.issuerCode)"></span>
									</button>
								</td>
							</tr>
						</tbody>
					</table>

					<table class="table table-RWD table-bordered table-horizontal-RWD">
						<tbody>
							<tr v-for="item in selTypeMessage">
								<th class="wd-100p" v-if="item.codeValue === 'N'">
									<span class="tx-require"
										><input class="form-check-input" type="checkbox" :value="item.codeValue" v-model="newsYn" />{{
											item.codeName
										}}</span
									>
								</th>
							</tr>
						</tbody>
					</table>
					<div class="form-check form-check-inline" v-for="item in selNewsType" :key="item.codeValue">
						<input
							class="form-check-input"
							name="balanceItems"
							:id="item.codeValue"
							type="checkbox"
							:value="item.codeValue"
							:disabled="item.disable"
							@change="reportItemDisable(item)"
							v-model="selReportItem"
						/>
						<label class="form-check-label" :for="item.codeValue">{{ item.codeName }}</label>
					</div>
					<br />
					<!--          持有基金相關商品              -->
					<div
						class="form-check form-check-inline"
						v-for="item in selNewRelFundType"
						:key="item.codeValue"
						v-if="selReportItem.includes('F')"
					>
						<input
							class="form-check-input"
							name="balanceItems"
							:id="item.codeValue"
							type="checkbox"
							:value="item.codeValue"
							v-model="selNewRelFundItem"
						/>
						<label class="form-check-label" :for="item.codeValue">{{ item.codeName }}</label>
					</div>
					<!-- 給持有相關商品的客戶 投資地區 投資標的-->
					<div
						class="form-check form-check-inline"
						v-for="item in selNewsRelProType"
						:key="item.codeValue"
						v-if="selReportItem.includes('P')"
					>
						<input
							class="form-check-input"
							name="balanceItems"
							:id="item.codeValue"
							type="checkbox"
							:value="item.codeValue"
							v-model="selNewRelProItem"
						/>
						<label class="form-check-label" :for="item.codeValue">{{ item.codeName }}</label>
					</div>
					<br />
					<!---->
					<!--         投資標的               -->
					<div
						class="form-check form-check-inline"
						v-for="item in proSectors"
						:key="item.codeValue"
						v-if="selNewRelProItem.includes('SEC')"
					>
						<input
							class="form-check-input"
							name="balanceItems"
							:id="item.sectorCode"
							type="checkbox"
							:value="item.sectorCode"
							v-model="checkedProSectors"
						/>
						<label class="form-check-label" :for="item.sectorCode">{{ item.sectorName }}</label>
					</div>
					<br />
					<!--         投資地區               -->
					<div
						class="form-check form-check-inline"
						v-for="item in proGeoFocus"
						:key="item.codeValue"
						v-if="selNewRelProItem.includes('GEO')"
					>
						<input
							class="form-check-input"
							name="balanceItems"
							:id="item.geoFocusCode"
							type="checkbox"
							:value="item.geoFocusCode"
							v-model="checkedProGeoFocus"
						/>
						<label class="form-check-label" :for="item.geoFocusCode">{{ item.geoFocusName }}</label>
					</div>
					<br />
				</div>
				<div class="text-end">
					<button class="btn btn-primary" type="button" @click.prevent="saveDocument(docId)">儲存</button>
				</div>
			</vue-form>
		</div>
	</div>

	<!-- Modal 1-->
	<vue-modal :is-open="isOpenModal" :before-close="closeModal">
		<template v-slot:content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">商品快速查詢</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()"><i class="bi bi-arrows-fullscreen"></i></button>
						<button type="button" class="btn-close" @click.prevent="props.close()"></button>
					</div>
					<div class="modal-body">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
								<h4>查詢條件</h4>
							</div>
							<div class="collapse show" id="collapseListGroup1">
								<form>
									<div class="form-row">
										<div class="form-group col-lg-4">
											<label class="form-label">商品名稱：</label>
											<input class="form-control" type="text" maxlength="20" v-model="proName" />
											<a data-bs-toggle="tooltip" class="table-icon JQ-singleSearch" onclick="result.style.display='';">
												<button type="button" class="btn btn-dark btn-icon" data-bs-toggle="tooltip" title="檢視">
													<i class="bi bi-search" @click="getProQuickSearch(proName, '', '')"></i>
												</button>
											</a>
										</div>
										<div class="form-group col-lg-4">
											<label class="form-label">商品代號：</label>
											<input class="form-control" type="text" maxlength="20" v-model="proCode" />
											<a data-bs-toggle="tooltip" class="table-icon JQ-singleSearch" onclick="result.style.display='';">
												<button type="button" class="btn btn-dark btn-icon" data-bs-toggle="tooltip" title="檢視">
													<i class="bi bi-search" @click="getProQuickSearch('', proCode, '')"></i>
												</button>
											</a>
										</div>
										<div class="form-group col-lg-4">
											<label class="form-label tx-require">商品主類：</label>
											<select class="form-select" id="cat2" v-model="proType">
												<option selected="selected" value="">全部</option>
												<option v-for="item in selProCat" :value="item.pfcatCode">
													{{ item.pfcatName }}
												</option>
											</select>
											<a
												data-bs-toggle="tooltip"
												class="table-icon JQ-singleSearch"
												id="idn"
												onclick="result.style.display='';"
											>
												<button type="button" class="btn btn-dark btn-icon" data-bs-toggle="tooltip" title="檢視">
													<i class="bi bi-search" @click="getProQuickSearch('', '', proType)"></i>
												</button>
											</a>
										</div>
									</div>
								</form>
							</div>
							<p class="tx-note">[商品名稱]及[商品代號]使用模糊查詢。</p>
						</div>
					</div>

					<div id="result">
						<div class="card card-table mb-3">
							<div class="card-header">
								<h4>查詢結果</h4>
								<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
							</div>
							<div class="table-responsive">
								<table class="table table-RWD table-bordered">
									<thead>
										<tr>
											<th width="5%">選取</th>
											<th width="10%">商品代號</th>
											<th width="14%">中文名稱</th>
											<th width="14%">英文名稱</th>
											<th width="12%">商品次分類</th>
											<th width="10%">風險屬性</th>
											<th width="10%">商品幣別</th>
											<th width="12%">是否可申購</th>
											<th width="13%">商品到期日</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="(item, index) in pageData.content" :key="index">
											<td class="text-center" data-th="">
												<input
													name="pro_code"
													class="form-check-input"
													type="checkbox"
													:checked="isSelected(item)"
													:value="item"
													v-model="selProducts"
												/>
											</td>
											<td data-th="商品代號">{{ item.bankProCode }}</td>
											<td data-th="中文名稱">
												<a class="link-underline">{{ item.proName }}</a>
											</td>
											<td data-th="英文名稱">{{ item.engProName }}</td>
											<td data-th="商品次分類">{{ item.proSubType }}</td>
											<td data-th="風險屬性">{{ item.riskName }}</td>
											<td data-th="商品幣別">{{ item.curName }}</td>
											<td data-th="是否可申購">{{ item.buyYn }}</td>
											<td data-th="商品到期日">{{ item.expireDt }}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div class="modal-footer" id="btn">
							<div class="text-alignRight">
								<input name="btnClose" class="btn btn-white" type="button" value="確認" id="proBtn" @click="chkSelProducts()" />
								<button @click.prevent="props.close()" type="button" class="btn btn-white">關閉</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>

	<vue-modal :is-open="isNoticeOpenModal" :before-close="closeNoticeModal">
		<template v-slot:content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">發行機構查詢</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()"><i class="bi bi-arrows-fullscreen"></i></button>
						<button type="button" class="btn-close" @click.prevent="props.close()"></button>
					</div>
					<div class="modal-body">
						<div id="noticeResult">
							<div class="caption">發行機構清單</div>
							<div class="card card-table mb-3">
								<vue-pagination :pageable="noticePageData" :goto-page="gotoNoticePage"></vue-pagination>
								<div class="table-responsive">
									<table class="table table-RWD table-hover table-bordered table-padding">
										<thead>
											<tr>
												<th width="10%">選取</th>
												<th width="30%">資產類別</th>
												<th width="30%">發行機構代號</th>
												<th width="30%">發行機構中文名稱</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="(item, index) in noticePageData.content" :key="index">
												<td class="text-center" data-th="">
													<input
														name="pro_code"
														class="form-check-input"
														type="checkbox"
														:checked="isNoticeSelected(item)"
														:value="item"
														v-model="selNotices"
													/>
												</td>
												<td data-th="資產類別">{{ item.pfcatCode }}</td>
												<td data-th="發行機構代號">{{ item.issuerCode }}</td>
												<td data-th="發行機構中文名稱">{{ item.issuerName }}</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
					<div class="modal-footer" id="appointmentFooter1">
						<input name="btnClose" class="btn btn-white" type="button" value="確認" id="noticeBtn" @click="chkSelNotice()" />
						<button @click.prevent="props.close()" type="button" class="btn btn-white">關閉</button>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
	<!-- Modal 1 End -->
</template>
<script>
import { Field, Form } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import VuePagination from '@/views/components/pagination.vue';
import _ from 'lodash';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueModal,
		VuePagination
	},
	data: function () {
		return {
			//下拉選單
			selPriority: [],
			selShowCus: [],
			selTypeMessage: [],
			selProMessage: [],
			selNewsType: [],
			selNewsRelProType: [],
			selNewRelFundType: [],
			selMainCat: [],
			selSubCat: [],
			selOutlookMainCat: [],
			selOutlookSubCat: [],
			selProCat: [],
			productList: [],
			noticeList: [],

			productInfo: [],
			selProducts: [],
			selNotices: [],
			selProItem: [],
			selReportItem: [],
			selNewRelProItem: [],
			selNewRelFundItem: [],
			fileObject: [],
			fileInfo: [],
			selDoc: {},

			showModal: false,

			selectedTabCode: null,
			proCode: '',
			proName: '',
			proType: '',
			docId: null,
			docCat: null,
			docName: null,
			validDate: null,
			expireDate: null,
			priority: null,
			showCusYn: null,
			docDesc: null,
			productYn: [], // 產品
			holdCode: null,
			subsCode: null,
			mktYn: [], // 行銷活動
			mainCat: null,
			subTypeCode: null,
			newsYn: [],

			//分頁元件
			pageable: {
				page: 0,
				size: 10,
				sort: 'PRO_CODE',
				direction: 'ASC'
			},
			//查詢結果
			pageData: {
				content: {}
			},
			//分頁元件
			noticePageable: {
				page: 0,
				size: 10,
				sort: 'ISSUER_CODE',
				direction: 'ASC'
			},
			noticePageData: {
				content: {}
			},
			//檔案處裡
			validExts: ['doc', 'docx', 'pdf', 'xlsx', 'txt'],
			symbols: ['/', ':', '*', '?', '"', '<', '>', '|'],
			tempFile: null,
			uploadFiles: [],
			files: [],
			maxFileCount: 3,
			fileCnt: 0,
			errors: {
				uploadFile: null
			},
			//Modal
			isOpenModal: null,
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',
			buttonClass: 'btn-expand',

			researchYn: [],
			researchMainTypeCode: null,
			researchSubTypeCode: null,

			isNoticeOpenModal: null,
			noticeYn: [], // 發行機構
			noticeMainTypeCode: null,
			noticeSubTypeCode: null,

			forAllCusYn: null,
			forHaveProCusYn: null,
			forCashRelCusYn: null,

			proSectors: [],
			proGeoFocus: [],

			checkedProSectors: [],
			checkedProGeoFocus: []
		};
	},
	watch: {
		selectedTabCode(newVal) {
			if (newVal) {
				var self = this;
				this.gotoPage(0);
			}
		},
		selProItem: {
			handler(newVal) {
				var self = this;
				self.holdCode = _.some(newVal, (item) => typeof item === 'string' && item === 'H') ? 'H' : '';
				self.subsCode = _.some(newVal, (item) => typeof item === 'string' && item === 'S') ? 'S' : '';
			},
			deep: true
		},
		mainCat: async function (newVal, oldVal) {
			var self = this;
			if (newVal) {
				const res = await self.getMainCatType('MKTEVENT', newVal);
				self.selSubCat = res;
			} else {
				self.subTypeCode = null;
			}
		},
		researchMainTypeCode: async function (newVal, oldVal) {
			var self = this;
			if (newVal) {
				const res = await self.getMainCatType('MKTOUTLOOK', newVal);
				self.selOutlookSubCat = res;
			} else {
				self.selOutlookSubCat = null;
			}
		}
	},
	mounted: async function () {
		var self = this;
		self.getProCat();
		self.docId = self.$route.params.docId;
		self.docCat = self.$route.params.docCat;
		if (!_.isNil(self.docId)) {
			self.getSelDoc(self.docId, self.docCat);
		}
		self.selPriority = await self.getAdmCodeDetail('DOC_PRIORITY');
		self.selShowCus = await self.getAdmCodeDetail('DOC_SHOWCUS_YN');
		self.selTypeMessage = await self.getAdmCodeDetail('DOC_TYPE_MESSAGE');
		self.selProMessage = await self.getAdmCodeDetail('DOC_P_MESSAGE');
		self.selNewsType = await self.getAdmCodeDetail('DOC_NEWS_TYPE');
		self.selNewsRelProType = await self.getAdmCodeDetail('DOC_NEWS_REL_PRO_TYPE');
		self.selNewRelFundType = await self.getAdmCodeDetail('DOC_NEWS_REL_FUND_TYPE');
		self.getMainCat();
		self.getOutlookMainCat();
		self.getProSectors();
		self.getProGeoFocus();
	},
	computed: {
		userInfo: function () {
			return self.$store.getters['userInfo/info'];
		},
		fileCntObj: function () {
			return {
				fieldCnt: this.fileCnt,
				maxFileCount: this.maxFileCount
			};
		}
	},
	methods: {
		async getProCat() {
			var self = this;
			const ret = await self.$api.getProCatApi();
			self.selProCat = ret.data;
		},
		getMainCat: async function () {
			var self = this;
			const ret = await self.$api.getMainDocTypeApi({
				catCode: 'MKTEVENT'
			});
			if (ret.data) {
				self.selMainCat = ret.data;
			}
		},
		getOutlookMainCat: async function () {
			var self = this;
			const ret = await self.$api.getMainDocTypeApi({
				catCode: 'MKTOUTLOOK'
			});
			if (ret.data) {
				self.selOutlookMainCat = ret.data;
			}
		},
		getMainCatType: async function (catCode, mainTypeCode) {
			var self = this;
			var data = { catCode: catCode, mainTypeCode: mainTypeCode };
			let reqData = _.omitBy(data, (value) => _.isNil(value) || value === '');
			const ret = await self.$api.getMainDocTypeApi(reqData);
			return ret.data;
		},
		getAdmCodeDetail: async function (codeType) {
			var self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: codeType
			});
			return ret.data;
		},
		getProQuickSearch: function (proName, proCode, proType) {
			var self = this;
			self.getPageData(0, proName, proCode, proType);
		},
		getPageData: async function (page, proName, proCode, proType) {
			var self = this;
			var url = _.toPageUrl('', page, self.pageable);
			const ret = await self.$api.getDocProPageData(
				{
					proName: proName,
					proCode: proCode,
					proType: proType
				},
				url
			);
			self.pageData = ret.data;
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getNoticePageData: async function (page) {
			var self = this;
			var url = _.toPageUrl('', page, self.noticePageable);
			const ret = await self.$api.getDocProNoticePageData(url);

			self.noticePageData = ret.data;
		},
		gotoNoticePage: function (page) {
			this.noticePageable.page = page;
			this.getNoticePageData(page);
		},
		getSelDoc: async function (docId, docCat) {
			var self = this;
			const ret = await self.$api.getMainDocTypeApi({
				docCat: docCat,
				docId: docId
			});
			if (!ret.data?.length > 0) return;
			self.selDoc = ret.data[0];
			self.docName = self.selDoc.docName;
			self.validDate = moment(self.selDoc.validDt).format('YYYY-MM-DD');
			self.expireDate = moment(self.selDoc.expireDt).format('YYYY-MM-DD');
			self.priority = self.selDoc.priority;
			self.showCusYn = self.selDoc.showCusYn;
			self.docDesc = self.selDoc.docDesc;
			self.productList = self.selDoc.productInfo;
			self.holdName = self.selDoc.holdName;
			self.subsName = self.selDoc.subsName;
			self.mainCat = self.selDoc.gdmMainTypeCode;
			self.subTypeCode = self.selDoc.gdmSubTypeCode;
			if (!_.isEmpty(self.selDoc.gdmMainTypeCode)) {
				self.mktYn.push('M');
			}
			if (!_.isEmpty(self.selDoc.gmoMainTypeCode)) {
				self.researchYn.push('R');
			}
			self.researchMainTypeCode = self.selDoc.gmoMainTypeCode;
			self.researchMainTypeCode = self.selDoc.gmoSubTypeCode;
			if (!_.isEmpty(self.selDoc.issuerInfo)) {
				self.noticeYn.push('I');
			}

			_.forEach(self.selDoc.issuerInfo, function (issuer) {
				self.noticeList.push(_.pick(issuer, ['issuerCode', 'issuerName', 'pfcatCode']));
			});

			if (!_.isEmpty(self.selDoc.newsAllCusYn)) {
				self.newsYn.push('N');
				self.selReportItem.push('A');
			}

			if (!_.isEmpty(self.selDoc.newsConditionInfo)) {
				let geoYn = false;
				let secYn = false;
				let fndYn = false;
				_.forEach(self.selDoc.newsConditionInfo, function (newsCondition) {
					if (_.isEqual(newsCondition.condType, 'GEO')) {
						geoYn = true;
						self.checkedProGeoFocus.push(newsCondition.condMajorCode);
					}
					if (_.isEqual(newsCondition.condType, 'SEC')) {
						secYn = true;
						self.checkedProSectors.push(newsCondition.condMajorCode);
					}
					if (_.isEqual(newsCondition.condType, 'FND')) {
						fndYn = true;
						self.selNewRelFundItem.push(newsCondition.condMajorCode);
					}
				});
				if (geoYn == true) {
					self.selNewRelProItem.push('GEO');
					if (!self.selReportItem.includes('P')) {
						self.selReportItem.push('P');
					}
				}
				if (secYn == true) {
					self.selNewRelProItem.push('SEC');
					if (!self.selReportItem.includes('P')) {
						self.selReportItem.push('P');
					}
				}
				if (fndYn == true) {
					self.selReportItem.push('F');
				}
			}

			self.files = _.clone(self.selDoc.fileInfo);
			_.forEach(self.files, function (file) {
				file.fileNo = file.docFileId;
			});
			if (self.holdName == '目前持有該商品客戶') {
				self.selProItem.push('H');
				$('#H').prop('checked', true);
			}
			if (self.subsName == '有訂閱此商品服務的客戶') {
				self.selProItem.push('S');
				$('#S').prop('checked', true);
			}
			self.addOldPro();
		},
		chkSelNotice: function () {
			var self = this;
			if (_.isEmpty(self.selNotices)) {
				self.$swal.fire({
					icon: 'error',
					text: '請選擇機構',
					showCloseButton: true,
					confirmButtonText: '確認',
					buttonStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}

			if (self.selNotices) {
				_.forEach(self.selNotices, function (item) {
					if (!_.find(self.noticeList, { issuerCode: item.issuerCode })) {
						self.noticeList.push(_.pick(item, ['issuerCode', 'issuerName', 'pfcatCode']));
					}
				});
			}

			// self.addNewPro(self.productList);
			self.$swal
				.fire({
					icon: 'success',
					text: '新增成功',
					showCloseButton: true,
					confirmButtonText: '確認',
					buttonsStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-success'
					}
				})
				.then(function (ret) {
					self.isNoticeOpenModal = false;
				});
		},
		chkSelProducts: function () {
			var self = this;
			if (_.isEmpty(self.selProducts)) {
				self.$swal.fire({
					icon: 'error',
					text: '請選擇商品',
					showCloseButton: true,
					confirmButtonText: '確認',
					buttonStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}

			if (self.selProducts) {
				_.forEach(self.selProducts, function (item) {
					if (!_.find(self.productList, { proCode: item.proCode })) {
						self.productList.push(_.pick(item, ['proCode', 'proName']));
					}
				});
			}

			self.addNewPro(self.productList);
			self.$swal
				.fire({
					icon: 'success',
					text: '新增成功',
					showCloseButton: true,
					confirmButtonText: '確認',
					buttonsStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-success'
					}
				})
				.then(function (ret) {
					self.isOpenModal = false;
				});
		},
		deleteProduct: function (proCode) {
			var self = this;
			self.$swal
				.fire({
					icon: 'warning',
					text: '是否要刪除此商品？',
					showCloseButton: true,
					confirmButtonText: '確認',
					showCancelButton: true,
					cancelButtonText: '取消',
					buttonsStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-danger',
						cancelButton: 'btn btn-secondary'
					}
				})
				.then(function (result) {
					if (result.isConfirmed) {
						_.remove(self.productList, (item) => item.proCode === proCode);
						_.remove(self.selProducts, (item) => item.proCode === proCode);
						self.$swal.fire({
							icon: 'success',
							text: '刪除成功',
							showCloseButton: true,
							confirmButtonText: '確認',
							buttonsStyling: false, // remove default button style
							customClass: {
								confirmButton: 'btn btn-success'
							}
						});
					}
				});
		},
		deleteNotice: function (issuerCode) {
			var self = this;
			self.$swal
				.fire({
					icon: 'warning',
					text: '是否要刪除此機構？',
					showCloseButton: true,
					confirmButtonText: '確認',
					showCancelButton: true,
					cancelButtonText: '取消',
					buttonsStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-danger',
						cancelButton: 'btn btn-secondary'
					}
				})
				.then(function (result) {
					if (result.isConfirmed) {
						_.remove(self.noticeList, (item) => item.issuerCode === issuerCode);
						_.remove(self.selNotices, (item) => item.issuerCode === issuerCode);
						self.$swal.fire({
							icon: 'success',
							text: '刪除成功',
							showCloseButton: true,
							confirmButtonText: '確認',
							buttonsStyling: false, // remove default button style
							customClass: {
								confirmButton: 'btn btn-success'
							}
						});
					}
				});
		},
		addOldPro: function () {
			var self = this;
			_.forEach(self.productList, function (item) {
				self.productInfo.push({
					oldPro: item.proCode
				});
			});
		},
		addNewPro: function (newProductList) {
			var self = this;
			_.forEach(newProductList, function (item) {
				var exists =
					_.find(self.productInfo, function (info) {
						return info.newPro === item.proCode || info.oldPro === item.proCode;
					}) !== undefined;
				if (!exists) {
					self.productInfo.push({
						newPro: item.proCode
					});
				}
			});
		},
		addNewNotice: function (newNoticeLis) {},
		isSelected: function (item) {
			var self = this;
			return _.some(self.productList, { proCode: item.proCode });
		},
		isNoticeSelected: function (item) {
			var self = this;
			return _.some(self.noticeList, { issuerCode: item.issuerCode });
		},
		reportItemDisable: function (item) {
			var self = this;
			if (_.isEqual(item.codeValue, 'A')) {
				self.selNewsType.forEach(function (newsItem) {
					if (!_.isEqual(newsItem.codeValue, 'A') && self.selReportItem.includes('A')) {
						newsItem.disable = true;
					} else {
						newsItem.disable = false;
					}
					if (self.selReportItem.includes('F') || self.selReportItem.includes('P')) {
						let index = self.selReportItem.indexOf('F');
						if (index !== -1) {
							self.selReportItem.splice(index, 1);
						}
						index = self.selReportItem.indexOf('P');
						if (index !== -1) {
							self.selReportItem.splice(index, 1);
						}
						self.selNewRelFundType = [];
						self.selNewsRelProType = [];
						self.proSectors = [];
						self.proGeoFocus = [];
					}
				});
			}
		},
		saveDocument: function (docId) {
			var self = this;
			self.$refs.queryForm.validate().then(function (pass) {
				if (pass.valid) {
					if (!_.isNil(self.validDate) && !_.isNil(self.expireDate)) {
						let validDateObj = new Date(self.validDate);
						let expireDateObj = new Date(self.expireDate);
						if (expireDateObj < validDateObj) {
							self.$swal.fire({
								icon: 'error',
								text: '到期日不可早於生效日',
								showCloseButton: true,
								confirmButtonText: '確認',
								buttonStyling: false, // remove default button style
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							});
							return;
						}
					}
					if (
						_.isEmpty(self.mktYn) &&
						_.isEmpty(self.productYn) &&
						_.isEmpty(self.researchYn) &&
						_.isEmpty(self.noticeYn) &&
						_.isEmpty(self.newsYn)
					) {
						self.$swal.fire({
							icon: 'error',
							text: '請勾選相關設定（至少一項）',
							showCloseButton: true,
							confirmButtonText: '確認',
							buttonStyling: false, // remove default button style
							customClass: {
								confirmButton: 'btn btn-danger'
							}
						});
						return;
					}
					if (self.productYn.includes('P')) {
						if (_.isEmpty(self.productList)) {
							self.$swal.fire({
								icon: 'error',
								text: '請選擇產品（至少一項）',
								showCloseButton: true,
								confirmButtonText: '確認',
								buttonStyling: false, // remove default button style
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							});
							return;
						}

						if (_.isEmpty(self.selProItem)) {
							self.$swal.fire({
								icon: 'error',
								text: '請勾選通知對象（至少一項）',
								showCloseButton: true,
								confirmButtonText: '確認',
								buttonStyling: false, // remove default button style
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							});
							return;
						}
					}
					if (self.mktYn.includes('M')) {
						if (_.isNil(self.mainCat) || _.isNil(self.subTypeCode)) {
							self.$swal.fire({
								icon: 'error',
								text: '請選擇主分類/次分類',
								showCloseButton: true,
								confirmButtonText: '確認',
								buttonStyling: false, // remove default button style
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							});
							return;
						}
					}
					let errMsg = '';
					let validFlag = false;
					if (self.researchYn.includes('R')) {
						if (_.isNil(self.researchMainTypeCode) || _.isNil(self.researchSubTypeCode)) {
							validFlag = true;
							errMsg = '請選擇研究報告主分類/次分類';
						}
					}
					if (self.noticeYn.includes('I')) {
						if (_.isEmpty(self.noticeList)) {
							validFlag = true;
							errMsg = '至少選擇一筆發行機構';
						}
					}

					if (self.newsYn.includes('N')) {
						if (_.isEmpty(self.selReportItem)) {
							validFlag = true;
							errMsg = '請勾選通知對象（至少一項）';
						}
						if (self.selReportItem.includes('P')) {
							if (_.isEmpty(self.selNewRelProItem)) {
								validFlag = true;
								errMsg = '請勾選投資標的或投資地區';
							}
							if (self.selNewRelProItem.includes('SEC')) {
								if (_.isEmpty(self.checkedProSectors)) {
									validFlag = true;
									errMsg = '至少勾選一筆相關投資標的';
								}
							}
							if (self.selNewRelProItem.includes('GEO')) {
								if (_.isEmpty(self.checkedProGeoFocus)) {
									validFlag = true;
									errMsg = '至少勾選一筆相關投資地區';
								}
							}
						}
						if (self.selReportItem.includes('F')) {
							if (_.isEmpty(self.selNewRelFundItem)) {
								validFlag = true;
								errMsg = '至少勾選本國基金或外國基金';
							}
						}
					}
					if (validFlag == true) {
						self.$swal.fire({
							icon: 'error',
							text: errMsg,
							showCloseButton: true,
							confirmButtonText: '確認',
							buttonStyling: false, // remove default button style
							customClass: {
								confirmButton: 'btn btn-danger'
							}
						});
						return;
					}
					self.postOrPatchDocument(docId);
				}
			});
			const errField = Object.keys(this.$refs.queryForm.errors)[0];
			if (errField) {
				document.querySelector(`[name="${errField}"]`).focus();
			}
		},
		postOrPatchDocument: async function (docId) {
			var self = this;
			var proCodes = _.map(self.productList, 'proCode');
			var data = {};
			data.docId = self.docId;
			data.docName = self.docName;
			data.validDate = _.formatDate(self.validDate);
			data.expireDate = _.formatDate(self.expireDate);
			data.priority = self.priority;
			data.showCusYn = self.showCusYn;
			data.docDesc = self.docDesc;
			data.productYn = self.productYn.includes('P') ? 'Y' : 'N';
			data.productList = proCodes;
			data.holdCode = self.holdCode;
			data.subsCode = self.subsCode;
			data.mktYn = self.mktYn.includes('M') ? 'Y' : 'N';
			data.mainTypeCode = self.mainCat;
			data.subTypeCode = self.subTypeCode;
			data.mktOutLookYn = self.researchYn.includes('R') ? 'Y' : 'N';
			data.outLookMainTypeCode = self.researchMainTypeCode;
			data.outLookSubTypeCode = self.researchSubTypeCode;
			data.issuerYn = self.noticeYn.includes('I') ? 'Y' : 'N';
			data.issuerList = self.noticeList;
			data.newsYn = self.newsYn.includes('N') ? 'Y' : 'N';
			data.newsAllCusYn = self.selReportItem.includes('A') ? 'Y' : 'N';
			data.newsFnd = self.selReportItem.includes('F') ? 'Y' : 'N';
			data.newsSecList = self.checkedProSectors;
			data.newsGeoList = self.checkedProGeoFocus;

			data.fileInfo = _.reduce(
				self.files,
				function (result, file) {
					var msgAppendixReq = _.cloneDeep(file);
					msgAppendixReq.docFileId = file.fileNo;
					result.push(msgAppendixReq);
					return result;
				},
				[]
			);
			data.productInfo = self.productInfo;

			var formData = new FormData();
			var jsonString = JSON.stringify(data);
			formData.append('docInfo', new Blob([jsonString], { type: 'application/json' }));
			_.forEach(self.files, function (fileInfo) {
				if (fileInfo.file) {
					formData.append('fileNo', fileInfo.fileNo);
					formData.append('fileObject', fileInfo.file);
				}
			});
			var apiMethod = 'POST';
			var successText = '新增成功';
			if (docId != null) {
				// 編輯
				apiMethod = 'PATCH';
				successText = '編輯成功';
			}

			const ret = await self.$api.postOrPatchDoc({
				httpMethod: apiMethod,
				formData: formData
			});
			self.$swal
				.fire({
					icon: 'success',
					text: successText,
					showCloseButton: true,
					confirmButtonText: '確認',
					buttonsStyling: false,
					customClass: {
						confirmButton: 'btn btn-success'
					}
				})
				.then(function (ret) {
					self.$router.push('/gen/docPro');
				});
		},
		//檔案處裡
		handleChange: function (event) {
			var self = this;
			self.fileTemp = event.target.files[0];
			self.fileCnt = self.files.length;
		},
		addFile: function () {
			var self = this;
			$.when(self.$refs.fileForm.validate()).then(function (result) {
				if (result.valid) {
					if (self.files.length >= self.maxFileCount) {
						return;
					}

					var fileInfo = {
						fileNo: self.generatorId('file'),
						groupId: null,
						showName: self.fileTemp.name,
						fileName: null,
						contentType: self.fileTemp.contentType,
						filePath: null,
						file: self.fileTemp
					};
					self.files.push(fileInfo);
					self.$refs.fileForm.resetForm();
					self.$refs.uploadFile.$el.value = null;
				}
			});
		},
		previewFile: function (targetFileId) {
			var self = this;
			var index = self.files.findIndex((f) => f.fileNo === targetFileId);
			var fileInfo = self.files[index];
			var url;
			// 預覽待上傳檔案
			if (fileInfo.file) {
				url = URL.createObjectURL(fileInfo.file);
				var previewWindow = window.open(url, '_blank');
				previewWindow.document.title = fileInfo.showName;
				previewWindow.addEventListener('beforeunload', () => {
					URL.revokeObjectURL(url);
				});
				// 預覽伺服器檔案
			} else {
				self.$api.previewServerGenOtherFile({
					fileId: targetFileId,
					fileTitle: fileInfo.showName
				});
			}
		},
		deleteFile: function (targetFileId) {
			var self = this;
			var index = self.files.findIndex((f) => f.fileId === targetFileId);
			if (index != -1) {
				self.files.splice(index, 1);
			}
		},
		generatorId: function (name) {
			return name + '-' + _.now() + _.random(0, 99);
		},
		//Modal相關
		changeModalSize: function () {
			var self = this;
			if (self.modalClass === 'modal-dialog modal-lg modal-dialog-centered') {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered fullscreen';
				self.buttonClass = 'btn-expand mini';
			} else {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered';
				self.buttonClass = 'btn-expand';
			}
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		openModal: function () {
			var self = this;
			self.isOpenModal = true;
		},
		closeNoticeModal: function () {
			var self = this;
			self.isNoticeOpenModal = false;
		},
		openNoticeModal: function () {
			var self = this;
			self.isNoticeOpenModal = true;
			this.getNoticePageData(0);
		},
		getProSectors: async function () {
			var self = this;
			const ret = await self.$api.getProSectorsApi();
			self.proSectors = ret.data;
		},
		getProGeoFocus: async function () {
			var self = this;
			const ret = await self.$api.getProGeoFocusApi();
			self.proGeoFocus = ret.data;
		}
	}
};
</script>
