import request from '@/utils/request';

const apiPath = import.meta.env.VITE_API_URL_V1;

// contentHeader.vue
export function getGenInternalMsgApi(forceYn) {
	return request({
		url: apiPath + '/gen/genInternalMsg',
		method: 'get',
		params: {
			forceYn
		}
	});
}
export function getMessageListApi(msgType) {
	return request({
		url: apiPath + '/gen/genNewsMessage',
		method: 'get',
		params: {
			msgType
		}
	});
}

export function getSwitchIdentityListApi(userCode) {
	return request({
		url: apiPath + '/adm/switchIdentity',
		method: 'get',
		params: {
			userCode
		}
	});
}

export function postChangeRole<PERSON>pi(userCode, user, posCode) {
	return request({
		url: apiPath + '/login/switch',
		method: 'post',
		data: {
			userCode,
			user,
			posCode
		}
	});
}

// msg.vue
// getGenInternalMsg() 呼叫了與 contentHeader.vue 的 getGenInternalMsgApi 一樣的Api
