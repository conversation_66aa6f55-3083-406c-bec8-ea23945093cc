import request from '@/utils/request';
const apiPath = import.meta.env.VITE_API_URL_V1;

// 商品資料查詢/維護-商品類型
export function getProPfcatsMenuApi() {
	return request({
		url: apiPath + '/pro/showProPfcatsMenu',
		method: 'get'
	});
}

// 商品資料查詢 - 一般查詢/查詢結果
export function searchProductsApi(
	{ assetcatCode, pfcatCode, bankProCode, proName, riskCodes, curCodes, intFreqUnitType, principalGuarYn, isinCode, source },
	queryString
) {
	return request({
		url: apiPath + '/pro/search/searchProducts' + queryString,
		method: 'get',
		params: {
			assetcatCode,
			pfcatCode,
			bankProCode,
			proName,
			riskCodes: riskCodes.join(','),
			curCodes: curCodes.join(','),
			intFreqUnitType,
			principalGuarYn,
			isinCode,
			source
		}
	});
}

// 商品資料查詢 - 快速查詢/查詢結果
export function fastSearchProductsApi({ fastCode, rangeType, rangeFixed, perfTime, rowNumber, queryString }) {
	return request({
		url: apiPath + '/pro/search/fastSearchProducts' + queryString,
		method: 'get',
		params: {
			fastCode,
			rangeType,
			rangeFixed,
			perfTime,
			rowNumber
		}
	});
}

// 商品資料查詢 - 加入觀察
export function addFavoriteApi({ proCode, pfcatCode }) {
	return request({
		url: apiPath + '/pro/proFavorites',
		method: 'post',
		data: {
			proCode,
			pfcatCode
		}
	});
}

// 商品資料查詢 - 取消觀察/刪除我的最愛
export function deleteFavoriteApi({ proCode }) {
	return request({
		url: apiPath + '/pro/proFavorites',
		method: 'delete',
		params: {
			proCode
		}
	});
}

// 取得檔案預覽資訊
export function downloadOtherFileApi({ fileId }) {
	self.$api.downloadGenOtherFileApi({ fileId: fileId });
}

// 風險等級選單
export function getRiskMenuApi() {
	return request({
		url: apiPath + '/pro/riskMenu',
		method: 'get'
	});
}

// 取得資產類別
export function getAssetcatsMenuApi() {
	return request({
		url: apiPath + '/pro/proAssetcatsMenu',
		method: 'get'
	});
}
// 取得快速篩選選單
export function getFastFilterMenuApi() {
	return request({
		url: apiPath + '/pro/allFastFilterMenu',
		method: 'get'
	});
}

// 取得顯示區間
export function getTimeRangeMenuApi() {
	return request({
		url: apiPath + '/pro/ins/searchTimeRangeMenu',
		method: 'get'
	});
}
// 取得顯示資料筆數
export function getRowNumerMenuApi() {
	return request({
		url: apiPath + '/pro/ins/searchRowNumerMenu',
		method: 'get'
	});
}

// 取得標的績效
export function getPerfMenuApi() {
	return request({
		url: apiPath + '/pro/perfMenu',
		method: 'get'
	});
}

// 配息頻率選單
export function getIntFreqUnitTypeMenuApi() {
	return request({
		url: apiPath + '/pro/intFreqUnitTypeMenu',
		method: 'get'
	});
}

// 幣別-常用幣別選單
export function groupProCurrenciesMenuApi() {
	return request({
		url: apiPath + '/pro/groupProCurrenciesMenu',
		method: 'get'
	});
}

// 信託分析ETF-績效表現(歷史績效走勢圖)-顯示區間列表
export function getProPriceRangeMenuApi() {
	return request({
		url: apiPath + '/pro/etf/priceTimeRangeMenu',
		method: 'get'
	});
}

// 商品資料維護-查詢結果
export function editProductsApi({ pfcatCode, principalGuarYn, bankProCode, proName, riskCode, intFreqUnitType, curCodes }, pageable) {
	return request({
		url: apiPath + '/pro/search/editProducts',
		method: 'get',
		params: {
			pfcatCode,
			principalGuarYn,
			bankProCode,
			proName,
			riskCode,
			intFreqUnitType,
			curCodes,
			...pageable
		}
	});
}

// pro 檔案檢視
export function downloadProFileApi({ proFileId, eventId }) {
	return request({
		url: apiPath + '/pro/proFile',
		method: 'get',
		responseType: 'blob', // 重要：告訴 axios 這是下載檔案
		params: {
			// 用 params 傳 GET 查詢參數
			proFileId,
			eventId
		}
	});
}

// 商品資料查詢(全商品)-商品檢視-商品基本資料
export function getProductInfoApi({ proCode, pfcatCode }) {
	return request({
		url: apiPath + '/pro/productInfo',
		method: 'get',
		params: {
			proCode,
			pfcatCode
		}
	});
}

// 商品資料維護-傳送主管審核
export function patchProductApi(formData) {
	return request({
		url: apiPath + '/pro/product',
		method: 'patch',
		data: formData
	});
}

// 商品資料審核-商品檢視
export function getProductLogApi({ eventId }) {
	return request({
		url: apiPath + '/pro/productLog',
		method: 'get',
		params: {
			eventId
		}
	});
}

// 商品資料查詢(全商品)-商品檢視-商品附加資料(全部)
export function getProductsCommInfo({ proCode, pfcatCode }) {
	return request({
		url: apiPath + '/pro/productsCommInfo',
		method: 'get',
		params: {
			proCode,
			pfcatCode
		}
	});
}

// 發行機構選單
export function getGroupIssuersMenuApi({ pfcatCode }) {
	return request({
		url: apiPath + '/pro/issuersMenuByPfcatCode',
		method: 'GET',
		params: {
			pfcatCode
		}
	});
}

export function getBondPriceAnaApi({ proCodes, freqType, freqFixed }) {
	return request({
		url: apiPath + '/pro/bond/bondPriceAna',
		method: 'get',
		params: {
			proCodes: proCodes.join(','),
			freqType,
			freqFixed
		}
	});
}

// 取得基金類型選項
export function getProTypeListApi({ pfcatCode }) {
	return request({
		url: apiPath + '/pro/proTypeList',
		method: 'get',
		params: {
			pfcatCode
		}
	});
}

// 取得快速篩選條件選項
export function getFundFastMenuApi() {
	return request({
		url: apiPath + '/pro/fund/fundFastFilterMenu',
		method: 'get'
	});
}

// 進階搜尋 基金類別選擇全資料庫基金=>計價幣別選單
export function getCurMenuApi() {
	return request({
		url: apiPath + '/pro/groupProCurrenciesMenu',
		method: 'get'
	});
}

// 基金類別下拉
export function getInvestmentTypeMenuApi({ local }) {
	return request({
		url: apiPath + '/pro/proInvestmentType',
		method: 'get',
		params: {
			local
		}
	});
}

// 投資地區下拉
export function getGeoFocusMenuApi({ local }) {
	return request({
		url: apiPath + '/pro/proGeoFocus',
		method: 'get',
		params: {
			local
		}
	});
}

export function getGlobalClassCodeMenuApi() {
	return request({
		url: apiPath + '/pro/fund/globalClassCodeMenu',
		method: 'get'
	});
}

export function getGlobalClassCodeOtherMenuApi() {
	return request({
		url: apiPath + '/pro/fund/globalClassCodeOtherMenu',
		method: 'get'
	});
}

export function getLocalClassMenuApi() {
	return request({
		url: apiPath + '/pro/fund/localClassCodeMenu',
		method: 'get'
	});
}

// 進階搜尋 基金公司
export function getLocalFundCompanies() {
	return request({
		url: apiPath + '/pro/fund/localFundCompanies',
		method: 'get'
	});
}

export function getForeignFundCompanies() {
	return request({
		url: apiPath + '/pro/fund/foreignFundCompanies',
		method: 'get'
	});
}

// 取得商品資料查詢(信託-基金)-風險/績效選單
export function getProWeightedTypeMenuApi() {
	return request({
		url: apiPath + '/pro/fund/proWeightedTypeMenu',
		method: 'get'
	});
}

export function getFundProducts(
	{
		bankProCode,
		proName,
		curCodes,
		riskCodes,
		protypeCode,
		localYn,
		intFreqUnitType,
		intRateRank,
		backEndLoadYn,
		isinCode,
		lipperPointer,
		lipperRank,
		compCode,
		minDValue,
		maxDValue
	},
	queryString
) {
	return request({
		url: apiPath + '/pro/fund/fundProducts' + queryString,
		method: 'get',
		params: {
			bankProCode,
			proName,
			curCodes: curCodes.join(','),
			riskCodes,
			protypeCode,
			localYn,
			intFreqUnitType,
			intRateRank,
			backEndLoadYn,
			isinCode,
			lipperPointer,
			lipperRank,
			compCode,
			minDValue,
			maxDValue
		}
	});
}

export function getFastPageDataApi({ filterCodeValue, timeRangeType, timeRangeFixed, rowNumberFixed, perfTimeCode, lipperClassCodes }, queryString) {
	return request({
		url: apiPath + '/pro/fund/fundProductsFilterQuery' + queryString,
		method: 'get',
		params: {
			filterCodeValue,
			timeRangeType,
			timeRangeFixed,
			rowNumberFixed,
			perfTimeCode,
			lipperClassCodes
		}
	});
}

export function getRankPageDataApi({ lipperClassCodes, perfCurCode, rowNumberFixed, proSearchesMaps }) {
	return request({
		url: apiPath + '/pro/fund/fundProductsRank',
		method: 'post',
		data: {
			lipperClassCodes,
			perfCurCode,
			rowNumberFixed,
			proSearchesMaps
		}
	});
}

export function getProSearchesApi() {
	return request({
		url: apiPath + '/pro/fund/proSearches',
		method: 'get'
	});
}

// 歷史查詢條件刪除
export function deleteHistorySearchApi({ searchSeq }) {
	return request({
		url: apiPath + '/pro/fund/proSearches',
		method: 'delete',
		data: {
			searchSeq
		}
	});
}

// 加權條件篩選 儲存條件
export function postProSearchesApi({ searchName, memo, proSearchesMaps }) {
	return request({
		url: apiPath + '/pro/fund/proSearches',
		method: 'post',
		data: {
			searchName,
			memo,
			proSearchesMaps
		}
	});
}

// 信託-基金 進階搜尋
export function getAdvancePageDataApi(
	{ fundSaleType, assetStatPctCode, annualPerfType, perfRangeType, perfType, lipperPointer, rank, sharpeValueType, investRange, dcaRange },
	queryString
) {
	return request({
		url: apiPath + '/pro/fund/fundProductsAdvance' + queryString,
		method: 'get',
		params: {
			fundSaleType,
			assetStatPctCode,
			annualPerfType,
			perfRangeType,
			perfType,
			lipperPointer,
			rank,
			sharpeValueType,
			investRange,
			dcaRange
		}
	});
}

export function getBondIssuersMenuApi() {
	return request({
		url: apiPath + '/pro/groupIssuersMenu',
		method: 'get'
	});
}
// 選擇債券-發行機構選單
export function getBondIssuersApi() {
	return request({
		url: apiPath + '/pro/bond/allBondIssuers',
		method: 'get'
	});
}

// 取得保證機構選單
export function getBondGuaranteesApi() {
	return request({
		url: apiPath + '/pro/bond/allBondGuarantees',
		method: 'get'
	});
}

// 取得快速篩選條件選項
export function getBondFastMenuApi() {
	return request({
		url: apiPath + '/pro/bond/bondFastFilterMenu',
		method: 'get'
	});
}

// 取得債券商品列表
export function getBondProductsApi({
	page,
	size,
	sort,
	bankProCode,
	proName,
	proType,
	curCodes,
	riskCodes,
	intFreqUnitType,
	buyMin,
	invAccAmt,
	price,
	issuerCodes,
	buCode,
	parRateMin,
	parRateMax,
	isinCode,
	profInvestorYn,
	issuerType,
	invQuality,
	bondRateType,
	listingType,
	payAllocation,
	expireYieldMin,
	expireYieldMax,
	periodMin,
	periodMax,
	guarCodes
}) {
	return request({
		url: apiPath + '/pro/bond/bondProducts' + queryString,
		method: 'get',
		params: {
			page,
			size,
			sort,
			bankProCode,
			proName,
			proType,
			curCodes: curCodes.join(','),
			riskCodes,
			intFreqUnitType,
			buyMin,
			invAccAmt,
			price,
			buCode,
			parRateMin,
			parRateMax,
			isinCode,
			profInvestorYn,
			issuerType,
			invQuality,
			bondRateType,
			listingType,
			payAllocation,
			expireYieldMin,
			expireYieldMax,
			periodMin,
			periodMax,
			issuerCodes: issuerCodes.join(','),
			guarCodes: guarCodes.join(',')
		}
	});
}

// 取得快速篩選債券商品列表
export function getBondFastProductsApi(payload, queryString = '') {
	return request({
		url: apiPath + '/pro/bond/bondProductsFilterQuery' + queryString,
		method: 'get',
		params: payload
	});
}

// 刪除我的最愛
export function deleteBondFavoriteApi({ proCode }) {
	return request({
		url: apiPath + '/pro/proFavorites',
		method: 'delete',
		data: {
			proCode
		}
	});
}
export function getGlobalFundsApi({ lipperIds }) {
	return request({
		url: apiPath + '/pro/fund/globalFunds',
		method: 'get',
		params: {
			lipperIds
		}
	});
}

export function getFundInfo({ proCode }) {
	return request({
		url: apiPath + '/pro/fund/fundInfo/' + proCode,
		method: 'get',
		loading: false
	});
}

//歷史查詢條件 編輯
export function getProSearchesMap({ searchSeq }) {
	return request({
		url: apiPath + '/pro/fund/proSearchesMap/',
		method: 'get',
		loading: false,
		params: {
			searchSeq
		}
	});
}

export function getFundCompany({ proCode }) {
	return request({
		url: apiPath + '/pro/fund/fundCompany/' + proCode,
		method: 'get',
		loading: false
	});
}

export function getLipperScoreApi({ proCode }) {
	return request({
		url: apiPath + '/pro/fund/lipperScore/' + proCode,
		method: 'get',
		loading: false
	});
}

export function getFundInfoFundSizeLatest2Api({ proCode }) {
	return request({
		url: apiPath + '/pro/asset/monthEndTna/latest2/' + proCode,
		method: 'get',
		loading: false
	});
}

export function getTechsApi({ proCode, techCurrencyCode }) {
	return request({
		url: apiPath + '/pro/asset/tech/' + proCode,
		method: 'get',
		params: {
			techCurrencyCode
		},
		loading: false
	});
}

export function getPreMonthEndPriceApi({ prCode, beginDate, endDate }) {
	return request({
		url: apiPath + '/pro/fundInfo/priceHistory/' + prCode,
		method: 'get',
		params: {
			beginDate,
			endDate
		},
		loading: false
	});
}

// 刪除使用者各功能來源的商品名單
export function deleteProPotSearchRstApi() {
	return request({
		url: apiPath + '/pro/proPotSearchRst',
		method: 'delete'
	});
}

export function getSdMenu() {
	return request({
		url: apiPath + '/pro/sdMenu',
		method: 'get'
	});
}

export function getEtfFastMenuApi() {
	return request({
		url: apiPath + '/pro/etf/etfFastFilterMenu',
		method: 'get'
	});
}

export function getEtfProductsApi(
	{
		bankProCode,
		proName,
		curCodes,
		riskCodes,
		profInvestorYn,
		protypeCode,
		isinCode,
		sdRangeMin,
		sdRangeMax,
		isOtherSdRange,
		etfSizeMin,
		etfSizeMax,
		perfTimeCodeValue,
		perfMin,
		perfMax,
		issuerExchangeCodes,
		exchangeCodes
	},
	queryString
) {
	return request({
		url: apiPath + '/pro/etf/etfProducts' + queryString,
		method: 'get',
		params: {
			bankProCode,
			proName,
			curCodes,
			riskCodes,
			profInvestorYn,
			protypeCode,
			isinCode,
			sdRangeMin,
			sdRangeMax,
			isOtherSdRange,
			etfSizeMin,
			etfSizeMax,
			perfTimeCodeValue,
			perfMin,
			perfMax,
			issuerExchangeCodes,
			exchangeCodes: exchangeCodes.join(',')
		}
	});
}

export function getEtfFastPageDataApi({ filterCodeValue, timeRangeType, timeRangeFixed, rowNumberFixed, perfTimeCode }, queryString) {
	return request({
		url: apiPath + '/pro/etf/etfProductsFilterQuery' + queryString,
		method: 'get',
		params: {
			filterCodeValue,
			timeRangeType,
			timeRangeFixed,
			rowNumberFixed,
			perfTimeCode
		}
	});
}
export function getFundProfileBenchmarksMenuApi({ lipperIds }) {
	return request({
		url: apiPath + '/pro/fund/fundAssetMenu',
		method: 'get',
		params: {
			lipperIds
		}
	});
}

export function getFundPerformanceClassMajorMenuApi({ lipperIds }) {
	return request({
		url: apiPath + '/pro/fund/fundGlobalAssetMenu',
		method: 'get',
		params: {
			lipperIds
		}
	});
}
export function getEtfProfileNameMenuApi() {
	return request({
		url: apiPath + '/pro/etf/etfProfileNameMenu',
		method: 'get'
	});
}

export function getEtfProfileBenchmarksMenuApi() {
	return request({
		url: apiPath + '/pro/etf/etfProfileBenchmarksMenu',
		method: 'get'
	});
}

export function getEtfPerformanceClassMajorMenuApi() {
	return request({
		url: apiPath + '/pro/etf/etfPerformanceClassMajorMenu',
		method: 'get'
	});
}

export function getProductByPfcatCodeApi({ pfcatCode, issuerCode }) {
	return request({
		url: apiPath + '/pro/productByPfcatCode',
		method: 'get',
		params: {
			pfcatCode,
			issuerCode
		}
	});
}

export function comparePropItemApi({ proCodes, url }) {
	return request({
		url: apiPath + url,
		method: 'get',
		params: {
			proCodes
		}
	});
}

// 績效比較圖-已加入商品
export function observedFundsApi({ lipperIds }) {
	return request({
		url: apiPath + '/pro/fund/observedFunds',
		method: 'get',
		params: {
			lipperIds
		}
	});
}

// -商品歷史績效走勢圖
export function fundRunChartApi({ lipperIds }) {
	return request({
		url: apiPath + '/pro/fund/fundRunChart',
		method: 'get',
		params: {
			lipperIds
		}
	});
}

export function getObservedEtfsApi({ lipperIds }) {
	return request({
		url: apiPath + '/pro/etf/observedEtfs',
		method: 'get',
		params: {
			lipperIds
		}
	});
}

export function getPerformanceRunChartApi({ proCodes, freqType, freqFixed }) {
	return request({
		url: apiPath + '/pro/performanceRunChart',
		method: 'post',
		params: {
			proCodes,
			freqType,
			freqFixed
		}
	});
}

export function getEtfDetailApi({ proCode }) {
	return request({
		url: apiPath + '/pro/etf/etfProfileDetail',
		method: 'get',
		params: {
			proCode
		}
	});
}

export function getEtfStockHoldApi({ proCode }) {
	return request({
		url: apiPath + '/pro/etf/etfPortfolioTopHolding',
		method: 'get',
		params: {
			proCode
		}
	});
}

export function getEtfPriceApi({ prodCode }) {
	return request({
		url: apiPath + '/pro/etf/etfPriceAnalyze',
		method: 'get',
		params: {
			prodCode
		}
	});
}

export function getPricesChartDataApi({ prodCode, freqType, freqFixeds }) {
	return request({
		url: apiPath + '/pro/etf/etfAssetPriceHis',
		method: 'get',
		params: {
			prodCode,
			freqType,
			freqFixeds
		}
	});
}

export function getEtfPerformancesApi({ proCodes, freqType, freqFixed }) {
	return request({
		url: apiPath + '/pro/etf/etfPrformanceHis',
		method: 'get',
		params: {
			proCodes,
			freqType,
			freqFixed
		}
	});
}

export function getEtfPerformanceStatsApi({ proCode }) {
	return request({
		url: apiPath + '/pro/etf/etfPerformanceStats',
		method: 'get',
		params: {
			proCode
		}
	});
}

export function getEtfIntRateApi({ proCode }) {
	return request({
		url: apiPath + '/pro/etf/etfIntRate',
		method: 'get',
		params: {
			proCode
		}
	});
}

export function getGroupProExchangesMenuApi() {
	return request({
		url: apiPath + '/pro/groupExchangeMenu',
		method: 'get'
	});
}

export function getGroupFundCmpsMenuApi({ localYn, fuseYn }) {
	return request({
		url: apiPath + '/pro/fund/fundCompanyList',
		method: 'get',
		params: {
			localYn,
			fuseYn
		}
	});
}

export function getFundListApi({ fundCode }) {
	return request({
		url: apiPath + '/pro/fundInfo/sameCompanyFund',
		method: 'get',
		data: {
			fundCode
		}
	});
}

export function getGlobalFundListApi({ fundCode, globalGlassCode }) {
	return request({
		url: apiPath + '/pro/fundInfo/sameGlobalClassFund',
		method: 'get',
		data: {
			funCode,
			globalGlassCode
		}
	});
}

export function get2x2PerfsApi({ searchData }) {
	return request({
		url: apiPath + '/pro/fundInfo/2x2Perf',
		method: 'get',
		data: searchData
	});
}

export function getFundCodeApi({ fundCode }) {
	return request({
		url: apiPath + '/pro/assetCode/' + fundCode,
		method: 'get'
	});
}

export function getDividendsApi({ fundCode }) {
	return request({
		url: apiPath + '/pro/fundInfo/dividend/' + fundCode,
		method: 'get'
	});
}

export function getBenchmarksApi() {
	return request({
		url: apiPath + '/pro/fundInfo/fundBenchmark',
		method: 'get',
		loading: false
	});
}

export function getFundCompaniesApi() {
	return request({
		url: apiPath + '/pro/fundInfo/compareFundCompany',
		method: 'get',
		loading: false
	});
}

export function getFundsApi({ companyCode }) {
	return request({
		url: apiPath + '/pro/fundInfo/compareFund',
		method: 'get',
		loading: false,
		data: {
			companyCode
		}
	});
}

export function getFundSizeApi({ fundCode, beginDate, endDate }) {
	return request({
		url: apiPath + '/pro/asset/monthEndTna/' + fundCode,
		method: 'get',
		loading: false,
		data: {
			beginDate,
			endDate
		}
	});
}

export function getPctsApi({ beginDate, endDate, techCurrencyCode, assetCodes }) {
	return request({
		url: apiPath + '/pro/asset/pct',
		method: 'get',
		loading: false,
		data: {
			beginDate,
			endDate,
			techCurrencyCode,
			assetCodes
		}
	});
}

export function getPerfRankApi({ techCurrencyCode, fundCode, statCode, fundPerfRank, fundPerfRankCode }) {
	return request({
		url: apiPath + '/pro/fundInfo/perfRank',
		method: 'get',
		loading: false,
		data: {
			techCurrencyCode,
			fundCode,
			statCode,
			fundPerfRank,
			fundPerfRankCode
		}
	});
}

export function getMonthPctsApi({ fundCode, beginDate, endDate, techCurrencyCode }) {
	return request({
		url: apiPath + '/pro/asset/pct/month/' + fundCode,
		method: 'get',
		loading: false,
		data: {
			techCurrencyCode,
			beginDate,
			endDate
		}
	});
}

export function getAllocsApi(fundCode) {
	return request({
		url: apiPath + '/pro/fundInfo/alloc/' + fundCode,
		method: 'get',
		loading: false
	});
}

export function getObservedProductsApi({ proCodes }) {
	return request({
		url: apiPath + '/pro/observedProducts',
		method: 'get',
		params: {
			proCodes: proCodes.join(',')
		}
	});
}

export function getPfdFastMenuApi() {
	return request({
		url: apiPath + '/pro/pfd/pfdFastFilterMenu',
		method: 'get'
	});
}

export function getPfdProductsApi({ bankProCode, proName, curCodes, riskCodes, isinCode, profInvestorYn, geoFocusCodes }, queryString) {
	return request({
		url: apiPath + '/pro/pfd/pfdProducts' + queryString,
		method: 'get',
		params: {
			bankProCode,
			proName,
			curCodes,
			riskCodes,
			isinCode,
			profInvestorYn,
			geoFocusCodes
		}
	});
}

export function getPfdFastPageDataApi({ filterCodeValue }, queryString) {
	return request({
		url: apiPath + '/pro/pfd/pfdProductsFilterQuery' + queryString,
		method: 'get',
		params: {
			filterCodeValue
		}
	});
}

export function groupTargetMenuApi({ stockCode }) {
	return request({
		url: apiPath + '/pro/groupTargetMenu',
		method: 'get',
		data: {
			stockCode
		}
	});
}

export function getTargetViewDataListApi({ proCode }) {
	return request({
		url: apiPath + '/pro/targetViewDataList',
		method: 'get',
		data: {
			proCode
		}
	});
}

// 取得快速篩選條件選項
export function getSpFastFilterMenuApi() {
	return request({
		url: apiPath + '/pro/sp/spFastFilterMenu',
		method: 'get'
	});
}

export function getSpProductsApi(
	{ bankProCode, proName, protypeCode, curCodes, riskCode, principalGuarYn, targetCodes, intFreqUnitType, remainDayMin, remainDayMax, issuerCodes },
	queryString
) {
	return request({
		url: apiPath + '/pro/sp/spProducts' + queryString,
		method: 'get',
		params: {
			bankProCode,
			proName,
			protypeCode,
			curCodes,
			riskCode,
			principalGuarYn,
			targetCodes,
			intFreqUnitType,
			remainDayMin,
			remainDayMax,
			issuerCodes
		}
	});
}
export function getSpProductsFilterQueryApi({ filterCodeValue, timeRangeType, timeRangeFixed, rowNumberFixed }, queryString) {
	return request({
		url: apiPath + '/pro/sp/spProductsFilterQuery' + queryString,
		method: 'get',
		params: {
			filterCodeValue,
			timeRangeType,
			timeRangeFixed,
			rowNumberFixed
		}
	});
}
// 取得價格分析資料
export function getPriceAnaApi({ proCodes }) {
	return request({
		url: apiPath + '/pro/priceAna',
		method: 'get',
		params: {
			proCodes: proCodes.join(',')
		}
	});
}

// 取得快速篩選條件選項
export function getInsFastFilterMenuApi() {
	return request({
		url: apiPath + '/pro/ins/insFastFilterMenu',
		method: 'get'
	});
}

// 取得保險商品列表
export function getInsProductsApi(payload, queryString) {
	return request({
		url: apiPath + '/pro/ins/insProducts' + queryString,
		method: 'get',
		params: payload
	});
}

// 取得保險商品快速篩選查詢
export function getInsProductsFilterQueryApi(payload, queryString) {
	return request({
		url: apiPath + '/pro/ins/insProductsFilterQuery' + queryString,
		method: 'get',
		params: payload
	});
}

export function getOtherInsCompanies() {
	return request({
		url: apiPath + '/pro/ins/otherInsCompanies',
		method: 'get'
	});
}

export function getGroupInsCompanies() {
	return request({
		url: apiPath + '/pro/ins/groupInsCompanies',
		method: 'get'
	});
}

// DCI 快速篩選選單
export function getDciFastFilterMenuApi() {
	return request({
		url: apiPath + '/pro/dci/dciFastFilterMenu',
		method: 'get'
	});
}

// DCI 商品查詢
export function getDciProductsApi(params, queryString) {
	return request({
		url: apiPath + '/pro/dci/dciProducts' + queryString,
		method: 'get',
		params
	});
}

// DCI 商品快速查詢
export function getDciProductsFilterQueryApi(params, queryString) {
	return request({
		url: apiPath + '/pro/dci/dciProductsFilterQuery' + queryString,
		method: 'get',
		params
	});
}

export function getSecFastMenuApi() {
	return request({
		url: apiPath + '/pro/sec/secFastFilterMenu',
		method: 'get'
	});
}
export function getSecProductsApi(payload, queryString) {
	return request({
		url: apiPath + '/pro/sec/secProducts' + queryString,
		method: 'get',
		params: payload
	});
}

export function getSecProductsFilterQueryApi(payload, queryString) {
	return request({
		url: apiPath + '/pro/sec/secProductsFilterQuery' + queryString,
		method: 'get',
		params: payload
	});
}

export function getNewShelfProductList({ eventId }) {
	return request({
		url: apiPath + '/pro/newShelfProductList',
		method: 'get',
		params: {
			eventId
		}
	});
}

export function getProSelected({ selproId, eventId }) {
	return request({
		url: apiPath + '/pro/proSelected',
		method: 'get',
		params: {
			selproId,
			eventId
		}
	});
}

export function getNewProPfcatsMenuApi() {
	return request({
		url: apiPath + '/pro/newProPfcatsMenu',
		method: 'get'
	});
}

export function deleteShelfProduct({ proCode, eventId }) {
	return request({
		url: apiPath + '/pro/newShelfProduct',
		method: 'delete',
		params: {
			proCode,
			eventId
		}
	});
}

export function getSelectProPfcatsMenuApi() {
	return request({
		url: apiPath + '/pro/selectProPfcatsMenu',
		method: 'get'
	});
}
export function getSelProPfcatsMenuApi() {
	return request({
		url: apiPath + '/pro/selProcatCodeMenu',
		method: 'get'
	});
}

export function getPrdSearchSelected({ pfcatCode, selprocatCode, startDate, endDate }) {
	return request({
		url: apiPath + '/pro/proSelecteds',
		method: 'get',
		data: {
			pfcatCode,
			selprocatCode,
			startDate,
			endDate
		}
	});
}
export function getPrdSearchSelectedDetail({ pfcatCode, selprocatCode, startDate, endDate }) {
	return request({
		url: apiPath + '/pro/proSelectedDetail',
		method: 'get',
		data: {
			pfcatCode,
			selprocatCode,
			startDate,
			endDate
		}
	});
}

export function getProFastSelected({ bankProCode, pfcatCode, proName, protypeCode, assetcatCode, issuerCode }, queryString = '') {
	return request({
		url: apiPath + '/pro/proFastSelected' + queryString,
		method: 'get',
		data: {
			bankProCode,
			pfcatCode,
			proName,
			protypeCode,
			assetcatCode,
			issuerCode
		}
	});
}

export function getProSelectedCombs({ pfcatCode, selprocatCode, startDate, endDate }, queryString = '') {
	console.log('pfcatCode:', pfcatCode);
	return request({
		url: apiPath + '/pro/proSelectedCombs' + queryString,
		method: 'get',
		data: {
			pfcatCode,
			selprocatCode,
			startDate,
			endDate
		}
	});
}

export function getProSelectedLog({ pfcatCode, selprocatCode, startDate, endDate }, queryString) {
	return request({
		url: apiPath + '/pro/proSelectedLog' + queryString,
		method: 'get',
		data: {
			pfcatCode,
			selprocatCode,
			startDate,
			endDate
		}
	});
}

export function getIssuersMenu({ tranPrdtypeCodes }) {
	return request({
		url: apiPath + '/pro/issuersMenu',
		method: 'get',
		data: {
			tranPrdtypeCodes
		}
	});
}
export function deleteProSelectedLog({ eventId }) {
	return request({
		url: apiPath + '/pro/proSelectedLog',
		method: 'delete',
		data: {
			eventId
		}
	});
}

export function postProSelected({ pfcatCode, selprocatCode, selproName, startDate, endDate, selproId, proSelectedMapLog, actionCode }) {
	return request({
		url: apiPath + '/pro/proSelected',
		method: 'post',
		data: {
			pfcatCode,
			selprocatCode,
			selproName,
			startDate,
			endDate,
			selproId,
			proSelectedMapLog,
			actionCode
		},
		contentType: 'application/json'
	});
}

export function downloadProFileNewLog({ fileId }) {
	const url = apiPath + '/com/fileView?fileType=ProFileNewLog&fileId=' + fileId;
	var previewWindow = window.open(url, '_blank');
	previewWindow.addEventListener('beforeunload', () => {
		URL.revokeObjectURL(url);
	});
}

export function getProCatApi({ tranYn, assetacYn, pfcatCodes }) {
	return request({
		url: apiPath + '/pro/proCat',
		method: 'get',
		params: {
			tranYn,
			assetacYn,
			pfcatCodes
		}
	});
}

export function getDocProPageData({ proName, proCode, proType }, queryString) {
	return request({
		url: apiPath + '/pro/proQuickSearch/page' + queryString,
		method: 'get',
		params: {
			proName,
			proCode,
			proType
		}
	});
}

export function getDocProNoticePageData(queryString) {
	return request({
		url: apiPath + '/pro/proIssuerSearch/page' + queryString,
		method: 'get'
	});
}

export function getSelDocList({ docCat, docId }) {
	return request({
		url: apiPath + '/gen/document/list',
		method: 'get',
		params: {
			docCat,
			docId
		}
	});
}

export function getProSectorsApi() {
	return request({
		url: apiPath + '/pro/proSectors/list',
		method: 'get'
	});
}

export function getProGeoFocusApi() {
	return request({
		url: apiPath + '/pro/proGeoFocus/list',
		method: 'get'
	});
}

export function getProCurrenciesListApi() {
	return request({
		url: apiPath + '/pro/proCurrenciesList',
		method: 'get'
	});
}

export function getProTypesApi({ pfcatCode }) {
	return request({
		url: apiPath + '/pro/proTypes',
		method: 'GET',
		params: {
			pfcatCode
		}
	});
}
