<template>
	<!-- 銀行結構型商品  組合式商品-->
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">銀行結構型商品</h4>
				<button type="button" class="btn-close" @click.prevent="close()" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="card card-prointro shadow-none mb-3">
					<div class="pro_left">
						<div class="pro_name">
							<div class="avatar avatar-md avatar-structure"></div>
							<h4>
								<span>商品名稱</span> <br />{{ $filters.defaultValue(proInfo.proName, '--') }} <br /><span class="tx-black">{{
									$filters.defaultValue(proInfo.proEName, '--')
								}}</span>
							</h4>
						</div>
						<h4 class="pro_value">
							<span>評估損益</span>
							<br /><span>{{ $filters.defaultValue(proInfo.aprice, '--') }}</span> <br /><span>{{
								$filters.defaultValue(proInfo.priceDt, '--')
							}}</span>
						</h4>
					</div>
					<div class="pro_class">
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品代碼</span> <br />{{ $filters.defaultValue(proInfo.bankProCode, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6>
								<span>資產類別 <br /></span>{{ $filters.defaultValue(proInfo.assetcatName, '--') }}
							</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品主類</span> <br />{{ $filters.defaultValue(proInfo.pfcatName, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品次類</span><br />{{ $filters.defaultValue(proInfo.proTypeName, '--') }}</h6>
						</div>
					</div>
				</div>
				<div class="tab-nav-pills">
					<ul class="nav nav-pills justify-content-center" role="tablist">
						<li class="nav-item"><a class="nav-link active" href="#Sectionst1" data-bs-toggle="pill">商品基本資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionst2" data-bs-toggle="pill">商品共同資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionst3" data-bs-toggle="pill">商品附加資料</a></li>
					</ul>

					<div class="tab-content">
						<div class="tab-pane fade show active" id="Sectionst1">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>商品資訊</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>風險等級</th>
											<td class="wd-30p">{{ $filters.defaultValue(proInfo.dciInfo.riskName, '--') }}</td>
											<th>計價幣別</th>
											<td class="wd-30p">{{ $filters.defaultValue(proInfo.dciInfo.curCode, '--') }}</td>
										</tr>
										<tr>
											<th>投資標的</th>
											<td>{{ $filters.defaultValue(proInfo.dciInfo.linkTargetDesc, '--') }}</td>
											<th>募集狀態</th>
											<td>{{ $filters.defaultValue(proInfo.dciInfo.capitalRaiseStatusCode, '--') }}</td>
										</tr>
										<tr>
											<th>募集起日</th>
											<td>{{ $filters.defaultValue(proInfo.dciInfo.intStartDt, '--') }}</td>
											<th>募集迄日</th>
											<td>{{ $filters.defaultValue(proInfo.dciInfo.intEndDt, '--') }}</td>
										</tr>
										<tr>
											<th>商品類型</th>
											<td>{{ $filters.defaultValue(proInfo.dciInfo.guaranteeStatus, '--') }}</td>
											<th>保障收益率</th>
											<td>
												<span>{{ $filters.formatPct(proInfo.dciInfo.principalGuarRate, '') }}%</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>投資金額限制</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>最低存款金額</th>
											<td class="wd-30p">
												<span>{{ $filters.formatNumber(proInfo.dciInfo.mininvLcAmt) }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionst2">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>銷售相關資料</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>銷售地區</th>
											<td class="wd-30p" v-if="proInfo.allYn == 'Y'">全行</td>
											<td class="wd-30p" v-else></td>
											<th><span>銷售對象</span></th>
											<td class="wd-30p">{{ $filters.defaultValue(proInfo.targetCusBuName, '--') }}</td>
										</tr>
										<tr>
											<th>是否開放申購</th>
											<td>請洽金融行銷部</td>
											<th>是否開放贖回</th>
											<td>{{ $filters.defaultValue(proInfo.sellYn, '--') }}</td>
										</tr>
										<tr>
											<th><span>波動類型</span></th>
											<td>
												<span v-if="actionType !== 'EDIT'">{{ proInfo.volatilityTypeName }}</span>
												<template v-for="(item, index) in volatilityTypeList" v-if="actionType === 'EDIT'">
													<div class="form-check form-check-inline">
														<input
															class="form-check-input"
															type="radio"
															:id="'volatilityType_' + index"
															name="volatilityType"
															v-model="proInfo.volatilityType"
															:value="item.codeValue"
														/>
														<label class="form-check-label" :for="'volatilityType_' + index">{{ item.codeName }}</label>
													</div>
												</template>
											</td>
											<th><span>配息頻率</span></th>
											<td>
												<span v-if="actionType !== 'EDIT'">{{ proInfo.intFreqUnitypeName }}</span>
												<select
													class="form-select"
													name="intFreqUnitype"
													id="intFreqUnitype"
													v-if="actionType === 'EDIT'"
													v-model="proInfo.intFreqUnitype"
													rules="required"
												>
													<option v-for="item in intFreqUnitypeList" :value="item.codeValue">
														{{ item.codeName }}
													</option>
												</select>
											</td>
										</tr>
										<tr>
											<th><span>理財需求</span></th>
											<td colspan="3">
												<div class="form-check form-check-inline" v-for="item in finReqCodeMenu">
													<input
														class="form-check-input"
														name="finReqCodes"
														disabled
														id="c1"
														:disabled="actionType == 'EDIT' ? false : true"
														v-model="finReqCodes"
														:value="item.codeValue"
														type="checkbox"
													/>
													<label class="form-check-label" for="c1">{{ $filters.defaultValue(item.codeName, '--') }}</label>
												</div>
											</td>
										</tr>
										<tr>
											<th>商品標籤</th>
											<td disabled="disabled" colspan="3">
												{{ $filters.defaultValue(proInfo.selprocatNames, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionst3">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>其他設定</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>備註</span></th>
											<td>
												<textarea
													class="form-control"
													cols="80"
													rows="4"
													size="200"
													maxlength="200"
													v-model="proInfo.memo"
													:readonly="actionType !== 'EDIT'"
												></textarea>
												<div class="tx-note" v-if="proInfo.memo">{{ 200 - proInfo.memo.length }} 個字可輸入</div>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>商品說明書</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_A"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['A']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="dciUploadFileA"
																@change="triggerFile($event, 'A')"
																accept=".xlsx,.xls"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('A')">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['A'] && uploadFiles['A'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['A'].url" target="_blank">{{ uploadFiles['A'].url }}</a
													><br v-if="uploadFiles['A']" />
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['A'] && uploadFiles['A'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['A'])"
														>{{ uploadFiles['A'].showName }}</span
													>
													<span
														v-show="uploadFiles['A'] && uploadFiles['A'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('A', uploadFiles['A'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
										<tr>
											<th>投資人須知</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_D"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['D']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="dciUploadFileD"
																@change="triggerFile($event, 'D')"
																accept="application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.wordprocessingml.document"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('D')">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['D'] && uploadFiles['D'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['D'].url" target="_blank">{{ uploadFiles['D'].url }}</a
													><br v-if="uploadFiles['D']" />
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['D'] && uploadFiles['D'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['D'])"
														>{{ uploadFiles['D'].showName }}</span
													>
													<span
														v-show="uploadFiles['D'] && uploadFiles['D'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('D', uploadFiles['D'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
										<tr>
											<th>DM</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_F"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['F']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="dciUploadFileF"
																@change="triggerFile($event, 'F')"
																accept="application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.wordprocessingml.document"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('F')">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['F'] && uploadFiles['F'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['F'].url" target="_blank">{{ uploadFiles['F'].url }}</a
													><br v-if="uploadFiles['F']" />
												</span>
												<a href="#">
													<span v-if="uploadFiles['F']" class="tx-link" @click="downloadFile(uploadFiles['F'])">{{
														uploadFiles['F'].showName
													}}</span>
													<span
														v-show="uploadFiles['F'] && uploadFiles['F'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('F', uploadFiles['F'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
										<tr>
											<th>其他</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_G"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['G']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="dciUploadFileG"
																@change="triggerFile($event, 'G')"
																accept="application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.wordprocessingml.document"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('G')">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['G'] && uploadFiles['G'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['G'].url" target="_blank">{{ uploadFiles['G'].url }}</a
													><br v-if="uploadFiles['G']" />
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['G'] && uploadFiles['G'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['G'])"
														>{{ uploadFiles['G'].showName }}</span
													>
													<span
														v-show="uploadFiles['G'] && uploadFiles['G'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('G', uploadFiles['G'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>其他相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tr>
										<td>
											<span v-for="(item, index) in otherFileList">
												<a
													v-if="index === otherFileList.length - 1"
													v-show="item.show"
													href="#"
													class="tx-link"
													@click="downloadOtherFile(item.docFileId)"
													>{{ $filters.defaultValue(item.showName, '--') }}</a
												>
												<a v-else href="#" class="tx-link" v-show="item.show" @click="downloadOtherFile(item.docFileId)"
													>{{ $filters.defaultValue(item.showName, '--') }}、</a
												>
											</span>
										</td>
									</tr>
								</table>
							</div>
							<div class="tx-note">其他相關附件為文件管理內所上傳與本商品相關的文件。</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<input id="modalCloseButton" type="button" @click.prevent="close()" class="btn btn-white" value="關閉" />
				<input
					type="button"
					class="btn btn-primary"
					value="傳送主管審核"
					v-if="actionType == 'EDIT'"
					@click="
						updateProduct();
						close();
					"
				/>
			</div>
		</div>
	</div>
	<!-- Modal 2 End -->
</template>
<script>
import _ from 'lodash';
import moment from 'moment';
export default {
	props: {
		actionType: String,
		gotoPage: Function,
		finReqCodeMenu: Array,
		downloadFile: Function,
		downloadOtherFile: Function,
		close: Function
	},
	data: function () {
		const now = moment();
		return {
			proInfo: {
				fundInfo: {},
				etfInfo: {},
				dciInfo: {},
				spInfo: {},
				insInfo: {},
				secInfo: {}
			},
			finReqCodes: [],
			proFileList: [], // 相關附件檔案清單
			otherFileList: [], // 其他相關附件
			commInfo: {
				proFiles: []
			},
			proCode: '',
			pfcatCode: '',
			selectYnList: [],
			targetCusBuList: [], // 銷售對象資料
			volatilityTypeList: [], // 波動類型選單
			intFreqUnitypeList: [], //配息頻率選單

			//File 用參數
			url: [],
			uploadFile: {}, //上傳檔案
			uploadFiles: [] // 已上傳檔案陣列
		};
	},
	watch: {},
	mounted: function () {},
	methods: {
		// 銷售對象 來源資料
		getTargetCusBuList() {
			let self = this;
			self.$api
				.getAdmCodeDetail({
					codeType: 'CUS_BU'
				})
				.then(function (ret) {
					self.targetCusBuList = ret.data;
					resolve();
				});
		},
		// 配息頻率來源資料
		getVolatilityTypeList() {
			let self = this;
			self.$api
				.getAdmCodeDetail({
					codeType: 'VOLATILITY_TYPE'
				})
				.then(function (ret) {
					self.volatilityTypeList = ret.data;
					resolve();
				});
		},
		// 配息頻率來源資料
		getIntFreqUnitypeList() {
			let self = this;
			self.$api
				.getAdmCodeDetail({
					codeType: 'INT_FREQ_UNITTYPE'
				})
				.then(function (ret) {
					self.intFreqUnitypeList = ret.data;
					resolve();
				});
		},
		getProInfo: function (bankProCode, pfcatCode, eventId) {
			var self = this;
			self.resetModalVaule();
			Promise.all([self.getTargetCusBuList(), self.getIntFreqUnitypeList(), self.getVolatilityTypeList()]).then(() => {
				if (eventId) {
					self.doViewProLog(bankProCode, pfcatCode, eventId); // //審核資料
				} else {
					self.getProductInfo(bankProCode, pfcatCode); // 基本資料 共用資料
					self.getProductCommInfo(bankProCode, pfcatCode); // 附加資料
				}
			});
		},
		getProductInfo: function (bankProCode, pfcatCode, callback) {
			var self = this;
			self.$api.etProductInfoApi({
				proCode: bankProCode,
				pfcatCode: pfcatCode
			});
			vueProMgtDciModal.then(function (ret) {
				if (_.isNil(ret.data)) {
					ret.data = {};
					thi.$bi.alert('資料不存在');
					return;
				}
				if (_.isNil(ret.data.dciInfo)) {
					ret.data.dciInfo = {};
				}

				self.proInfo = ret.data;
				self.proCode = bankProCode;
				self.pfcatCode = pfcatCode;

				if (!_.isUndefined(self.proInfo.dciInfo.guaranteeStatus)) {
					var dcdGuaranteeTypeList = [];
					self.$api
						.getAdmCodeDetail({
							codeType: 'DCD_GUARANTEE_TYPE'
						})
						.then(function (r) {
							if (r.data.length > 0) {
								dcdGuaranteeTypeList = r.data;
								var dcdGuaranteeTypeObjs = _.filter(dcdGuaranteeTypeList, {
									codeValue: self.proInfo.dciInfo.guaranteeStatus
								});
								self.proInfo.dciInfo.guaranteeStatus = dcdGuaranteeTypeObjs[0].codeName;
							}
						});
				}

				if (!_.isUndefined(self.proInfo.dciInfo.linkTargetDesc)) {
					var dcdLinkTargetList = [];
					self.$api
						.getAdmCodeDetail({
							codeType: 'DCD_LINK_TARGET'
						})
						.then(function (r) {
							if (r.data.length > 0) {
								dcdLinkTargetList = r.data;
								var dcdLinkTargetObjs = _.filter(dcdLinkTargetList, {
									codeValue: self.proInfo.dciInfo.linkTargetDesc
								});
								self.proInfo.dciInfo.linkTargetDesc = dcdLinkTargetObjs[0].codeName;
							}
						});
				}

				if (!_.isUndefined(self.proInfo.dciInfo.capitalRaiseStatusCode)) {
					var dcdRaiseStatusList = [];
					self.$api
						.getAdmCodeDetail({
							codeType: 'DCD_RAISE_STATUS'
						})
						.then(function (r) {
							dcdRaiseStatusList = r.data;
							if (dcdRaiseStatusList.length > 0) {
								var dcdRaiseStatusObjs = _.filter(dcdRaiseStatusList, {
									codeValue: self.proInfo.dciInfo.capitalRaiseStatusCode
								});
								self.proInfo.dciInfo.capitalRaiseStatusCode = dcdRaiseStatusObjs[0].codeName;
							}
						});
				}

				if (!_.isUndefined(self.proInfo.targetCusBu)) {
					var targetCusBuObjs = _.filter(self.targetCusBuList, {
						codeValue: self.proInfo.targetCusBu
					});
					if (!_.isEmpty(targetCusBuObjs)) {
						self.proInfo.targetCusBuName = targetCusBuObjs[0].codeName;
					} else {
						self.proInfo.targetCusBuName = self.proInfo.targetCusBu;
					}
				}

				if (!_.isUndefined(self.proInfo.buyYn) && !_.isUndefined(self.proInfo.sellYn)) {
					self.$api
						.getAdmCodeDetail({
							codeType: 'SELECT_YN'
						})
						.then(function (r) {
							self.selectYnList = r.data;

							if (!_.isUndefined(self.proInfo.buyYn)) {
								var buyYnObjs = _.filter(self.selectYnList, {
									codeValue: self.proInfo.buyYn
								});
								self.proInfo.buyYn = buyYnObjs[0].codeName;
							}

							if (!_.isUndefined(self.proInfo.sellYn)) {
								var sellYnObjs = _.filter(self.selectYnList, {
									codeValue: self.proInfo.sellYn
								});
								self.proInfo.sellYn = sellYnObjs[0].codeName;
							}
						});
				}

				// 波動類型
				if (!_.isUndefined(ret.data.volatilityType)) {
					var volatilityTypeObjs = _.filter(self.volatilityTypeList, {
						codeValue: ret.data.volatilityType
					});
					// 區分編輯與(檢視、審核)
					if ((volatilityTypeObjs.length > 0) & (self.actionType === 'EDIT')) {
						self.proInfo.volatilityType = volatilityTypeObjs[0].codeValue;
					} else {
						self.proInfo.volatilityTypeName = volatilityTypeObjs[0].codeName;
					}
				}

				// 配息頻率
				if (!_.isUndefined(ret.data.intFreqUnitype)) {
					var intFreqUnitypeObjs = _.filter(self.intFreqUnitypeList, {
						codeValue: ret.data.intFreqUnitype
					});
					if (self.actionType === 'EDIT') {
						self.proInfo.intFreqUnitype = intFreqUnitypeObjs[0].codeValue;
					} else {
						self.proInfo.intFreqUnitypeName = intFreqUnitypeObjs[0].codeName;
					}
				}

				// 理財需求
				if (!_.isUndefined(ret.data.finReqCode)) {
					self.finReqCodes = ret.data.finReqCode.split(',');
				}

				if (!_.isUndefined(self.proInfo.selprocatNames)) {
					var selprocatNames = self.proInfo.selprocatNames.replaceAll(',', '、');
					self.proInfo.selprocatNames = selprocatNames;
				}

				callback && callback();
			});
		},
		getProductCommInfo: function (bankProCode, pfcatCode) {
			var self = this;
			// 商品附加資料
			self.$api
				.getProductsCommInfo({
					proCode: bankProCode,
					pfcatCode: pfcatCode
				})
				.then(function (ret) {
					if (!_.isNil(ret.data)) {
						if (ret.data.proDocs) {
							self.otherFileList = ret.data.proDocs; // 其他相關附件
							self.otherFileList.forEach(function (item) {
								// 其他相關附件 檔案顯示時間範圍
								item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
							});
						}

						self.proFileList = ret.data.proFiles; //相關附件檔案清單
						if (!_.isNil(self.proFileList)) {
							self.uploadFiles['A'] = self.proFileList.filter((proFile) => proFile.fileType === 'A')[0];
							self.uploadFiles['D'] = self.proFileList.filter((proFile) => proFile.fileType === 'D')[0];
							self.uploadFiles['F'] = self.proFileList.filter((proFile) => proFile.fileType === 'F')[0];
							self.uploadFiles['G'] = self.proFileList.filter((proFile) => proFile.fileType === 'G')[0];
							self.url['A'] = self.uploadFiles['A'] ? self.uploadFiles['A'].url : null;
							self.url['D'] = self.uploadFiles['D'] ? self.uploadFiles['D'].url : null;
							self.url['F'] = self.uploadFiles['F'] ? self.uploadFiles['F'].url : null;
							self.url['G'] = self.uploadFiles['G'] ? self.uploadFiles['G'].url : null;
						}
					}
				});
		},
		updateProduct: function () {
			var self = this;
			self.commInfo.finReqCode = self.finReqCodes.join(',');

			Object.keys(self.url).forEach((key) => {
				// 檢查是否只有輸入url 沒有上傳檔案的type
				if (self.url[key] !== null) {
					// 有輸入url
					let typeInclude = self.proFileList.some((obj) => obj.fileType === key); // 找出是否有存在fileList
					if (!typeInclude) {
						var proFile = {};
						proFile.fileType = key;
						self.proFileList.push(proFile);
					}
				}
			});

			self.proFileList.forEach((e) => {
				e.createDt = null; // 移除日期避免轉型錯誤
				e.url = self.url[e.fileType];
			});
			self.commInfo = self.proInfo;
			self.commInfo.proFiles = self.proFileList;

			var formData = new FormData();
			// json model
			formData.append('model', JSON.stringify(self.commInfo));

			// upload file
			for (const key in self.uploadFiles) {
				let item = self.uploadFiles[key];
				if (item) {
					formData.append('files', item);
				}
			}

			self.$api.patchProductApi(formData).then(function (ret) {
				self.$bi.alert('提交審核成功。');
				self.gotoPage(0);
				self.resetModalVaule();
			});
		},
		resetModalVaule: function () {
			var self = this;
			$('[type="file"]').val(null);
			self.url = [];
			self.proFile = [];
			self.uploadFile = {};
			self.uploadFiles = [];
		},
		doViewProLog: function (proCode, pfcatCode, eventId) {
			//審核資料
			var self = this;
			self.resetModalVaule();
			if (proCode) {
				const callback = () => {
					self.getProductCommLogInfo(eventId);
				};

				self.getProductInfo(proCode, pfcatCode, callback);
			}
		},
		getProductCommLogInfo: function (eventId) {
			var self = this;
			self.$api
				.getProductLogApi({
					eventId: eventId
				})
				.then(function (ret) {
					if (!_.isNil(ret.data)) {
						// 取得維護資料帶入
						// 共同資料
						// 波動類型
						if (!_.isUndefined(ret.data.volatilityType)) {
							var volatilityTypeObjs = _.filter(self.volatilityTypeList, {
								codeValue: ret.data.volatilityType
							});
							self.proInfo.volatilityTypeName = volatilityTypeObjs[0].codeName;
						}
						// 配息頻率
						if (!_.isUndefined(ret.data.intFreqUnitype)) {
							var intFreqUnitypeObjs = _.filter(self.intFreqUnitypeList, {
								codeValue: ret.data.intFreqUnitype
							});
							self.proInfo.intFreqUnitypeName = intFreqUnitypeObjs[0].codeName;
						}

						//附加資料
						self.proInfo.memo = ret.data.memo; // 備註

						if (ret.data.proDocs) {
							self.otherFileList = ret.data.proDocs; // 其他相關附件
							self.otherFileList.forEach(function (item) {
								// 其他相關附件 檔案顯示時間範圍
								item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
							});
						}

						self.proFileList = ret.data.proFiles; //相關附件檔案清單
						if (!_.isNil(self.proFileList)) {
							self.uploadFiles['A'] = self.proFileList.filter((proFile) => proFile.fileType === 'A')[0];
							self.uploadFiles['D'] = self.proFileList.filter((proFile) => proFile.fileType === 'D')[0];
							self.uploadFiles['F'] = self.proFileList.filter((proFile) => proFile.fileType === 'F')[0];
							self.uploadFiles['G'] = self.proFileList.filter((proFile) => proFile.fileType === 'G')[0];
						}
					}
				});
		},
		triggerFile: function (event, fileType) {
			var self = this;
			self.uploadFile[fileType] = event.target.files[0];
			self.uploadFile[fileType].showName = event.target.files[0].name;
		},
		doUploadFile: function (fileType) {
			var self = this;
			if (self.uploadFile[fileType]) {
				if (self.uploadFile[fileType].size > 10485760) {
					thi.$bi.alert('檔案大小不得超過10MB！');
					return;
				}
				var proFile = {};

				proFile.fileName = self.uploadFile[fileType].name;
				proFile.showName = self.uploadFile[fileType].name;

				proFile.contentType = self.uploadFile[fileType].type;
				proFile.fileSize = self.uploadFile[fileType].size;
				proFile.fileType = fileType;

				if (!self.proFileList || self.proFileList.length <= 0) {
					self.proFileList.push(proFile);
				} else {
					// 有資料先刪除就檔案再新增
					self.proFileList.forEach((e, index) => {
						if (e.fileType === fileType) {
							self.proFileList.splice(index, 1);
						}
					});
					self.proFileList.push(proFile);
				}

				self.uploadFiles[fileType] = null; // 先將原先檔案清除
				self.uploadFiles[fileType] = self.uploadFile[fileType];

				$('#dciUploadFile' + fileType).val(''); // 清空上傳區域檔案

				self.commInfo.isUpdateProFiles = true;
				self.uploadFile[fileType] = null;
			}
		},
		deleteFiles: function (fileType, proFileId) {
			var self = this;
			if (self.proFileList.length > 0) {
				self.proFileList.forEach((e, index, arr) => {
					if (e.proFileId === proFileId) {
						arr.splice(index, 1);
					}
				});
			}
			self.uploadFiles[fileType] = null;
		}
	} // methods end
};
</script>
