{"status": 200, "data": [{"eventId": "EVN20250227000010", "wfgId": "WFG20141112001", "createDt": "2025/02/27 15:53:53", "createBy": "040978", "userName": "陳OO", "status": "P", "itemDatas": [{"varItemCode": "ITM20180118001", "varItemValue": "112790"}, {"varItemCode": "ITM20180118002", "varItemValue": "簡OO"}, {"varItemCode": "ITM20180118003", "varItemValue": "財富管理部    ->系統管理經辦<br/>財富管理部    ->系統管理幹部<br/>財富管理部    ->系統管理者<br/>"}, {"varItemCode": "ITM20180118004", "varItemValue": "關閉"}], "wkfEngineFlows": [{"wfgId": "WFG20141112001", "actionName": "待覆核", "actionStatus": "P", "actionCode": "ACT20141112001"}, {"wfgId": "WFG20141112001", "actionName": "覆核完成", "actionStatus": "A", "actionCode": "ACT20141112002"}, {"wfgId": "WFG20141112001", "actionName": "退回修改", "actionStatus": "R", "actionCode": "ACT20141112003"}], "buttonYn": "Y"}], "timestamp": "2025/04/18", "sqlTracer": [{"data": [{"eventId": "EVN20250227000010", "wfgId": "WFG20141112001", "createDt": "2025/02/27 15:53:53", "createBy": "040978", "userName": "陳OO", "status": "P", "itemDatas": [{"varItemCode": "ITM20180118001", "varItemValue": "112790"}, {"varItemCode": "ITM20180118002", "varItemValue": "簡OO"}, {"varItemCode": "ITM20180118003", "varItemValue": "財富管理部    ->系統管理經辦<br/>財富管理部    ->系統管理幹部<br/>財富管理部    ->系統管理者<br/>"}, {"varItemCode": "ITM20180118004", "varItemValue": "關閉"}], "wkfEngineFlows": [{"wfgId": "WFG20141112001", "actionName": "待覆核", "actionStatus": "P", "actionCode": "ACT20141112001"}, {"wfgId": "WFG20141112001", "actionName": "覆核完成", "actionStatus": "A", "actionCode": "ACT20141112002"}, {"wfgId": "WFG20141112001", "actionName": "退回修改", "actionStatus": "R", "actionCode": "ACT20141112003"}], "buttonYn": "Y"}], "sqlInfo": "SELECT EVT.*, USR.USER_NAME, WE.CONFIRM_ACTION, EVT.WFGS_CODE CUR_WFGS_CODE, CASE WHEN ((WE.WFG_ROLE_KIND = 'rule2' AND EXISTS (SELECT 1 FROM WKF_ENGINE_ROLES WER WHERE EVT.WFGS_CODE = WER.WFGS_CODE AND WER.WFGS_ROLE = :roleCode ))) OR (WE.WFG_ROLE_KIND = 'rule3' AND JSON_VALUE (EXTRA, '$.nextUserCode') = :userCode ) OR (WE.WFG_ROLE_KIND = 'rule4') OR (WE.WFG_ROLE_KIND = 'rule5' AND EXISTS (SELECT 1 FROM WKF_ENGINE_ROLES wer WHERE EVT.WFGS_CODE = wer.WFGS_CODE AND wer.WFGS_ROLE = :roleCode ) AND (EVT.USER_CODE <> :userCode OR (EVT.POS_CODE <> :posCode AND JSON_VALUE (EXTRA, '$.eventList[0].branCode' ) = :branCode ) OR (EVT.POS_CODE <> :posCode AND JSON_VALUE (EXTRA, '$.eventList[0].secBranCode' ) = :branCode )) ) THEN 'Y' ELSE 'N' END AS BUTTON_YN FROM WKF_EVENTS EVT JOIN WKF_ENGINES WE ON EVT.WFG_ID = WE.WFG_ID JOIN ADM_USERS USR ON EVT.CREATE_BY = USR.USER_CODE WHERE EVT.WFG_ID IN ( :wfgIds ) AND EVT.STATUS = 'P' AND EVT.CREATE_BY <> :userCode AND ((WE.WFG_ROLE_KIND IN ('rule2', 'rule4') AND EXISTS (SELECT 1 FROM (SELECT P.BRAN_CODE, P.POS_CODE, P.ROLE_CODE, P.BU_CODE, AUPM.USER_CODE FROM ADM_POS_ACCESS_MAP PAM JOIN ADM_POSITIONS P ON PAM.ACCESS_POS_CODE = P.POS_CODE LEFT JOIN ADM_USER_POS_MAP AUPM ON P.POS_CODE = AUPM.POS_CODE WHERE PAM.POS_CODE =  '891_98' ) auth WHERE JSON_VALUE(extra, '$.eventList[0].posCode') = auth.pos_code)) OR (WE.WFG_ROLE_KIND = 'rule3' AND JSON_VALUE (extra, '$.nextUserCode') = :userCode ) OR (WE.WFG_ROLE_KIND = 'rule5' AND (JSON_VALUE (extra, '$.eventList[0].branCode' ) = :branCode OR JSON_VALUE (extra, '$.eventList[0].secBranCode' ) = :branCode )))  ORDER BY EVT.EVENT_ID DESC ,class com.bi.pbs.wkf.web.model.WkfEventResp,{branCode=891, roleCode=98, posCode=891_98, wfgIds=[WFG20141112001], userCode=112790}"}, {"data": [{"eventId": "EVN20250227000010", "varItemCode": "ITM20180118001", "varItemValue": "112790"}, {"eventId": "EVN20250227000010", "varItemCode": "ITM20180118002", "varItemValue": "簡OO"}, {"eventId": "EVN20250227000010", "varItemCode": "ITM20180118003", "varItemValue": "財富管理部    ->系統管理經辦<br/>財富管理部    ->系統管理幹部<br/>財富管理部    ->系統管理者<br/>"}, {"eventId": "EVN20250227000010", "varItemCode": "ITM20180118004", "varItemValue": "關閉"}], "sqlInfo": " SELECT EVENT_ID, WVD.VARITEM_CODE, WVD.VARITEM_VALUE  FROM WKF_VARITEM_DATALIST WVD  JOIN WKF_VARITEM_MAPPING WVM ON WVD.VARITEM_CODE = WVM.VARITEM_CODE  WHERE EVENT_ID IN ( :eventIds )  ORDER BY WVM.SHOW_ORDER ,class com.bi.pbs.wkf.model.WkfVarItemDataList,{eventIds=[EVN20250227000010]}"}, {"data": [{"wfgId": "WFG20141112001", "actionName": "待覆核", "actionStatus": "P", "actionCode": "ACT20141112001"}, {"wfgId": "WFG20141112001", "actionName": "覆核完成", "actionStatus": "A", "actionCode": "ACT20141112002"}, {"wfgId": "WFG20141112001", "actionName": "退回修改", "actionStatus": "R", "actionCode": "ACT20141112003"}], "sqlInfo": " SELECT WEF.* , WEA.ACTION_STATUS , WEA.ACTION_NAME  FROM WKF_MENUS WM  JOIN WKF_MENU_ENGINE_MAPPING WMEM ON WM.MENU_CODE = WMEM.MENU_CODE  JOIN WKF_ENGINES WE ON WMEM.WFG_ID = WE.WFG_ID  JOIN WKF_ENGINE_FLOWS WEF ON WEF.WFG_ID = WE.WFG_ID  JOIN WKF_ENGINE_ACTIONS WEA ON WEF.ACTION_CODE = WEA.ACTION_CODE  WHERE 1<>1  OR WE.WFG_ID IN ( :wfgIds ) ,class com.bi.pbs.wkf.web.model.WkfEngineFlowsResp,{wfgIds=[WFG20141112001]}"}]}