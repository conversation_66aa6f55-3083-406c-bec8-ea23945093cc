{"status": 200, "data": [{"order": 0, "leaf": false, "branCode": "6100", "branName": "北一區", "parentBranCode": "891", "depths": 3}, {"order": 0, "leaf": false, "branCode": "6200", "branName": "北二區", "parentBranCode": "891", "depths": 3}, {"order": 0, "leaf": false, "branCode": "6300", "branName": "北三區", "parentBranCode": "891", "depths": 3}, {"order": 0, "leaf": false, "branCode": "6400", "branName": "桃竹苗東區", "parentBranCode": "891", "depths": 3}, {"order": 0, "leaf": false, "branCode": "6500", "branName": "中彰投區", "parentBranCode": "891", "depths": 3}, {"order": 0, "leaf": false, "branCode": "6600", "branName": "嘉南區", "parentBranCode": "891", "depths": 3}, {"order": 0, "leaf": false, "branCode": "6700", "branName": "高屏一區", "parentBranCode": "891", "depths": 3}, {"order": 0, "leaf": false, "branCode": "6800", "branName": "高屏二區", "parentBranCode": "891", "depths": 3}], "timestamp": "2025/07/16", "sqlTracer": [{"data": [], "sqlInfo": " SELECT DISTINCT B.<PERSON>AN_CODE, <PERSON>.BRAN_NAME, B.BRAN_ENAME, B.BRAN_ADDR, B<PERSON>BRAN_EADDR, B.PARENT_BRAN_CODE, B.STRSET,  B.DEPTHS, B.BRAN_LVL_CODE  FROM ADM_POSITIONS P  JOIN ADM_BRANCHES B ON P.BRAN_CODE = B.BRAN_CODE AND P.BU_CODE = B.BU_CODE  WHERE ( P.POS_CODE IN (:posCodeList)  OR EXISTS (  SELECT 1 FROM ADM_POS_ACCESS_MAP M  WHERE P.POS_CODE = M.ACCESS_POS_CODE AND POS_CODE IN (:posCodeList)  ))  AND B.BRAN_LVL_CODE IN (:lvlCodeList)  AND B.BU_CODE = :buCode  ORDER BY B.BRAN_CODE ,class com.bi.pbs.cus.web.model.BranchesResp,{posCodeList=[891_98], lvlCodeList=[40], buCode=Z}"}, {"data": [], "sqlInfo": " SELECT * FROM ADM_BRANCHES  WHERE BRAN_LVL_CODE in (:lvlCodes) AND REMOVE_YN = 'N'  AND :strset like STRSET + '%' ,class com.bi.pbs.cus.web.model.BranchesResp,{strset=000F, lvlCodes=[40]}"}, {"data": [{"order": 0, "leaf": false, "branCode": "6100", "branName": "北一區", "parentBranCode": "891", "depths": 3}, {"order": 0, "leaf": false, "branCode": "6200", "branName": "北二區", "parentBranCode": "891", "depths": 3}, {"order": 0, "leaf": false, "branCode": "6300", "branName": "北三區", "parentBranCode": "891", "depths": 3}, {"order": 0, "leaf": false, "branCode": "6400", "branName": "桃竹苗東區", "parentBranCode": "891", "depths": 3}, {"order": 0, "leaf": false, "branCode": "6500", "branName": "中彰投區", "parentBranCode": "891", "depths": 3}, {"order": 0, "leaf": false, "branCode": "6600", "branName": "嘉南區", "parentBranCode": "891", "depths": 3}, {"order": 0, "leaf": false, "branCode": "6700", "branName": "高屏一區", "parentBranCode": "891", "depths": 3}, {"order": 0, "leaf": false, "branCode": "6800", "branName": "高屏二區", "parentBranCode": "891", "depths": 3}], "sqlInfo": " SELECT DISTINCT B.<PERSON>AN_CODE, <PERSON>.BRAN_NAME, B.BRAN_ENAME, B.BRAN_ADDR, B<PERSON>BRAN_EADDR, B.PARENT_BRAN_CODE, B.STRSET,  B.DEPTHS, B.BRAN_LVL_CODE  FROM ADM_POSITIONS P  JOIN ADM_BRANCHES B ON P.BRAN_CODE = B.BRAN_CODE AND P.BU_CODE = B.BU_CODE  WHERE ( P.POS_CODE IN (:posCodeList)  OR EXISTS (  SELECT 1 FROM ADM_POS_ACCESS_MAP M  WHERE P.POS_CODE = M.ACCESS_POS_CODE AND POS_CODE IN (:posCodeList)  ))  AND B.BRAN_LVL_CODE IN (:lvlCodeList)  AND B.BU_CODE = :buCode  ORDER BY B.BRAN_CODE ,class com.bi.pbs.cus.web.model.BranchesResp,{posCodeList=[891_98], lvlCodeList=[50], buCode=Z}"}]}