import request from '@/utils/request';
const apiPath = import.meta.env.VITE_API_URL_V1;

export function getAdmCodeDetail({ codeType, codeValue, codeName }) {
	return request({
		url: apiPath + '/adm/admCodeDetail',
		method: 'get',
		params: {
			codeType,
			codeValue,
			codeName
		}
	});
}

//ADM0110/programSet.vue
export function getFunctionMenuTreeApi(menuCode) {
	return request({
		url: apiPath + '/adm/functionMenuModifyInfo',
		method: 'get',
		params: menuCode ? { menuCode } : {}
	});
}
// getPrograSet() 呼叫了與 ADM0110/programSet.vue 的 getFunctionMenuTreeApi 一樣的Api(有帶參數menuCode)

export function patchSaveMenuActive(activeArray) {
	return request({
		url: apiPath + '/adm/functionMenuActive',
		method: 'patch',
		data: activeArray,
		headers: { 'Content-Type': 'application/json' }
	});
}

// ADM0101/admRole.vue
export function getRoleMenuApi() {
	return request({
		url: apiPath + '/adm/roleMenu',
		method: 'get'
	});
}

export function getAdmRolesApi(roleCode) {
	return request({
		url: apiPath + '/adm/systemRoles',
		method: 'get',
		params: {
			roleCode
		}
	});
}

// ADM0101/include/admRoleAuthority.vue
// getAdmRoles() 呼叫了與 ADM0101/admRole.vue 的 getAdmRolesApi 一樣的Api

export function getMenuEnableTreeApi(roleCode) {
	return request({
		url: apiPath + '/adm/menuEnableTree',
		method: 'get',
		params: {
			roleCode
		}
	});
}

export function postUpdateRolAuthorityApi(roleMetadata, checkedMenuCodes) {
	return request({
		url: apiPath + '/adm/roleMenuMap',
		method: 'post',
		data: {
			roleMetadata,
			checkedMenuCodes
		},
		headers: { 'Content-Type': 'application/json' }
	});
}

// ADM0101/include/admRoleReviewDetail.vue
export function getDetailApi(eventId) {
	return request({
		url: apiPath + '/adm/roleMenuLog',
		method: 'get',
		params: {
			eventId
		}
	});
}

// ADM0103/admMenuPreview.vue
// getRoleMenuDatas() 呼叫了與 ADM0101/admRole.vue 的 getRoleMenuApi 一樣的Api

export function getRoleMenuTreeApi(roleCode) {
	return request({
		url: apiPath + '/adm/roleMenuMapPreview',
		method: 'get',
		params: {
			roleCode
		}
	});
}

// components/biTabs.vue
export function getMenuTabApi(parentMenuCode) {
	return request({
		url: apiPath + '/adm/menuTab',
		method: 'get',
		params: {
			parentMenuCode
		}
	});
}

export function postLoggingApi(menuCode) {
	return request({
		url: apiPath + '/adm/admUserAccessLog',
		method: 'post',
		data: {
			menuCode
		},
		headers: { 'Content-Type': 'application/json' }
	});
}

// adm/ADM0104/include/userAccountTab.vue
export function getBranMenuApi() {
	return request({
		url: apiPath + '/adm/branchesMenu',
		method: 'get'
	});
}
// getRoleMenu() 呼叫了與 ADM0101/admRole.vue 的 getRoleMenuApi 一樣的Api
export function getSysUserApi(branCode, roleCode, userCode, userName, pageable) {
	return request({
		url: apiPath + '/adm/sysUser',
		method: 'get',
		params: {
			branCode,
			roleCode,
			userCode,
			userName,
			...pageable
		}
	});
}

export function getExportExcelApi(branCode, roleCode, userCode, userName) {
	return request({
		url: apiPath + '/adm/sysUser/excel',
		method: 'get',
		params: {
			branCode,
			roleCode,
			userCode,
			userName
		},
		responseType: 'blob'
	});
}

export function getUserPosEventApi(eventId) {
	return request({
		url: apiPath + '/adm/userPosEvent',
		method: 'get',
		params: {
			eventId
		}
	});
}

// adm/ADM0104/include/userAccountNewTab.vue
export function getEditUserInfoApi(userCode) {
	return request({
		url: apiPath + '/adm/userInfo',
		method: 'get',
		params: {
			userCode
		}
	});
}

export function getUserBranPosInfoApi(userCode) {
	return request({
		url: apiPath + '/adm/userBranPosInfo',
		method: 'get',
		params: {
			userCode
		}
	});
}
// getBranMenu() 呼叫了與 ADM0104/include/userAccountTab.vue 的 getBranMenuApi 一樣的Api

export function getPosBranMenuApi(branCode, buCode) {
	return request({
		url: apiPath + '/adm/posBranMenu',
		method: 'get',
		params: {
			branCode,
			buCode
		}
	});
}

export function postUserAccountApi(userCode, buCode, branCode, posCodes, deletePosCodes, startFlag) {
	return request({
		url: apiPath + '/adm/userAccount',
		method: 'post',
		data: {
			userCode,
			buCode,
			branCode,
			posCodes,
			deletePosCodes,
			startFlag
		},
		headers: { 'Content-Type': 'application/json' }
	});
}

export function getGroupBranNameApi() {
	return request({
		url: apiPath + '/adm/groupBranName',
		method: 'get'
	});
}

export function getBranPageData({ groupCode }, queryString) {
	return request({
		url: apiPath + '/adm/groupBranMap' + queryString,
		method: 'get',
		params: {
			groupCode
		}
	});
}
export function postBranPageData() {
	return request({
		url: apiPath + '/adm/groupBranMap',
		method: 'post'
	});
}

export function getBranExportPageData({ groupCode }) {
	return request({
		url: apiPath + '/adm/groupBranMap/excel',
		method: 'get',
		params: {
			groupCode
		},
		responseType: 'blob'
	});
}

export function postFcBranMapChkApi(formData) {
	return request({
		url: apiPath + '/adm/fcBranMapChk',
		method: 'post',
		data: formData,
		enctype: 'multipart/form-data;charset=UTF-8',
		processData: false,
		contentType: false,
		cache: false
	});
}
export function postGroupBranMapChkApi(formData) {
	return request({
		url: apiPath + '/adm/groupBranMapChk',
		method: 'post',
		data: formData,
		enctype: 'multipart/form-data;charset=UTF-8',
		processData: false,
		contentType: false,
		cache: false
	});
}

export function getAdmRoleByRoleTypeApi({ roleType }) {
	return request({
		url: apiPath + '/adm/admRoleByRoleType',
		method: 'get',
		params: {
			roleType
		}
	});
}

export function postFcBranMapApi() {
	return request({
		url: apiPath + '/adm/fcBranMap',
		method: 'post',
		data: {}
	});
}

export function getFcBranMapData({ roleCode }, queryString) {
	return request({
		url: apiPath + '/adm/fcBranMap' + queryString,
		method: 'get',
		params: {
			roleCode
		}
	});
}

export function getFcBranExportPageData({ roleCode }) {
	return request({
		url: apiPath + '/adm/fcBranMap/excel',
		method: 'get',
		responseType: 'blob',
		params: {
			roleCode
		}
	});
}

export function getAreaMenu() {
	return request({
		url: apiPath + '/adm/minorArea',
		method: 'get'
	});
}

export function getUserDeputiesApi() {
	return request({
		url: apiPath + '/adm/userDeputies',
		method: 'get'
	});
}

export function getDeputiesRmMgrApi() {
	return request({
		url: apiPath + '/adm/deputiesBm',
		method: 'get'
	});
}

export function getUnderUserDeputiesPageDataApi({ groupCode, branCode }, queryString) {
	return request({
		url: apiPath + '/adm/underUserDeputies' + queryString,
		method: 'get',
		params: {
			groupCode,
			branCode
		}
	});
}

export function getDeputyUserCodeApi({ deputyUserCode }) {
	return request({
		url: apiPath + '/adm/userDeputiesPos',
		method: 'get',
		params: {
			deputyUserCode
		}
	});
}

export function postInsertDeputyApi({ userCode, roleMetadata, branCode, deputyUserCode, deputyBranCode, stdDt, endDt }) {
	return request({
		url: apiPath + '/adm/userDeputies',
		method: 'post',
		data: {
			userCode,
			roleMetadata,
			branCode,
			deputyUserCode,
			deputyBranCode,
			stdDt,
			endDt
		}
	});
}

export function getchkValidDeputiesTimeApi({ stdDt, endDt }) {
	return request({
		url: apiPath + '/adm/chkValidDeputiesTime',
		method: 'get',
		params: {
			stdDt,
			endDt
		}
	});
}

export function getdoCheckIsBusinessDtApi({ date }) {
	return request({
		url: apiPath + '/adm/isBusinessDt',
		method: 'get',
		params: {
			date
		}
	});
}

export function getcheckDeputyUserCodeApi({ userCode, deputyUserCode }) {
	return request({
		url: apiPath + '/adm/underUserDeputiesPos',
		method: 'get',
		params: {
			userCode,
			deputyUserCode
		}
	});
}

export function deleteUserdeputyApi({ userCode, deputyUserCode }) {
	return request({
		url: apiPath + '/adm/userDeputies',
		method: 'delete',
		params: {
			userCode,
			deputyUserCode
		}
	});
}

export function getAdmBranchesApi({ parentBranCode, branLvlCode, removeYn, branCode }) {
	return request({
		url: apiPath + '/adm/admBranches',
		method: 'get',
		params: {
			parentBranCode,
			branLvlCode: branLvlCode.join(','),
			removeYn,
			branCode
		}
	});
}

export function getAdmUsersListApi({ branCode, userCode, parentBranCode, roleCode }) {
	return request({
		url: apiPath + '/adm/admUsersList',
		method: 'get',
		params: {
			branCode,
			userCode,
			parentBranCode,
			roleCode
		}
	});
}

export function getUserCodeLengthApi() {
	return request({
		url: apiPath + '/adm/sys/userCodeLength',
		method: 'get'
	});
}

export function getDeputiesLogApi({ parentBranCode, branCode, userCode, stdDt, endDt }) {
	return request({
		url: apiPath + '/adm/deputiesLog',
		method: 'get',
		params: {
			parentBranCode,
			branCode,
			userCode,
			stdDt,
			endDt
		}
	});
}

export function getMinorAreaApi({ buCode, majorCode }) {
	return request({
		url: apiPath + '/adm/minorArea',
		method: 'get',
		params: {
			buCode,
			majorCode
		}
	});
}

export function getBranchesApi({ buCode, majorCode, minorCode }) {
	return request({
		url: apiPath + '/adm/branches',
		method: 'get',
		params: {
			buCode,
			majorCode,
			minorCode
		}
	});
}

export function getDeputiesApi({ branCode }) {
	return request({
		url: apiPath + '/adm/deputiesUserMenu',
		method: 'get',
		params: {
			branCode
		}
	});
}

export function getShutdownInfoApi() {
	return request({
		url: apiPath + '/adm/shutdownInfo',
		method: 'get'
	});
}

export function postShutdownInfoApi({ startDt, endDt, shutdownDesc }) {
	return request({
		url: apiPath + '/adm/shutdownInfo',
		method: 'post',
		data: {
			startDt,
			endDt,
			shutdownDesc
		}
	});
}

export function patchShutdownInfoApi({ shutdownId, startDt, endDt, shutdownDesc }) {
	return request({
		url: apiPath + '/adm/shutdownInfo',
		method: 'patch',
		data: {
			shutdownId,
			startDt,
			endDt,
			shutdownDesc
		}
	});
}

export function deleteShutdownInfoApi({ shutdownId }) {
	return request({
		url: apiPath + '/adm/shutdownInfo',
		method: 'delete',
		params: { shutdownId }
	});
}

export function getAllBranchesMenuApi() {
	return request({
		url: apiPath + '/adm/allBranchesMenu',
		method: 'get'
	});
}

export function getModuleMenuApi() {
	return request({
		url: apiPath + '/adm/moduleMenu',
		method: 'get'
	});
}

export function getBranEmployeeApi({ buCode, branCode }) {
	return request({
		url: apiPath + '/adm/branEmployee',
		method: 'get',
		params: {
			buCode,
			branCode
		}
	});
}

export function getUserMenuApi({ branCode }) {
	return request({
		url: apiPath + '/adm/userMenu',
		method: 'get',
		params: {
			branCode
		}
	});
}

export function getBranchFunctionMenuApi({ menuCode }) {
	return request({
		url: apiPath + '/adm/branchFunctionMenu',
		method: 'get',
		params: {
			menuCode
		}
	});
}

export function getUserMenu({ branCode }) {
	return request({
		url: apiPath + '/adm/userMenus',
		method: 'get',
		params: {
			branCode
		}
	});
}

export function getUserAccessCntLogApi({ branCode, logStartDt, logEndDt, moduleMenuCode, functionMenuCode, userCode }, queryString) {
	return request({
		url: apiPath + '/adm/userAccessCntLog' + queryString,
		method: 'get',
		params: {
			branCode,
			logStartDt,
			logEndDt,
			moduleMenuCode,
			functionMenuCode,
			userCode
		}
	});
}

export function getCusSaveResultCountApi({ paramType, paramCode }) {
	return request({
		url: apiPath + '/adm/admParam',
		method: 'get',
		params: {
			paramType,
			paramCode
		}
	});
}

export function getBranAccessCntLogApi({ branCode, logStartDt, logEndDt, moduleMenuCode, functionMenuCode, userCode }, queryString) {
	return request({
		url: apiPath + '/adm/branAccessCntLog' + queryString,
		method: 'get',
		params: {
			branCode,
			logStartDt,
			logEndDt,
			moduleMenuCode,
			functionMenuCode,
			userCode
		}
	});
}

export function getCusFunctionMenuApi() {
	return request({
		url: apiPath + '/adm/cusFunctionMenu',
		method: 'get'
	});
}

export function getUserAccessCusLogsApi({ branCode, logStartDt, logEndDt, menuCode, userCode, cusCode }, queryString) {
	return request({
		url: apiPath + '/adm/userAccessCusLogs' + queryString,
		method: 'get',
		params: {
			branCode,
			logStartDt,
			logEndDt,
			menuCode,
			userCode,
			cusCode
		}
	});
}

export function getUserFunctionMenuApi({ depths, strset }) {
	return request({
		url: apiPath + '/adm/userFunctionMenu',
		method: 'get',
		params: {
			depths,
			strset
		}
	});
}

export function getUserRoleMenuApi() {
	return request({
		url: apiPath + '/adm/userRoleMenus',
		method: 'GET'
	});
}

export function getUserInfoApi({ userCode, path }) {
	return request({
		url: apiPath + '/adm/userInfo',
		method: 'get',
		params: {
			userCode,
			path
		}
	});
}

export function getUserAccessLogsApi({ userCode, logStartDt, logEndDt, m1MenuCode, m2MenuCode, m3MenuCode, m4MenuCode }, queryString) {
	return request({
		url: apiPath + '/adm/userAccessLogs' + queryString,
		method: 'get',
		params: {
			userCode,
			logStartDt,
			logEndDt,
			m1MenuCode,
			m2MenuCode,
			m3MenuCode,
			m4MenuCode
		}
	});
}

export function getCarryOutItemsActionTypeApi({ actionMode }) {
	return request({
		url: apiPath + '/adm/carryOutItemsActionType',
		method: 'get',
		params: {
			actionMode
		}
	});
}

export function getItemsActionTypeMenuApi() {
	return request({
		url: apiPath + '/adm/itemsActionTypeMenu',
		method: 'get'
	});
}

export function getCarryOutItemsApi({ userCode, progCode, actionTypes, logStdDt, logEndDt }, queryString) {
	return request({
		url: apiPath + '/adm/carryOutItems' + queryString,
		method: 'get',
		params: {
			userCode,
			progCode,
			actionTypes,
			logStdDt,
			logEndDt
		}
	});
}

export function getTdItemCat1MenuApi() {
	return request({
		url: apiPath + '/adm/tdItemCat1Menu',
		method: 'get'
	});
}

export function getTdItemsApi({ tdCat1Code }) {
	return request({
		url: apiPath + '/adm/tdItems',
		method: 'get',
		params: {
			tdCat1Code
		}
	});
}

export function patchTdItemsApi(changedTdItems) {
	return request({
		url: apiPath + '/adm/tdItems',
		method: 'patch',
		data: changedTdItems
	});
}

export function getRmLvlMenuApi() {
	return request({
		url: apiPath + '/adm/rmLvlMenu',
		method: 'get'
	});
}

export function getCusGradesSetListsApi() {
	return request({
		url: apiPath + '/adm/cusGradesSetLists',
		method: 'get'
	});
}

export function patchCusGradesSetListsApi({ graCode, rmLvlCode, graName, auaMin, auaMax }) {
	return request({
		url: apiPath + '/adm/cusGradesSetLists',
		method: 'patch',
		data: {
			graCode,
			rmLvlCode,
			graName,
			auaMin,
			auaMax
		}
	});
}

export function getGradesApi() {
	return request({
		url: apiPath + '/adm/grades',
		method: 'get'
	});
}

export function patchGradesApi(changedGradesItems) {
	return request({
		url: apiPath + '/adm/grades',
		method: 'patch',
		data: changedGradesItems
	});
}

export function getCodeDetailApi({ codeType }) {
	return request({
		url: apiPath + '/adm/codeDetail',
		method: 'get',
		params: {
			codeType
		}
	});
}

export function getHasEnableVerifyOptionApi() {
	return request({
		url: apiPath + '/adm/hasEnableVerifyOption',
		method: 'get'
	});
}
