<template>
	<!--頁面內容 兼營證券自營商品sec  start-->
	<div class="tab-nav-tabs my-3">
		<ul class="nav nav-tabs nav-justified">
			<li class="nav-item">
				<a class="nav-link" :class="{ active: activeTab == 'common' }" href="#SectionA" data-bs-toggle="tab"
					@click="changeTab('common')">一般篩選</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" :class="{ active: activeTab == 'fast' }" href="#SectionB" data-bs-toggle="tab"
					@click="changeTab('fast')">快速篩選</a>
			</li>
		</ul>
		<div class="tab-content">
			<div class="tab-pane fade show active" id="SectionA">
				<div class="card card-form-collapse">
					<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
						<h4>查詢條件</h4>
						<span class="tx-square-bracket">為必填欄位</span>
					</div>
					<div class="collapse show" id="collapseListGroup1">
						<div class="card-body">
							<form>
								<div class="form-row">
									<div class="form-group col-12 col-lg-4">
										<label class="form-label"> 商品代號 </label>
										<input class="form-control" id="prod_bank_pro_code" maxlength="20" v-model="bankProCode" size="25"
											type="text" value="" />
									</div>

									<div class="form-group col-12 col-lg-4">
										<label class="form-label"> 商品名稱</label>
										<input class="form-control" id="prod_pro_name" maxlength="20" v-model="proName" size="45"
											type="text" value="" />
									</div>

									<div class="form-group col-12 col-lg-4">
										<label class="form-label"> 商品類型</label>
										<select class="form-select" id="proType" name="proType" title="請選擇類型" v-model="proTypeCode"
											data-style="btn-white">
											<option value="">全部</option>
											<option v-for="item in proTypeMenu" value="item.proTypeCode">
												{{ $filters.defaultValue(item.proTypeName, '--') }}
											</option>
										</select>
									</div>

									<div class="form-group col-12 col-lg-4">
										<label class="form-label"> 計價幣別</label>
										<select class="selectpicker form-control" id="curMenuSec" multiple title="請選擇幣別" v-model="curObjs"
											data-style="btn-white">
											<option v-for="item in curOption" :key="index" :value="item.value">
												{{ $filters.defaultValue(item.name, '--') }}
											</option>
										</select>
									</div>

									<div class="form-group col-12 col-lg-4">
										<label class="form-label"> 風險等級 </label>
										<select v-model="riskCode" class="form-select" id="riskCode">
											<option value="">全部</option>
											<option v-for="item in riskMenu" :value="item.riskCode">
												{{ $filters.defaultValue(item.riskName, '--') }}
											</option>
										</select>
									</div>

									<div class="form-group col-12 col-lg-4">
										<label class="form-label">配息頻率</label>
										<select name="select" class="form-select" v-model="intFreqUnitType">
											<option value="">全部</option>
											<option v-for="intFreqUnit in intFreqUnitMenu" :value="intFreqUnit.codeValue">
												{{ $filters.defaultValue(intFreqUnit.codeName, '--') }}
											</option>
										</select>
									</div>

									<div class="form-group col-12 col-lg-4">
										<label class="form-label">最低投資金額</label>
										<input class="form-control" id="mininvFcAmtStart" maxlength="20" v-model="mininvFcAmtStart"
											size="10" type="text" value="" />~
										<input class="form-control" id="mininvFcAmtEnd" maxlength="20" v-model="mininvFcAmtEnd" size="10"
											type="text" value="" />
									</div>

									<div class="form-group col-12 col-lg-4">
										<label class="form-label">剩餘年限(年)</label>
										<input class="form-control" id="expireYearStart" maxlength="20" v-model="expireYearStart" size="10"
											type="text" value="" />~
										<input class="form-control" id="expireYearEnd" maxlength="20" v-model="expireYearEnd" size="10"
											type="text" value="" />
									</div>

									<!-- Moved 發行機構 to the bottom -->
									<div class="form-group col-12 col-lg-4">
										<label class="form-label">發行機構</label>
										<button type="button" class="btn btn-primary" @click="groupIssuerModalHandler()">選擇發行機構</button>
										<vue-modal :is-open="isOpenIssuerModal" :before-close="isOpenIssuerModal = false">
											<template v-slot:content="props">
												<vue-group-issuer-modal :close="props.close" ref="groupIssuerModalRef" id="groupIssuerModal"
													:pfcat-code="'SEC'" @selected="selectedIssuer"></vue-group-issuer-modal>
											</template>
										</vue-modal>
									</div>
								</div>

								<div style="padding-left: 1145px; padding-bottom: 15px" v-for="item in issuerItem">
									<span class="form-check-label"> {{ $filters.defaultValue(item.issuerName, '--') }}</span>
									<a href="#" @click="deleteIssuerItem(item.issuerCode)">
										<img :src="getImgURL('icon', 'i-cancel.png')" />
									</a>
								</div>

								<div class="form-footer">
									<button class="btn btn-primary" @click.prevent="gotoPage(0)">查詢</button>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>

			<div class="tab-pane fade" id="SectionB">
				<div class="card card-form-collapse">
					<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2">
						<h4>查詢條件</h4>
						<span class="tx-square-bracket">為必填欄位</span>
					</div>
					<div class="collapse show" id="collapseListGroup2">
						<div class="card-body">
							<div class="form-row">
								<div class="form-group col-12 col-lg-12">
									<label class="form-label tx-require"> 篩選條件 </label>
									<div class="form-check-group" v-for="item in secFastMenu" @change="fastChange(item.codeValue)">
										<input class="form-check-input" :id="'fast' + item.codeValue" name="fastCode" v-model="fastCode"
											:value="item.codeValue" type="radio" />
										<label class="form-check-label" :for="'fast' + item.codeValue">{{
											$filters.defaultValue(item.codeName, '--')
										}}</label>
									</div>
								</div>
								<div class="form-group col-12 col-lg-6" id="rangeFixedTr" style="display: none">
									<label class="form-label tx-require"> 顯示區間</label>

									<select class="form-select" id="prod_protype_code" v-model="timeRange">
										<option v-for="item in timeRangeMenu" :value="item.rangeType">
											{{ $filters.defaultValue(item.termName, '--') }}
										</option>
									</select>
								</div>

								<div class="form-group col-12 col-lg-6" id="proPerfTimeTr" style="display: none">
									<label class="form-label tx-require">標的績效 </label>

									<select class="form-select" id="vfAstStat_stat_code" v-model="perf">
										<option v-for="item in perfMenu" :value="item.codeValue">
											{{ $filters.defaultValue(item.codeName, '--') }}
										</option>
									</select>
								</div>

								<div class="form-group col-12 col-lg-6" id="maxRowIdTr" style="display: none">
									<label class="form-label tx-require">顯示資料筆數</label>
									<select class="form-select" id="maxRowId" v-model="rowNumber">
										<option v-for="item in rowNumerMenu" :value="item.rangeFixed">
											{{ $filters.defaultValue(item.termName, '--') }}
										</option>
									</select>
								</div>
							</div>
							<div class="form-footer">
								<button class="btn btn-primary" @click.prevent="gotoFastPage(0)">查詢</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div id="searchResult" v-if="pageData.content.length > 0">
			<div class="card card-table">
				<div class="card-header">
					<h4>查詢結果</h4>
					<div style="display: flex">
						<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
						<button type="button" class="btn btn-info ms-2" @click="performancesCompareModelHandler()">績效比較圖</button>
						<vue-modal :is-open="isOpenCompareModal" :before-close="isOpenCompareModal = false">
							<template v-slot:content="props">
								<vue-performances-compare-modal :close="props.close" ref="performancesCompareModalRef"
									id="performancesCompareModal"></vue-performances-compare-modal>
							</template>
						</vue-modal>
					</div>
				</div>
				<div class="table-responsive">
					<table class="table table-RWD table-bordered text-center">
						<thead>
							<tr>
								<th class="wd-100 text-start">加入比較</th>
								<th>商品代號</th>
								<th class="10% text-start">商品中文名稱</th>
								<th>計價幣別</th>
								<th>風險等級</th>
								<th>淨值日期</th>
								<th>參考淨值</th>
								<th>配息頻率</th>
								<th>最低投資金額</th>
								<th class="text-center" width="120">執行</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="(item, index) in pageData.content">
								<td data-th="加入比較" class="text-start text-center">
									<input class="form-check-input text-center" v-model="selectedItems[item.proCode]"
										:id="'id-' + item.bankProCode" type="checkbox" />
									<label class="form-check-label" :for="'id-' + item.bankProCode"></label>
								</td>
								<td data-th="商品代號">
									<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
								</td>
								<td class="text-start" data-th="商品中文名稱">
									<span>
										<a class="tx-link" @click="secModalHandler(item.proCode, item.pfcatCode)">{{
											$filters.defaultValue(item.proName, '--')
										}}</a>
									</span>
								</td>
								<td class="text-end" data-th="計價幣別">
									<span>{{ $filters.defaultValue(item.curCode, '--') }}</span>
								</td>
								<td data-th="風險等級">
									<span>{{ $filters.defaultValue(item.riskName, '--') }}</span>
								</td>
								<td class="text-end" data-th="淨值日期">
									<span>{{ $filters.defaultValue(item.priceDt, '--') }}</span>
								</td>
								<td data-th="參考淨值">
									<span>{{ $filters.defaultValue(item.aprice, '--') }}</span>
								</td>
								<td class="text-end" data-th="配息頻率">
									<span>{{ $filters.defaultValue(item.intFreqName, '--') }}</span>
								</td>
								<td data-th="最低申購面額">{{ $filters.defaultValue(item.mininvFcAmt, '--') }}</td>
								<td class="text-center" data-th="執行">
									<button v-if="activeTab === 'fast' && fastCode === '06'" type="button" class="btn btn-primary"
										title="移除我的最愛" @click="remove(item.proCode)">
										移除最愛
									</button>
									<button v-else type="button" class="btn btn-dark btn-icon" data-bs-toggle="tooltip" title="加入我的最愛"
										@click="favoritesHandler(item.proCode, item.pfcatCode)">
										<i class="bi bi-heart text-danger"></i>
									</button>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			<div class="tx-note">
				<ol>
					<li><span>資料日期：</span></li>
					<li><span>商品是否可申購以交易系統為主</span></li>
				</ol>
			</div>
		</div>
	</div>
</template>
<script>
import pagination from '@/views/components/pagination.vue';
import vuePerformancesCompareModal from './performancesCompareModal.vue';
import vueModal from '@/views/components/model.vue';
import vueGroupIssuerModal from './groupIssuerModal.vue';
import _ from 'lodash';
import { getImgURL } from '@/utils/imgURL.js';

export default {
	components: {
		'vue-pagination': pagination,
		vuePerformancesCompareModal,
		vueModal,
		vueGroupIssuerModal
	},
	props: {
		secModalHandler: Function,
		favoritesHandler: Function,
		curOption: Array,
		riskMenu: Array
	},
	data: function () {
		return {
			activeTab: 'common',
			bankProCode: null,
			proName: null,
			proTypeCode: '', //商品類型
			curObjs: [],
			riskCode: '',
			intFreqUnitType: '',
			mininvFcAmtStart: null, // 最低投資金額起
			mininvFcAmtEnd: null, // 最低投資金額迄
			expireYearStart: null, // 剩餘年限起
			expireYearEnd: null, // 剩餘年限迄

			fastCode: '05', // 快速 篩選條件
			timeRange: null, // 快速 顯示區間
			rowNumber: null, // 快速 顯示資料筆數

			proTypeMenu: [], // 商品類型選單
			intFreqUnitMenu: [], // 配息頻率下拉
			secFastMenu: [], //快速篩選條件
			timeRangeMenu: [], // 顯示區間
			rowNumerMenu: [], // 顯示資料筆數

			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: ['PRO_CODE'],
				direction: 'ASC'
			},
			issuerItem: [], //發行機構
			proCodes: [], // 商品代碼
			selectedItems: {}, // 加入比較選項,

			isOpenIssuerModal: false,
			isOpenCompareModal: false
		};
	},
	watch: {
		selectedItems: {
			handler(newValues) {
				this.proCodes = Object.keys(newValues).filter((proCode) => newValues[proCode]);
			},
			deep: true
		},
		activeTab(newVal, oldVal) {
			var self = this;
			self.fastCode = '06';
			self.fastChange(self.fastCode);
		}
	},
	mounted: function () {
		var self = this;
		$('#curMenuSec').selectpicker('refresh');
		self.getProTypeMenu(); // 取得商品類型選單
		self.getIntFreqUnitTypeMenu(); // 配息頻率
		self.getSecFastMenu(); // 取得快速篩選條件選項
		self.getTimeRangeMenu(); // 取得顯示區間選項
		self.getRowNumerMenu(); // 取得顯示資料筆數選項
		self.fastChange(self.fastCode);
	},
	methods: {
		getImgURL,
		// 條件Tab切換
		changeTab: function (tabName) {
			var self = this;
			self.activeTab = tabName;
			self.pageData = {
				// 清空查詢資料
				content: []
			};
		},
		// 商品類型選單
		async getProTypeMenu() {
			var self = this;
			const r = await this.$api.etProTypeListApi({
				pfcatCode: 'SEC'
			});
			self.proTypeMenu = r.data;
		},
		// 取得配息頻率選項
		getIntFreqUnitTypeMenu: async function () {
			var self = this;
			const ret = await this.$api.getAdmCodeDetail({
				codeType: 'INT_FREQ_UNITTYPE'
			});
			self.intFreqUnitMenu = ret.data;
		},
		// 取得快速篩選條件選項
		getSecFastMenu: async function () {
			var self = this;
			const ret = await this.$api.getSecFastMenuApi();
			self.secFastMenu = ret.data;
		},
		// 快速查詢切換
		fastChange(fastCode) {
			var self = this;
			self.timeRange = null; // 快速 顯示區間
			self.rowNumber = null; // 快速 顯示資料筆數
			self.pageData = {
				// 清空查詢資料
				content: []
			};
			if (fastCode === '05') {
				// 超人氣
				$('#proPerfTimeTr').hide();
				$('#rangeFixedTr').show();
				$('#maxRowIdTr').show();
				self.timeRange = 'D'; // 快速 顯示區間
				self.rowNumber = '10'; // 快速 顯示資料筆數
				$('#proPerfTimeTr').hide();
			} else {
				$('#maxRowIdTr').hide();
				$('#rangeFixedTr').hide();
				$('#proPerfTimeTr').hide();
			}
		},
		// 取得顯示區間
		getTimeRangeMenu: async function () {
			var self = this;
			const ret = await this.$api.getTimeRangeMenuApi();
			self.timeRangeMenu = ret.data;
		},
		// 取得顯示資料筆數
		getRowNumerMenu: async function () {
			var self = this;
			const ret = await this.$api.getRowNumerMenuApi();
			self.rowNumerMenu = ret.data;
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (_page) {
			var self = this;
			var url = '';

			var page = _.isNumber(_page) ? _page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;

			var curCodes = [];
			self.curObjs.forEach(function (item) {
				curCodes.push(item.value);
			});

			var issuerCodes = [];
			self.issuerItem.forEach(function (item) {
				issuerCodes.push(item.issuerCode);
			});

			const ret = await this.$api.getSecProductsApi(
				{
					bankProCode: self.bankProCode, // 商品代號
					proName: self.proName, //商品名稱
					protypeCode: self.proTypeCode, //商品類型
					curCodes: self.curObjs, // 計價幣別
					riskCode: self.riskCode, // 風險等級
					issuerCodes: issuerCodes, // 發行機構
					intFreqUnitType: self.intFreqUnitType, // 配息頻率
					mininvFcAmtStart: self.mininvFcAmtStart, // 最低投資金額起
					mininvFcAmtEnd: self.mininvFcAmtEnd, // 最低投資金額迄
					expireYearStart: self.expireYearStart, // 剩餘年限起
					expireYearEnd: self.expireYearEnd // 剩餘年限迄
				},
				url
			);
			self.pageData = ret.data;
			self.pageData.content.forEach((data) => {
				self.checkboxs.push(false);
			});
		},
		//執行績效比較圖
		performancesCompareModelHandler: function () {
			var self = this;
			if (self.proCodes.length > 0) {
				if (self.proCodes.length > 6) {
					thi.$bi.alert('最多加入6筆');
				} else {
					this.$refs.performancesCompareModalRef.comparePropItem(self.proCodes, 'sec');
					this.isOpenCompareModal = true;
				}
			} else {
				thi.$bi.alert('至少要勾選一項商品');
			}
		},
		gotoFastPage: function (page) {
			this.pageable.page = page;
			this.getFastPageData(page);
		},
		getFastPageData: async function (_page) {
			var self = this;
			var url = '';
			var page = _.isNumber(_page) ? _page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;

			const ret = await this.$api.getSecProductsFilterQueryApi({
				filterCodeValue: self.fastCode,
				timeRangeType: self.timeRange, // 顯示區間類型
				timeRangeFixed: self.rowNumber // 顯示區間數值
			});
			self.pageData = ret.data;
			self.pageData.content.forEach((data) => {
				self.checkboxs.push(false);
			});
		},
		//顯示發行機構 model
		groupIssuerModalHandler: function () {
			var self = this;
			this.$refs.groupIssuerModalRef.issuerPropItem(self.issuerItem);
			this.isOpenIssuerModal = true;
		},
		// 顯示發行機構選擇項目
		selectedIssuer(issuerItem) {
			var self = this;
			this.isOpenIssuerModal = false;
			self.issuerItem = issuerItem; //取得發行機構資料
		},
		// 刪除 發行機構選擇項目
		deleteIssuerItem(issuerCode) {
			var self = this;
			_.remove(self.issuerItem, (item) => item.issuerCode === issuerCode); // 移除刪除項目
		},
		// 刪除我的最愛
		async remove(proCode) {
			var self = this;
			await this.$api.deleteFavoriteApi({
				proCode: proCode
			});
			this.$bi.alert('刪除成功');
			self.checkboxs = [];
			self.proCodes = [];
			self.gotoFastPage(0);
		}
	} // methods end
};
</script>
