{"status": 200, "data": {"cusList": {"content": [{"cusCode": "00957600", "cusName": "聯00", "branName": "大屯分行      ", "col6": "72"}], "pageable": {"pageNumber": 0, "pageSize": 20, "sort": {"sorted": true, "unsorted": false, "empty": false}, "offset": 0, "unpaged": false, "paged": true}, "totalElements": 1, "totalPages": 1, "last": true, "first": true, "numberOfElements": 1, "size": 20, "number": 0, "sort": {"sorted": true, "unsorted": false, "empty": false}, "empty": false}, "fieldList": [{"fieldName": "CS.AUA_1M", "fieldShowName": " 近1月AUM", "fieldType": "F", "showOrder": 2}, {"fieldName": "CS.AUA_3M", "fieldShowName": " 近3月AUM", "fieldType": "F", "showOrder": 3}, {"fieldName": "CS.AUA_6M", "fieldShowName": " 近6月AUM", "fieldType": "F", "showOrder": 4}, {"fieldName": "CS.AUA_9M", "fieldShowName": " 近9月AUM", "fieldType": "F", "showOrder": 5}, {"fieldName": "CS.AUA_1Y", "fieldShowName": " 近1年AUM", "fieldType": "F", "showOrder": 6}, {"fieldName": "CS.AGE", "fieldShowName": " 年齡", "fieldType": "I", "showOrder": 9}]}, "timestamp": "2025/07/09", "sqlTracer": [{"data": [{"fieldName": "CS.AUA_1M", "fieldShowName": " 近1月AUM", "fieldType": "F", "showOrder": 2}, {"fieldName": "CS.AUA_3M", "fieldShowName": " 近3月AUM", "fieldType": "F", "showOrder": 3}, {"fieldName": "CS.AUA_6M", "fieldShowName": " 近6月AUM", "fieldType": "F", "showOrder": 4}, {"fieldName": "CS.AUA_9M", "fieldShowName": " 近9月AUM", "fieldType": "F", "showOrder": 5}, {"fieldName": "CS.AUA_1Y", "fieldShowName": " 近1年AUM", "fieldType": "F", "showOrder": 6}, {"fieldName": "CS.AGE", "fieldShowName": " 年齡", "fieldType": "I", "showOrder": 9}], "sqlInfo": "SELECT CSF.FIELD_NAME, CSF.FIELD_SHOW_NAME, CSF.FIELD_TYPE, CSF.SHOW_ORDER FROM CUS_SELFSET_FIELDS CSF1 LEFT JOIN CUS_SEARCH_FIELDS CSF ON CSF1.FIELD_NAME = CSF.FIELD_NAME WHERE CSF1.USER_CODE = :userId AND CSF.FIELD_KIND = :fieldKind AND CSF1.MENU_CODE = 'M21-01' ORDER BY CSF.SHOW_ORDER ,class com.bi.pbs.cus.web.model.SelfsetFieldsResp,{fieldKind=M01, userId=112790}"}, {"data": [], "sqlInfo": "SELECT DISTINCT TBL.REL_JOIN, TBL.REL_TABLE FROM CUS_SEARCH_REL_TABLES TBL INNER JOIN CUS_SEARCH_FIELD_RELT_MAP MAP ON TBL.RELT_CODE = MAP.RELT_CODE INNER JOIN CUS_SEARCH_FIELDS CSF ON CSF.FIELD_KIND = MAP.FIELD_KIND AND CSF.FIELD_NAME = MAP.FIELD_NAME WHERE CSF.FIELD_KIND = :fieldKind AND CSF.FIELD_NAME IN (:rFieldName),class com.bi.pbs.cus.model.CusSearchRelTables,{fieldKind=M01, rFieldName=[CS.AUA_1M, CS.AUA_3M, CS.AUA_6M, CS.AUA_9M, CS.AUA_1Y, CS.AGE]}"}, {"data": {"content": [{"cusCode": "00957600", "cusName": "聯00", "branName": "大屯分行      ", "col6": "72"}], "pageable": {"pageNumber": 0, "pageSize": 20, "sort": {"sorted": true, "unsorted": false, "empty": false}, "offset": 0, "unpaged": false, "paged": true}, "totalElements": 1, "totalPages": 1, "last": true, "first": true, "numberOfElements": 1, "size": 20, "number": 0, "sort": {"sorted": true, "unsorted": false, "empty": false}, "empty": false}, "sqlInfo": "SELECT CS.CUS_CODE ,CS.CUS_NAME ,AB.BRAN_NAME ,CAI.AO_CODE ,AU.USER_NAME ,CS.AUA_1M AS COL1 ,CS.AUA_3M AS COL2 ,CS.AUA_6M AS COL3 ,CS.AUA_9M AS COL4 ,CS.AUA_1Y AS COL5 ,CS.AGE AS COL6 FROM CUS_SUMMARIES CS INNER JOIN CUS_AO_INFO CAI ON CAI.CUS_CODE = CS.CUS_CODE LEFT JOIN ADM_USERS AU ON AU.USER_CODE = CAI.USER_CODE LEFT JOIN ADM_BRANCHES AB ON AB.BRAN_CODE = CAI.BRAN_CODE AND AB.BU_CODE = 'Z' LEFT JOIN CUS_GRADES CG ON CS.GRA_CODE = CG.GRA_CODE LEFT JOIN CUS_RANKS CR ON CS.RANK_CODE = CR.RANK_CODE LEFT JOIN ADM_CODE_DETAIL ACD ON CS.CUS_BU = ACD.CODE_VALUE AND ACD.CODE_TYPE = 'CUS_BU' WHERE 1 = 1  AND EXISTS ( \tSELECT 1 FROM ( SELECT P.BRAN_CODE, P.POS_CODE, P.ROLE_CODE, P.BU_CODE, AUPM.USER_CODE FROM ADM_POS_ACCESS_MAP PAM JOIN ADM_POSITIONS P ON PAM.ACCESS_POS_CODE = P.POS_CODE LEFT JOIN ADM_USER_POS_MAP AUPM ON P.POS_CODE = AUPM.POS_CODE WHERE PAM.POS_CODE =  '891_98'  \t) AUTH  \tWHERE CAI.BRAN_CODE = AUTH.BRAN_CODE AND CAI.BU_CODE = AUTH.BU_CODE ) AND EXISTS (SELECT 1 FROM CUS_SEARCH_RESULT_LISTS CSRL WHERE CSRL.CUS_CODE = CS.CUS_CODE AND CSRL.USER_CODE = :userId AND CSRL.RESULT_CODE = :resultCode) ,Page request [number: 0, size 20, sort: CUS_CODE: ASC],class com.bi.pbs.cus.web.model.GetSingleCusResp,{resultCode=RC20250415000002, userId=112790}"}]}