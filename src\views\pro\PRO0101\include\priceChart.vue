<template>
	<!-- 線圖 -->
	<div :ref="chartId" :id="chartId" style="height: 500px"></div>
</template>
<script>
import * as am5 from '@amcharts/amcharts5';
import * as am5xy from '@amcharts/amcharts5/xy';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import { toRaw } from 'vue';
export default {
	props: {
		chartId: String
	},
	data: function () {
		return {
			am5Obj: {}
		};
	},
	watch: {},
	mounted: function () {},
	beforeDestroy: function () {
		this.destroyChart();
	},
	methods: {
		// 畫圖，傳入資料(單組或多組資料)
		initChart(chartData) {
			let self = this;
			// Create root element
			// https://www.amcharts.com/docs/v5/getting-started/#Root_element
			let { am5Obj } = self;
			// 處理props proxy data問題
			// chartData = JSON.parse(JSON.stringify(chartData));
			let firstLoad = false;
			//透過 toRaw 解決因為 vue data proxy 導致跟 amcharts 衝突
			let { xAxis, yAxis, sbxAxis, sbyAxis, root, chart, legend, sbseries } = toRaw(am5Obj); // 用toRaw把元件load出來
			if (!root) {
				firstLoad = true;

				// Create root element
				// https://www.amcharts.com/docs/v5/getting-started/#Root_element
				root = am5.Root.new(self.chartId); // 由商品頁塞入id
				root._logo.dispose();

				const myTheme = am5.Theme.new(self.chartId);
				myTheme.rule('AxisLabel', ['minor']).setAll({
					dy: 1
				});
				myTheme.rule('Grid', ['x']).setAll({
					strokeOpacity: 0.05
				});
				myTheme.rule('Grid', ['x', 'minor']).setAll({
					strokeOpacity: 0.05
				});
				// Set themes
				// https://www.amcharts.com/docs/v5/concepts/themes/
				root.setThemes([am5themes_Animated.new(root), myTheme]);

				// Create chart
				// https://www.amcharts.com/docs/v5/charts/xy-chart/
				chart = root.container.children.push(
					am5xy.XYChart.new(root, {
						panX: true,
						panY: true,
						wheelX: 'panX',
						wheelY: 'zoomX',
						maxTooltipDistance: 0,
						pinchZoomX: true
					})
				);

				chart.get('colors').set('colors', [am5.color(0xfca631), am5.color(0xef4141), am5.color(0x2986cc)]);

				// Create axes X軸
				// https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
				xAxis = chart.xAxes.push(
					am5xy.DateAxis.new(root, {
						maxDeviation: 0.2,
						groupData: false,
						baseInterval: {
							timeUnit: 'day',
							count: 1
						},
						renderer: am5xy.AxisRendererX.new(root, {
							minorGridEnabled: true
						}),
						tooltip: am5.Tooltip.new(root, {})
					})
				);
				// Y軸
				yAxis = chart.yAxes.push(
					am5xy.ValueAxis.new(root, {
						renderer: am5xy.AxisRendererY.new(root, {})
					})
				);
			} else {
				chart.series.clear();
				legend.data.clear();
				chartData = JSON.parse(JSON.stringify(chartData));
			}

			if (firstLoad) {
				// Add cursor
				// https://www.amcharts.com/docs/v5/charts/xy-chart/cursor/
				var cursor = chart.set(
					'cursor',
					am5xy.XYCursor.new(root, {
						behavior: 'zoomX'
					})
				);
				cursor.lineY.set('visible', false);

				var scrollbar;
				if (!this.scrollBarItem) {
					// Add scrollbar
					// https://www.amcharts.com/docs/v5/charts/xy-chart/scrollbars/
					scrollbar = am5xy.XYChartScrollbar.new(root, {
						orientation: 'horizontal',
						height: 50
					});

					chart.set('scrollbarX', scrollbar);
					sbxAxis = scrollbar.chart.xAxes.push(
						am5xy.DateAxis.new(root, {
							groupData: true,
							groupIntervals: [{ timeUnit: 'month', count: 1 }],
							baseInterval: { timeUnit: 'day', count: 1 },
							renderer: am5xy.AxisRendererX.new(root, {
								minorGridEnabled: true,
								opposite: false,
								strokeOpacity: 0
							})
						})
					);
					sbyAxis = scrollbar.chart.yAxes.push(
						am5xy.ValueAxis.new(root, {
							renderer: am5xy.AxisRendererY.new(root, {})
						})
					);
					this.scrollBarItem = scrollbar;
					this.sbxAxis = sbxAxis;
					this.sbyAxis = sbyAxis;
				} else {
					scrollbar = this.scrollBarItem;
					scrollbar.chart.series.clear();
				}

				sbseries = scrollbar.chart.series.push(
					am5xy.LineSeries.new(root, {
						xAxis: sbxAxis,
						yAxis: sbyAxis,
						valueYField: 'value',
						valueXField: 'date'
					})
				);

				// Add legend 圖表最上面線的標示(含名稱)
				// https://www.amcharts.com/docs/v5/charts/xy-chart/legend-xy-series/
				legend = chart.children.push(
					am5.Legend.new(root, {
						centerY: am5.p50,
						y: am5.p0
					})
				);

				// When legend item container is hovered, dim all the series except the hovered one
				legend.itemContainers.template.events.on('pointerover', function (e) {
					var itemContainer = e.target;

					// As series list is data of a legend, dataContext is series
					var series = itemContainer.dataItem.dataContext;

					chart.series.each(function (chartSeries) {
						if (chartSeries != series) {
							chartSeries.strokes.template.setAll({
								strokeOpacity: 0.15,
								stroke: am5.color(0xffffff)
							});
						} else {
							chartSeries.strokes.template.setAll({
								strokeWidth: 3
							});
						}
					});
				});

				// When legend item container is unhovered, make all series as they are
				legend.itemContainers.template.events.on('pointerout', function (e) {
					var itemContainer = e.target;
					// var series = itemContainer.dataItem.dataContext;

					chart.series.each(function (chartSeries) {
						chartSeries.strokes.template.setAll({
							strokeOpacity: 1,
							strokeWidth: 1,
							stroke: chartSeries.get('fill')
						});
					});
				});

				// legend.itemContainers.template.set("width", am5.p100);

				// It's is important to set legend data after all the events are set on template, otherwise events won't be copied
				legend.data.setAll(chart.series.values);
				Object.assign(am5Obj, { xAxis, yAxis, sbxAxis, sbyAxis, root, chart, legend, sbseries });
			}

			// Add series
			// https://www.amcharts.com/docs/v5/charts/xy-chart/series/
			for (var i = 0; i < chartData.length; i++) {
				let item = chart.series.push(
					am5xy.LineSeries.new(root, {
						name: chartData[i].name,
						xAxis: xAxis,
						yAxis: yAxis,
						valueYField: 'value',
						valueXField: 'date',
						tooltip: am5.Tooltip.new(root, {
							pointerOrientation: 'horizontal',
							// labelText: '[bold]{name}[/]\n{valueX.formatDate()}: {valueY}'
							labelText: '[bold]{name}[/]{categoryX}: {valueY}'
						})
					})
				);
				item.data.setAll(chartData[i].datas);
				sbseries.data.setAll(chartData[i].datas);
				legend.data.push(item);

				// Make stuff animate on load
				// https://www.amcharts.com/docs/v5/concepts/animations/
				if (firstLoad) {
					item.appear(1000, 100);
				}
			}

			// Make stuff animate on load
			// https://www.amcharts.com/docs/v5/concepts/animations/
			if (firstLoad) {
				chart.appear(1000, 100);
			}
		},
		destroyChart: function () {
			let { am5Obj } = this;
			let { root } = Vue.toRaw(am5Obj);
			if (root) {
				root.dispose();
			}
		}
	} // methods end
};
</script>
