<template>
	<div class="col-12">
		<h4>
			基金累積報酬/規模比較線圖
			<small class="small-text">資料更新日： {{ $filters.defaultValue($filters.formatDate(latestDate), '--') }}</small>
		</h4>
		<div class="bg-contrast-lower padding-10 m-b-10">
			<div class="form-group row align-items-center">
				<div class="col-sm-3">
					<select v-model="searchData.bmCode" class="form-select selectPicker w-100">
						<option disabled :value="null">-指數-</option>
						<option v-for="item in benchmarks" :value="item.bmCode" v-show="item.name">{{ item.name }}</option>
					</select>
				</div>
				<div class="col-sm-1">
					<button class="btn btn-info" @click="addCompareBenchmark" :disabled="!searchData.bmCode || hasAddedPct(searchData.bmCode)">
						加入
					</button>
				</div>
				<div class="col-sm-3">
					<select v-model="searchData.companyCode" @change="getFunds" class="form-select selectPicker w-100">
						<option disabled :value="null">-基金公司-</option>
						<option v-for="item in companies" :value="item.companyCode" v-show="item.companyName">{{ item.companyName }}</option>
					</select>
				</div>
				<div class="col-sm-4">
					<select v-model="searchData.fundCode" :disabled="funds.length == 0" class="form-select selectPicker w-100">
						<option disabled :value="null">-基金-</option>
						<option v-for="item in funds" :value="item.fundCode" v-show="item.fundName">{{ item.fundName }}</option>
					</select>
				</div>
				<div class="col-sm-1">
					<button class="btn btn-info" @click="addCompareFund" :disabled="!searchData.fundCode || hasAddedPct(searchData.fundCode)">
						加入
					</button>
				</div>
			</div>
		</div>
		<div class="btn-group m-l-20 m-r-20">
			<span class="btn btn-sm btn-default" :class="clsPeriod(1)" @click="changePeriod(1)">1月</span>
			<span class="btn btn-sm btn-default" :class="clsPeriod(12)" @click="changePeriod(12)">1年</span>
			<span class="btn btn-sm btn-default" :class="clsPeriod('YTD')" @click="changePeriod('YTD')">今年以來</span>
			<span class="btn btn-sm btn-default" :class="clsPeriod(60)" @click="changePeriod(60)">5年</span>
		</div>
		<div class="btn-group pull-right m-r-20">
			<span class="btn btn-sm btn-default" @click="resetPcts">重置線圖</span>
		</div>
		<div class="row">
			<div class="col-12">
				<vue-fund-stock-chart
					v-if="fundInfo && fundInfo.fundEnName && fundInfo.fundEnName.trim()"
					:fund-name="fundInfo.fundEnName"
					chart-id="fundStockLineChartId"
					:prop-chart-data="chartData"
					:line-array="chartLineArray"
					style="height: 500px"
				></vue-fund-stock-chart>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import moment from 'moment';
import vueFundStockChart from './fundStockChart.vue';
export default {
	components: {
		vueFundStockChart
	},
	props: {
		techCurrencyCode: String,
		fundInfo: Object
	},
	data: function () {
		return {
			searchData: {
				bmCode: null,
				companyCode: null,
				fundCode: null
			},
			selectedPeriod: 'YTD',
			benchmarks: [],
			fundCompanies: [],
			funds: [],
			fundSizes: [],
			compareAssets: [],
			pcts: [],
			fundStockLineChartId: 'fundStockLineChartId',
			chartLineArray: []
		};
	},
	watch: {
		fundInfo: {
			handler: async function (newVal, oldVal) {
				this.compareAssets.push({
					assetCode: newVal.fundCode || '',
					assetName: newVal.fundEnName || ''
				});
				await this.getFundSize();
				await this.getPcts();
			}
		}
	},
	computed: {
		latestDate: function () {
			if (this.fundSizes.length > 0) {
				return _.orderBy(
					_.filter(this.fundSizes, function (item) {
						return item.tnaDate;
					}),
					['tnaDate'],
					['desc']
				)[0].tnaDate;
			} else {
				return null;
			}
		},
		chartData: function () {
			var self = this;
			var seriesValues = [];
			self.chartLineArray = [];
			var dates = _.flatMap(self.pcts, function (item) {
				return _.map(item.value, 'dataDate');
			});
			dates = _.sortBy(dates);
			dates = _.filter(dates, function (item, i, iteratee) {
				return _.includes(iteratee, item, i + (self.pcts.length - 1));
			});
			_.forEach(self.pcts, function (item, i) {
				self.chartLineArray.push({
					valueName: 'value' + i,
					fundName: item.assetName
				});
				_.forEach(item.value, function (value) {
					if (self.pcts.length === 1 || _.includes(dates, value.dataDate)) {
						var seriesValue = _.find(seriesValues, { date: value.dataDate });
						if (seriesValue) {
							seriesValue['value' + i] = _.round(value.dvalue, 2);
						} else {
							var fundSize = _.find(self.fundSizes, {
								tnaDate: value.dataDate
							});
							seriesValues.push({
								['value' + i]: _.round(value.dvalue, 2),
								volume: fundSize ? _.round(fundSize.tnaValue / 1000000, 2) : null,
								date: value.dataDate
							});
						}
					}
				});
			});

			seriesValues.forEach(function (value) {
				value.date = Date.parse(value.date);
			});

			return _.orderBy(seriesValues, ['date']);
		},
		companies: function () {
			var companies = _.map(this.fundCompanies, function (item) {
				return {
					companyCode: item.companyCode,
					companyName: item.name
				};
			});
			companies = _.uniqBy(companies, 'companyCode');
			return _.orderBy(companies, ['companyName']);
		}
	},
	mounted: async function () {
		await this.getBenchmarks();
		await this.getFundCompanies();
	},
	methods: {
		getBenchmarks: async function () {
			var self = this;
			self.companies = [];
			const ret = await this.$api.getBenchmarksApi();
			elf.benchmarks = _.orderBy(ret.data, ['name']);
		},
		getFundCompanies: async function () {
			var self = this;
			self.funds = [];
			const ret = await this.$api.getFundCompaniesApi();
			self.fundCompanies = ret.data;
		},
		getFunds: async function () {
			var self = this;
			self.searchData.fundCode = null;
			const ret = await this.$api.getFundsApi({
				companyCode: self.searchData.companyCode
			});
			self.funds = ret.data;
			self.funds = _.orderBy(funds, ['fundName']);
		},
		hasAddedPct: function (assetCode) {
			return _.find(this.compareAssets, { assetCode: assetCode });
		},
		addCompareFund: function () {
			var fund = _.find(this.funds, { fundCode: this.searchData.fundCode });
			if (fund) {
				this.compareAssets.push({
					assetCode: fund.fundCode,
					assetName: fund.fundName
				});
				this.getPcts();
			}
		},
		addCompareBenchmark: function () {
			var benchmark = _.find(this.benchmarks, {
				bmCode: this.searchData.bmCode
			});
			if (benchmark) {
				this.compareAssets.push({
					assetCode: benchmark.bmCode,
					assetName: benchmark.name
				});
				this.getPcts();
			}
		},
		clsPeriod: function (period) {
			return this.selectedPeriod === period ? 'active' : null;
		},
		changePeriod: function (period) {
			this.selectedPeriod = period;
			this.getFundSize();
			this.getPcts();
		},
		resetPcts: function () {
			var compareAssets = [
				{
					assetCode: this.fundInfo.fundCode || '',
					assetName: this.fundInfo.fundEnName || ''
				}
			];
			this.compareAssets = compareAssets;
			this.getPcts();
		},
		getFundSize: async function () {
			var self = this;
			var period = self.selectedPeriod;
			var beginDate;
			if (period === 1 || period === 'YTD') {
				beginDate = moment().startOf('year').format('YYYY/MM/DD');
			} else {
				beginDate = moment().subtract(period, 'months').format('YYYY/MM/DD');
			}
			const ret = await this.$api.getFundSizeApi({
				fundCode: self.fundInfo.fundCode,
				beginDate: beginDate,
				endDate: moment().format('YYYY/MM/DD')
			});
			self.fundSizes = ret.data;
			return;
		},
		getPcts: async function () {
			var self = this;
			var period = self.selectedPeriod;
			var beginDate;
			if (period === 'YTD') {
				beginDate = moment().startOf('year').format('YYYY/MM/DD');
			} else {
				beginDate = moment().subtract(period, 'months').format('YYYY/MM/DD');
			}
			const ret = await this.$api.getPctsApi({
				beginDate: beginDate,
				endDate: moment().format('YYYY/MM/DD'),
				techCurrencyCode: self.techCurrencyCode,
				assetCodes: _.map(this.compareAssets, 'assetCode').join()
			});

			var list = ret.data;
			var pcts = [];
			_.forEach(self.compareAssets, function (item) {
				var assetCode = item.assetCode;
				var assetName = item.assetName;
				var assetPcts = _.filter(list, function (item) {
					return item.assetCode === assetCode;
				});
				if (assetPcts && assetPcts.length > 0) {
					pcts.push({
						assetCode: assetCode,
						assetName: assetName,
						value: assetPcts
					});
				}
			});
			self.pcts = pcts;
		}
	}
};
</script>
