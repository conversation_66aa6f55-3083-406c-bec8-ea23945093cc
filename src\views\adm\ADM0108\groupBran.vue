<template>
	<dynamicTitle />
	<div>
		<div class="row">
			<div class="col-12">
				<vue-bi-tabs :menu-code="'M00-07'" @change-tab="changeTab">
					<template #default="{ id }">
						<component :is="id"></component>
					</template>
				</vue-bi-tabs>
			</div>
		</div>
		<!--頁面內容 end-->
	</div>
</template>
<script>
import vueBiTabs from '@/views/components/biTabs.vue';
import vueGroupBranFpUpload from './include/groupBranFpUpload.vue';
import vueGroupBranFpSearch from './include/groupBranFpSearch.vue';
import dynamicTitle from '@/views/components/dynamicTitle.vue';

export default {
	components: {
		dynamicTitle,
		vueBiTabs,
		vueGroupBranFpUpload,
		vueGroupBranFpSearch
	},
	data: function () {
		return {
			//畫面顯示用參數
			customTitle: null,
			//畫面邏輯判斷用參數
			tabCodeTitleMap: {
				'M00-070': '分行分區設定',
				'M00-071': '分行分區查詢'
			}
		};
	},
	methods: {
		changeTab: function (tabCode) {
			var self = this;
			self.customTitle = self.tabCodeTitleMap[tabCode];
		}
	}
};
</script>
