<template>
	<dynamic-title></dynamic-title>
	<div class="container-fluid">
		<div class="row">
			<div class="col-12">
				<div class="card card-table-list">
					<div class="card-header">
						<h4>待審核清單</h4>
					</div>
					<!-- 待審核清單 -->
					<table class="table table-RWD table-list text-center">
						<thead>
							<tr>
								<th>提交日期</th>
								<th>提交人員</th>
								<th v-for="varItem in varItems">{{ varItem.varItemName }}</th>
								<th>申請明細</th>
								<th class="wd-100">審核狀態</th>
								<th class="wd-10p">其他說明</th>
							</tr>
						</thead>
						<tbody>
							<template v-for="item in wkfEvents">
								<tr>
									<td data-th="提交日期">
										<span>{{ item.createDt }}</span>
									</td>
									<td data-th="提交人員">
										<span>{{ item.createBy }} {{ item.userName }}</span>
									</td>

									<td v-if="isEmptyItemData(item)" v-for="(itemData, index) in item.itemDatas" :key="index">
										<span v-html="itemData.varItemValue"></span>
									</td>
									<!-- 無值輸出空白 避免排版錯誤 -->
									<td v-else v-for="varItem in varItems">
										<span></span>
									</td>

									<td data-th="申請明細" class="text-center">
										<button type="button" class="btn btn-dark btn-icon" @click="getDetail(item.eventId)">
											<i class="bi bi-search"></i>
										</button>
									</td>
									<td data-th="審核狀態" class="text-start">
										<ul class="list-unstyled mb-0" v-for="(chekcItem, index) in item.wkfEngineFlows">
											<li v-if="item.buttonYn == 'Y'">
												<div class="form-check">
													<input
														class="form-check-input"
														type="radio"
														:id="item.eventId + index"
														:name="item.eventId"
														v-model="item.status"
														:value="chekcItem.actionStatus"
													/>
													<label class="form-check-label" :for="item.eventId + index">{{ chekcItem.actionName }}</label>
												</div>
											</li>
										</ul>
									</td>
									<td data-th="其他說明">
										<textarea class="form-control" rows="3" :disabled="item.status != 'R'" v-model="item.desc"></textarea>
									</td>
								</tr>
							</template>
						</tbody>
					</table>
				</div>
			</div>
			<div class="col-12 mt-3 text-end">
				<input class="btn btn-primary btn-lg" type="button" @click="audit()" value="審核完成" />
			</div>
		</div>

		<adm-role-review-detail ref="admRoleReviewDetail"></adm-role-review-detail>
		<vue-mkt-camp-review-detail ref="mktCampReviewDetail"></vue-mkt-camp-review-detail>
		<user-account-detail ref="userAccountDetail"></user-account-detail>

		<vue-pro-mgt-review-detail ref="proMgtReviewDetail" v-if="wfgId === 'WFG20241203001'"></vue-pro-mgt-review-detail>
		<vue-pro-new-review-detail ref="proNewReviewDetail" v-if="wfgId === 'WFG20241203002'"></vue-pro-new-review-detail>
		<vue-pro-pfcats-review-detail ref="proPfcatsReviewDetail" v-if="wfgId === 'WFG20241203003'"></vue-pro-pfcats-review-detail>
	</div>
</template>

<script>
import dynamicTitle from '@/views/components/dynamicTitle.vue';
import userAccountDetail from './include/userAccountDetail.vue';
import admRoleReviewDetail from './include/admRoleReviewDetail.vue';
import vueMktCampReviewDetail from '@/views/mkt/MKT106/include/mktCampReviewDetail.vue';
import vueProMgtReviewDetail from '@/views/pro/PrdReview/proMgtReviewDetail.vue';
import vueProNewReviewDetail from '@/views/pro/PrdAdd/proNewReviewDetail.vue';
import vueProPfcatsReviewDetail from '@/views/pro/PrdVerify/proPfcatsReviewDetail.vue';
export default {
	components: {
		dynamicTitle,
		userAccountDetail,
		admRoleReviewDetail,
		vueMktCampReviewDetail,
		vueProMgtReviewDetail,
		vueProNewReviewDetail,
		vueProPfcatsReviewDetail
	},
	data: function () {
		return {
			//主要顯示資料
			wkfEvents: [],
			varItems: [],
			wfgId: null
		};
	},
	created() {
		this.wfgId = this.$route.params.wfgId;
	},
	mounted: function () {
		this.getVarItems();
		this.getWkfEvents();
	},
	methods: {
		getVarItems: async function () {
			let ret = await this.$api.getVarItemsApi(this.wfgId);
			this.varItems = ret.data;
		},
		getWkfEvents: async function () {
			let ret = await this.$api.getWkfEventsApi(this.wfgId);
			this.wkfEvents = ret.data;
		},
		getDetail: function (eventId) {
			if (this.$_.isEqual(this.wfgId, 'WFG20121005008')) {
				// 系統角色維護
				this.$refs.admRoleReviewDetail.getDetail(eventId);
			}
			if (this.$_.isEqual(this.wfgId, 'WFG20241118001')) {
				// 活動建立審核
				this.$refs.mktCampReviewDetail.getDetail(eventId);
			}
			if (this.$_.isEqual(this.wfgId, 'WFG20141112001')) {
				// 系統使用者管理審核
				this.$refs.userAccountDetail.getDetail(eventId);
			}
			if (this.$_.isEqual(this.wfgId, 'WFG20241203001')) {
				// 商品資料審核
				this.$refs.proMgtReviewDetail.getDetail(eventId);
			}
			if (this.$_.isEqual(this.wfgId, 'WFG20241203002')) {
				// 新商品臨時上架審核
				this.$refs.proNewReviewDetail.getDetail(eventId);
			}
			if (this.$_.isEqual(this.wfgId, 'WFG20241203003')) {
				// 精選推薦商品審核
				this.$refs.proPfcatsReviewDetail.getDetail(eventId);
			}
		},
		isEmptyItemData: function (item) {
			return !this.$_.isEmpty(item.itemDatas);
		},
		audit: async function () {
			let wkfEvents = this.wkfEvents;
			let updateRoleMenuLogs = [];

			for (let i = 0; i < wkfEvents.length; i++) {
				let wkfEvent = wkfEvents[i];
				if (wkfEvent.status == 'A' || wkfEvent.status == 'R') {
					if (wkfEvent.status == 'R' && this.$_.isBlank(wkfEvent.desc)) {
						this.$bi.alert('請輸入退回原因。');
						return;
					}
					updateRoleMenuLogs.push(wkfEvent);
				}
			}

			if (this.$_.isEmpty(updateRoleMenuLogs)) {
				this.$bi.alert('無可審核資料。');
				return;
			}

			let finishCnt = 0;
			for (let i = 0; i < updateRoleMenuLogs.length; i++) {
				let roleMenuLog = updateRoleMenuLogs[i];

				let ret = await this.$api.patchAuditApi(
					roleMenuLog.eventId,
					this.$_.filter(roleMenuLog.wkfEngineFlows, ['actionStatus', roleMenuLog.status])[0].actionCode,
					roleMenuLog.desc
				);

				this.$_.handleWkfResp(ret.data, true);
				finishCnt++;

				if (finishCnt == updateRoleMenuLogs.length) {
					this.$swal
						.fire({
							icon: 'success',
							text: '審核完成。',
							showCloseButton: true,
							confirmButtonText: '確認',
							buttonsStyling: false,
							customClass: {
								confirmButton: 'btn btn-success'
							}
						})
						.then(function () {
							this.getWkfEvents();
						});
				}
			}
		}
	},
	watch: {
		'$route.params.wfgId': function () {
			this.wfgId = this.$route.params.wfgId;
			this.getVarItems();
			this.getWkfEvents();
		}
	}
};
</script>
