<template>
	<!-- modal -->
	<vue-modal :is-open="isOpenModal" :before-close="closeModal">
		<template v-slot:content="props">
			<div class="modal-dialog modal-dialog-centered modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">新商品臨時上架</h4>
						<button type="button" class="btn-expand"><i class="bi bi-arrows-fullscreen"></i></button>
						<button type="button" class="btn-close" @click.prevent="props.close()" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="tx-title">商品基本資料</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th width="20%">商品主類</th>
									<td width="30%">{{ proPreview.pfcatName }}</td>
									<th width="20%">商品次類</th>
									<td width="30%">{{ proPreview.proTypeName }}</td>
								</tr>
								<tr>
									<th>商品代碼</th>
									<td>{{ proPreview.bankProCode }}</td>
									<th>資產類別</th>
									<td>{{ proPreview.assetcatName }}</td>
								</tr>
								<tr>
									<th>商品中文名稱</th>
									<td>{{ proPreview.proName }}</td>
									<th>商品風險屬性</th>
									<td>{{ proPreview.riskName }}</td>
								</tr>
								<tr>
									<th>計價幣別</th>
									<td>{{ proPreview.curName }}</td>
									<th>銷售對象</th>
									<td>{{ getTargetCusBuName(proPreview.targetCusBu) }}</td>
								</tr>
								<tr>
									<th>限PI銷售</th>
									<td>{{ proPreview.profInvestorYn === 'Y' ? '是' : '否' }}</td>
									<th>是否為國內基金</th>
									<td>{{ proPreview.localYn === 'Y' ? '是' : '否' }}</td>
								</tr>
								<tr v-if="!disabledFields">
									<th>到期日</th>
									<td>{{ proPreview.expireDt }}</td>
									<th>狀態</th>
									<td>{{ proPreview.actionName }}</td>
								</tr>
								<tr v-if="!disabledFields">
									<th>檔案上傳</th>
									<td colspan="3">
										<span v-for="(item, index) in proPreview.files">
											<a v-if="item" href="#" class="tx-link" @click="downloadFile(item)">{{ item.fileName }}</a>
											<br />
										</span>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
					<!--
				<div class="modal-body">
					<div class="tx-title">商品基本資料</div>
					<table class="table table-bordered">

						<tbody>
							<tr>
								<th width="20%">商品主類</th>
								<td width="30%">{{proPreview.pfcatName}}</td>
								<th width="20%">商品次類</th>
								<td width="30%">{{proPreview.proTypeName}}</td>
							</tr>
							<tr>
								<th>商品代碼</th>
								<td>{{proPreview.bankProCode}}</td>
								<th>資產類別</th>
								<td>{{proPreview.assetcatName}}</td>
							</tr>
							<tr>
								<th>商品中文名稱</th>
								<td>{{proPreview.proName}}</td>
								<th>商品風險屬性</th>
								<td>{{proPreview.riskName}}</td>
							</tr>
							<tr>
								<th>計價幣別</th>
								<td>{{proPreview.curName}}</td>
								<th>發行機構</th>
								<td>{{proPreview.issuerName}}</td>
							</tr>
							<tr>
								<th>到期日</th>
								<td>{{proPreview.expireDt}}</td>
								<th>狀態</th>
								<td>{{proPreview.actionName}}</td>
							</tr>
							<tr>
								<th>檔案上傳</th>
                				<td colspan="3">
									<span v-for="(item, index) in proPreview.files">
										<a v-if="item" href="#" class="tx-link" @click="downloadFile(item)">{{item.fileName}}</a>
										<br>
									</span>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
				-->

					<div class="modal-footer">
						<input
							name="btnClose"
							class="btn btn-white"
							id="appointmentCloseButton"
							type="button"
							value="關閉"
							@click.prevent="props.close()"
						/>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
	<!-- modal end -->
</template>
<script>
import _ from 'lodash';
export default {
	props: {},
	data: function () {
		return {
			disabledFields: true,
			proPreview: {},
			isOpenModal: false,
			buCodeMenu: [
				{ codeValue: 'Y', codeName: '全部' },
				{ codeValue: 'D', codeName: 'DBU' },
				{ codeValue: 'O', codeName: 'OBU' }
			]
		};
	},
	methods: {
		getDetail: function (eventId) {
			var self = this;
			if (_.isBlank(eventId)) {
				return;
			}
			self.$api
				.getNewShelfProductList({
					eventId: eventId
				})
				.then(function (ret) {
					if (ret.data && ret.data.length > 0) {
						self.proPreview = ret.data[0];
					}
					self.isOpenModal = true;
				});
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		getTargetCusBuName(code) {
			var self = this;
			const item = self.buCodeMenu.find((item) => item.codeValue === code);
			return item ? item.codeName : '';
		},
		downloadFile: function (proFile) {
			var self = this;
			var id = proFile.proFileId;
			self.$api.downloadProFileNewLog({ fileId: id });
		}
	}
};
</script>
