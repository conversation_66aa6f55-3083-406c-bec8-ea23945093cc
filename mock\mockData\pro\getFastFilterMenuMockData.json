{"status": 200, "data": [{"codeValue": "03", "codeName": "聚焦商品"}, {"codeValue": "06", "codeName": "我的最愛"}], "timestamp": "2025/06/30", "sqlTracer": [{"data": [{"codeValue": "03", "codeName": "聚焦商品"}, {"codeValue": "02", "codeName": "主力商品"}, {"codeValue": "01", "codeName": "本週主打星"}, {"codeValue": "04", "codeName": "精選櫥窗"}, {"codeValue": "05", "codeName": "超人氣商品"}, {"codeValue": "06", "codeName": "我的最愛"}, {"codeValue": "07", "codeName": "績效排行"}, {"codeValue": "08", "codeName": "最高配息率商品"}, {"codeValue": "10", "codeName": "4433篩選"}, {"codeValue": "11", "codeName": "百元基金"}, {"codeValue": "12", "codeName": "折價債券"}, {"codeValue": "13", "codeName": "買賣價差"}, {"codeValue": "09", "codeName": "最高殖利率債券"}], "sqlInfo": " SELECT ACD.CODE_VALUE , ACD.CODE_NAME  FROM ADM_CODE_DETAIL ACD  WHERE ACD.CODE_TYPE = :codeType  ORDER BY ACD.SHOW_ORDER ,class com.bi.pbs.adm.web.model.CodeDetailResp,{codeType=PRO_FAST_FILTER}"}]}