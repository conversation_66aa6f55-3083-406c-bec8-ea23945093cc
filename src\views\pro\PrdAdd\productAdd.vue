<template>
	<div>
		<div class="card card-form-collapse">
			<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
				<h4>{{ title }}</h4>
				<span class="tx-square-bracket">為必填欄位</span>
			</div>

			<vue-form v-slot="{ errors }" ref="queryForm">
				<div class="collapse show" id="collapseListGroup1">
					<form>
						<div class="card-body">
							<div class="form-row">
								<div class="form-group col-12 col-lg-4">
									<label style="height: 30px" class="form-label tx-require"> 商品主類</label>
									<div>
										<vue-field
											as="select"
											class="form-select"
											id="pfcatCodeForAllProd"
											rules="required"
											name="pfcatCodeForAllProd"
											label="商品主類"
											:class="{ 'is-invalid': errors.pfcatCodeForAllProd }"
											v-model="pfcatCode"
											@change="
												getProTypeMenu(pfcatCode);
												getIssuersMenu(pfcatCode);
											"
										>
											<option v-for="item in pfcatsMenu" :value="item.pfcatCode">
												{{ item.pfcatName }}
											</option>
										</vue-field>
										<div style="height: 25px">
											<span class="text-danger" v-show="errors.pfcatCodeForAllProd">{{ errors.pfcatCodeForAllProd }}</span>
										</div>
									</div>
								</div>
								<div class="form-group col-12 col-lg-4">
									<label style="height: 30px" class="form-label tx-require">商品次類 </label>
									<div>
										<vue-field
											as="select"
											class="form-select JQdata-hide"
											rules="required"
											name="proTypeCode"
											label="商品次類"
											:class="{ 'is-invalid': errors.proTypeCode }"
											v-model="proTypeCode"
										>
											<option value="">--</option>
											<option v-for="item in proTypeMenu" :value="item.proTypeCode">{{ item.proTypeName }}</option>
										</vue-field>
										<div style="height: 25px">
											<span class="text-danger" v-show="errors.proTypeCode">{{ errors.proTypeCode }}</span>
										</div>
									</div>
								</div>
								<div class="form-group col-12 col-lg-4">
									<label style="height: 30px" class="form-label tx-require">商品代碼</label>
									<div>
										<vue-field
											as="input"
											type="text"
											rules="required"
											name="proCode"
											label="商品代碼"
											:class="{ 'is-invalid': errors.proCode }"
											v-model="proCode"
											class="form-control"
										>
										</vue-field>
										<div style="height: 25px">
											<span class="text-danger" v-show="errors.proCode">{{ errors.proCode }}</span>
										</div>
									</div>
								</div>
							</div>
							<div class="form-row">
								<div class="form-group col-12 col-lg-4">
									<label style="height: 30px" class="form-label">資產類別 </label>
									<div>
										{{ assetcatName }}
									</div>
								</div>

								<div class="form-group col-12 col-lg-4">
									<label style="height: 30px" class="form-label tx-require"> 商品中文名稱 </label>
									<div>
										<vue-field
											as="input"
											class="form-control JQdata-hide"
											id="prod_chinese_name"
											maxlength="20"
											rules="required"
											name="proName"
											label="商品中文名稱"
											:class="{ 'is-invalid': errors.proName }"
											v-model="proName"
											size="25"
											type="text"
											value=""
										>
										</vue-field>
										<div style="height: 25px">
											<span class="text-danger" v-show="errors.proName">{{ errors.proName }}</span>
										</div>
									</div>
								</div>

								<div class="form-group col-12 col-lg-4">
									<label style="height: 30px" class="form-label tx-require"> 商品風險屬性 </label>
									<div>
										<vue-field
											as="select"
											class="form-select"
											id="riskCode"
											rules="required"
											name="riskCode"
											label="商品風險屬性"
											:class="{ 'is-invalid': errors.riskCode }"
											v-model="riskCode"
										>
											<option value="">--</option>
											<option v-for="item in riskMenu" :value="item.riskCode">
												{{ item.riskName }}
											</option>
										</vue-field>
										<div style="height: 25px">
											<span class="text-danger" v-show="errors.riskCode">{{ errors.riskCode }}</span>
										</div>
									</div>
								</div>
							</div>
							<div class="form-row">
								<div class="form-group col-12 col-lg-4">
									<label style="height: 30px" class="form-label tx-require"> 計價幣別</label>
									<div>
										<vue-field
											as="select"
											class="form-select"
											id="curMenuAll"
											title="請選擇幣別"
											rules="required"
											name="curCode"
											label="計價幣別"
											:class="{ 'is-invalid': errors.curCode }"
											v-model="curCode"
											data-style="btn-white"
										>
											<option value="">--</option>
											<option v-for="item in curOption" :key="index" :value="item.value">{{ item.name }}</option>
										</vue-field>
										<div style="height: 25px">
											<span class="text-danger" v-show="errors.curCode">{{ errors.curCode }}</span>
										</div>
									</div>
								</div>

								<div class="form-group col-12 col-lg-4" v-if="!disabledFields">
									<label style="height: 30px" class="form-label tx-require"> 發行機構</label>
									<div>
										<vue-field
											as="select"
											class="form-select"
											id="issuerCode"
											title="請選擇發行機構"
											rules="required"
											name="issuerCode"
											label="發行機構"
											:class="{ 'is-invalid': errors.issuerCode }"
											v-model="issuerCode"
											data-style="btn-white"
										>
											<option value="">--</option>
											<option v-for="item in issuersMenu" :key="index" :value="item.issuerCode">{{ item.issuerName }}</option>
										</vue-field>
										<div style="height: 25px">
											<span class="text-danger" v-show="errors.issuerCode">{{ errors.issuerCode }}</span>
										</div>
									</div>
								</div>

								<div class="form-group col-12 col-lg-8">
									<label style="height: 30px" class="form-label tx-require">銷售對象</label>
									<div>
										<div class="form-check form-check-inline" v-for="(item, i) in buCodeMenu">
											<vue-field
												type="radio"
												class="form-check-input"
												:id="'targetCusBu_' + i"
												name="targetCusBu"
												:value="item.codeValue"
												v-model="targetCusBu"
												rules="required"
												label="銷售對象"
												:class="{ 'is-invalid': errors.targetCusBu }"
											>
											</vue-field>
											<label :for="'targetCusBu_' + i" class="form-check-label">{{ item.codeName }}</label>
										</div>
										<div style="height: 25px">
											<span class="text-danger" v-show="errors.targetCusBu">{{ errors.targetCusBu }}</span>
										</div>
									</div>
								</div>

								<div class="form-group col-12 col-lg-4">
									<label style="height: 30px" class="form-label tx-require">限PI銷售</label>
									<div>
										<div class="form-check form-check-inline" v-for="(item, i) in profInvestorYnMenu">
											<vue-field
												type="radio"
												class="form-check-input"
												:id="'profInvestorYn_' + i"
												name="profInvestorYn"
												:value="item.codeValue"
												v-model="profInvestorYn"
												rules="required"
												label="限PI銷售"
												:class="{ 'is-invalid': errors.profInvestorYn }"
											>
											</vue-field>
											<label :for="'profInvestorYn_' + i" class="form-check-label">{{ item.codeName }}</label>
										</div>
										<div style="height: 25px">
											<span class="text-danger" v-show="errors.profInvestorYn">{{ errors.profInvestorYn }}</span>
										</div>
									</div>
								</div>

								<div class="form-group col-12 col-lg-4" v-if="pfcatCode === 'FUND'">
									<label style="height: 30px" class="form-label tx-require">是否為國內基金</label>
									<div>
										<div class="form-check form-check-inline" v-for="(item, i) in localYnMenu">
											<vue-field
												type="radio"
												class="form-check-input"
												:id="'localYn_' + i"
												name="localYn"
												:value="item.codeValue"
												v-model="localYn"
												rules="required"
												label="是否為國內基金"
												:class="{ 'is-invalid': errors.localYn }"
											>
											</vue-field>
											<label :for="'localYn_' + i" class="form-check-label">{{ item.codeName }}</label>
										</div>
										<div style="height: 25px">
											<span class="text-danger" v-show="errors.localYn">{{ errors.localYn }}</span>
										</div>
									</div>
								</div>

								<div class="form-group col-12 col-lg-4" v-if="false">
									<label style="height: 30px" class="form-label"> 債券評級</label>
									<div>
										<select class="form-select JQdata-hide" v-model="bondCode" id="bondCode" name="bondCode">
											<option value="">--</option>
											<option value="1">AAA</option>
											<option value="2">AA+</option>
											<option value="3">BBB</option>
											<option value="4">BB+</option>
											<option value="5">CCC</option>
											<option value="6">CC+</option>
										</select>
										<div style="height: 25px">
											<span class="text-danger" />
										</div>
									</div>
								</div>

								<div class="form-row" v-if="pfcatCode === 'ETF' || pfcatCode === 'FB' || pfcatCode === 'PFD'">
									<div class="form-group col-12 col-lg-4">
										<label class="form-label tx-require"> 到期日 </label>
										<vue-field
											type="date"
											class="form-control JQdata-hide"
											id="expiredDate"
											maxlength="20"
											rules="required"
											name="expiredDate"
											label="到期日"
											:class="{ 'is-invalid': errors.expiredDate }"
											v-model="expiredDate"
											size="25"
											style="min-width: 200px"
										>
										</vue-field>
									</div>
									<div style="height: 45px">
										<span class="text-danger" v-show="errors.expiredDate">{{ errors.expiredDate }}</span>
									</div>
								</div>
							</div>
							<div class="form-row">
								<div class="form-group col-12 col-lg-8" v-if="!disabledFields">
									<label class="form-label"> 檔案上傳 </label>
									<div class="row g-2">
										<div class="input-group">
											<input
												class="form-control form-file"
												type="file"
												size="30"
												@change="triggerFile($event)"
												ref="fileInput"
												accept="application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.wordprocessingml.document"
											/>
											<button class="btn btn-info btn-glow" type="button" @click="doUploadFile()">上傳</button>
										</div>

										<ul class="list-group list-inline-tags mt-2">
											<li class="list-group-item" v-for="(item, index) in files">
												<a href="#">
													<span v-if="item">{{ item.fileName }}</span>
													<span
														v-if="item"
														class="img-delete"
														data-bs-toggle="tooltip"
														style="right: -25px"
														@click="deleteFiles(item.fileId)"
														title="刪除"
													></span>
												</a>
											</li>
										</ul>
									</div>
								</div>
							</div>

							<div class="form-footer">
								<input class="btn btn-primary JQdata-hide" type="button" value="提交審核" @click="submit()" />
								<input class="btn btn-primary JQdata-show" type="button" value="取消修改" @click="cancel()" />
							</div>
						</div>
					</form>
				</div>
			</vue-form>
		</div>

		<div class="card card-table">
			<div class="card-header">
				<h4>新增商檢商品資料列表</h4>
			</div>
			<div class="table-responsive">
				<table class="table table-RWD table-hover">
					<thead>
						<tr>
							<th width="11%">商品主類</th>
							<th width="8%">商品代碼</th>
							<th width="14%">商品中文名稱</th>
							<th width="8%">商品風險等級</th>
							<th width="8%">計價幣別</th>
							<th width="8%" v-if="!disabledFields">發行機構</th>
							<th width="8%" v-if="!disabledFields">到期日</th>
							<th width="8%">專投商品</th>
							<th width="8%">銷售對象</th>
							<th width="8%">狀態</th>
							<th width="14%">異動人員</th>
							<th width="8%">異動時間</th>
							<th width="10%" class="text-center">執行</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="item in productList">
							<td>{{ item.pfcatName }}</td>
							<td>{{ item.bankProCode }}</td>
							<td>{{ item.proName }}</td>
							<td>{{ item.riskName }}</td>
							<td>{{ item.curName }}</td>
							<td v-if="!disabledFields">{{ item.issuerName }}</td>
							<td v-if="!disabledFields">{{ item.expireDt }}</td>
							<td>{{ item.profInvestorYn === 'Y' ? '是' : '否' }}</td>
							<td>{{ getTargetCusBuName(item.targetCusBu) }}</td>
							<td>{{ item.modifyBy }} {{ item.modifyName }}</td>
							<td>
								<template v-if="item.wkfStatus !== 'R'">
									{{ item.actionName }}
								</template>
								<a class="tx-link" href="#" @click="showRejectMsg(item)" v-if="item.wkfStatus === 'R'">
									{{ item.actionName }}
								</a>
							</td>
							<td>{{ formatDtTime(item.modifyDt) }}</td>
							<td class="text-center">
								<a data-bs-toggle="tooltip" href="#" class="table-icon" title="檢視" @click="preview(item.proCode)">
									<button type="button" class="btn btn-dark btn-icon"><i class="bi bi-search"></i></button>
								</a>

								<a data-bs-toggle="tooltip" href="#" class="table-icon" id="edit" v-if="item.wkfStatus !== 'P'">
									<button
										type="button"
										class="btn btn-info btn-icon"
										data-bs-toggle="tooltip"
										title="編輯"
										@click="edit(item.proCode)"
									>
										<i class="fa-solid fa-pen"></i>
									</button>
								</a>

								<a data-bs-toggle="tooltip" href="#" class="table-icon" id="delete" v-if="item.wkfStatus !== 'P'">
									<button
										type="button"
										class="btn btn-info btn-icon"
										data-bs-toggle="tooltip"
										title="刪除"
										@click="deleteItem(item.proCode, item.eventId)"
									>
										<i class="fa-solid fa-trash"></i>
									</button>
								</a>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>

		<div class="tx-note">
			<ol>
				<li>此功能設定的資料，僅限當天商檢交易使用。</li>
				<li>設定的資料於當天批次處理時將全部刪除。</li>
				<li>正式商品資料當天晚上交易主機將重新提供給系統使用。</li>
			</ol>
		</div>

		<!-- modal -->
		<vue-modal :is-open="isOpenModal" :before-close="isOpenModal = false">
			<template v-slot:content="props">
				<div class="modal-dialog modal-lg modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">新商品臨時上架</h4>
							<button type="button" class="btn-expand"><i class="bi bi-arrows-fullscreen"></i></button>
							<button type="button" class="btn-close" @click.prevent="props.close()" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<div class="tx-title">商品基本資料</div>
							<table class="table table-bordered">
								<tbody>
									<tr>
										<th width="20%">商品主類</th>
										<td width="30%">{{ proPreview.pfcatName }}</td>
										<th width="20%">商品次類</th>
										<td width="30%">{{ proPreview.proTypeName }}</td>
									</tr>
									<tr>
										<th>商品代碼</th>
										<td>{{ proPreview.bankProCode }}</td>
										<th>資產類別</th>
										<td>{{ proPreview.assetcatName }}</td>
									</tr>
									<tr>
										<th>商品中文名稱</th>
										<td>{{ proPreview.proName }}</td>
										<th>商品風險屬性</th>
										<td>{{ proPreview.riskName }}</td>
									</tr>
									<tr>
										<th>計價幣別</th>
										<td>{{ proPreview.curName }}</td>
										<th>銷售對象</th>
										<td>{{ getTargetCusBuName(proPreview.targetCusBu) }}</td>
									</tr>
									<tr>
										<th>限PI銷售</th>
										<td>{{ proPreview.profInvestorYn === 'Y' ? '是' : '否' }}</td>
										<template v-if="proPreview.pfcatCode === 'FUND'">
											<th>是否為國內基金</th>
											<td>{{ proPreview.localYn === 'Y' ? '是' : '否' }}</td>
										</template>
										<template
											v-if="proPreview.pfcatCode === 'ETF' || proPreview.pfcatCode === 'FB' || proPreview.pfcatCode === 'PFD'"
										>
											<th>到期日</th>
											<td>{{ proPreview.expireDt }}</td>
										</template>
									</tr>
									<tr v-if="!disabledFields">
										<th>狀態</th>
										<td>{{ proPreview.actionName }}</td>
									</tr>
									<tr v-if="!disabledFields">
										<th>檔案上傳</th>
										<td colspan="3">
											<span v-for="(item, index) in proPreview.files">
												<a v-if="item" href="#" class="tx-link" @click="downloadFile(item)">{{ item.fileName }}</a>
												<br />
											</span>
										</td>
									</tr>
								</tbody>
							</table>
						</div>

						<div class="modal-footer">
							<input
								name="btnClose"
								class="btn btn-white"
								id="appointmentCloseButton"
								type="button"
								value="關閉"
								@click.prevent="props.close()"
							/>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- modal end -->
	</div>
	<!--頁面內容 end-->
</template>
<script>
import vueModal from '@/views/components/model.vue';
import { Form, Field } from 'vee-validate';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueModal
	},
	data: function () {
		return {
			disabledFields: true,
			title: '新增資料',
			pfcatCode: null, // 商品主類
			proTypeCode: null, // 商品次類
			proCode: null, // 商品代碼
			assetcatCode: null, // 資產類別
			assetcatName: null, // 資產類別名稱
			proName: null, // 商品中文名稱
			riskCode: null, // 商品風險屬性
			curCode: null, // 計價幣別
			issuerCode: null, // 發行機構
			bondCode: null, // 債券評級
			expiredDate: null, // 到期日
			targetCusBu: null, // 銷售對象
			profInvestorYn: null, // 限PI銷售
			localYn: null, // 是否為國內基金
			fileList: {
				fileName: null,
				fileResult: null,
				fileCnt: 0
			},
			productList: [], //商檢商品資料列表
			pfcatsMenu: [], //商品主類選單
			proTypeMenu: [], //商品次類選單
			curOption: [], // 幣別選單
			riskMenu: [], // 風險等級選單
			assetcatsMenu: [], // 資產類別選單
			issuersMenu: [], // 發行機構選單
			proPreview: {}, // 檢視欄位

			//File 用參數
			files: [],
			uploadFile: null,
			uploadFiles: [],
			buCodeMenu: [],
			profInvestorYnMenu: [],
			localYnMenu: [],
			isOpenModal: false
		};
	},
	watch: {
		proTypeCode(newVal) {
			if (newVal) {
				var self = this;
				self.getAssetCasts(newVal);
			}
		},
		pfcatCode: {
			immediate: true,
			handler: function (newVal, oldVal) {
				var self = this;
				if (newVal) {
					if (newVal !== 'FUND') {
						self.localYn = null;
					}
					if (!(newVal === 'ETF' || newVal === 'FB' || newVal === 'PFD')) {
						self.expiredDate = null;
					}
				}
			}
		}
	},
	computed: {},
	mounted: function () {
		var self = this;
		Promise.all([
			self.fetchInitDataSelectYn(),
			self.fetchInitDataBuCodeMenu(),
			self.getProPfcatsMenu(),
			// self.getAssetcatsMenu(),
			self.getcurrenciesMenu(),
			self.getRiskMenu()
		]).then(() => {
			self.getNewShelfProductList(); // 取得商檢商品資料列表
			// self.getIssuersMenu(); // 取得發行機構
		});
	},
	methods: {
		fetchInitDataSelectYn: function () {
			let self = this;
			self.$api
				.getAdmCodeDetail({
					codeType: 'SELECT_YN'
				})
				.then(function (ret) {
					self.profInvestorYnMenu = ret.data;
					self.localYnMenu = ret.data;
				});
		},
		fetchInitDataBuCodeMenu: function () {
			let self = this;
			self.$api
				.getAdmCodeDetail({
					codeType: 'CUS_BU'
				})
				.then(function (ret) {
					self.buCodeMenu = ret.data;
				});
		},
		// 取得商品資料列表
		getNewShelfProductList: function () {
			var self = this;
			self.$api.getNewShelfProductList().then(function (ret) {
				self.productList = ret.data;
			});
		},
		// 取得商品主類
		getProPfcatsMenu: function () {
			var self = this;
			self.$api.getNewProPfcatsMenuApi().then(function (ret) {
				// const needPfcatCode = ['SP', 'DCI', 'SEC'];
				let pfcatsMenu = [];
				ret.data.forEach((e) => {
					//if (needPfcatCode.includes(e.pfcatCode)) {
					pfcatsMenu.push(e);
					//}
				});
				self.pfcatsMenu = pfcatsMenu;
			});
		},
		getProTypeMenu(pfcatCode) {
			// 商品次類選項
			var self = this;
			self.$api
				.getProTypeListApi({
					pfcatCode: pfcatCode
				})
				.then(function (r) {
					self.proTypeMenu = r.data;
					self.getAssetCasts(self.proTypeCode);
				});
		},
		// 取得資產類別
		getAssetCasts: function (proTypeCode) {
			var self = this;
			self.assetcatCode = null;
			self.assetcatName = null;
			if (self.proTypeMenu && self.proTypeMenu.length > 0) {
				let item = self.proTypeMenu.find((e) => e.proTypeCode === proTypeCode);
				if (item) {
					self.assetcatCode = item.assetcatCode;
					self.assetcatName = item.assetcatName;
				}
			}
		},
		getAssetcatsMenu: function () {
			var self = this;
			self.$api.getAssetcatsMenuApi().then(function (ret) {
				self.assetcatsMenu = ret.data;
				resolve();
			});
		},
		// 取得幣別
		getcurrenciesMenu: function () {
			var self = this;
			self.$api.groupProCurrenciesMenuApi().then(function (ret) {
				// self.currenciesMenu = ret.data;
				ret.data.forEach(function (item) {
					var obj = { value: item.curCode, name: item.curName };
					self.curOption.push(obj);
				});
			});
		},
		// 取得風險等級
		getRiskMenu: function () {
			var self = this;
			self.$api.getRiskMenuApi().then(function (ret) {
				self.riskMenu = ret.data;
			});
		},
		// 取得發行機構
		getIssuersMenu: function (pfcatCode) {
			var self = this;
			self.$api
				.getGroupIssuersMenuApi({
					pfcatCode: pfcatCode
				})
				.then(function (ret) {
					self.issuersMenu = ret.data;
					resolve();
				});
		},

		// 取得新商品臨時上架維護
		preview: function (proCode) {
			var self = this;
			/*
      var url = self.config.apiPath + '/pro/newShelfProduct';
      $.bi
        .ajax({
          url: url,
          method: 'GET',
          data: {
            proCode: proCode
          }
        })
        .then(function (r) {
          self.proPreview = r.data;
        });
      */

			self.productList.forEach((e) => {
				if (e.proCode === proCode) {
					self.proPreview = e;
				}
			});
			self.isOpenModal = true;
		},

		getTargetCusBuName(code) {
			var self = this;
			const item = self.buCodeMenu.find((item) => item.codeValue === code);
			return item ? item.codeName : '';
		},

		// 取得新商品臨時上架維護
		edit(proCode) {
			var self = this;
			/*
      var url = self.config.apiPath + '/pro/newShelfProduct';
      $.bi
        .ajax({
          url: url,
          method: 'GET',
          data: {
            proCode: proCode
          }
        })
        .then(function (r) {
          self.proTypeMenu = r.data;
        });
      */

			let pro = {};
			self.productList.forEach((e) => {
				if (e.proCode === proCode) {
					pro = e;

					self.title = '編輯資料';
					find = true;
				}
			});

			self.pfcatCode = pro.pfcatCode;
			self.proTypeCode = pro.proTypeCode;
			self.proCode = pro.bankProCode;
			self.assetcatCode = pro.assetcatCode;
			self.proName = pro.proName;
			self.riskCode = pro.riskCode;
			self.curCode = pro.curCode;
			self.issuerCode = pro.issuerCode;
			self.bondCode = pro.bondCode;
			self.targetCusBu = pro.targetCusBu;
			self.profInvestorYn = pro.profInvestorYn;
			self.localYn = pro.localYn;
			if (pro.expireDt) {
				self.expiredDate = moment(pro.expireDt, ['YYYY-MM-DD', 'YYYY/MM/DD', 'MM-DD-YYYY'], true).format('YYYY-MM-DD');
			}

			self.getProTypeMenu(self.pfcatCode);
			self.getIssuersMenu(self.pfcatCode); // 取得發行機構

			/*
      self.uploadFiles = [];
      self.files = [];
      self.$refs.fileInput.value = null;
      self.uploadFile = null;
      */
		},

		// 刪除
		deleteItem(proCode, eventId) {
			var self = this;

			thi.$bi.confirm('確定要刪除此筆資料嗎?', {
				event: {
					confirmOk: function () {
						self.$api
							.deleteShelfProduct({
								proCode: proCode,
								eventId: eventId
							})
							.then(function (ret) {
								self.getNewShelfProductList(); // 取得商檢商品資料列表
								self.title = '新增資料';
								self.expiredDate = null;
								var queryForm = self.$refs.queryForm;
								queryForm.resetForm();
								self.uploadFiles = [];
								self.files = [];
								self.$refs.fileInput.value = null;
								self.uploadFile = null;

								thi.$bi.alert('刪除成功');
							});
					}
				}
			});
		},

		//檔案處裡
		handleChange: function (event) {
			var self = this;
			self.fileTemp = event.target.files[0];
		},
		//上傳檔案
		addFile: function () {
			var self = this;
			$.when(self.$refs.fileForm.validate()).then(function (result) {
				if (result.valid) {
					if (self.files.length >= self.maxFileCount) {
						return;
					}

					var fileInfo = {
						fileNo: self.generatorId('file'),
						groupId: null,
						showName: self.fileTemp.name,
						fileName: null,
						contentType: self.fileTemp.contentType,
						filePath: null,
						file: self.fileTemp
					};
					self.files.push(fileInfo);
					self.$refs.fileForm.resetForm();
					self.$refs.uploadFile.$el.value = null;
				}
			});
		},
		previewFile: function (targetFileId) {
			var self = this;
			var index = self.files.findIndex((f) => f.fileNo === targetFileId);
			var fileInfo = self.files[index];
			var url;
			// 預覽待上傳檔案
			if (fileInfo.file) {
				url = URL.createObjectURL(fileInfo.file);
				var previewWindow = window.open(url, '_blank');
				previewWindow.document.title = fileInfo.showName;
				previewWindow.addEventListener('beforeunload', () => {
					URL.revokeObjectURL(url);
				});
				// 預覽伺服器檔案
			} else {
				self.$api.previewServerDoc({
					fileId: targetFileId,
					fileTitle: fileInfo.showName
				});
			}
		},
		deleteFile: function (targetFileId) {
			var self = this;
			var index = self.files.findIndex((f) => f.fileId === targetFileId);
			if (index != -1) {
				self.files.splice(index, 1);
			}
		},
		cancel: function () {
			var self = this;
			self.title = '新增資料';
			self.expiredDate = null;
			var queryForm = self.$refs.queryForm;
			self.issuersMenu = null;

			queryForm.resetForm();
			/*
      self.uploadFiles = [];
      self.files = [];
      self.$refs.fileInput.value = null;
      self.uploadFile = null;
      */
		},
		triggerFile: function (event) {
			var self = this;
			self.uploadFile = event.target.files[0];
			self.uploadFile.fileId = crypto.randomUUID();
		},
		doUploadFile: function () {
			var self = this;
			if (self.uploadFile) {
				if (self.uploadFile.size > 10485760) {
					thi.$bi.alert('檔案大小不得超過10MB！');
					return;
				}
				var fileInfo = {};

				fileInfo.fileName = self.uploadFile.name;
				fileInfo.fileId = self.uploadFile.fileId;

				fileInfo.contentType = self.uploadFile.type;
				fileInfo.fileSize = self.uploadFile.size;
				fileInfo.fileType = 'H';
				self.files.push(fileInfo);

				self.uploadFiles.push(self.uploadFile);
				self.uploadFile = null;
			}
		},
		deleteFiles: function (fileId) {
			var self = this;
			self.files.forEach((e, index, arr) => {
				if (e.fileId === fileId) {
					arr.splice(index, 1);
				}
			});

			self.uploadFiles.forEach((e, index, arr) => {
				if (e.fileId === fileId) {
					arr.splice(index, 1);
				}
			});
		},
		downloadFile: function (proFile) {
			var self = this;
			var id = proFile.proFileId;
			self.$api.downloadProFileNewLog({ fileId: id });
		},
		showRejectMsg: function (item) {
			thi.$bi.alert(item.reason);
		},
		formatDtTime: function (value) {
			return moment(value).format('YYYY/MM/DD HH:mm');
		},
		submit: function () {
			var self = this;
			var queryForm = self.$refs.queryForm;
			var proCode = self.pfcatCode + '_' + self.proCode;
			queryForm.validate().then(function (pass) {
				if (pass.valid) {
					var url = self.config.apiPath + '/pro/insertProductNew';

					let data = {
						pfcatCode: self.pfcatCode,
						proTypeCode: self.proTypeCode,
						proCode: proCode,
						bankProCode: self.proCode,
						assetcatCode: self.assetcatCode,
						assetcatName: self.assetcatName,
						proName: self.proName,
						riskCode: self.riskCode,
						curCode: self.curCode,
						issuerCode: self.issuerCode,
						bondCode: self.bondCode,
						targetCusBu: self.targetCusBu,
						profInvestorYn: self.profInvestorYn,
						localYn: self.localYn,
						expireDt: self.expiredDate,
						files: self.files
					};
					var formData = new FormData();
					formData.append('model', JSON.stringify(data));

					// upload file
					self.uploadFiles.forEach((e) => {
						formData.append('files', e, e.fileId);
					});

					self.$api.patchProductApi(formData).then(function (ret) {
						this.$swal
							.fire({
								icon: 'success',
								text: '提交審核成功',
								showCloseButton: true,
								confirmButtonText: '確認',
								buttonsStyling: false,
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							})
							.then(function () {
								self.getNewShelfProductList(); // 取得商檢商品資料列表
								self.title = '新增資料';
								self.expiredDate = null;
								queryForm.resetForm();
								self.uploadFiles = [];
								self.files = [];
								self.$refs.fileInput.value = null;
								self.uploadFile = null;
							});
					});
				}
			});
		}
	}
};
</script>
