<template>
	<dynamicTitle />
	<div>
		<div class="row">
			<div class="col-12">
				<vue-bi-tabs :menu-code="'M00-06'" @change-tab="changeTab" :tab-name-decorator="showNameDecorator" ref="tab">
					<template #default="{ id }">
						<component :is="id"></component>
					</template>
				</vue-bi-tabs>
			</div>
		</div>
		<!--頁面內容 end-->
	</div>
</template>
<script>
import dynamicTitle from '@/views/components/dynamicTitle.vue';
import vueFcStepFpUpload from './include/fcStepFpUpload.vue';
import vueFcStepFpSearch from './include/fcStepFpSearch.vue';
import vueBiTabs from '@/views/components/biTabs.vue';

export default {
	components: {
		dynamicTitle,
		vueFcStepFpUpload,
		vueFcStepFpSearch,
		vueBiTabs
	},
	data: function () {
		return {
			//畫面顯示用參數
			customTitle: null,
			//畫面邏輯判斷用參數
			tabCode: 0,
			tabs: [
				{ tabCode: 1, label: '區域人員設定' },
				{ tabCode: 2, label: '區域分區查詢' }
			],
			tabCodeTitleMap: {
				'M00-060': '區域人員設定', // 區域人員設定
				'M00-061': '區域分區查詢' // 區域分區查詢
			}
		};
	},
	mounted: function () {
		var self = this;
		self.tabCode = self.tabs[0].tabCode;
		self.customTitle = self.tabs[0].label;
	},
	methods: {
		changeTab: function (tabCode) {
			var self = this;
			self.customTitle = self.tabCodeTitleMap[tabCode];
		}
	}
};
</script>
