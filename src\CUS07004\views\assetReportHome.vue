<template>
    <div class="asset-report-home">
        <div class="card card-table">
            <div class="card-header">
                <h4>資產報告書清單</h4>
                <input 
                    name="createReport" 
                    type="button" 
                    class="btn btn-primary" 
                    value="新增報告書"
                    @click="gotoCreateReport">
            </div>
            <div class="table-responsive">
                <table class="bih-table table table-RWD">
                    <thead>
                        <tr>
                            <th>報告書名稱</th>
                            <th>客戶代碼</th>
                            <th>帳戶</th>
                            <th>執行</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr  v-for="report in reportList" :key="report.id">
                            <td>{{ report.reportName }}</td>
                            <td>{{ report.cusCode }}</td>
                            <td>{{ report.accCode }}</td>
                            <td >
                                <button type="button" class="btn btn-info btn-icon JQ-MainEdit" data-bs-toggle="tooltip" title="" data-bs-original-title="編輯" aria-label="編輯">
                                    <i class="fa-solid fa-pen"></i>
                                </button>
                                <button type="button" class="btn btn-danger btn-icon" data-bs-toggle="tooltip" title="" data-bs-original-title="刪除" aria-label="刪除">
                                    <i class="fa-solid fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</template>

<script>

export default {
    data() {
        return {
            reportList: []
        };
    },
    computed: {
        userCode() {
            return this.$store.getters['userInfo/info']?.userCode || '';
        }
    },
    mounted: async function() {
        this.getAssetReportList();
    },
    methods: {
        async getAssetReportList() {
            console.log(this.userCode);
            var self = this;
            const reportList = await self.$api.getAssetReportList({userCode: self.userCode});
            self.reportList = reportList;

        },
        gotoCreateReport() {
           //this.$router.push({ name: 'CreateAssetReport' });
        }
    },
}
</script>