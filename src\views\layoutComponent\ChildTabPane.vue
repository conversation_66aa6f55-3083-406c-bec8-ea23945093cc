<template>
	<el-tabs v-model="editTabsValue" type="card" :class="getChildCSS(this.layoutId, this.childIndex)">
		<div class="h-100">
			<el-tab-pane v-for="(comp, compIdx) in pane.Components" :label="comp" :key="compIdx" class="h-100" :name="comp">
				<component :is="COMPONENT_LIST.find((x) => x.Name == comp)?.Component || 'TabView'" :tabKey="comp"> </component>
			</el-tab-pane>
		</div>
	</el-tabs>
</template>
<script>
import { LAYOUT_LIST, COMPONENT_LIST } from '@/views/layoutComponent/layoutData.js';
import TabView from '../components/tabView.vue';

import { findTabUrl } from '@/utils/findTabUrl';

export default {
	name: 'ChildTabPane',
	components: {
		TabView
	},
	props: {
		pane: {
			type: Object,
			required: true
		},
		childIndex: {
			type: Number,
			required: true
		},
		layoutId: {
			type: [String, Number],
			required: true
		}
	},
	data() {
		return {
			editTabsValue: '',
			COMPONENT_LIST
		};
	},
	methods: {
		getChildCSS(layoutId, index) {
			const layout = LAYOUT_LIST.find((layout) => layout.LayoutId == layoutId);
			if (layout) {
				return layout.LayoutGrid.Children[index];
			}
			return null;
		}
	},
	mounted() {
		this.editTabsValue = this.pane.Components[0] || '';
	}
};
</script>
