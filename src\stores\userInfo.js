import api from '@/api/apiService.js';

export const userInfo = {
	namespaced: true,
	state: {
		userInfo: () => ({})
	},
	mutations: {
		SET_INFO: function (state, info) {
			state.userInfo = info;
		}
	},
	actions: {
		getInfo: async function (context) {
			let ret = await api.getInfoApi();
			context.commit('SET_INFO', ret);
		}
	},
	getters: {
		info: function (state) {
			return state.userInfo;
		}
	}
};
