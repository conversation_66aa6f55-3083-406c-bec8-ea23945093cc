{"status": 200, "data": [{"posCode": "001_00", "posName": "理財專員"}, {"posCode": "001_01", "posName": "分行經辦"}, {"posCode": "001_02", "posName": "分行幹部"}, {"posCode": "001_03", "posName": "分行經理"}, {"posCode": "001_04", "posName": "區域督導"}], "timestamp": "2025/04/24", "sqlTracer": [{"data": [{"posCode": "001_00", "posName": "理財專員"}, {"posCode": "001_01", "posName": "分行經辦"}, {"posCode": "001_02", "posName": "分行幹部"}, {"posCode": "001_03", "posName": "分行經理"}, {"posCode": "001_04", "posName": "區域督導"}], "sqlInfo": "SELECT AUPM.P<PERSON>_CODE, AP.POS_NAME FROM ADM_USER_POS_MAP AUPM JOIN ADM_POSITIONS AP ON AP.POS_CODE = AUPM.POS_CODE WHERE AUPM.USER_CODE = ? AND AP.VALID_YN = 'N' ,class com.bi.ifa.adm.web.model.UserPositionResp,[Ljava.lang.Object;@59d1724d"}]}