{"status": 200, "data": [{"termType": "PRO_ROW_NUM", "termValue": "1", "termName": "10筆", "termDesc": "商品查詢條件-顯示資料筆數", "rangeType": "", "rangeFixed": 10, "showOrder": 1}, {"termType": "PRO_ROW_NUM", "termValue": "2", "termName": "20筆", "termDesc": "商品查詢條件-顯示資料筆數", "rangeType": "", "rangeFixed": 20, "showOrder": 2}, {"termType": "PRO_ROW_NUM", "termValue": "3", "termName": "30筆", "termDesc": "商品查詢條件-顯示資料筆數", "rangeType": "", "rangeFixed": 30, "showOrder": 3}, {"termType": "PRO_ROW_NUM", "termValue": "4", "termName": "50筆", "termDesc": "商品查詢條件-顯示資料筆數", "rangeType": "", "rangeFixed": 50, "showOrder": 4}, {"termType": "PRO_ROW_NUM", "termValue": "5", "termName": "100筆", "termDesc": "商品查詢條件-顯示資料筆數", "rangeType": "", "rangeFixed": 100, "showOrder": 5}], "timestamp": "2025/06/30", "sqlTracer": [{"data": [{"termType": "PRO_ROW_NUM", "termValue": "1", "termName": "10筆", "termDesc": "商品查詢條件-顯示資料筆數", "rangeType": "", "rangeFixed": 10, "showOrder": 1}, {"termType": "PRO_ROW_NUM", "termValue": "2", "termName": "20筆", "termDesc": "商品查詢條件-顯示資料筆數", "rangeType": "", "rangeFixed": 20, "showOrder": 2}, {"termType": "PRO_ROW_NUM", "termValue": "3", "termName": "30筆", "termDesc": "商品查詢條件-顯示資料筆數", "rangeType": "", "rangeFixed": 30, "showOrder": 3}, {"termType": "PRO_ROW_NUM", "termValue": "4", "termName": "50筆", "termDesc": "商品查詢條件-顯示資料筆數", "rangeType": "", "rangeFixed": 50, "showOrder": 4}, {"termType": "PRO_ROW_NUM", "termValue": "5", "termName": "100筆", "termDesc": "商品查詢條件-顯示資料筆數", "rangeType": "", "rangeFixed": 100, "showOrder": 5}], "sqlInfo": " SELECT *  FROM ADM_SEARCH_TERMS   WHERE TERM_TYPE = :termType ORDER BY SHOW_ORDER  ,class com.bi.pbs.pro.web.model.AdmSearchTermsResp,{termType=PRO_ROW_NUM}"}]}