<template>
	<div id="searchResult2">
		<div class="card card-table mb-3">
			<div class="card-header">
				<h4>公告訊息列表</h4>
				<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
			</div>
			<div class="table-responsive">
				<table class="table table-RWD table-bordered text-center">
					<thead>
						<tr>
							<th>重要性</th>
							<th>是否過期</th>
							<th>公告日期</th>
							<th>類別</th>
							<th>主分類</th>
							<th>次分類</th>
							<th>公告標題</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="item in pageData.selMsgList">
							<td
								:class="{ 'text-center tx-red bi-exclamation': item.importantYn == 'Y' }"
								data-th="重要性"
								style="font-size: 24px"
							></td>
							<td data-th="是否過期">{{ item.expiredYn === 'Y' ? '是' : '否' }}</td>
							<td data-th="公告日期">{{ item.validBgnDt }}<br />~{{ item.validEndDt }}</td>
							<td data-th="類別">{{ item.msgName }}</td>
							<td data-th="主分類">{{ item.mainCatName }}</td>
							<td data-th="次分類">{{ item.subCatName }}</td>
							<td data-th="公告標題">
								<a href="javascript:void(0);" class="link-underline" @click="getView(item.msgId)">{{ item.msgTitle }}</a>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>

	<!-- Modal -->
	<vue-modal :is-open="isOpenModal" :before-close="closeModal">
		<template v-slot:content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">公告訊息</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()"><i class="bi bi-arrows-fullscreen"></i></button>
						<button type="button" class="btn-close" @click.prevent="props.close()"></button>
					</div>
					<div class="modal-body">
						<table class="table table-bordered">
							<caption>
								公告分類
							</caption>
							<tbody>
								<tr>
									<th>訊息類別</th>
									<td>{{ selMsg.msgName }}</td>
								</tr>
							</tbody>
						</table>

						<table class="table table-bordered">
							<caption>
								公告內容
							</caption>
							<tbody>
								<tr>
									<th class="wd-15p">重要性</th>
									<td>{{ subTypeName(selMsg.importantYn) }}</td>
								</tr>
								<tr>
									<th>主分類</th>
									<td>{{ selMsg.mainCatName }}</td>
								</tr>
								<tr>
									<th>次分類</th>
									<td>{{ selMsg.subCatName }}</td>
								</tr>
								<tr>
									<th>有效日期</th>
									<td>起 {{ selMsg.validBgnDt }} ~ 迄 {{ selMsg.validEndDt }}</td>
								</tr>
								<tr>
									<th><span>公告標題</span></th>
									<td>{{ selMsg.msgTitle }}</td>
								</tr>
								<tr>
									<th>公告內容</th>
									<td>{{ selMsg.msgContent }}</td>
								</tr>
								<tr>
									<th>首頁是否顯示</th>
									<td>{{ subTypeName(selMsg.showYn) }}</td>
								</tr>
								<tr>
									<th>上傳檔案</th>
									<td>
										<ul class="list-group list-inline-tags" v-for="file in selMsg.fileList">
											<li class="list-group-item">
												<a href="#" @click="viewFile(file.msgFileId)">
													<span>{{ file.showName }}</span>
												</a>
											</li>
										</ul>
									</td>
								</tr>
								<tr>
									<th>連結</th>
									<td>
										<a :href="selMsg.favoriteLink">{{ selMsg.favoriteLink }}</a>
									</td>
								</tr>
							</tbody>
						</table>

						<table class="table table-bordered">
							<caption>
								維護資訊
							</caption>
							<tbody>
								<tr>
									<th>建立人員</th>
									<td>{{ selMsg.createUser }}</td>
								</tr>
								<tr>
									<th>建立人員分機</th>
									<td>{{ selMsg.createUserExt }}</td>
								</tr>
								<tr>
									<th>建立日期</th>
									<td>{{ selMsg.createDt }}</td>
								</tr>
								<tr>
									<th>最後維護人員</th>
									<td>{{ selMsg.modifyUser }}</td>
								</tr>
								<tr>
									<th>維護人員分機</th>
									<td>{{ selMsg.modifyUserExt }}</td>
								</tr>
								<tr>
									<th>最後維護日期</th>
									<td>{{ selMsg.modifyDt }}</td>
								</tr>
							</tbody>
						</table>
					</div>
					<div class="modal-footer">
						<button @click.prevent="props.close()" type="button" class="btn btn-white">關閉視窗</button>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
	<!-- Modal End -->
</template>
<script>
import vueModal from '@/views/components/model.vue';
import { Field, Form } from 'vee-validate';
import VuePagination from '@/views/components/pagination.vue';
import _ from 'lodash';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueModal,
		VuePagination
	},
	props: {
		title: {
			type: String,
			required: true
		}
	},
	watch: {
		title: {
			handler() {
				this.initTab();
			},
			immediate: true
		}
	},
	data: function () {
		return {
			selMessageMap: [], // 公告類別資料
			selMsgMainCat: [], // 主分類資料
			selMsgSubCat: [], // 次分類資料
			selOptionYn: [
				{ codeName: '是', codeValue: 'Y' },
				{ codeName: '否', codeValue: 'N' }
			],

			msgCode: null,
			mainCatCode: null,
			subCatCode: null,
			validBgnDt: null,
			validEndDt: null,
			msgTitle: null,

			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: 'MSG_ID',
				direction: 'ASC'
			},
			isOpenModal: null,
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',
			buttonClass: 'btn-expand',
			//getView
			selMsg: {}
		};
	},
	methods: {
		async initTab() {
			var self = this;
			// initial
			await self.getMessageMap();
			self.selMessageMap.forEach(function (message) {
				if (self.title === message.msgName) {
					self.msgCode = message.msgCode;
				}
			});

			self.gotoPage(0);
		},
		async getMessageMap() {
			var self = this;
			try {
				const response = await self.$api.getMessageMapApi({});
				if (response.data) {
					self.selMessageMap = response.data;
					self.setMessageCode();
				}
			} catch (error) {
				console.error('Error fetching message map:', error);
			}
		},
		setMessageCode() {
			var self = this;
			self.selMessageMap.forEach(function (message) {
				if (self.title === message.msgName) {
					self.msgCode = message.msgCode;
				}
			});
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (_page) {
			var self = this;
			var page = _.isNumber(_page) ? _page : self.pageable.page;
			var url = '';
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;
			const ret = await self.$api.getBbsMgtHeadPageData(
				{
					msgCode: self.msgCode
				},
				url
			);
			self.pageData = ret.data;
			self.pageData.selMsgList = self.pageData.content;
		},
		getView: async function (msgId) {
			var self = this;
			const ret = await self.$api.getGenMessageApi({
				msgId: msgId
			});
			if (ret.data) {
				self.selMsg = _.clone(ret.data);
				self.isOpenModal = true;
			}
		},
		changeModalSize: function () {
			var self = this;
			if (self.modalClass === 'modal-dialog modal-lg modal-dialog-centered') {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered fullscreen';
				self.buttonClass = 'btn-expand mini';
			} else {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered';
				self.buttonClass = 'btn-expand';
			}
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		viewFile: function (targetFileId) {
			var self = this;
			self.$api.downloadBbsHeadFileApi({ fileId: targetFileId });
		},
		subTypeName: function (codeValue) {
			var self = this;
			if (!_.isBlank(self.selOptionYn) && !_.isBlank(codeValue)) {
				return _.find(self.selOptionYn, { codeValue: codeValue }).codeName;
			} else {
				return codeValue;
			}
		}
	}
};
</script>
