<template>
	<div class="col-12">
		<h4>基金持股</h4>
		<div class="row">
			<div class="col-sm-7 padding-10 bg-lighter">
				<p class="d-flex justify-content-between font-md bold">
					前十大持股
					<small>資料更新日：{{ $filters.defaultValue($filters.formatDate(getAllocsDataDateBySchemeCode('TOP_HOLDING')), '--') }}</small>
				</p>
				<table class="table-Hfix">
					<tr v-for="(item, i) in getAllocsBySchemeCode('TOP_HOLDING')">
						<td>{{ item.itemName }}</td>
						<td>
							<div class="progress">
								<div
									class="progress-bar bg-success"
									role="progressbar"
									:style="{ width: item.tnaPct + '%' }"
									aria-valuenow="30"
									aria-valuemin="0"
									aria-valuemax="100"
								></div>
							</div>
						</td>
						<td>{{ item.tnaPct }}%</td>
					</tr>
					<tr v-if="getAllocsBySchemeCode('TOP_HOLDING').length === 0">
						<td class="text-center">無資料</td>
					</tr>
				</table>
			</div>
			<div class="col-sm-5 padding-10">
				<p class="d-flex justify-content-between font-md bold">
					行業配置 <small>資料更新日：{{ $filters.defaultValue($filters.formatDate(getAllocsDataDateBySchemeCode('GICS')), '--') }}</small>
				</p>
				<table width="100%" class="table-Hfix">
					<tr v-for="(item, i) in getAllocsBySchemeCode('GICS')">
						<td>{{ item.itemName }}</td>
						<td>
							<div class="progress">
								<div
									class="progress-bar bg-complete"
									role="progressbar"
									:style="{ width: item.tnaPct + '%' }"
									aria-valuenow="30"
									aria-valuemin="0"
									aria-valuemax="100"
								></div>
							</div>
						</td>
						<td>{{ item.tnaPct }}%</td>
					</tr>
					<tr v-if="getAllocsBySchemeCode('GICS').length === 0">
						<td class="text-center">無資料</td>
					</tr>
				</table>
				<hr />
				<p class="d-flex justify-content-between font-md bold">
					資產配置
					<small>資料更新日：{{ $filters.defaultValue($filters.formatDate(getAllocsDataDateBySchemeCode('ASSTALL')), '--') }}</small>
				</p>
				<table width="100%" class="table-Hfix">
					<tr v-for="(item, i) in getAllocsBySchemeCode('ASSTALL')">
						<td>{{ item.itemName }}</td>
						<td>
							<div class="progress">
								<div
									class="progress-bar bg-warning"
									role="progressbar"
									:style="{ width: item.tnaPct + '%' }"
									aria-valuenow="30"
									aria-valuemin="0"
									aria-valuemax="100"
								></div>
							</div>
						</td>
						<td>{{ item.tnaPct }}%</td>
					</tr>
					<tr v-if="getAllocsBySchemeCode('ASSTALL').length === 0">
						<td class="text-center">無資料</td>
					</tr>
				</table>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
export default {
	props: {
		fundCode: String
	},
	data: function () {
		return {
			allocs: null
		};
	},
	watch: {
		fundCode: {
			handler: function (newVal, oldVal) {
				this.getAllocs();
			}
		}
	},
	mounted: function () {},
	methods: {
		getAllocs: async function () {
			var self = this;
			const ret = await this.$api.getAllocsApi(self.fundCode);
			self.allocs = ret.data;
		},
		getAllocsBySchemeCode: function (schemeCode) {
			return _.filter(this.allocs, { schemeCode: schemeCode });
		},
		getAllocsDataDateBySchemeCode: function (schemeCode) {
			var alloc = _.filter(this.allocs, { schemeCode: schemeCode });
			return alloc && alloc[0] ? alloc[0].dataDate : null;
		}
	}
};
</script>
