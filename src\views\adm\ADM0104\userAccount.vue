<template>
	<dynamic-title></dynamic-title>
	<div>
		<div class="row">
			<div class="col-12">
				<bi-tabs ref="biTabs" :menu-code="'M00-04'" @change-tab="changeTab">
					<template #default="{ id }">
						<component :is="id" :set-edit-account="setEditAccount" :edit-account="editAccount"></component>
					</template>
				</bi-tabs>
			</div>
		</div>
	</div>
</template>

<!-- <th:block th:insert="include/common :: modal"></th:block>-->

<script>
import dynamicTitle from '@/views/components/dynamicTitle.vue';
import biTabs from '@/views/components/biTabs.vue';
import vueUserAccount from './include/userAccountTab.vue';
import vueUserAccountNew from './include/userAccountNewTab.vue';

export default {
	components: {
		dynamicTitle,
		biTabs,
		vueUserAccount,
		vueUserAccountNew
	},
	data: function () {
		return {
			//畫面顯示用參數
			customTitle: null,

			//畫面邏輯判斷用參數
			tabCodeTitleMap: {
				'M00-040': '系統使用者查詢',
				'M00-041': '系統使用者設定'
			},

			//子組件用條件物件
			editAccount: { tabCode: null, userCode: null }
		};
	},
	watch: {
		'editAccount.tabCode': function () {
			this.changeTab(this.editAccount.tabCode);
		}
	},
	methods: {
		changeTab: function (tabCode) {
			this.customTitle = this.tabCodeTitleMap[tabCode];

			if (tabCode == 'M00-040') {
				this.editAccount.tabCode = 'M00-040';
			}
		},
		setEditAccount: function (val) {
			this.editAccount = { ...val };
			this.$refs.biTabs.selectedCode = val.tabCode;
		}
	}
};
</script>
