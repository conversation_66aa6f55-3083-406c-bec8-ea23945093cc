<template>
	<teleport to="body">
		<div class="modal fade" :id="modalId" ref="modal">
			<slot name="content" :open="open" :close="close"></slot>
		</div>
	</teleport>
</template>

<script>
import Modal from 'bootstrap/js/dist/modal.js';
export default {
	props: {
		isOpen: {
			type: Boolean,
			default: false
		},
		id: String,
		beforeOpen: Function,
		beforeClose: Function
		// isOpen: Boolean
	},
	data: function () {
		return {
			modal: null,
			defaultId: 'modal' + this.$_.now() + this.$_.random(0, 99)
		};
	},
	watch: {
		isOpen: {
			handler: function (newVal) {
				if (newVal) {
					this.open();
				} else {
					this.close();
				}
				// if (newVal == true) {
				// 	this.$nextTick(function () {
				// 		this.open();
				// 	});
				// } else if (newVal == false) {
				// 	this.$nextTick(function () {
				// 		this.close();
				// 	});
				// }
			},
			immediate: true
		}
	},
	computed: {
		modalId: function () {
			return this.id ? this.id : this.defaultId;
		}
	},
	mounted: function () {
		// 確保在 Vue 完成渲染後再初始化 Modal
		this.$nextTick(() => {
			this.modal = new Modal(this.$refs.modal); // 在 Vue 渲染完成後初始化
		});
		// this.event();
	},
	methods: {
		// event: function () {
		// 	let id = this.modalId;
		// 	$('#' + id).on('shown.bs.modal', function (e) {
		// 		if (this.$_.isFunction(this.beforeOpen)) {
		// 			this.beforeOpen();
		// 		}
		// 	});
		// 	$('#' + id).on('hidden.bs.modal', function (e) {
		// 		if ($('body > .modal.show').length > 0) {
		// 			$('body').addClass('modal-open');
		// 		}
		// 		if (this.$_.isFunction(this.beforeClose)) {
		// 			this.beforeClose();
		// 			e.stopPropagation();
		// 		}
		// 	});
		// },
		open: function () {
			if (this.modal) {
				this.modal.show();
			}
		},

		close: function () {
			if (this.modal) {
				this.modal.hide();
			}
		}
	}
};
</script>
