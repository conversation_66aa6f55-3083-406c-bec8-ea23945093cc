<template>
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">信託-ETF</h4>
				<button type="button" class="btn-close" @click.prevent="close()" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="card card-prointro shadow-none mb-3">
					<div class="pro_left">
						<div class="pro_name">
							<div class="avatar avatar-md avatar-fund"></div>
							<h4><span>商品名稱</span> <br />{{ $filters.defaultValue(proInfo.proName, '--') }}</h4>
						</div>
						<h4 class="pro_value">
							<span>最新淨值</span>
							<br />{{ $filters.formatNumber(proInfo.aprice, '0,0.00' || '--') }} <br /><span>{{
								$filters.formatDate(proInfo.priceDt, '--')
							}}</span>
						</h4>
						<h4 class="pro_value">
							<span>最新市價</span>
							<br />{{ $filters.formatNumber(proInfo.sprice, '0,0.00' || '--') }} <br /><span>{{
								$filters.formatDate(proInfo.priceDt, '--')
							}}</span>
						</h4>
					</div>
					<div class="pro_class">
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品代碼</span> <br />{{ $filters.defaultValue(proInfo.bankProCode, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6>
								<span>資產類別 <br /></span>{{ $filters.defaultValue(proInfo.assetcatName, '--') }}
							</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品主類</span> <br />{{ $filters.defaultValue(proInfo.pfcatName, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品次類</span><br />{{ $filters.defaultValue(proInfo.proTypeName, '--') }}</h6>
						</div>
					</div>
				</div>
				<div class="tab-nav-pills">
					<ul class="nav nav-pills justify-content-center" role="tablist">
						<li class="nav-item"><a class="nav-link active" href="#Sectionetf1" data-bs-toggle="pill">商品基本資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionetf2" data-bs-toggle="pill">商品共同資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionetf3" data-bs-toggle="pill">商品附加資料</a></li>
						<li class="nav-item"><a class="nav-link datainfo" href="#Sectionetf4" data-bs-toggle="pill">基金資料</a></li>
						<li class="nav-item"><a class="nav-link datainfo" href="#Sectionetf5" data-bs-toggle="pill">ETF持股</a></li>
						<li class="nav-item"><a class="nav-link datainfo" href="#Sectionetf6" data-bs-toggle="pill">淨值分析</a></li>
						<li class="nav-item"><a class="nav-link datainfo" href="#Sectionetf7" data-bs-toggle="pill">績效表現</a></li>
						<li class="nav-item">
							<a class="nav-link datainfo" href="#Sectionetf8" data-bs-toggle="pill" @click="getEtfPerformanceStats(proInfo.proCode)"
								>各項統計</a
							>
						</li>
						<li class="nav-item">
							<a class="nav-link datainfo" href="#Sectionetf9" data-bs-toggle="pill" @click="getEtfIntRate(proInfo.proCode)">ETF配息</a>
						</li>
					</ul>
					<div class="tab-content">
						<div class="tab-pane fade show active" id="Sectionetf1">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>ETF商品資訊</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>交易所名稱</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.etfInfo.exchangeName, '--') }}
											</td>
											<th>交易所代碼</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.etfInfo.exchangeCode, '--') }}
											</td>
										</tr>
										<tr>
											<th>ETF類型</th>
											<td>{{ $filters.defaultValue(proInfo.etfInfo.proTypeName, '--') }}</td>
											<th>風險等級</th>
											<td>{{ $filters.defaultValue(proInfo.etfInfo.riskName, '--') }}</td>
										</tr>
										<tr>
											<th>計價幣別</th>
											<td>{{ $filters.defaultValue(proInfo.etfInfo.curCode, '--') }}</td>
											<th>是否可銷售</th>
											<td>{{ $filters.defaultValue(proInfo.etfInfo.buyYn, '--') }}</td>
										</tr>
										<tr>
											<th>國際代碼</th>
											<td>{{ $filters.defaultValue(proInfo.etfInfo.isinCode, '--') }}</td>
											<th>交易單位</th>
											<td>{{ $filters.defaultValue(proInfo.etfInfo.txUnit, '--') }}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionetf2">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>銷售相關資料</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th width="20%">銷售地區</th>
											<td width="30%" v-if="proInfo.allYn == 'Y'">全行</td>
											<td width="30%" v-else>--</td>
											<th width="20%">限PI申購</th>
											<td width="30%">
												{{ $filters.defaultValue(proInfo.profInvestorYn, '--') }}
											</td>
										</tr>
										<tr>
											<th><span>交易所下單碼</span></th>
											<td class="wd-80p" colspan="3">
												<span>{{ $filters.defaultValue(proInfo.brokerCode, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>銷售對象</span></th>
											<td class="wd-30p" colspan="3">
												{{ $filters.defaultValue(proInfo.targetCusBu, '--') }}
											</td>
										</tr>
										<tr>
											<th>是否開放申購</th>
											<td>{{ $filters.defaultValue(proInfo.buyYn, '--') }}</td>
											<th>是否開放贖回</th>
											<td>{{ $filters.defaultValue(proInfo.sellYn, '--') }}</td>
										</tr>
										<tr>
											<th><span>波動類型</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.volatilityType, '--') }}</span>
											</td>
											<th><span>配息頻率</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.intFreqUnitype, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th>保本要求</th>
											<td colspan="3">{{ $filters.defaultValue(proInfo.principalGuarYn, '--') }}</td>
										</tr>
										<tr>
											<th><span>理財需求</span></th>
											<td colspan="3">
												<div class="form-check form-check-inline" v-for="item in finReqCodeMenu">
													<input
														class="form-check-input"
														name="finReqCodes"
														disabled
														id="c1"
														v-model="finReqCodes"
														:value="item.codeValue"
														type="checkbox"
													/>
													<label class="form-check-label" for="c1">{{ $filters.defaultValue(item.codeName, '--') }}</label>
												</div>
											</td>
										</tr>
										<tr>
											<th>商品標籤</th>
											<td disabled="disabled" colspan="3">
												{{ $filters.defaultValue(proInfo.selprocatNames, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionetf3">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>其他設定</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>商品投資標的</span></th>
											<td class="wd-80p">
												<span>{{ $filters.defaultValue(proInfo.sectorName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>商品投資地區</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.geoFocusName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>比較基準設定</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.benchmarkName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>備註</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.memo, '--') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>公開說明書</th>
											<td class="wd-80p">
												<a v-if="proFileB && proFileB.url" :href="proFileB.url" target="_blank">{{
													$filters.defaultValue(proFileB.url, '--')
												}}</a
												><br v-if="proFileB && proFileB.url" /><a v-else>--</a>
												<a v-if="proFileB" class="tx-link" href="#" @click="downloadFile(proFileB)">{{
													$filters.defaultValue(proFileB.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>投資人須知</th>
											<td class="wd-80p">
												<a v-if="proFileD && proFileD.url" :href="proFileD.url" target="_blank">{{
													$filters.defaultValue(proFileD.url, '--')
												}}</a
												><br v-if="proFileD && proFileD.url" /><a v-else>--</a>
												<a v-if="proFileD" class="tx-link" href="#" @click="downloadFile(proFileD)">{{
													$filters.defaultValue(proFileD.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>基金月報</th>
											<td class="wd-80p">
												<a v-if="proFileE && proFileE.url" :href="proFileE.url" target="_blank">{{
													$filters.defaultValue(proFileE.url, '--')
												}}</a
												><br v-if="proFileE && proFileE.url" /><a v-else>--</a>
												<a v-if="proFileE" class="tx-link" href="#" @click="downloadFile(proFileE)">{{
													$filters.defaultValue(proFileE.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>DM</th>
											<td class="wd-80p">
												<a v-if="proFileF && proFileF.url" :href="proFileF.url" target="_blank">{{
													$filters.defaultValue(proFileF.url, '--')
												}}</a
												><br v-if="proFileF && proFileF.url" /><a v-else>--</a>
												<a v-if="proFileF" class="tx-link" href="#" @click="downloadFile(proFileF)">{{
													$filters.defaultValue(proFileF.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>其他</th>
											<td class="wd-80p">
												<a v-if="proFileG && proFileG.url" :href="proFileG.url" target="_blank">{{
													$filters.defaultValue(proFileG.url, '--')
												}}</a
												><br v-if="proFileG && proFileG.url" /><a v-else>--</a>
												<a v-if="proFileG" class="tx-link" href="#" @click="downloadFile(proFileG)">{{
													$filters.defaultValue(proFileG.showName, '--')
												}}</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>其他相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<td>
												<span v-for="(item, index) in otherFileList">
													<a
														v-if="index === otherFileList.length - 1"
														v-show="item.show"
														href="#"
														class="tx-link"
														@click="downloadOtherFile(item.docFileId)"
														>{{ $filters.defaultValue(item.showName, '--') }}</a
													>
													<a v-else href="#" class="tx-link" v-show="item.show" @click="downloadOtherFile(item.docFileId)"
														>{{ $filters.defaultValue(item.showName, '--') }}、</a
													>
												</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="tx-note">其他相關附件為文件管理內所上傳與本商品相關的文件。</div>
						</div>

						<div class="tab-pane fade" id="Sectionetf4">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>資料明細</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th class="wd-20p">發行公司</th>
											<td class="wd-30p">{{ $filters.defaultValue(etfDetail.fproviderName, '--') }}</td>
											<th class="wd-20p">成立日期</th>
											<td class="wd-30p">{{ $filters.defaultValue(etfDetail.inceptionDt, '--') }}</td>
										</tr>
										<tr>
											<th>計價幣別</th>
											<td>{{ $filters.defaultValue(etfDetail.currency, '--') }}</td>
											<th>風險屬性</th>
											<td>{{ $filters.defaultValue(etfDetail.attrValName, '--') }}</td>
										</tr>
										<tr>
											<th>交易所</th>
											<td>{{ $filters.defaultValue(etfDetail.primaryExchange, '--') }}</td>
											<th>交易所代碼</th>
											<td>{{ $filters.defaultValue(etfDetail.refCode, '--') }}</td>
										</tr>
										<tr>
											<th>公開說明書</th>
											<td>{{ $filters.defaultValue(etfDetail.fileName, '--') }}</td>
											<th>總管理費用</th>
											<td>{{ $filters.defaultValue(etfDetail.grossExpenseRatio, '--') }}%</td>
										</tr>
										<tr>
											<th>ETF規模</th>
											<td>{{ $filters.defaultValue(etfDetail.etfSize, '--') }}</td>
											<th>註冊地</th>
											<td>{{ $filters.defaultValue(etfDetail.domicile, '--') }}</td>
										</tr>
										<tr>
											<th>投資標的</th>
											<td>{{ $filters.defaultValue(etfDetail.className, '--') }}</td>
											<th>經銷商</th>
											<td>{{ $filters.defaultValue(etfDetail.dproviderName, '--') }}</td>
										</tr>
										<tr>
											<th>投資區域</th>
											<td>{{ $filters.defaultValue(etfDetail.geographicalFocus, '--') }}</td>
											<th>保管機構</th>
											<td>{{ $filters.defaultValue(etfDetail.cproviderName, '--') }}</td>
										</tr>
										<tr>
											<th>經理人</th>
											<td colspan="3" width="80%">
												{{ $filters.defaultValue(etfDetail.mgrName, '--') }}
											</td>
										</tr>
										<tr>
											<th>指標指數</th>
											<td colspan="3">{{ $filters.defaultValue(etfDetail.benchmarkName, '--') }}</td>
										</tr>
										<tr>
											<th>投資策略</th>
											<td colspan="3">{{ $filters.defaultValue(etfDetail.objective, '--') }}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionetf5">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>持股明細</h4>
								</div>
								<div class="card-body">
									<div class="row">
										<div class="col-lg-6">
											<vue-column-chart
												v-if="proInfo"
												ref="etfStockDetailChartRef"
												:chart-id="etfStockDetailChartId"
												:prop-chart-data="etfStockDetailChartData"
											></vue-column-chart>
										</div>
										<div class="col-lg-6">
											<table class="table table-RWD table-bordered">
												<thead>
													<tr>
														<th class="wd-10p">排名</th>
														<th>持股公司</th>
														<th>日期</th>
													</tr>
												</thead>
												<tbody v-if="etfStockDetail.length > 0">
													<tr v-for="item in etfStockDetail">
														<td class="tx-sort" data-th="排名">
															{{ $filters.defaultValue(item.rank, '--') }}
														</td>
														<td data-th="持股公司">{{ $filters.defaultValue(item.name, '--') }}</td>
														<td class="text-center" data-th="日期">
															{{ $filters.defaultValue(item.dataDt, '--') }}
														</td>
													</tr>
												</tbody>
												<tbody v-else>
													<tr>
														<td colspan="3">基金公司尚未提供持股明細資料</td>
													</tr>
												</tbody>
											</table>
										</div>
									</div>
								</div>
							</div>
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>持股分佈(依產業)</h4>
								</div>
								<div class="card-body">
									<div class="row">
										<div class="col-lg-6">
											<vue-column-chart
												v-if="proInfo"
												ref="etfStockDistributionChartRef"
												:chart-id="etfStockDistributionChartId"
												:prop-chart-data="etfStockDistributionChartData"
											></vue-column-chart>
										</div>
										<div class="col-lg-6">
											<table class="table table-RWD table-bordered">
												<thead>
													<tr>
														<th class="wd-10p">排名</th>
														<th>配置組合</th>
													</tr>
												</thead>
												<tbody v-if="etfStockDistribution.length > 0">
													<tr v-for="item in etfStockDistribution">
														<td data-th="排名">{{ $filters.defaultValue(item.rank, '--') }}</td>
														<td data-th="配置組合">{{ $filters.defaultValue(item.itemName, '--') }}</td>
													</tr>
												</tbody>
												<tbody v-else>
													<tr>
														<td colspan="2">基金公司尚未提供配置組合資料</td>
													</tr>
												</tbody>
											</table>
										</div>
									</div>
								</div>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionetf6">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>價格分析</h4>
								</div>
								<table class="table table-RWD table-bordered">
									<thead>
										<tr>
											<th class="wd-20p">項目</th>
											<th class="wd-30p">價格</th>
											<th class="wd-25p">最高價格(年)</th>
											<th class="wd-25p">最低價格(年)</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td data-th="項目">淨值</td>
											<td class="text-end" data-th="價格">
												<span>{{ $filters.defaultValue(etfPrice.nprice, '--') }}</span>
											</td>
											<td class="text-end" data-th="最高價格(年)">
												<span>{{ $filters.defaultValue(etfPrice.maxNavPrice, '--') }}</span>
											</td>
											<td class="text-end" data-th="最低價格(年)">
												<span>{{ $filters.defaultValue(etfPrice.minNavPrice, '--') }}</span>
											</td>
										</tr>
										<tr>
											<td data-th="項目">市價</td>
											<td class="text-end" data-th="價格">
												<span>{{ $filters.defaultValue(etfPrice.mprice, '--') }}</span>
											</td>
											<td class="text-end" data-th="最高價格(年)">
												<span>{{ $filters.defaultValue(etfPrice.maxMidPrice, '--') }}</span>
											</td>
											<td class="text-end" data-th="最低價格(年)">
												<span>{{ $filters.defaultValue(etfPrice.minMidPrice, '--') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>歷史價格走勢圖：{{ $filters.defaultValue(proInfo.proName, '--') }}</h4>
								</div>
								<div class="text-center">
									<vue-price-chart
										v-if="proInfo"
										ref="etfPriceChartRef"
										:chart-id="priceChartId"
										:prop-chart-data="pricChartData"
										:pro-price-range-menu="proPriceRangeMenu"
									></vue-price-chart>
									<div class="btn-group btn-group-sm mb-4" role="group">
										<div class="btn-group btn-group-sm mb-4" role="group">
											<input
												v-for="item in proPriceRangeMenu"
												type="radio"
												class="btn-check"
												name="time"
												:id="'etfPricePeriod' + item.termValue"
												:checked="item.termValue == '4'"
												@click="getPricesChartData(proInfo.proCode, item.rangeType, item.rangeFixed)"
											/>
											<label
												v-for="item in proPriceRangeMenu"
												class="btn btn-outline-secondary"
												:for="'etfPricePeriod' + item.termValue"
												>{{ $filters.defaultValue(item.termName, '--') }}</label
											>
										</div>
									</div>
								</div>

								<div class="caption">近30日淨值</div>
								<table class="table table-RWD table-bordered text-center">
									<thead>
										<tr>
											<th>日期</th>
											<th class="text-end">淨值</th>
											<th class="text-end">市價</th>
											<th>日期</th>
											<th class="text-end">淨值</th>
											<th class="text-end">市價</th>
											<th>日期</th>
											<th class="text-end">淨值</th>
											<th class="text-end">市價</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="item in etfPriceHis">
											<td data-th="日期">{{ $filters.defaultValue(item.priceDt1, '--') }}</td>
											<td class="text-end" data-th="淨值">
												{{ $filters.defaultValue(item.nprice1, '--') }}
											</td>
											<td class="text-end" data-th="市價">
												{{ $filters.defaultValue(item.mprice1, '--') }}
											</td>
											<td data-th="日期">{{ $filters.defaultValue(item.priceDt2, '--') }}</td>
											<td class="text-end" data-th="淨值">
												{{ $filters.defaultValue(item.nprice2, '--') }}
											</td>
											<td class="text-end" data-th="市價">
												{{ $filters.defaultValue(item.mprice2, '--') }}
											</td>
											<td data-th="日期">{{ $filters.defaultValue(item.priceDt3, '--') }}</td>
											<td class="text-end" data-th="淨值">
												{{ $filters.defaultValue(item.nprice3, '--') }}
											</td>
											<td class="text-end" data-th="市價">
												{{ $filters.defaultValue(item.mprice3, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionetf7">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>ETF績效分析</h4>
								</div>
								<div class="row">
									<div class="col-sm-2">
										<label class="tc-blue">選擇ETF/美股：</label>
									</div>
									<div class="col-sm-6">
										<select class="form-select" v-model="profileName">
											<option selected value="">--</option>
											<option v-for="item in etfProfileNameMenu" :value="item.lipperId">
												{{ $filters.defaultValue(item.nameFull, '--') }}
											</option>
										</select>
									</div>
									<div class="col-sm-4">
										<p>
											<input
												id="profileNameBtn"
												class="btn btn-primary text-alignRight"
												type="button"
												value="加入"
												@click="addEtf()"
											/>
										</p>
									</div>

									<div class="col-sm-2">
										<label class="tc-blue">選擇對應指數：</label>
									</div>
									<div class="col-sm-6">
										<select class="form-select" v-model="benchmark">
											<option selected value="">--</option>
											<option v-for="item in etfProfileBenchmarksMenu" :value="item.lipperId">
												{{ $filters.defaultValue(item.benchmarkName, '--') }}
											</option>
										</select>
									</div>
									<div class="col-sm-4">
										<p><input class="btn btn-primary text-alignRight" type="button" value="加入" @click="addBenchmark()" /></p>
									</div>
								</div>
								<div class="caption">已加入ETF/美股</div>
								<div class="table-responsive mb-3">
									<table class="table">
										<thead>
											<tr>
												<th>ETF代碼</th>
												<th>ETF名稱</th>
												<th>計價幣別</th>
												<th class="text-end">一年報酬率</th>
												<th class="text-end">三年報酬率</th>
												<th class="text-end">五年報酬率</th>
												<th class="text-end">今年以來累積報酬率</th>
												<th class="text-end">一年標準差</th>
												<th class="text-center">動作</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="item in observedEtfsList">
												<td>{{ $filters.defaultValue(item.bankProCode, '--') }}</td>
												<td>{{ $filters.defaultValue(item.nameFull, '--') }}</td>
												<td>{{ $filters.defaultValue(item.currency, '--') }}</td>
												<td class="text-end">{{ $filters.defaultValue(item.value1Y, '--') }}%</td>
												<td class="text-end">{{ $filters.defaultValue(item.value3Y, '--') }}%</td>
												<td class="text-end">{{ $filters.defaultValue(item.value5Y, '--') }}%</td>
												<td class="text-end">{{ $filters.defaultValue(item.valueYtd, '--') }}%</td>
												<td class="text-end">{{ $filters.defaultValue(item.valueStd, '--') }}</td>
												<td class="text-center">
													<button
														type="button"
														class="btn btn-danger btn-icon"
														data-bs-toggle="tooltip"
														title=""
														data-bs-original-title="刪除"
														@click="deleteObservedEtfs(item.lipperId)"
													>
														<i class="fa-solid fa-trash"></i>
													</button>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
								<div class="text-center">
									<vue-performances-chart ref="etfPerformancesChartRef" :chart-id="performancesId"></vue-performances-chart>
									<div class="btn-group btn-group-sm mb-4" role="group">
										<template v-for="item in proPriceRangeMenu">
											<input
												type="radio"
												class="btn-check"
												name="time"
												:id="'etfPerformancesPeriod' + item.termValue"
												:checked="item.termValue == '4' ? true : false"
												@click="getEtfPerformances(item.rangeType, item.rangeFixed)"
											/>
											<label class="btn btn-outline-secondary" :for="'etfPerformancesPeriod' + item.termValue">{{
												$filters.defaultValue(item.termName, '--')
											}}</label>
										</template>
									</div>
								</div>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionetf8">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>相關統計資訊</h4>
								</div>
								<table class="table table-RWD table-bordered">
									<thead>
										<tr>
											<th width="32%"></th>
											<th width="17%" class="text-end">6月</th>
											<th width="17%" class="text-end">1年</th>
											<th width="17%" class="text-end">3年</th>
											<th width="17%" class="text-end">5年</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="item in performanceStatsList">
											<td>{{ $filters.defaultValue(item.calcTypeName, '--') }}</td>
											<td data-th="6月" class="text-end">
												<span v-if="item.value6M">{{ $filters.defaultValue(item.value6M, '--') }}</span
												><span v-else>----</span>
											</td>
											<td data-th="1年" class="text-end">
												<span v-if="item.value1Y">{{ $filters.defaultValue(item.value1Y, '--') }}</span
												><span v-else>----</span>
											</td>
											<td data-th="3年" class="text-end">
												<span v-if="item.value3Y">{{ $filters.defaultValue(item.value3Y, '--') }}</span
												><span v-else>----</span>
											</td>
											<td data-th="5年" class="text-end">
												<span v-if="item.value5Y">{{ $filters.defaultValue(item.value5Y, '--') }}</span
												><span v-else>----</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionetf9">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>ETF配息紀錄</h4>
								</div>
								<table class="table table-RWD table-bordered">
									<thead>
										<tr>
											<th>除息日</th>
											<th>發放日</th>
											<th class="text-end">配息總額</th>
											<th class="text-end">年化配息率(%)</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="item in etfIntRateList">
											<td data-th="除息日">{{ $filters.defaultValue(item.xdate, '--') }}</td>
											<td data-th="發放日">{{ $filters.defaultValue(item.paymentDt, '--') }}</td>
											<td data-th="配息總額" class="text-end">
												{{ $filters.defaultValue(item.xvalue, '--') }}
											</td>
											<td data-th="年化配息率(%)" class="text-end">
												{{ $filters.defaultValue(item.yield, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button @click.prevent="close()" type="button" class="btn btn-white">關閉視窗</button>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import moment from 'moment';
import vuePerformancesChart from './performancesChart.vue';
import vuePriceChart from './priceChart.vue';
import vueColumnChart from './columnChart.vue';

export default {
	components: {
		vuePerformancesChart,
		vuePriceChart,
		vueColumnChart
	},
	props: {
		downloadFile: Function,
		downloadOtherFile: Function,
		finReqCodeMenu: Array,
		proPriceRangeMenu: Array,
		close: Function
	},
	data: function () {
		const now = moment();
		return {
			proInfo: {
				fundInfo: {},
				etfInfo: {},
				bondInfo: {},
				spInfo: {},
				insInfo: {},
				dciInfo: {},
				secInfo: {}
			},
			finReqCodes: [],
			proFileB: {},
			proFileD: {},
			proFileE: {},
			proFileF: {},
			proFileG: {},
			otherFileList: [],
			etfDetail: {},
			etfStockDetail: [],
			etfStockDetailChartId: 'etfStockDetailChart',
			etfStockDetailChartData: [],
			etfStockDistribution: [],
			etfStockDistributionChartId: 'etfStockDistributionChart',
			etfStockDistributionChartData: [],
			etfPrice: {},
			etfPriceHis: [],
			priceChartId: 'etfPriceChart',
			pricChartData: [],
			profileName: '',
			benchmark: '',
			etfProfileLippers: [], // 績效表現-已加入ETF LipperIds
			observedEtfsList: [], // 績效表現-已加入ETF表資料
			etfProfileNameMenu: [], // 績效表現-ETF/美股下拉
			etfProfileBenchmarksMenu: [], // 績效表現-對應指數下拉
			performancesId: 'etfPerformancesChart',
			chartsData: [], // 績效表現圖表資料
			performanceStatsList: [],
			etfIntRateList: []
		};
	},
	watch: {},
	mounted: function () {},
	methods: {
		getProInfo: async function (bankProCode, pfcatCode) {
			var self = this;
			const ret = await this.$api.getProductInfoApi({
				proCode: bankProCode,
				pfcatCode: pfcatCode
			});
			if (_.isEmpty(ret.data)) {
				ret.data = {};
				this.$bi.alert('資料不存在');
				return;
			}
			if (_.isEmpty(ret.data.etfInfo)) {
				ret.data.etfInfo = {};
			}

			self.proInfo = ret.data;
			this.$forceUpdate();

			var selectYnList = [];

			const ret2 = await this.$api.getAdmCodeDetail({
				codeType: 'SELECT_YN'
			});
			selectYnList = ret2.data;
			if (!_.isUndefined(self.proInfo.etfInfo.buyYn)) {
				var buyYnObjs = _.filter(selectYnList, {
					codeValue: self.proInfo.etfInfo.buyYn
				});
				self.proInfo.etfInfo.buyYn = buyYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.buyYn)) {
				var buyYnObjs = _.filter(selectYnList, {
					codeValue: self.proInfo.buyYn
				});
				self.proInfo.buyYn = buyYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.sellYn)) {
				var sellYnObjs = _.filter(selectYnList, {
					codeValue: self.proInfo.sellYn
				});
				self.proInfo.sellYn = sellYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.profInvestorYn)) {
				var profInvestorYnObjs = _.filter(selectYnList, {
					codeValue: self.proInfo.profInvestorYn
				});
				self.proInfo.profInvestorYn = profInvestorYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.targetCusBu)) {
				var targetCusBuList = [];

				const ret = await this.$api.getAdmCodeDetail({
					codeType: 'CUS_BU'
				});
				targetCusBuList = ret.data;
				var targetCusBuObjs = _.filter(targetCusBuList, {
					codeValue: self.proInfo.targetCusBu
				});
				self.proInfo.targetCusBu = targetCusBuObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.volatilityType)) {
				var volatilityTypeList = [];
				const ret = await this.$api.getAdmCodeDetail({
					codeType: 'VOLATILITY_TYPE'
				});
				volatilityTypeList = ret.data;
				var volatilityTypeObjs = _.filter(volatilityTypeList, {
					codeValue: self.proInfo.volatilityType
				});
				self.proInfo.volatilityType = volatilityTypeObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.intFreqUnitype)) {
				var intFreqUnitypeList = [];
				const ret = await this.$api.getAdmCodeDetail({
					codeType: 'INT_FREQ_UNITTYPE'
				});
				intFreqUnitypeList = ret.data;
				var intFreqUnitypeObjs = _.filter(intFreqUnitypeList, {
					codeValue: self.proInfo.intFreqUnitype
				});
				self.proInfo.intFreqUnitype = intFreqUnitypeObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.principalGuarYn)) {
				var principalGuarYnList = [];
				const ret = await this.$api.getAdmCodeDetail({
					codeType: 'GUAR_YN'
				});
				principalGuarYnList = ret.data;
				var principalGuarYnObjs = _.filter(principalGuarYnList, {
					codeValue: self.proInfo.principalGuarYn
				});
				self.proInfo.principalGuarYn = principalGuarYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.selprocatNames)) {
				var selprocatNames = self.proInfo.selprocatNames.replaceAll(',', '、');
				self.proInfo.selprocatNames = selprocatNames;
			}

			if (!_.isUndefined(self.proInfo.finReqCode)) {
				self.finReqCodes = self.proInfo.finReqCode.split(',');
			}

			// 商品附加資料
			const ret3 = await this.$api.productsCommInfo({
				proCode: bankProCode,
				pfcatCode: pfcatCode
			});

			if (!_.isEmpty(ret3.data)) {
				if (ret3.data.proDocs) {
					self.otherFileList = ret3.data.proDocs; // 其他相關附件
					self.otherFileList.forEach(function (item) {
						// 其他相關附件 檔案顯示時間範圍
						item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
					});
				}

				var proFileList = ret3.data.proFiles;
				if (!_.isEmpty(proFileList)) {
					self.proFileB = proFileList.filter((proFile) => proFile.fileType === 'B')[0];
					self.proFileD = proFileList.filter((proFile) => proFile.fileType === 'D')[0];
					self.proFileE = proFileList.filter((proFile) => proFile.fileType === 'E')[0];
					self.proFileF = proFileList.filter((proFile) => proFile.fileType === 'F')[0];
					self.proFileG = proFileList.filter((proFile) => proFile.fileType === 'G')[0];
				}
			}
			self.etfProfileLippers = [];
		},
		// ETF-基本資料
		getEtfDetail: async function (proCode) {
			var self = this;
			const ret = await this.$api.getEtfDetailApi({
				prodCode: proCode
			});
			if (!_.isEmpty(ret.data)) {
				self.etfDetail = ret.data;
			}
		},
		// ETF-ETF持股
		getEtfStockHold: async function (proCode) {
			var self = this;
			const ret = await this.$api.getEtfStockHoldApi({
				proCode: proCode
			});

			if (!_.isEmpty(ret.data)) {
				if (!_.isNil(ret.data.topHolding)) {
					self.etfStockDetail = ret.data.topHolding;
					self.etfStockDetail.forEach((d) => {
						var ddata = {
							name: d.name,
							value: d.weight
						};
						self.etfStockDetailChartData.push(ddata);
					});
				}
				if (!_.isNil(ret.data.derivedAllocation)) {
					self.etfStockDistribution = ret.data.derivedAllocation;
					self.etfStockDistribution.forEach((d) => {
						var ddata = {
							name: d.itemName,
							value: d.weight
						};
						self.etfStockDistributionChartData.push(ddata);
					});
				}
			}
			self.$refs.etfStockDetailChartRef.initChart();
			self.$refs.etfStockDistributionChartRef.initChart();
		},
		// 淨值分析
		getEtfPrice: async function (proCode) {
			var self = this;
			const ret = await this.$api.getEtfPriceApi({
				prodCode: proCode
			});
			if (!_.isEmpty(ret.data)) {
				self.etfPrice = ret.data;
				if (!_.isNil(ret.data.nearly30daysAssetPriceHis)) {
					var orgPriceHis = _.orderBy(ret.data.nearly30daysAssetPriceHis, ['priceDt'], ['asc']);

					var newPriceHis = [];

					var npriceList = _.filter(orgPriceHis, {
						priceType: 'NAV'
					});
					var mpriceList = _.filter(orgPriceHis, {
						priceType: 'MID'
					});

					let dataSize = 0;
					if (npriceList.length >= mpriceList) {
						dataSize = npriceList.length;
					} else {
						dataSize = mpriceList.length;
					}

					for (var index = 0; index < dataSize; index++) {
						if (index % 3 == 0) {
							var priceDt1, nprice1, mprice1, priceDt2, nprice2, mprice2, priceDt3, nprice3, mprice3;

							if (!_.isNil(npriceList[index])) {
								priceDt1 = npriceList[index].priceDt;
								nprice1 = npriceList[index].price;
								var mpriceArray = _.filter(mpriceList, {
									priceDt: npriceList[index].priceDt
								});
								if (!_.isNil(mpriceArray[0])) {
									mprice1 = mpriceArray[0].price;
								}
							} else {
								priceDt1 = mpriceList[index]?.priceDt;
								mprice1 = mpriceList[index]?.price;
								nprice1 = null;
							}

							if (!_.isNil(npriceList[index + 1])) {
								priceDt2 = npriceList[index + 1].priceDt;
								nprice2 = npriceList[index + 1].price;
								var mpriceArray = _.filter(mpriceList, {
									priceDt: npriceList[index + 1].priceDt
								});
								if (!_.isNil(mpriceArray[0])) {
									mprice2 = mpriceArray[0].price;
								}
							} else {
								priceDt2 = mpriceList[index + 1]?.priceDt;
								mprice2 = mpriceList[index + 1]?.price;
								nprice2 = null;
							}

							if (!_.isNil(npriceList[index + 2])) {
								priceDt3 = npriceList[index + 2].priceDt;
								nprice3 = npriceList[index + 2].price;
								var mpriceArray = _.filter(mpriceList, {
									priceDt: npriceList[index + 2].priceDt
								});
								if (!_.isNil(mpriceArray[0])) {
									mprice3 = mpriceArray[0].price;
								}
							} else {
								priceDt3 = mpriceList[index + 2]?.priceDt;
								mprice3 = mpriceList[index + 2]?.price;
								nprice3 = null;
							}
							var pricHisObj = {
								priceDt1: priceDt1,
								nprice1: nprice1,
								mprice1: mprice1,
								priceDt2: priceDt2,
								nprice2: nprice2,
								mprice2: mprice2,
								priceDt3: priceDt3,
								nprice3: nprice3,
								mprice3: mprice3
							};
							newPriceHis.push(pricHisObj);
						}
					}
					self.etfPriceHis = newPriceHis;
				}
			}
		},
		// 淨值分析歷史資料線圖
		getPricesChartData: async function (proCode, rangeType, rangeFixed) {
			var self = this;
			var mData = [];
			var nData = [];
			const ret = await this.$api.getPricesChartDataApi({
				prodCode: proCode,
				freqType: rangeType,
				freqFixeds: rangeFixed
			});
			if (!_.isEmpty(ret.data)) {
				ret.data.forEach((d) => {
					if (d.priceType === 'NAV') {
						var nLineData = {
							date: Date.parse(d.priceDt),
							value: d.price
						};
						nData.push(nLineData);
					}
					if (d.priceType === 'MID') {
						var mLineData = {
							date: Date.parse(d.priceDt),
							value: d.price
						};
						mData.push(mLineData);
					}
				});

				if (!self.pricChartData[0]) {
					self.pricChartData[0] = {};
				}
				if (!self.pricChartData[1]) {
					self.pricChartData[1] = {};
				}

				if (!self.pricChartData[0]) {
					self.pricChartData[0] = {};
				}
				if (!self.pricChartData[1]) {
					self.pricChartData[1] = {};
				}
				self.pricChartData[0].datas = nData;
				self.pricChartData[0].name = '淨值';
				self.pricChartData[1].datas = mData;
				self.pricChartData[1].name = '市價';
			}
			self.$refs.etfPriceChartRef.initChart(self.pricChartData);
		},
		// 績效分析-ETF/美股下拉
		getEtfProfileNameMenu: async function (proCode) {
			var self = this;
			const ret = await this.$api.getEtfProfileNameMenuApi;
			if (!_.isEmpty(ret.data)) {
				self.etfProfileNameMenu = ret.data;
				var foundItem = _.find(self.etfProfileNameMenu, (item) => item.proCode === proCode);

				if (foundItem) {
					self.etfProfileLippers.push(foundItem.lipperId);
					self.addObservedEtfs();
					self.getEtfPerformances('Y', -1.0); // 商品資訊/績效表現圖表
				}
			}
		},
		// 績效分析-對應指數下拉
		getEtfProfileBenchmarksMenu: async function () {
			var self = this;
			const ret = await this.$api.getEtfProfileBenchmarksMenuApi();
			if (!_.isEmpty(ret.data)) {
				self.etfProfileBenchmarksMenu = ret.data;
			}
		},
		// 績效表現-加入ETF(ETF/美股)
		addEtf() {
			var self = this;
			let pk = null;
			pk = _.find(self.etfProfileLippers, function (item) {
				return item == self.profileName;
			});
			if (self.profileName != null && self.profileName != '' && pk == null) {
				self.etfProfileLippers.push(self.profileName); // 加入選擇商品
				self.addObservedEtfs(); // 績效表現 取得 已加入商品清單
				self.getEtfPerformances('Y', -1.0); // 商品資訊/績效表現圖表
			} else if (self.profileName != null && pk != null) {
				this.$bi.alert('此商品已加入');
			} else {
				this.$bi.alert('請選擇商品');
			}
		},
		// 績效表現-加入ETF(對應指數)
		addBenchmark() {
			var self = this;
			let pk = null;
			pk = _.find(self.etfProfileLippers, function (item) {
				return item == self.benchmark;
			});
			if (self.benchmark != null && self.benchmark != '' && pk == null) {
				self.etfProfileLippers.push(self.benchmark); // 加入選擇商品
				self.addObservedEtfs(); // 績效表現 取得 已加入商品清單
				self.getEtfPerformances('Y', -1.0); // 商品資訊/績效表現圖表
			} else if (self.benchmark != null && pk != null) {
				this.$bi.alert('此商品已加入');
			} else {
				this.$bi.alert('請選擇商品');
			}
		},
		// 績效表現-已加入ETF
		addObservedEtfs: async function () {
			var self = this;

			var proCodes = [];
			self.etfProfileLippers.forEach(function (item) {
				proCodes.push(item);
			});

			const ret = await this.$api.getObservedEtfsApi({
				lipperIds: proCodes.join()
			});

			self.observedEtfsList = ret.data;
		},
		// 績效表現 已加入商品 刪除按鈕
		deleteObservedEtfs(proCode) {
			var self = this;
			let index = self.etfProfileLippers.indexOf(proCode); // 找出要移除的index
			self.etfProfileLippers.splice(index, 1); // 移除加入的商品(要插入或刪除的索引位置, 要刪除的元素數量)
			self.addObservedEtfs(); // 績效表現 取得 已加入商品清單
			self.getEtfPerformances('Y', -1.0); // 商品資訊/績效表現圖表
		},
		// 商品資訊/績效表現圖表
		getEtfPerformances: async function (rangeType, rangeFixed) {
			var self = this;

			if (_.isEmpty(self.etfProfileLippers)) {
				return;
			}

			const ret = await this.$api.getEtfPerformancesApi({
				proCodes: self.etfProfileLippers,
				freqType: rangeType,
				freqFixed: rangeFixed
			});
			if (!_.isEmpty(ret.data)) {
				for (var i = 0; i < ret.data.length; i++) {
					ret.data[i].datas.forEach((e) => {
						e.value = e.returnFc;
						e.date = new Date(e.returnDt).getTime();
					});
				}
				self.chartsData = ret.data;
				self.$refs.etfPerformancesChartRef.initChart(self.chartsData);
			}
		},
		// 各項統計
		getEtfPerformanceStats: async function (proCode) {
			var self = this;
			const ret = await this.$api.getEtfPerformanceStatsApi({
				proCode: proCode
			});
			if (!_.isNil(ret.data)) {
				self.performanceStatsList = ret.data;
			}
		},
		// ETF配息
		getEtfIntRate: async function (proCode) {
			var self = this;
			const ret = await this.$api.getEtfIntRateApi({
				proCode: proCode
			});
			if (!_.isNil(ret.data)) {
				self.etfIntRateList = ret.data;
			}
		}
	} // methods end
};
</script>
