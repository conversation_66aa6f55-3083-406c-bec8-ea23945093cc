import request from '@/utils/request';

const apiPath = import.meta.env.VITE_API_URL_V1;

// wkfProcessor.vue
export function getVarItemsApi(wfgId) {
	return request({
		url: apiPath + '/wkf/wkfVarItems',
		method: 'get',
		params: {
			wfgId
		}
	});
}

export function getWkfEventsApi(wfgId) {
	return request({
		url: apiPath + '/wkf/wkfEvents',
		method: 'get',
		params: {
			wfgId
		}
	});
}

export function patchAuditApi(eventId, actionCode, desc) {
	return request({
		url: apiPath + '/wkf/process',
		method: 'patch',
		params: {
			eventId,
			actionCode,
			desc
		}
	});
}

// userAccountDetail.vue
export function getUserAccountDetailApi(eventId) {
	return request({
		url: apiPath + '/adm/userPosEvent',
		method: 'get',
		params: {
			eventId
		}
	});
}

// admRoleReviewDetail.vue
// getDetail() 呼叫了與 ADM0101/include/admRoleReviewDetail.vue 的 getDetailApi 一樣的Api
