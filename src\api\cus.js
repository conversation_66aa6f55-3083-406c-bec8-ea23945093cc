import request from '@/utils/request';
const apiPath = import.meta.env.VITE_API_URL_V1;

export function getCusGradesApi() {
	return request({
		url: apiPath + '/cus/cusGrades',
		method: 'get'
	});
}

export function cusSingleQuery(queryReq, queryString) {
	return request({
		url: apiPath + '/cus/singleCus' + queryString,
		method: 'get',
		params: queryReq
	});
}

export function getCusGroupListApi() {
	return request({
		url: apiPath + '/cus/cusGroupList',
		method: 'get'
	});
}

export function getSelectedMenuItemsApi() {
	return request({
		url: apiPath + '/cus/cusSearchFields',
		method: 'get'
	});
}

export function getAllSearchFieldsApi() {
	return request({
		url: apiPath + '/cus/cusAllSearchFields',
		method: 'get'
	});
}

export function postCusSelfsetFields({ fieldNameList }) {
	return request({
		url: apiPath + '/cus/cusSelfsetFields',
		method: 'post',
		traditional: true,
		data: {
			fieldNameList
		}
	});
}
export function getCountSearchResultApi() {
	return request({
		url: apiPath + '/cus/countSearchResult',
		method: 'get'
	});
}

export function postCusSearchList({ resultName, cusCodeList }) {
	return request({
		url: apiPath + '/cus/cusSearchList',
		method: 'post',
		headers: { 'Content-Type': 'application/json' },
		data: {
			resultName,
			cusCodeList
		}
	});
}

export function insertCusSearchLogApi({ resultCode, resultName, logType }) {
	return request({
		url: apiPath + '/cus/cusSearchLog',
		method: 'post',
		headers: { 'Content-Type': 'application/json' },
		data: {
			resultCode,
			resultName,
			logType
		}
	});
}

export function postCusGroupDetail({ groupCode, cusCodeList }) {
	return request({
		url: apiPath + '/cus/cusGroupDetail',
		method: 'post',
		traditional: true,
		data: {
			groupCode,
			cusCodeList
		}
	});
}

export function checkCusAuthApi({ cusCode }) {
	return request({
		url: apiPath + '/cus/customerAuth',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getCusInfoApi({ cusCode }) {
	return request({
		url: apiPath + '/cus/clientOverview/cusInfo',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getCustomersApi({ cusCode }) {
	return request({
		url: apiPath + '/cus/customers',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getAssetTrendApi({ cusCode }) {
	return request({
		url: apiPath + '/cus/clientOverview/assetTrend',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getTdListsDataApi({ cusCode }) {
	return request({
		url: apiPath + '/cus/clientOverview/tdLists',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getAssetLoansDataApi({ cusCode }) {
	return request({
		url: apiPath + '/cus/clientOverview/assetLoans',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getCusGroupMenuApi() {
	return request({
		url: apiPath + '/cus/cusGroupMenu',
		method: 'GET'
	});
}
export function postGroupApi({ groupName }) {
	return request({
		url: apiPath + '/cus/group',
		method: 'POST',
		params: {
			groupName
		}
	});
}
export function deleteGroupApi({ groupCode }) {
	return request({
		url: apiPath + '/cus/group',
		method: 'DELETE',
		params: {
			groupCode
		}
	});
}

export function getCusSummariesMenuApi({ pbStatus }) {
	return request({
		url: apiPath + '/cus/cusSummariesMenu',
		method: 'GET',
		params: {
			pbStatus
		}
	});
}
export function getCusSummariesApi({ idn, graCode, cusName }, queryString) {
	return request({
		url: apiPath + '/cus/cusSummaries' + queryString,
		method: 'GET',
		params: {
			idn,
			graCode,
			cusName
		}
	});
}

export function getDisabledDimensionsApi({ cusCode }) {
	return request({
		url: apiPath + '/cus/disabledDimensions',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getCustomerInfoApi({ cusCode }) {
	return request({
		url: apiPath + '/cus/customerInfo',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getCusAoHistoryApi({ cusCode }, queryString) {
	return request({
		url: apiPath + '/cus/cusAoHistory/page' + queryString,
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getAoChangeHistApi({ cusCode }) {
	return request({
		url: apiPath + '/cus/aoChangeHist',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getCompaniesApi({ cusCode }) {
	return request({
		url: apiPath + '/cus/companies',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getCountriesMenuApi() {
	return request({
		url: apiPath + '/cus/cusCountriesList',
		method: 'GET'
	});
}

export function postCompanyApi({
	cusCode,
	ownType,
	comName,
	comEname,
	vatNum,
	owner,
	establishDt,
	url,
	phone,
	contactPerson,
	contactPhone,
	indusCode,
	cunCode,
	regCunCode,
	zip,
	caddress,
	employeeNum,
	listedYn,
	note,
	capital
}) {
	return request({
		url: apiPath + '/cus/companies',
		method: 'POST',
		data: {
			cusCode,
			ownType,
			comName,
			comEname,
			vatNum,
			owner,
			establishDt,
			url,
			phone,
			contactPerson,
			contactPhone,
			indusCode,
			cunCode,
			regCunCode,
			zip,
			caddress,
			employeeNum,
			listedYn,
			note,
			capital
		}
	});
}

export function patchCompanyApi({
	comId,
	cusCode,
	ownType,
	comName,
	comEname,
	vatNum,
	owner,
	establishDt,
	url,
	phone,
	contactPerson,
	contactPhone,
	indusCode,
	cunCode,
	regCunCode,
	zip,
	caddress,
	employeeNum,
	listedYn,
	note,
	capital
}) {
	return request({
		url: apiPath + '/cus/companies',
		method: 'PATCH',
		data: {
			comId,
			cusCode,
			ownType,
			comName,
			comEname,
			vatNum,
			owner,
			establishDt,
			url,
			phone,
			contactPerson,
			contactPhone,
			indusCode,
			cunCode,
			regCunCode,
			zip,
			caddress,
			employeeNum,
			listedYn,
			note,
			capital
		}
	});
}
export function deleteCompanyApi({ comId, cusCode }) {
	return request({
		url: apiPath + '/cus/companies',
		method: 'DELETE',
		params: {
			comId,
			cusCode
		}
	});
}

export function getOwnCompanies({ comId }) {
	return request({
		url: apiPath + '/cus/ownCompanies',
		method: 'GET',
		params: {
			comId
		}
	});
}

export function saveOwnCompaniesApi({ comId, ownComList }) {
	return request({
		url: apiPath + '/cus/ownCompanies',
		method: 'PATCH',
		data: {
			comId,
			ownComList
		}
	});
}

export function getCompOverseasApi({ comId }) {
	return request({
		url: apiPath + '/cus/compOverseas',
		method: 'GET',
		params: {
			comId
		}
	});
}

export function updateCompOtherDatasApi({ comId, overseaList }) {
	return request({
		url: apiPath + '/cus/compOverseas',
		method: 'PATCH',
		data: {
			comId,
			overseaList
		}
	});
}
export function chkCustomerAuthApi({ cusCode, progCode }) {
	return request({
		url: apiPath + '/cus/chkCustomerAuth',
		method: 'GET',
		params: {
			cusCode,
			progCode
		}
	});
}

export function getRelativeFriendsApi({ cusCode }) {
	return request({
		url: apiPath + '/cus/relativeFriends',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function insertRelativeFriendsApi({ cusCode, idn, relName, reltypeCode, gender, birthday, cphone, education, organization, post, note }) {
	return request({
		url: apiPath + '/cus/relativeFriends',
		method: 'POST',
		data: {
			cusCode,
			idn,
			relName,
			reltypeCode,
			gender,
			birthday,
			cphone,
			education,
			organization,
			post,
			note
		}
	});
}

export function updateRelativeFriendsApi({ id, cusCode, idn, relName, reltypeCode, gender, birthday, cphone, education, organization, post, note }) {
	return request({
		url: apiPath + '/cus/relativeFriends',
		method: 'PATCH',
		data: {
			id,
			cusCode,
			idn,
			relName,
			reltypeCode,
			gender,
			birthday,
			cphone,
			education,
			organization,
			post,
			note
		}
	});
}

export function deleteRelativeFriendsApi({ id, cusCode }) {
	return request({
		url: apiPath + '/cus/relativeFriends',
		method: 'DELETE',
		params: {
			id,
			cusCode
		}
	});
}

export function getMemoryDateApi({ cusCode }) {
	return request({
		url: apiPath + '/cus/memoryDate',
		method: 'GET',
		params: {
			cusCode
		}
	});
}
export function postMemoryDateApi({ cusCode, dateDt, remindYn, remindDays, note }) {
	return request({
		url: apiPath + '/cus/memoryDate',
		method: 'POST',
		data: {
			cusCode,
			dateDt,
			remindYn,
			remindDays,
			note
		}
	});
}
export function updateMemoryDateApi({ id, cusCode, dateDt, remindYn, remindDays, note }) {
	return request({
		url: apiPath + '/cus/memoryDate',
		method: 'PATCH',
		data: {
			id,
			cusCode,
			dateDt,
			remindYn,
			remindDays,
			note
		}
	});
}

export function deleteMemoryDateApi({ id }) {
	return request({
		url: apiPath + '/cus/memoryDate',
		method: 'DELETE',
		params: {
			id
		}
	});
}

export function getExtDataItemApi() {
	return request({
		url: apiPath + '/cus/extDataItem',
		method: 'GET'
	});
}

export function getExtDataAnswersApi({ cusCode }) {
	return request({
		url: apiPath + '/cus/extData',
		method: 'GET',
		params: {
			cusCode
		}
	});
}
export function getExtDataLogApi({ cusCode }) {
	return request({
		url: apiPath + '/cus/extDataLog',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function updateExtDataAnsApi({ cusCode, queitem }) {
	return request({
		url: apiPath + '/cus/extData',
		method: 'PATCH',
		data: {
			cusCode,
			queitem
		}
	});
}

export function getTranTypeListApi() {
	return request({
		url: apiPath + '/cus/cusTranTypes',
		method: 'GET'
	});
}

export function getTransactionLogApi({ cusCode, pfcatCode, tranTypeCode, bankProCode, refNo, tranDtB, tranDtE }) {
	return request({
		url: apiPath + '/cus/srchCusTrans',
		method: 'GET',
		params: {
			cusCode,
			pfcatCode: pfcatCode.join(','),
			tranTypeCode: tranTypeCode.join(','),
			bankProCode,
			refNo,
			tranDtB,
			tranDtE
		}
	});
}

export function getInvTargetApi({ cusCode }) {
	return request({
		url: apiPath + '/cus/invAnalysisInvTarget',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getInvTargetGeoFocusApi({ cusCode }) {
	return request({
		url: apiPath + '/cus/invAnalysisGeoFocus',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getInvTargetRiskApi({ cusCodes }) {
	return request({
		url: apiPath + '/cus/invAnalysisRisk',
		method: 'GET',
		params: {
			cusCodes: cusCodes.join(',')
		}
	});
}

export function getInvTargetProTypes({ cusCodes }) {
	return request({
		url: apiPath + '/cus/invAnalysisInvProType',
		method: 'GET',
		params: {
			cusCodes: cusCodes.join(',')
		}
	});
}

export function getCusFilesList({ cusCode, fileType }) {
	return request({
		url: apiPath + '/cus/cusFiles/list',
		method: 'GET',
		params: {
			cusCode,
			fileType
		}
	});
}

export function generateCuaAssetReportApi({ cusCode }) {
	return request({
		url: apiPath + '/cus/cusAssetReport',
		method: 'POST',
		params: {
			cusCode
		}
	});
}

export function downloadCusFile(fileId) {
	const url = apiPath + '/com/fileView?fileType=CusFiles&fileId=' + fileId;
	var previewWindow = window.open(url, '_blank');
	previewWindow.addEventListener('beforeunload', () => {
		URL.revokeObjectURL(url);
	});
}
export function getGroupCustomers({ groupCode }) {
	return request({
		url: apiPath + '/cus/groupCustomers',
		method: 'GET',
		params: {
			groupCode
		}
	});
}
export function postGroupCustomers({ groupCode, groupName, cusCodes }) {
	return request({
		url: apiPath + '/cus/groupCustomers',
		method: 'POST',
		data: {
			groupCode,
			groupName,
			cusCodes
		}
	});
}

export function getCustomer({ cusCode }) {
	return request({
		url: apiPath + '/cus/customer',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getSearchHistoryApi({ userCode }) {
	return request({
		url: apiPath + '/cus/cusSearchResult',
		method: 'GET',
		params: {
			userCode
		}
	});
}

export function postCusSearchLog({ userCode, resultCode, resultName, logType, deputyUserCode }) {
	return request({
		url: apiPath + '/cus/cusSearchLog',
		method: 'POST',
		data: {
			userCode,
			resultCode,
			resultName,
			logType,
			deputyUserCode
		}
	});
}

export function deleteSearchResult({ resultCode }) {
	return request({
		url: apiPath + '/cus/cusSearchResult',
		method: 'DELETE',
		params: {
			resultCode
		}
	});
}

export function getMultiCusApi(
	{
		graCode,
		area,
		branCode,
		userCode,
		birthStart,
		birthEnd,
		rankCode,
		aumAmountS,
		aumAmountE,
		contbAmountS,
		contbAmountE,
		contbTime,
		contbTimeS,
		contbTimeE,
		savingType,
		savingC,
		savingAmountS,
		savingAmountE,
		proCatList,
		proCode,
		proC,
		intType,
		mktAmountS,
		mktAmountE,
		invAmountS,
		invAmountE,
		returnAmountS,
		returnAmountE,
		invType,
		debitType,
		fundC,
		efficiencyInv,
		fundType,
		insType,
		insC,
		insAAmountS,
		insAAmountE,
		loanProCode,
		loanRemark,
		loanStdDateS,
		loanStdDateE,
		loanPeriodS,
		loanPeriodE,
		appropriationS,
		appropriationE,
		loanOverAmountS,
		loanOverAmountE,
		conAppropriationS,
		conAppropriationE,
		conRateS,
		conRateE,
		conReturnType,
		conOverAmountS,
		conOverAmountE,
		conRepaymentRateS,
		conRepaymentRateE,
		approveAmountS,
		approveAmountE,
		interestRateS,
		interestRateE,
		status,
		userAmountS,
		userAmountE,
		availableAmountS,
		availableAmountE,
		queList
	},
	queryString
) {
	return request({
		url: apiPath + '/cus/multiCus' + queryString,
		method: 'GET',
		params: {
			graCode,
			area,
			branCode,
			userCode,
			birthStart,
			birthEnd,
			rankCode,
			aumAmountS,
			aumAmountE,
			contbAmountS,
			contbAmountE,
			contbTime,
			contbTimeS,
			contbTimeE,
			savingType,
			savingC,
			savingAmountS,
			savingAmountE,
			proCatList,
			proCode,
			proC,
			intType,
			mktAmountS,
			mktAmountE,
			invAmountS,
			invAmountE,
			returnAmountS,
			returnAmountE,
			invType,
			debitType,
			fundC,
			efficiencyInv,
			fundType,
			insType,
			insC,
			insAAmountS,
			insAAmountE,
			loanProCode,
			loanRemark,
			loanStdDateS,
			loanStdDateE,
			loanPeriodS,
			loanPeriodE,
			appropriationS,
			appropriationE,
			loanOverAmountS,
			loanOverAmountE,
			conAppropriationS,
			conAppropriationE,
			conRateS,
			conRateE,
			conReturnType,
			conOverAmountS,
			conOverAmountE,
			conRepaymentRateS,
			conRepaymentRateE,
			approveAmountS,
			approveAmountE,
			interestRateS,
			interestRateE,
			status,
			userAmountS,
			userAmountE,
			availableAmountS,
			availableAmountE,
			queList
		}
	});
}

export function getLastAssetAmount({ cusCode }) {
	return request({
		url: apiPath + '/cus/lastAssetAmount',
		method: 'GET',
		params: {
			cusCode
		}
	});
}
