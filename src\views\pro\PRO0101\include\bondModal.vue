<template>
	<!-- Modal 國外債-->
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">信託-海外債</h4>
				<button type="button" class="btn-close" @click.prevent="close()" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="card card-prointro shadow-none mb-3">
					<div class="pro_left">
						<div class="pro_name">
							<div class="avatar avatar-md avatar-fund"></div>
							<h4>
								<span>商品名稱</span> <br />{{ proInfo.proName }} <br /><span class="tx-black">{{ proInfo.proEName }}</span>
							</h4>
						</div>
						<h4 class="pro_value">
							<span>最新買入價</span>
							<br /><span>{{ $filters.formatNumber(proInfo.bprice, '0,0.00' || '--') }}</span> <br /><span>{{
								$filters.formatDate(proInfo.priceDt, '--')
							}}</span>
						</h4>
						<h4 class="pro_value">
							<span>最新賣出價</span>
							<br /><span>{{ $filters.formatNumber(proInfo.sprice, '0,0.00' || '--') }}</span> <br /><span>{{
								$filters.formatDate(proInfo.priceDt, '--')
							}}</span>
						</h4>
					</div>
					<div class="pro_class">
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品代碼</span> <br />{{ $filters.defaultValue(proInfo.bankProCode, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6>
								<span>資產類別 <br /></span>{{ $filters.defaultValue(proInfo.assetcatName, '--') }}
							</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品主類</span> <br />{{ $filters.defaultValue(proInfo.pfcatName, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品次類</span><br />{{ $filters.defaultValue(proInfo.proTypeName, '--') }}</h6>
						</div>
					</div>
				</div>

				<div class="tab-nav-pills">
					<ul class="nav nav-pills justify-content-center" role="tablist">
						<li class="nav-item"><a class="nav-link active" href="#Sectionbd1" data-bs-toggle="pill">商品基本資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionbd2" data-bs-toggle="pill">商品共同資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionbd3" data-bs-toggle="pill">商品附加資料</a></li>
						<li class="nav-item"><a class="nav-link datainfo" href="#Sectionbd4" data-bs-toggle="pill">價格分析</a></li>
						<li class="nav-item"><a class="nav-link datainfo" href="#Sectionbd5" data-bs-toggle="pill">績效表現</a></li>
					</ul>

					<div class="tab-content">
						<div class="tab-pane fade show active" id="Sectionbd1">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>債券商品資訊</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th class="wd-20p">風險等級</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.bondInfo.riskName, '--') }}
											</td>
											<th class="wd-20p">商品計價幣別</th>
											<td class="wd-30p">{{ $filters.defaultValue(proInfo.bondInfo.curCode, '--') }}</td>
										</tr>
										<tr>
											<th>商品狀態</th>
											<td>{{ $filters.defaultValue(proInfo.bondInfo.bondStatus, '--') }}</td>
											<th>預計通路服務費率</th>
											<td>
												<span v-if="proInfo.bondInfo.channelServiceRate"
													>{{ $filters.formatPct(proInfo.bondInfo.channelServiceRate, '--') }}%</span
												><span v-else>--</span>
											</td>
										</tr>
										<tr>
											<th>到期日</th>
											<td>{{ $filters.defaultValue(proInfo.bondInfo.expireDt, '--') }}</td>
											<th>發行日</th>
											<td>{{ $filters.defaultValue(proInfo.bondInfo.issueDt, '--') }}</td>
										</tr>
										<tr>
											<th>剩餘天數</th>
											<td>
												<span>{{ $filters.formatNumber(proInfo.bondInfo.remainDay, '--') }}</span>
											</td>
											<th>票面利率</th>
											<td>
												<span v-if="proInfo.bondInfo.parRate">{{ $filters.formatPct(proInfo.bondInfo.parRate) }}%</span>
												<span v-else>--</span>
											</td>
										</tr>
										<tr>
											<th>國際代碼(ISIN CODE)</th>
											<td>{{ $filters.defaultValue(proInfo.bondInfo.isinCode, '--') }}</td>
											<th>配息類別</th>
											<td>{{ $filters.defaultValue(proInfo.bondInfo.intRateType, '--') }}</td>
										</tr>
										<tr>
											<th>投資品質</th>
											<td>{{ $filters.defaultValue(proInfo.bondInfo.invQuality, '--') }}</td>
											<th>依受償順位</th>
											<td>{{ $filters.defaultValue(proInfo.bondInfo.payAllocation, '--') }}</td>
										</tr>
										<tr>
											<th>發行機構名稱</th>
											<td colspan="3">{{ $filters.defaultValue(proInfo.bondInfo.issuerName, '--') }}</td>
										</tr>
										<tr>
											<th>
												信用評等<br />
												(穆迪/標普/惠譽)
											</th>
											<td class="flex word-break">
												{{ $filters.defaultValue(proInfo.bondInfo.missuerLongRatingLvl, '--') }}/{{
													$filters.defaultValue(proInfo.bondInfo.sissuerLongRatingLvl, '--')
												}}/{{ $filters.defaultValue(proInfo.bondInfo.fissuerLongRatingLvl, '--') }}
											</td>
											<th>
												評等展望<br />
												(穆迪/標普/惠譽)
											</th>
											<td class="flex word-break">
												{{ $filters.defaultValue(proInfo.bondInfo.missuerRatingProsp, '--') }}/{{
													$filters.defaultValue(proInfo.bondInfo.sissuerRatingProsp, '--')
												}}/{{ $filters.defaultValue(proInfo.bondInfo.fissuerRatingProsp, '--') }}
											</td>
										</tr>
										<tr>
											<th>保證機構名稱</th>
											<td colspan="3">{{ $filters.defaultValue(proInfo.bondInfo.guarName, '--') }}</td>
										</tr>
										<tr>
											<th>
												信用評等<br />
												(穆迪/標普/惠譽)
											</th>
											<td class="flex word-break">
												{{ $filters.defaultValue(proInfo.bondInfo.mguarLongRatingLvl, '--') }}/{{
													$filters.defaultValue(proInfo.bondInfo.sguarLongRatingLvl, '--')
												}}/{{ $filters.defaultValue(proInfo.bondInfo.fguarLongRatingLvl, '--') }}
											</td>
											<th>
												評等展望<br />
												(穆迪/標普/惠譽)
											</th>
											<td class="flex word-break">
												{{ $filters.defaultValue(proInfo.bondInfo.mguarRatingProsp, '--') }}/{{
													$filters.defaultValue(proInfo.bondInfo.sguarRatingProsp, '--')
												}}/{{ $filters.defaultValue(proInfo.bondInfo.fguarRatingProsp, '--') }}
											</td>
										</tr>
										<tr>
											<th>
												債券評等<br />
												(穆迪/標普/惠譽)
											</th>
											<td class="flex word-break">
												{{ $filters.defaultValue(proInfo.bondInfo.mproLongRatingLvl, '--') }}/{{
													$filters.defaultValue(proInfo.bondInfo.sproLongRatingLvl, '--')
												}}/{{ $filters.defaultValue(proInfo.bondInfo.fproLongRatingLvl, '--') }}
											</td>
											<th></th>
											<td class="flex word-break"></td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>投資金額限制</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>最低投資金額</th>
											<td class="wd-30p" v-show="!this.$_.isNil(proInfo.bondInfo.mininvAmt)">
												<span>{{ proInfo.bondInfo.mininvAmt }}</span>
											</td>
											<td class="wd-30p" v-show="this.$_.isNil(proInfo.bondInfo.mininvAmt)">
												<span>---</span>
											</td>
											<th>最低累加金額</th>
											<td class="wd-30p" v-show="!this.$_.isNil(proInfo.bondInfo.mininvAccAmt)">
												<span>{{ proInfo.bondInfo.mininvAccAmt }}</span>
											</td>
											<td class="wd-30p" v-show="this.$_.isNil(proInfo.bondInfo.mininvAccAmt)">
												<span>---</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionbd2">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>銷售相關資料</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th width="20%">銷售地區</th>
											<td width="30%" v-if="proInfo.allYn == 'Y'">全行</td>
											<td width="30%" v-else>--</td>
											<th width="20%">限PI申購</th>
											<td width="30%">
												{{ $filters.defaultValue(proInfo.profInvestorYn, '--') }}
											</td>
										</tr>
										<tr>
											<th><span>銷售對象</span></th>
											<td class="wd-30p" colspan="3">{{ $filters.defaultValue(proInfo.targetCusBu, '--') }}</td>
										</tr>
										<tr>
											<th>是否開放申購</th>
											<td>{{ $filters.defaultValue(proInfo.buyYn, '--') }}</td>
											<th>是否開放贖回</th>
											<td>{{ $filters.defaultValue(proInfo.sellYn, '--') }}</td>
										</tr>
										<tr>
											<th><span>波動類型</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.volatilityType, '--') }}</span>
											</td>
											<th><span>配息頻率</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.intFreqUnitype, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th>配息率</th>
											<td v-if="proInfo.intRate">
												{{ $filters.formatPct(proInfo.intRate) }}%<span v-if="proInfo.intRate * 100 >= 5">高配息率</span
												><span v-else>一般配息率</span>
											</td>
											<td v-if="!proInfo.intRate">--</td>
											<th>保本要求</th>
											<td>{{ $filters.defaultValue(proInfo.principalGuarYn, '--') }}</td>
										</tr>
										<tr>
											<th><span>理財需求</span></th>
											<td colspan="3">
												<div class="form-check form-check-inline" v-for="item in finReqCodeMenu">
													<input
														class="form-check-input"
														name="finReqCodes"
														disabled
														id="c1"
														v-model="finReqCodes"
														:value="item.codeValue"
														type="checkbox"
													/>
													<label class="form-check-label" for="c1">{{ $filters.defaultValue(item.codeName, '--') }}</label>
												</div>
											</td>
										</tr>
										<tr>
											<th>商品標籤</th>
											<td disabled="disabled" colspan="3">
												{{ $filters.defaultValue(proInfo.selprocatNames, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionbd3">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>其他設定</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>商品投資標的</span></th>
											<td class="wd-80p">
												<span>{{ $filters.defaultValue(proInfo.sectorName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>商品投資地區</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.geoFocusName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>比較基準設定</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.benchmarkName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>備註</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.memo, '--') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>商品說明書</th>
											<td class="wd-80p">
												<a v-if="proFileA && proFileA.url" :href="proFileA.url" target="_blank">{{
													$filters.defaultValue(proFileA.url, '--')
												}}</a>
												<a v-else class="tx-link" href="#">--</a>
												<br />
												<a v-if="proFileA" class="tx-link" href="#" @click="downloadFile(proFileA)">{{
													$filters.defaultValue(proFileA.showName, '--')
												}}</a>
												<a v-else class="tx-link" href="#">--</a>
											</td>
										</tr>
										<tr>
											<th>投資人須知</th>
											<td class="wd-80p">
												<a v-if="proFileD && proFileD.url" :href="proFileD.url" target="_blank">{{
													$filters.defaultValue(proFileD.url, '--')
												}}</a
												><br v-if="proFileD && proFileD.url" />
												<a v-if="proFileD" class="tx-link" href="#" @click="downloadFile(proFileD)">{{
													$filters.defaultValue(proFileD.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>DM</th>
											<td class="wd-80p">
												<a v-if="proFileF && proFileF.url" :href="proFileF.url" target="_blank">{{
													$filters.defaultValue(proFileF.url, '--')
												}}</a
												><br v-if="proFileF && proFileF.url" />
												<a v-if="proFileF" class="tx-link" href="#" @click="downloadFile(proFileF)">{{
													$filters.defaultValue(proFileF.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>其他</th>
											<td class="wd-80p">
												<a v-if="proFileG && proFileG.url" :href="proFileG.url" target="_blank">{{
													$filters.defaultValue(proFileG.url, '--')
												}}</a
												><br v-if="proFileG && proFileG.url" />
												<a v-if="proFileG" class="tx-link" href="#" @click="downloadFile(proFileG)">{{
													$filters.defaultValue(proFileG.showName, '--')
												}}</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>其他相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<td>
												<span v-for="(item, index) in otherFileList">
													<a
														v-if="index === otherFileList.length - 1"
														v-show="item.show"
														href="#"
														class="tx-link"
														@click="downloadOtherFile(item.docFileId)"
														>{{ $filters.defaultValue(item.showName, '--') }}</a
													>
													<a v-else href="#" v-show="item.show" class="tx-link" @click="downloadOtherFile(item.docFileId)"
														>{{ $filters.defaultValue(item.showName, '--') }}、</a
													>
												</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="tx-note">其他相關附件為文件管理內所上傳與本商品相關的文件。</div>
						</div>

						<div class="tab-pane fade" id="Sectionbd4">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>價格分析</h4>
								</div>
								<table class="table table-RWD table-bordered">
									<thead>
										<tr>
											<th class="wd-20p">項目</th>
											<th class="wd-30p text-end">價格</th>
											<th class="wd-25p text-end">最高價格(年)</th>
											<th class="wd-25p text-end">最低價格(年)</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td data-th="項目">參考買入價</td>
											<td class="text-end" data-th="價格">
												<span
													>{{ $filters.defaultValue(bondPriceAna.bprice, '--') }}({{
														$filters.defaultValue(bondPriceAna.priceDt, '--')
													}})</span
												>
											</td>
											<td class="text-end" data-th="最高價格(年)">
												<span
													>{{ $filters.defaultValue(bondPriceAna.maxBprice, '--') }}({{
														$filters.defaultValue(bondPriceAna.maxBpriceDt, '--')
													}})</span
												>
											</td>
											<td class="text-end" data-th="最低價格(年)">
												<span
													>{{ $filters.defaultValue(bondPriceAna.minBprice, '--') }}({{
														$filters.defaultValue(bondPriceAna.minBpriceDt, '--')
													}})</span
												>
											</td>
										</tr>
										<tr>
											<td data-th="項目">參考賣出價</td>
											<td class="text-end" data-th="價格">
												<span
													>{{ $filters.defaultValue(bondPriceAna.sprice, '--') }}({{
														$filters.defaultValue(bondPriceAna.priceDt, '--')
													}})</span
												>
											</td>
											<td class="text-end" data-th="最高價格(年)">
												<span
													>{{ $filters.defaultValue(bondPriceAna.maxSprice, '--') }}({{
														$filters.defaultValue(bondPriceAna.maxSpriceDt, '--')
													}})</span
												>
											</td>
											<td class="text-end" data-th="最低價格(年)">
												<span
													>{{ $filters.defaultValue(bondPriceAna.minSprice, '--') }}({{
														$filters.defaultValue(bondPriceAna.minSpriceDt, '--')
													}})</span
												>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="col-12">
									<div class="card-header">
										<h4>歷史價格走勢圖：{{ $filters.defaultValue(proInfo.proName, '--') }}</h4>
									</div>
									<br />
									<div class="text-center">
										<vue-price-chart v-if="proInfo" ref="bondPriceChartRef" :chart-id="priceChartId"></vue-price-chart>
										<div class="btn-group btn-group-sm mb-4" role="group">
											<template v-for="item in proPriceRangeMenu">
												<input
													type="radio"
													class="btn-check"
													name="time"
													:id="'bondPricePeriod' + item.termValue"
													:checked="item.termValue == '4' ? true : false"
													@click="getPricesChartData(proInfo.proCode, item.rangeType, item.rangeFixed)"
												/>
												<label class="btn btn-outline-secondary" :for="'bondPricePeriod' + item.termValue">{{
													$filters.defaultValue(item.termName, '--')
												}}</label>
											</template>
										</div>
									</div>
								</div>

								<div class="caption">近30日價格</div>
								<table class="table table-RWD table-bordered text-center">
									<thead>
										<tr>
											<th>日期</th>
											<th class="text-end">參考買入價</th>
											<th class="text-end">參考賣出價</th>
											<th>日期</th>
											<th class="text-end">參考買入價</th>
											<th class="text-end">參考賣出價</th>
											<th>日期</th>
											<th class="text-end">參考買入價</th>
											<th class="text-end">參考賣出價</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="item in bondPriceHist">
											<td data-th="日期">{{ $filters.defaultValue(item.priceDt1, '--') }}</td>
											<td class="text-end" data-th="參考買入價">
												<span>{{ $filters.defaultValue(item.bprice1, '--') }}</span>
											</td>
											<td class="text-end" data-th="參考賣出價">
												<span>{{ item.sprice1 }}</span>
											</td>
											<td data-th="日期">{{ $filters.defaultValue(item.priceDt2, '--') }}</td>
											<td class="text-end" data-th="參考買入價">
												<span>{{ $filters.defaultValue(item.bprice2, '--') }}</span>
											</td>
											<td class="text-end" data-th="參考賣出價">
												<span>{{ $filters.defaultValue(item.sprice2, '--') }}</span>
											</td>
											<td data-th="日期">{{ $filters.defaultValue(item.priceDt3, '--') }}</td>
											<td class="text-end" data-th="參考買入價">
												<span>{{ $filters.defaultValue(item.bprice3, '--') }}</span>
											</td>
											<td class="text-end" data-th="參考賣出價">
												<span>{{ $filters.defaultValue(item.sprice3, '--') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionbd5">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>績效分析</h4>
								</div>
								<div class="card-body">
									<div class="row">
										<div class="col-sm-2">
											<label class="tc-blue">選擇債券：</label>
										</div>
										<div class="col-sm-8">
											<div class="input-group">
												<vue-field
													as="select"
													class="form-select"
													v-model="issuerCode"
													@change="getProCodeMenu()"
													name="issuerCode"
													label="選擇債券"
												>
													<option selected value>請選擇</option>
													<option value="">全部</option>
													<option v-for="item in issuersMenu" :value="item.issuerCode">
														{{ item.issuerName }}
													</option>
												</vue-field>
												<select class="form-select" v-model="proCode">
													<option selected value>請選擇</option>
													<option v-for="item in productList" :value="item.proCode">
														{{ $filters.defaultValue(item.proName, '--') }}
														{{ $filters.defaultValue(item.proCode, '--') }}
													</option>
												</select>
											</div>
										</div>
										<div class="col-sm-2">
											<p><input class="btn btn-primary text-alignRight" type="button" value="加入" @click="addPro()" /></p>
										</div>
									</div>

									<div class="caption">已加入商品</div>
									<div class="table-responsive mb-3">
										<table class="table table-bordered">
											<thead>
												<tr>
													<th>商品名稱</th>
													<th class="text-end">一年報酬率</th>
													<th class="text-center">動作</th>
												</tr>
											</thead>
											<tbody>
												<tr v-for="item in observedProList">
													<td>{{ $filters.defaultValue(item.proName) }}{{ $filters.defaultValue(item.proCode, '--') }}</td>
													<td class="text-end">{{ $filters.formatPct(item.fcTdReturn) }}%</td>
													<td class="text-center">
														<button
															type="button"
															class="btn btn-danger btn-icon"
															data-bs-toggle="tooltip"
															title="刪除"
															@click="deletePro(item.proCode)"
														>
															<i class="fa-solid fa-trash"></i>
														</button>
													</td>
												</tr>
											</tbody>
										</table>
									</div>

									<div class="text-center">
										<!-- 績效分析圖表  -->
										<vue-performances-chart ref="bondPerformancesChartRef" :chart-id="performancesId"></vue-performances-chart>
										<div class="btn-group btn-group-sm mb-4" role="group">
											<template v-for="item in proPriceRangeMenu">
												<input
													type="radio"
													class="btn-check"
													name="time"
													:id="'performancesPeriod' + item.termValue"
													:checked="item.termValue == '4' ? true : false"
													@click="getBondPerformances(proCodes, item.rangeType, item.rangeFixed)"
												/>
												<label class="btn btn-outline-secondary" :for="'performancesPeriod' + item.termValue">{{
													$filters.defaultValue(item.termName, '--')
												}}</label>
											</template>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button @click.prevent="close()" type="button" class="btn btn-white">關閉視窗</button>
			</div>
		</div>
	</div>

	<!-- Modal 5 End -->
</template>
<script>
import { Form, Field } from 'vee-validate';
import _ from 'lodash';
import moment from 'moment';
import vuePriceChart from './priceChart.vue';
import vuePerformancesChart from './performancesChart.vue';
export default {
	components: {
		'vue-field': Field,
		vuePriceChart,
		vuePerformancesChart
	},
	props: {
		downloadFile: Function,
		downloadOtherFile: Function,
		close: Function,
		finReqCodeMenu: Array,
		proPriceRangeMenu: Array
	},
	data: function () {
		const now = moment();
		return {
			proInfo: {
				testName: null,
				fundInfo: {},
				etfInfo: {},
				bondInfo: {},
				spInfo: {},
				insInfo: {},
				dciInfo: {},
				secInfo: {}
			},

			issuersMenu: [], // 績效分析 發行機構 下拉選項
			issuerCode: null, // 績效分析 發行機構
			proCode: null, // 績效分析 選擇商品
			productList: [], // 績效分析 商品下拉清單
			proCodes: [], // 績效分析 已加入商品
			observedProList: [], // 績效分析 已加入商品顯示報酬率清單

			finReqCodes: [], // 理財需求
			proFileA: {}, // 相關附件 商品說明書
			proFileD: {}, // 相關附件 投資人須知
			proFileF: {}, // 相關附件 DM
			proFileG: {}, // 相關附件 其他
			otherFileList: [], // 其他相關附件
			bondPriceAna: {},
			bondPriceHist: [],
			pricChartData: [
				{ name: null, datas: {} },
				{ name: null, datas: {} }
			],
			chartsData: [], //商品資料
			priceChartId: 'bondPriceChartId',
			performancesId: 'bondPerformancesChartId'
		};
	},
	watch: {},
	mounted: function () {},
	methods: {
		async getProInfo(proCode, pfcatCode) {
			this.proCodes = [];
			this.proCodes.push(proCode);

			const ret = await this.$api.getProductInfoApi({ proCode, pfcatCode });
			if (_.isNil(ret.data)) {
				ret.data = {};
				thi.$bi.alert('資料不存在');
				return;
			}
			if (_.isNil(ret.data.bondInfo)) {
				this.proInfo = ret.data;
				ret.data.bondInfo = {};
			} else {
				this.proInfo = ret.data;
			}

			this.$forceUpdate();
			if (!_.isUndefined(this.proInfo.bondInfo.bondStatus)) {
				const bondStatusRet = await this.$api.getAdmCodeDetail({ codeType: 'BOND_STATUS' });
				const bondStatusObjs = _.filter(bondStatusRet.data, { codeValue: this.proInfo.bondInfo.bondStatus });
				this.proInfo.bondInfo.bondStatus = bondStatusObjs[0]?.codeName;
			}

			if (!_.isUndefined(this.proInfo.bondInfo.intRateType)) {
				const bondRateTypeRet = await this.$api.getAdmCodeDetail({ codeType: 'BOND_RATE_TYPE' });
				const bondRateTypeObjs = _.filter(bondRateTypeRet.data, { codeValue: this.proInfo.bondInfo.intRateType });
				this.proInfo.bondInfo.intRateType = bondRateTypeObjs[0].codeName;
			}

			if (!_.isUndefined(this.proInfo.bondInfo.invQuality)) {
				const invQualityRet = await this.$api.getAdmCodeDetail({ codeType: 'INV_QUALITY' });
				const invQualityObjs = _.filter(invQualityRet.data, { codeValue: this.proInfo.bondInfo.invQuality });
				this.proInfo.bondInfo.invQuality = invQualityObjs[0].codeName;
			}

			if (!_.isUndefined(this.proInfo.bondInfo.payAllocation)) {
				const payAllocationRet = await this.$api.getAdmCodeDetail({ codeType: 'PAY_ALLOCATION' });
				const payAllocationObjs = _.filter(payAllocationRet.data, { codeValue: this.proInfo.bondInfo.payAllocation });
				this.proInfo.bondInfo.payAllocation = payAllocationObjs[0].codeName;
			}

			if (!_.isUndefined(this.proInfo.buyYn) && !_.isUndefined(this.proInfo.sellYn)) {
				const selectYnRet = await this.$api.getAdmCodeDetail({ codeType: 'SELECT_YN' });
				const selectYnList = selectYnRet.data;

				if (!_.isUndefined(this.proInfo.buyYn)) {
					const buyYnObjs = _.filter(selectYnList, { codeValue: this.proInfo.buyYn });
					this.proInfo.buyYn = buyYnObjs[0].codeName;
				}

				if (!_.isUndefined(this.proInfo.sellYn)) {
					const sellYnObjs = _.filter(selectYnList, { codeValue: this.proInfo.sellYn });
					this.proInfo.sellYn = sellYnObjs[0].codeName;
				}

				if (!_.isUndefined(this.proInfo.profInvestorYn)) {
					const profInvestorYnObjs = _.filter(selectYnList, { codeValue: this.proInfo.profInvestorYn });
					this.proInfo.profInvestorYn = profInvestorYnObjs[0].codeName;
				}
			}

			if (!_.isUndefined(this.proInfo.targetCusBu)) {
				const targetCusBuRet = await this.$api.getAdmCodeDetail({ codeType: 'CUS_BU' });
				const targetCusBuObjs = _.filter(targetCusBuRet.data, { codeValue: this.proInfo.targetCusBu });
				this.proInfo.targetCusBu = targetCusBuObjs[0].codeName;
			}

			if (!_.isUndefined(this.proInfo.volatilityType)) {
				const volatilityTypeRet = await this.$api.getAdmCodeDetail({ codeType: 'VOLATILITY_TYPE' });
				const volatilityTypeObjs = _.filter(volatilityTypeRet.data, { codeValue: this.proInfo.volatilityType });
				this.proInfo.volatilityType = volatilityTypeObjs[0].codeName;
			}

			if (!_.isUndefined(this.proInfo.intFreqUnitype)) {
				const intFreqUnitypeRet = await this.$api.getAdmCodeDetail({ codeType: 'INT_FREQ_UNITTYPE' });
				const intFreqUnitypeObjs = _.filter(intFreqUnitypeRet.data, { codeValue: this.proInfo.intFreqUnitype });
				this.proInfo.intFreqUnitype = intFreqUnitypeObjs[0].codeName;
			}

			if (!_.isUndefined(this.proInfo.selprocatNames)) {
				this.proInfo.selprocatNames = this.proInfo.selprocatNames.replaceAll(',', '、');
			}

			if (!_.isUndefined(this.proInfo.finReqCode)) {
				this.finReqCodes = this.proInfo.finReqCode.split(',');
			}

			if (!_.isUndefined(this.proInfo.principalGuarYn)) {
				const principalGuarYnRet = await this.$api.getAdmCodeDetail({ codeType: 'GUAR_YN' });
				const principalGuarYnObjs = _.filter(principalGuarYnRet.data, { codeValue: this.proInfo.principalGuarYn });
				this.proInfo.principalGuarYn = principalGuarYnObjs[0].codeName;
			}

			// 商品附加資料
			const commInfoRet = await this.$api.getProductsCommInfo({ proCode, pfcatCode });
			if (!_.isNil(commInfoRet.data)) {
				if (commInfoRet.data.proDocs) {
					this.otherFileList = commInfoRet.data.proDocs;
					this.otherFileList.forEach((item) => {
						item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
					});
				}

				const proFileList = commInfoRet.data.proFiles;
				if (!_.isNil(proFileList)) {
					this.proFileA = proFileList.filter((proFile) => proFile.fileType === 'A')[0];
					this.proFileD = proFileList.filter((proFile) => proFile.fileType === 'D')[0];
					this.proFileF = proFileList.filter((proFile) => proFile.fileType === 'F')[0];
					this.proFileG = proFileList.filter((proFile) => proFile.fileType === 'G')[0];
				}
			}

			this.$forceUpdate();
			// 績效分析  發行機構來源資料
			const issuersRet = await this.$api.getBondIssuersMenuApi();
			this.issuersMenu = issuersRet.data;

			this.proCodes = [];
			this.proCodes.push(proCode);

			this.observedPro();
		},

		async getBondPriceAna(proCode) {
			const ret = await this.$api.getBondPriceAnaApi({ proCodes: [proCode] });

			if (!_.isNil(ret.data)) {
				this.bondPriceAna = ret.data;
				if (!_.isNil(ret.data.bondPriceHist)) {
					const orgPriceHis = ret.data.bondPriceHist;
					const newPriceHis = [];
					orgPriceHis.forEach((item, index) => {
						if (index % 3 == 0) {
							if (index + 2 < orgPriceHis.length) {
								const pricHisObj = {
									priceDt1: orgPriceHis[index].priceDt,
									bprice1: Number.parseFloat(orgPriceHis[index].bprice),
									sprice1: Number.parseFloat(orgPriceHis[index].sprice),
									priceDt2: orgPriceHis[index + 1].priceDt,
									bprice2: Number.parseFloat(orgPriceHis[index + 1].bprice),
									sprice2: Number.parseFloat(orgPriceHis[index].sprice),
									priceDt3: orgPriceHis[index + 2].priceDt,
									bprice3: Number.parseFloat(orgPriceHis[index + 2].bprice),
									sprice3: Number.parseFloat(orgPriceHis[index + 2].sprice)
								};
								newPriceHis.push(pricHisObj);
							} else if (index + 1 < orgPriceHis.length) {
								const pricHisObj = {
									priceDt1: orgPriceHis[index].priceDt,
									bprice1: Number.parseFloat(orgPriceHis[index].bprice),
									sprice1: Number.parseFloat(orgPriceHis[index].sprice),
									priceDt2: orgPriceHis[index + 1].priceDt,
									bprice2: Number.parseFloat(orgPriceHis[index + 1].bprice),
									sprice2: Number.parseFloat(orgPriceHis[index].sprice),
									priceDt3: null,
									bprice3: null,
									sprice3: null
								};
								newPriceHis.push(pricHisObj);
							} else {
								const pricHisObj = {
									priceDt1: orgPriceHis[index].priceDt,
									bprice1: Number.parseFloat(orgPriceHis[index].bprice),
									sprice1: Number.parseFloat(orgPriceHis[index].sprice),
									priceDt2: null,
									bprice2: null,
									sprice2: null,
									priceDt3: null,
									bprice3: null,
									sprice3: null
								};
								newPriceHis.push(pricHisObj);
							}
						}
					});
					this.bondPriceHist = newPriceHis;
				}
			}
		},

		async getPricesChartData(proCode, rangeType, rangeFixed) {
			const proCodeArray = [proCode];
			const buyData = [];
			const saleData = [];

			const ret = await this.$api.getBondPriceAnaApi({
				proCodes: proCodeArray,
				freqType: rangeType,
				freqFixed: rangeFixed
			});

			if (!_.isNil(ret.data) && !_.isNil(ret.data.bondPriceHist)) {
				ret.data.bondPriceHist.forEach((d) => {
					const byuLineData = {
						date: Date.parse(d.priceDt),
						value: d.bprice
					};
					buyData.push(byuLineData);
					const saleLineData = {
						date: Date.parse(d.priceDt),
						value: d.sprice
					};
					saleData.push(saleLineData);
				});
				this.pricChartData[0].datas = buyData;
				this.pricChartData[0].name = '參考買入價';
				this.pricChartData[1].datas = saleData;
				this.pricChartData[1].name = '參考賣出價';
			}
			this.$forceUpdate();
			this.$refs.bondPriceChartRef.initChart(this.pricChartData);
		},
		//績效分析 圖表
		async getBondPerformances(proCodes, rangeType, rangeFixed) {
			const ret = await this.$api.getPerformanceRunChartApi({
				proCodes,
				freqType: rangeType, // 示區間類型
				freqFixed: rangeFixed // "顯示區間數值
			});

			if (!_.isEmpty(ret.data.datas)) {
				for (let i = 0; i < ret.data.length; i++) {
					ret.data[i].datas.forEach((e) => {
						e.value = e.returnFc;
						e.date = new Date(e.returnDt).getTime();
					});
				}
				this.chartsData = ret.data;
				this.$refs.bondPerformancesChartRef.initChart(this.chartsData);
			}
		},
		//績效分析 已加入商品清單
		async observedPro() {
			const ret = await this.$api.getObservedProductsApi({ proCodes: this.proCodes });
			this.observedProList = ret.data;
		},

		// 績效分析 選擇商品下拉
		async getProCodeMenu() {
			this.proCode = null;
			const ret = await this.$api.getProductByPfcatCode({
				pfcatCode: 'FB',
				issuerCode: this.issuerCode
			});
			this.productList = ret.data;
		},

		// 績效分析 選擇商品 加入按鈕
		async addPro() {
			const pk = _.find(this.proCodes, (item) => item == this.proCode);
			if (this.issuerCode != null && this.proCode != null && pk == null) {
				this.proCodes.push(this.proCode);
				await this.observedPro();
				await this.getBondPerformances(this.proCodes, 'Y', -1.0);
			} else if (this.issuerCode != null && this.proCode != null && pk != null) {
				thi.$bi.alert('此商品已加入');
			} else {
				thi.$bi.alert('請選擇商品');
			}
		},

		// 績效分析 已加入商品 刪除按鈕
		async deletePro(proCode) {
			if (this.proCodes.length > 1) {
				const index = this.proCodes.indexOf(proCode); // 找出要移除的index
				this.proCodes.splice(index, 1); // 移除加入的商品(要插入或刪除的索引位置, 要刪除的元素數量)
				await this.observedPro(); // 績效分析 取得 已加入商品清單
				await this.getBondPerformances(this.proCodes, 'Y', -1.0); // 商品資訊/績效分析圖表
			} else {
				thi.$bi.alert('至少要有一項商品');
			}
		},

		getPrices(proCode, rangeType, rangeFixed) {
			this.$refs.priceChartRef.getPrices(proCode, rangeType, rangeFixed);
		}
	}
};
</script>
