<template>
	<div>
		<!-- Modal 1 維護顧客重要日子 -->
		<vue-modal :is-open="modalStates.modal1" :before-close="() => closeModal('modal1')">
			<template v-slot:content="props">
				<div class="modal-dialog modal-xl modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">客戶重要日子</h4>
							<button type="button" class="btn-close" @click.prevent="props.close()" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<div class="card card-form">
								<div class="card-header">
									<h4>客戶重要日子</h4>
									<span class="tx-square-bracket">為必填欄位</span>
								</div>
							</div>
							<table class="biv-table table table-bordered">
								<tbody>
									<tr>
										<th class="wd-20p"><label>重要日子</label></th>
										<td class="wd-80p">
											<label>{{ cusMemoryTask.dateDt }}</label>
										</td>
									</tr>
									<tr>
										<th class="wd-20p"><label>說明</label></th>
										<td class="wd-80p">
											<label>{{ cusMemoryTask.note }}</label>
										</td>
									</tr>
									<tr>
										<th class="wd-20p"><label>到期通知設定</label></th>
										<td class="wd-80p">
											<label v-if="cusMemoryTask.remindYn === 'Y'">提前{{ cusMemoryTask.remindDays }}日通知</label>
											<label v-if="cusMemoryTask.remindYn === 'N'">不提前通知</label>
										</td>
									</tr>
								</tbody>
							</table>
						</div>

						<div class="modal-footer" id="modalFooterId">
							<input class="btn btn-white" id="cusMemoryModalCloseButton" type="button" value="關閉"
								@click.prevent="props.close()" />
							<input class="btn btn-danger" id="btnDelete" type="button" value="刪除"
								@click="deleteMemoryDate(cusMemoryTask.id)" />
							<input class="btn btn-primary" id="btnModify" type="button" value="修改" @click.prevent="
								doUpdate();
							openModal('modal2');
							" />
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- Modal 1 end -->

		<!-- Modal 2 維護顧客重要日子 -->
		<vue-modal :is-open="modalStates.modal2" :before-close="() => closeModal('modal2')">
			<template v-slot:content="props">
				<div class="modal-dialog modal-xl modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">顧客重要日子維護</h4>
							<button type="button" class="btn-close" @click.prevent="props.close()" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<div class="card-clientCard">
								<div class="card shadow-none mb-3">
									<table>
										<tbody>
											<tr>
												<td width="10%" class="clientCard-icon">
													<div class="avatar avatar-male">
														<img :src="getImgURL('avatar', 'man-1.png')" class="rounded-circle bg-info" />
														<!-- <img th:src="@{/images/avatar/man-3.png}" class="rounded-circle bg-info" v-if="gender =='F'">-->
													</div>
													<h5 class="mb-0">{{ cusInfo.cusName || '--' }}</h5>
												</td>
												<td width="90%">
													<div class="caption tx-black">
														<sapn class=" ">最近通聯日期：{{ $filters.formatDate(cusInfo.lastConnectionDt) || '--' }}</sapn>
													</div>
													<div class="row">
														<div class="col-lg-4">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-clipboard-data mg-xl-e-5-f"></i>投資屬性：{{
																		cusInfo.rankName || '--'
																	}}
																</li>
																<li><i class="bi bi-gift mg-xl-e-5-f"></i>生日：{{ cusInfo.birth || '--' }}</li>
																<li>
																	<i class="bi bi-envelope mg-xl-e-5-f"></i>電子郵件：{{ cusInfo.email || '--' }}
																</li>
															</ul>
														</div>
														<div class="col-lg-3">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-house-door mg-xl-e-5-f"></i>聯絡電話(住)：{{
																		cusInfo.phoneH || '--'
																	}}
																</li>
																<li>
																	<i class="bi bi-building mg-xl-e-5-f"></i>聯繫電話(公司)：{{
																		cusInfo.phoneO || '--'
																	}}
																</li>
																<li>
																	<i class="bi bi-telephone mg-xl-e-5-f"></i>聯絡電話(行動)：{{
																		cusInfo.phoneM || '--'
																	}}
																</li>
															</ul>
														</div>
														<div class="col-lg-5">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-journal-medical mg-xl-e-5-f"></i>未成年辦理財富管理業務與投資商品同意書：
																	<span :class="cusInfo.childInvYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ cusInfo.childInvYn === 'Y' ? '是' : '否' }}
																	</span>
																</li>
																<li>
																	<i class="bi bi-journal-text mg-xl-e-5-f"></i>特定金錢信託客戶投資有價證券推介同意書：
																	<span :class="cusInfo.specRecommYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ cusInfo.specRecommYn === 'Y' ? '是' : '否' }}
																	</span>
																</li>
																<li>
																	<i class="bi bi-journal-bookmark mg-xl-e-5-f"></i>財富特定客戶(不得主動推介)：
																	<span :class="cusInfo.specCusYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ cusInfo.specCusYn === 'Y' ? '是' : '否' }}
																	</span>
																</li>
															</ul>
														</div>
													</div>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
							<div class="card card-form shadow-none">
								<div class="card-header">
									<h4>客戶重要日子</h4>
									<span class="tx-square-bracket">為必填欄位</span>
								</div>
								<div class="card-content">
									<vue-form v-slot="{ errors, validate }" ref="memoryDateTask">
										<table class="biv-table table table-RWD table-bordered">
											<tbody>
												<tr>
													<th>
														<label class="form-label tx-require">重要日子</label>
													</th>
													<td>
														<div class="input-group">
															<vue-field type="date" name="memoryDate" class="form-control" v-model="memoryDate"
																:class="{ 'is-invalid': errors.memoryDate }" label="工作日期" rules="required">
															</vue-field>
															<div>
																<span class="text-danger" v-show="errors.memoryDate">{{ errors.memoryDate }}</span>
															</div>
														</div>
													</td>
												</tr>
												<tr>
													<th><label class="form-label tx-require">內容</label></th>
													<td colspan="3">
														<vue-field as="textarea" class="form-control" id="memContent" name="memContent" rows="5"
															cols="50" size="400" v-model="memContent" :class="{ 'is-invalid': errors.memContent }"
															label="內容" rules="required">
														</vue-field>
														<div>
															<span class="text-danger" v-show="errors.memContent">{{ errors.memContent }}</span>
														</div>
													</td>
												</tr>
												<tr>
													<th>
														<label class="form-label tx-require">到期通知設定</label>
													</th>
													<td colspan="3">
														<div class="input-group">
															<div class="form-check form-check-inline">
																<vue-field type="radio" name="memRemindYn" class="form-check-input" id="memRemindYn_NN"
																	value="N" v-model="memRemindYn" :class="{ 'is-invalid': errors.memRemindYn }"
																	rules="required" label="到期通知設定"></vue-field>
																<label class="form-check-label">不提前通知</label>
															</div>
															<div class="form-check form-check-inline">
																<vue-field type="radio" name="memRemindYn" class="form-check-input" id="memRemindYn_YY"
																	value="Y" v-model="memRemindYn" :class="{ 'is-invalid': errors.memRemindYn }"
																	rules="required" label="到期通知設定">
																</vue-field>
																<label class="form-check-label">提前</label>
															</div>
															<vue-field class="form-control" name="memRemindDays" type="text" size="30"
																v-model="memRemindDays" :rules="memRemindYn === 'Y' ? 'required' : ''"
																:class="{ 'is-invalid': errors.memRemindDays }" label="提前通知天/週數" rules="required">
															</vue-field>
															<div style="height: 3px">
																<span class="text-danger" v-show="errors.memRemindDays">{{
																	errors.memRemindDays
																}}</span>
															</div>
															<vue-field as="select" class="form-select" id="memRemindPrd" name="memRemindPrd"
																v-model="memRemindPrd" :rules="memRemindYn === 'Y' ? 'required' : ''"
																:class="{ 'is-invalid': errors.memRemindPrd }" label="到期通知設定">
																<option value="D">日</option>
																<option value="W">週</option>
															</vue-field>
															<span class="input-group-text">通知</span>
															<div style="height: 3px">
																<span class="text-danger" v-show="errors.memRemindPrd">{{
																	errors.memRemindPrd
																}}</span>
															</div>
														</div>
													</td>
												</tr>
											</tbody>
										</table>
									</vue-form>
								</div>
							</div>
						</div>
						<div class="modal-footer text-alignRight" id="modifyTaskFooter">
							<input class="btn btn-white" id="cusMemoryEditModalCloseButton" type="button" value="關閉"
								@click.prevent="props.close()" />
							<input class="btn btn-primary" id="btnSave" type="button" value="儲存" @click="updateMemoryDates()" />
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- Modal 2 end -->
	</div>
</template>
<script>
import _ from 'lodash';
import { Field, Form } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import { getImgURL } from '@/utils/imgURL.js';

export default {
	components: {
		vueModal,
		'vue-form': Form,
		'vue-field': Field
	},
	props: {
		cusMemoryTaskRecCode: String,
		getCalendarTasks: Function
	},

	data: function () {
		return {
			modalStates: {
				modal1: false,
				modal2: false
			},
			//顯示用參數
			cusMemoryTask: {},

			// 客戶資料
			cusCode: null,
			cusInfo: {
				cusName: null,
				birth: null,
				email: null,
				phoneH: '',
				phoneO: '',
				phoneM: '',
				rankName: null,
				childInvYn: null,
				specRecommYn: null,
				specCusYn: null,
				logsCreateDt: null
			},

			//API用參數
			recCode: null,
			memoryDate: null, // 重要日子
			memContent: null, // 內容

			memRemindYn: null, // 到期通知設定
			memRemindDays: null, // 到期通知天/週數
			memRemindPrd: null // 到期通知單位
		};
	},
	watch: {
		cusMemoryTaskRecCode: function (val) {
			var self = this;
			if (self.cusMemoryTaskRecCode) {
				self.getCusMemoryTask();
			}
		}
	},
	methods: {
		getImgURL,
		getCusMemoryTask: function () {
			var self = this;
			self.$api
				.getMemoryCalendarTaskApi({
					id: self.cusMemoryTaskRecCode
				})
				.then(function (ret) {
					self.cusMemoryTask = ret.data;
					self.cusInfo.cusCode = self.cusMemoryTask.cusCode;
					self.getCusInfo();
				});
		},
		getCusInfo: function () {
			var self = this;
			self.$api
				.getCustomer({
					cusCode: self.cusInfo.cusCode
				})
				.then(function (ret) {
					if (!ret.data) {
						self.$bi.alert('查無顧客，請確認是否為久未往來顧客。');
						self.clearValues();
						return;
					}
					self.cusInfo.cusName = ret.data.cusName;
					self.cusInfo.birth = ret.data.birth;
					self.cusInfo.email = ret.data.email;
					self.cusInfo.rankName = ret.data.rankName;
					self.cusInfo.childInvYn = ret.data.childInvYn;
					self.cusInfo.specRecommYn = ret.data.specRecommYn;
					self.cusInfo.specCusYn = ret.data.specCusYn;
					self.cusInfo.lastConnectionDt = ret.data.logCreateDt;

					// 將聯絡方式分類
					ret.data.contactInfoList.forEach(function (item) {
						switch (item.contactType) {
							case 'E': // email
								self.cusInfo.email = item.email;
								break;
							case 'H': // 住家電話
								self.cusInfo.phoneH = item.phone1;
								break;
							case 'O': // 公司電話
								self.cusInfo.phoneO = item.phone1;
								break;
							case 'M': // 手機
								self.cusInfo.phoneM = item.phone1;
								break;
						}
					});
				});
		},
		deleteMemoryDate: function (id) {
			var self = this;
			self.$bi.confirm('確定要刪除此筆資料嗎?', {
				event: {
					confirmOk: function () {
						self.$api
							.deleteMemoryDateApi({
								id: id
							})
							.then(function (ret) {
								self.$bi.alert('刪除成功');
								$('#cusMemoryModalCloseButton').click();
								if (self.getCalendarTasks) {
									self.getCalendarTasks();
								}
							});
					}
				}
			});
		},
		doUpdate: function () {
			var self = this;
			self.id = self.cusMemoryTask.id;
			self.memoryDate = self.cusMemoryTask.dateDt;
			self.memRemindYn = self.cusMemoryTask.remindYn;
			self.memRemindDays = self.cusMemoryTask.remindDays;
			self.memRemindPrd = 'D';
			self.memContent = self.cusMemoryTask.note;
		},
		updateMemoryDates: function () {
			var self = this;
			self.$refs.memoryDateTask.validate().then(function (pass) {
				if (pass.valid) {
					self.$api
						.patchMemoryDateApi({
							id: self.id,
							cusCode: self.cusInfo.cusCode,
							dateDt: self.memoryDate,
							remindYn: self.memRemindYn,
							remindDays: self.memRemindYn === 'Y' ? self.memRemindDays : null,
							remindPrd: self.memRemindYn === 'Y' ? self.memRemindPrd : null,
							note: self.memContent
						})
						.then(function (ret) {
							self.$bi.alert('更新成功');
							$('#cusMemoryEditModalCloseButton').click();
							self.getCusMemoryTask();
							if (self.getCalendarTasks) {
								self.getCalendarTasks();
							}
						});
				}
			});
		},
		clearValues: function () {
			var self = this;

			self.cusInfo.cusName = null;
			self.cusInfo.birth = null;
			self.cusInfo.email = null;
			self.cusInfo.phoneH = '';
			self.cusInfo.phoneO = '';
			self.cusInfo.phoneM = '';
			self.cusInfo.rankName = null;
			self.cusInfo.childInvYn = null;
			self.cusInfo.specRecommYn = null;
			self.cusInfo.specCusYn = null;
			self.cusInfo.logsCreateDt = null;
		},
		closeModal: function (modalName) {
			this.modalStates[modalName] = false;
		},
		openModal: function (modalName) {
			this.modalStates[modalName] = true;
		}
	}
};
</script>
