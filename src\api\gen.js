import request from '@/utils/request';
const apiPath = import.meta.env.VITE_API_URL_V1;

export function getGenMessageCat({ catType, catName, mainCatCode }) {
	return request({
		url: apiPath + '/gen/messageCat',
		method: 'get',
		params: {
			catName,
			catType,
			mainCatCode
		}
	});
}
export function postGenMessageCat({ catType, catName, mainCatCode, subCatCode }) {
	return request({
		url: apiPath + '/gen/messageCat',
		method: 'post',
		params: {
			catName,
			catType,
			mainCatCode,
			subCatCode
		}
	});
}
export function patchGenMessageCat({ catType, catName, mainCatCode, subCatCode }) {
	return request({
		url: apiPath + '/gen/messageCat',
		method: 'patch',
		data: {
			catName,
			catType,
			mainCatCode,
			subCatCode
		}
	});
}

export function getGenCountMessage({ catType, subCatCode, mainCatCode }) {
	return request({
		url: apiPath + '/gen/countMessage',
		method: 'get',
		params: {
			catType,
			subCatCode,
			mainCatCode
		}
	});
}
export function deleteGenMessageCat({ catType, catName, mainCatCode, subCatCode }) {
	return request({
		url: apiPath + '/gen/messageCat',
		method: 'delete',
		params: {
			catName,
			catType,
			mainCatCode,
			subCatCode
		}
	});
}

export function getGenMessageApi({ msgId }) {
	return request({
		url: apiPath + '/gen/message',
		method: 'get',
		params: {
			msgId
		}
	});
}
export function getBbmMaintainSaveApi() {
	return request({
		url: apiPath + '/gen/bbmMaintainSave/list',
		method: 'get'
	});
}

export function postMsgLogApi(formData) {
	return request({
		url: apiPath + '/gen/msgLog',
		method: 'post',
		data: formData,
		processData: false,
		contentType: false
	});
}

export function getMessageMapApi() {
	return request({
		url: apiPath + '/gen/messageMap',
		method: 'get'
	});
}

export function getBbsMgtPageData({ msgCode, mainCatCode, subCatCode, validBgnDt, validEndDt, msgTitle }, queryString) {
	return request({
		url: apiPath + '/gen/messageList/page' + queryString,
		method: 'get',
		params: {
			msgCode,
			mainCatCode,
			subCatCode,
			validBgnDt,
			validEndDt,
			msgTitle
		}
	});
}
export function getBbsMgtHeadPageData({ msgCode }, queryString) {
	return request({
		url: apiPath + '/gen/messageList/page' + queryString,
		method: 'get',
		params: {
			msgCode: msgCode
		}
	});
}

export function downloadBbsHeadFileApi({ fileId }) {
	const url = apiPath + '/com/fileView?fileType=GenFilesLog&fileId=' + fileId;
	var previewWindow = window.open(url, '_blank');
	previewWindow.addEventListener('beforeunload', () => {
		URL.revokeObjectURL(url);
	});
}
export function previewServerDoc({ fileId, fileTitle }) {
	const url = apiPath + '/com/fileView?fileType=GenFilesLog&fileId=' + fileId;
	var previewWindow = window.open(url, '_blank');
	previewWindow.document.title = fileTitle;
	previewWindow.addEventListener('beforeunload', () => {
		URL.revokeObjectURL(url);
	});
}

export function downloadGenOtherFileApi({ fileId }) {
	const url = apiPath + '/com/fileView?fileType=GenDocFiles&fileId=' + fileId;
	var previewWindow = window.open(url, '_blank');
	previewWindow.addEventListener('beforeunload', () => {
		URL.revokeObjectURL(url);
	});
}

export function previewServerGenOtherFile({ fileId, fileTitle }) {
	const url = apiPath + '/com/fileView?fileType=GenDocFiles&fileId=' + fileId;
	var previewWindow = window.open(url, '_blank');
	previewWindow.document.title = fileTitle;
	previewWindow.addEventListener('beforeunload', () => {
		URL.revokeObjectURL(url);
	});
}

export function getMainDocTypeApi({ catCode, mainTypeCode }) {
	return request({
		url: apiPath + '/gen/docType',
		method: 'get',
		params: {
			catCode,
			mainTypeCode
		}
	});
}
export function postOrPatchDoc({ httpMethod, formData }) {
	return request({
		url: apiPath + '/gen/document',
		method: httpMethod,
		data: formData,
		processData: false,
		contentType: false
	});
}

export function getDocProQueryPageData({ docCat, mainType, subType, bgnDt, endDt, docName }, queryString) {
	return request({
		url: apiPath + '/gen/document/page' + queryString,
		method: 'get',
		params: {
			docCat,
			mainType,
			subType,
			bgnDt,
			endDt,
			docName
		}
	});
}

export function getViewSelDoc({ docCat, docId }) {
	return request({
		url: apiPath + '/gen/document/list',
		method: 'get',
		params: {
			docCat,
			docId
		}
	});
}

export function deleteDocApi({ docId }) {
	return request({
		url: apiPath + '/gen/document',
		method: 'delete',
		params: {
			docId
		}
	});
}

export function getDocProTypeCntApi() {
	return request({
		url: apiPath + '/gen/docProTypeCnt',
		method: 'get'
	});
}

export function getDocProDCDPageData({ proTypeCode }, queryString) {
	return request({
		url: apiPath + '/gen/docPros/page' + queryString,
		method: 'get',
		params: {
			proTypeCode
		}
	});
}

export function getDocMktMainCntApi() {
	return request({
		url: apiPath + '/gen/docMktMainCnt',
		method: 'get'
	});
}

export function getDocMktSubCntApi({ mainTypeCode }) {
	return request({
		url: apiPath + '/gen/docMktMainCnt',
		method: 'get',
		params: {
			mainTypeCode
		}
	});
}

export function getDocSalesPageData({ mainTypeCode, subTypeCode }, queryString) {
	return request({
		url: apiPath + '/gen/docMkts/page' + queryString,
		method: 'get',
		params: {
			mainTypeCode,
			subTypeCode
		}
	});
}

export function getDocOutLookMainCntApi() {
	return request({
		url: apiPath + '/gen/docOutLookMainCnt',
		method: 'get'
	});
}

export function getDocOutLookSubCntApi({ mainTypeCode }) {
	return request({
		url: apiPath + '/gen/docOutLookSubCnt',
		method: 'get',
		params: {
			mainTypeCode
		}
	});
}

export function getDocResearchPageData({ mainTypeCode, subTypeCode }, queryString) {
	return request({
		url: apiPath + '/gen/docOutLooks/page' + queryString,
		method: 'get',
		params: {
			mainTypeCode,
			subTypeCode
		}
	});
}

export function getDocIssuersPageData({ queryString }) {
	return request({
		url: apiPath + '/gen/docIssuers/page' + queryString,
		method: 'get'
	});
}

export function getDocNewsPageData({ queryString }) {
	return request({
		url: apiPath + '/gen/docNews/page' + queryString,
		method: 'get'
	});
}
