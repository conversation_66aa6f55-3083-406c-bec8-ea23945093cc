<template>
	<li :leaf="defaultTo(leaf, false)">
		<div
			class="bi-tree-arrow"
			:class="{
				'bi-tree-expanded': expanded,
				'bi-tree-collapsed': !expanded
			}"
			@click="toggleExpand(data.leaf)"
			v-if="!leaf"
		></div>
		<template v-if="generateCheckbox">
			<div
				class="bi-tree-checkbox"
				:class="[
					{
						'bi-tree-checked': isCheckboxClicked,
						'bi-tree-halfChecked': isHalfChecked
					}
				]"
				@click="toggleCheck"
			></div>
			<input v-show="false" type="checkbox" :name="checkboxName" :value="data.code" v-model="checked" />
		</template>
		<div v-if="defaultTo(data.iconCls, '') !== ''" class="bi-tree-icons" :class="data.iconCls"></div>
		<div
			:id="data.code"
			class="bi-tree-text"
			:class="[
				{
					'bi-tree-leaf': leaf,
					'bi-tree-node': !leaf,
					hover: textIsHover
				},
				getClassRemoveYN(data)
			]"
			@mouseover="hoverText(true)"
			@mouseleave="hoverText(false)"
			@click="textClick"
		>
			{{ data.name }}
		</div>
		<div v-if="nodeSuffix" class="padding-left30">
			{{ getSuffixContent() }}
		</div>
		<component
			v-for="(link, index) in filteredLinks"
			:key="index"
			:is="link.type === 'anchor' ? 'a' : 'button'"
			href="#"
			@click.prevent="linkClick(link)"
			:class="link.cssCls"
		>
			<img v-if="link.imageUrl" :src="link.imageUrl" :alt="link.text" />
			<template v-else>{{ link.text }}</template>
		</component>

		<ul v-show="!leaf && expanded && children && children.length" class="bi-tree-children">
			<bi-tree-node
				v-for="child in children"
				:key="child.code"
				:data="child"
				:expandOnLoad="expandOnLoad"
				:onExpand="onExpand"
				:onCollapse="onCollapse"
				:generateCheckbox="generateCheckbox"
				:checkboxBehavior="checkboxBehavior"
				:checkboxName="checkboxName"
				:addRemoveYN="addRemoveYN"
				:onLabelHoverOver="onLabelHoverOver"
				:onLabelHoverOut="onLabelHoverOut"
				:onClick="onClick"
				:nodeSuffix="nodeSuffix"
				:url="url"
				:extraParam="extraParam"
				:tailLinks="tailLinks"
				:onLabelClick="onLabelClick"
				:labelAction="labelAction"
				:onCheck="onCheck"
				:onUnCheck="onUnCheck"
				@childChecked="updateParentCheckState"
				ref="treeNodeRefs"
			/>
		</ul>
	</li>
</template>

<script>
export default {
	name: 'TreeNode',
	props: [
		'data',
		'expandOnLoad',
		'onExpand',
		'onCollapse',
		'generateCheckbox',
		'checkboxBehavior',
		'checkboxName',
		'addRemoveYN',
		'onLabelHoverOver',
		'onLabelHoverOut',
		'onClick',
		'nodeSuffix',
		'url',
		'extraParam',
		'tailLinks',
		'onLabelClick',
		'labelAction',
		'onCheck',
		'onUnCheck',
		'action',
		'expandAll'
	],
	data() {
		return {
			expanded: this.expandOnLoad,
			checked: this.data.checked || false,
			leaf: this.data.leaf,
			checkedCheckboxs: [],
			textIsHover: false,
			children: this.data.nodes,
			hasTryLoadChildren: false,
			links: this.data.tailLinks || this.tailLinks,
			isCheckboxClicked: this.isCheckboxClicked || false,
			isHalfChecked: false
		};
	},
	methods: {
		toggleExpand(leaf) {
			if (leaf) {
				return;
			} else {
				this.expanded = !this.expanded;

				if (this.expanded) {
					this.onExpand();
				} else {
					this.onCollapse();
				}
			}
			if (this.url && !leaf && !this.data.nodes) {
				this.loadChildren();
			}
		},
		toggleCheck() {
			this.checked = !this.checked;
			this.checkboxOnClick();

			// 更新子節點勾選狀態
			this.updateChildCheckState(this.checked);
			// 通知父節點更新勾選狀態
			this.$emit('childChecked', this.checked);
		},
		updateChildCheckState(isChecked) {
			this.children.forEach((child) => {
				child.checked = isChecked;
			});
		},
		updateParentCheckState() {
			const allChecked = this.children.every((child) => child.checked);
			const someChecked = this.children.some((child) => child.checked);

			// 如果所有子節點都勾選，父節點也應勾選
			if (allChecked) {
				this.checkboxIsClick = true;
				this.isHalfChecked = false;
				this.checked = true;
			}
			// 如果有部分子節點被勾選，父節點顯示半選中
			else if (someChecked) {
				this.checkboxIsClick = false;
				this.isHalfChecked = true;
				this.checked = false;
			}
			// 如果沒有子節點被勾選，父節點不勾選
			else {
				this.checkboxIsClick = false;
				this.isHalfChecked = false;
				this.checked = false;
			}
		},
		notEmpty(obj) {
			return !(typeof obj === 'undefined' || obj == null);
		},
		defaultTo(obj, defaultValue) {
			if (!this.notEmpty(obj)) {
				obj = defaultValue;
			}
			return obj;
		},
		getClassRemoveYN(data) {
			let removeYN = '';
			if (this.addRemoveYN) {
				if (data.removeYn != null && data.removeYn.toString() == 'Y') {
					removeYN = ' bi-tree-removeY';
				} else if (data.removeYn != null && data.removeYn.toString() == 'N') {
					removeYN = ' bi-tree-removeN';
				}
			}
			return removeYN;
		},
		hoverText(isHover) {
			this.textIsHover = isHover;
			if (isHover) {
				this.onLabelHoverOver();
			} else {
				this.onLabelHoverOut();
			}
		},
		textClick() {
			if (this.onLabelClick) {
				this.onLabelClick.call(this.data.code);
			}
			// Clicking the labels should expand the children
			switch (this.labelAction) {
				case 'expand':
					this.toggleExpand(data.leaf);
					break;
				case 'check':
					this.toggleCheck();
					break;
			}

			this.onClick.call(this.data.code);
			if (this.url && !leaf && !this.data.nodes && !hasTryLoadChildren) {
				this.hasTryLoadChildren = true;
				this.loadChildren();
			}
		},
		getSuffixContent() {
			let suffixContent;
			if (typeof this.nodeSuffix === 'string') {
				suffixContent = this.nodeSuffix;
			} else if (typeof this.nodeSuffix === 'function') {
				suffixContent = this.nodeSuffix(this.data);
			}
			return suffixContent;
		},
		async loadChildren() {
			try {
				// checkedCheckboxs reassign
				this.checkedCheckboxs = [];

				const response = await fetch(this.url, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						...this.extraParam,
						id: this.data.code
					})
				});

				const data = await response.json();

				if (data) {
					this.children = data;
				} else {
					this.leaf = true;
					this.expanded = false;
				}
			} catch (e) {
				throw {
					name: 'biTreeError',
					message: 'Error while loading children: ' + e.message
				};
			}
		},
		linkClick(link) {
			const id = this.data.code; // 節點 ID
			const el = this.$el; // Vue 元件對應的 DOM 元素
			link.onClick(id, el);
		},
		checkboxOnClick() {},

		// 全部展開/收合用
		expandAllNodes(isExpand, menus = this.children) {
			this.expanded = isExpand;
			menus.forEach((item) => {
				if (item.nodes?.length) {
					this.$refs.treeNodeRefs.forEach((nodeRef) => {
						nodeRef.expandAllNodes(isExpand);
					});
				}
			});
		}
	},
	computed: {
		filteredLinks() {
			if (!Array.isArray(this.links)) {
				return [];
			} else {
				return this.links.filter((link) => {
					if (typeof link.toApply === 'function') {
						return link.toApply(this.data);
					}
					return true;
				});
			}
		}
	},
	watch: {
		children: {
			handler() {
				// 根據子節點的狀態來更新父節點的勾選狀態
				this.updateParentCheckState();
			},
			deep: true
		},
		action(newAction) {
			// 根據 action 字串來執行相應操作
			if (newAction === 'expand') {
				this.expanded = true;
			} else if (newAction === 'collapse') {
				this.expanded = false;
			} else if (newAction === 'check') {
				this.checked = true;
			} else if (newAction === 'uncheck') {
				this.checked = false;
			} else if (newAction === 'checkedNodes') {
				// todo: 待確認舊功能用途, SIO
			} else if (newAction === 'checkvalues') {
				// todo: 待確認舊功能用途, SIO
			} else if (newAction === 'log-checked') {
				// todo: 待確認舊功能用途, SIO
			}
		}
	},
	created() {
		if ((this.data.nodes && this.data.nodes.length > 0) || !this.notEmpty(this.data.leaf)) {
			this.leaf = false;
		}

		if (this.generateCheckbox) {
			//save the reference of checkbox that suppose to be checked;
			//if checkboxBehavior
			if (this.data.checked && (this.checkboxBehavior === 'none' || leaf)) {
				this.checkedCheckboxs.push(this.data);
			}
		}

		if (this.generateCheckbox && this.checkboxBehavior !== 'none') {
			this.checkboxOnClick = function () {
				this.isHalfChecked = false;
				this.isCheckboxClicked = !this.isCheckboxClicked;

				if (this.isCheckboxClicked) {
					this.checked = true;
					this.onCheck(this.data);
				} else {
					this.checked = false;
					this.onUnCheck(this.data);
				}
			};
		} else if (this.generateCheckbox) {
			this.checkboxOnClick = function () {
				this.isCheckboxClicked = !this.isCheckboxClicked;
				if (this.isCheckboxClicked) {
					this.checked = true;
					this.onCheck(this.data);
				} else {
					this.checked = false;
					this.onUnCheck(this.data);
				}
			};
		}

		//Hide all of the sub-trees that does not have a checkbox checked
		if (!this.data.leaf) {
			const someChecked = this.children?.some((child) => child.checked);
			if (!this.expandOnLoad || (this.generateCheckbox && !someChecked)) {
				this.expanded = false;
			} else {
				this.expanded = true;
			}
		}

		//recheck all checkboxes that suppose to be checked
		if (this.generateCheckbox) {
			this.checkedCheckboxs.forEach((item) => {
				item.checked = !item.checked;
			});
		}
	}
};
</script>

<style scoped>
@import '@/assets/css/bi/bi.tree.css';
</style>
