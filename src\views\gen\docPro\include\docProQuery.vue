<template>
	<div role="tabpanel" class="tab-pane fade active show">
		<div class="card card-form-collapse">
			<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formsearch1">
				<h4>查詢條件</h4>
				<span class="tx-square-bracket">為必填欄位</span>
			</div>

			<div class="card-body collapse show" id="formsearch1">
				<vue-form v-slot="{ errors, validate }" ref="queryForm">
					<div class="form-row">
						<div class="form-group col-lg-6">
							<label class="form-label tx-require">文件類型</label>
							<select class="form-select" id="cat1" v-model="docCat">
								<option :value="'ALL'">全部</option>
								<option v-for="item in selDocCat" :value="item.codeValue">
									{{ item.codeName }}
								</option>
							</select>
						</div>

						<div class="form-group col-lg-4">
							<label class="form-label">文件主分類</label>
							<select class="form-select" v-model="mainType">
								<option :value="null">全部</option>
								<option v-for="item in selMainCat" :value="item.typeCode">
									{{ item.typeName }}
								</option>
							</select>
						</div>

						<div class="form-group col-lg-4">
							<label class="form-label">文件次分類</label>
							<select class="form-select" v-model="subType" :disabled="!mainType">
								<option :value="null">全部</option>
								<option v-for="item in selSubCat" :value="item.typeCode">
									{{ item.typeName }}
								</option>
							</select>
						</div>
					</div>

					<div class="form-row">
						<div class="form-group col-lg-4">
							<label class="form-label">文件標題</label>
							<input class="form-control" type="text" maxlength="20" v-model="docName" />
						</div>
						<div class="form-group col-lg-8">
							<label class="form-label tx-require">到期日起迄</label><br />
							<div class="input-group">
								<span class="input-group-text">起</span>
								<input type="date" name="beginDate" value="" id="beginDate" class="form-control" v-model="bgnDt" />
								<span class="input-group-text">~</span>
								<span class="input-group-text">迄</span>
								<input type="date" name="endDate" value="" id="endDate" class="JQ-datepicker form-control" v-model="endDt" />
							</div>
						</div>
					</div>

					<div class="form-footer">
						<input class="btn btn-primary" type="button" value="查詢" @click.prevent="getDocument" />
					</div>
				</vue-form>
			</div>
		</div>

		<div v-if="pageData.totalElements >= 0">
			<div class="card card-table mb-3">
				<div class="card-header">
					<h4>查詢結果</h4>
					<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
				</div>
				<div class="table-responsive">
					<table class="table table-RWD table-hover table-bordered table-padding">
						<thead>
							<tr>
								<th width="10%">文件類型</th>
								<th width="20">文件標題</th>
								<th width="9%">生效日</th>
								<th width="9%">到期日</th>
								<th width="9%">緊急程度</th>
								<th width="9%">建立日期</th>
								<th width="10%">相關附件</th>
								<th width="14%">執行</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="(item, index) in pageData.content" :key="index">
								<td data-th="文件類型">{{ item.docCatName }}</td>
								<td data-th="文件標題">{{ item.docName }}</td>
								<td data-th="生效日">{{ item.validDt }}</td>
								<td data-th="到期日">{{ item.expireDt }}</td>
								<td data-th="緊急程度">{{ item.priorityName }}</td>
								<td data-th="建立日期">{{ item.createDt }}</td>
								<td class="text-center" data-th="相關附件">
									<a href="#" v-if="item.fileInfo != null" v-for="file in item.fileInfo" @click="viewFile(file.docFileId)">{{
										file.showName
									}}</a>
								</td>
								<td class="text-center" data-th="執行">
									<button
										type="button"
										class="btn btn-info btn-icon"
										data-bs-toggle="tooltip"
										title="編輯"
										@click="redirectEditPage(item.docId, item.docCatName)"
									>
										<i class="fa-solid fa-pen"></i>
									</button>
									<button type="button" class="btn btn-dark btn-icon" title="檢視" @click="viewSelDoc(item.docId, item.docCatName)">
										<i class="bi bi-search"></i>
									</button>

									<a data-bs-toggle="tooltip" href="#" class="table-icon">
										<button type="button" class="btn btn-danger btn-icon" data-bs-toggle="tooltip" title="刪除">
											<i class="fa-solid fa-trash" @click="deleteDoc(item.docId)"></i>
										</button>
									</a>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
	<!-- Modal -->
	<vue-modal :is-open="isOpenModal" :before-close="closeModal">
		<template v-slot:content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">文件查詢</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()"><i class="bi bi-arrows-fullscreen"></i></button>
						<button type="button" class="btn-close" @click.prevent="props.close()"></button>
					</div>
					<div class="modal-body">
						<div class="caption">文件分類</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p">文件類型</th>
									<td>{{ selDoc.docCatName }}</td>
								</tr>
							</tbody>
						</table>
						<div class="caption">文件內容</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p">文件標題</th>
									<td>{{ selDoc.docName }}</td>
								</tr>
								<tr>
									<th>生效日</th>
									<td>{{ selDoc.validDt }}</td>
								</tr>
								<tr>
									<th>到期日</th>
									<td>{{ selDoc.expireDt }}</td>
								</tr>
								<tr>
									<th>緊急程度</th>
									<td>{{ selDoc.priorityName }}</td>
								</tr>
								<tr>
									<th>可否提供給客戶</th>
									<td>{{ selDoc.showCusName }}</td>
								</tr>
								<tr>
									<th>摘要</th>
									<td>{{ selDoc.docDesc }}</td>
								</tr>
								<tr>
									<th>附加文檔</th>
									<td>
										<ul class="list-group list-inline-tags" v-for="file in selDoc.fileInfo">
											<li class="list-group-item">
												<a href="#" @click="viewFile(file.docFileId)">
													<span>{{ file.showName }}</span>
												</a>
											</li>
										</ul>
									</td>
								</tr>
								<tr>
									<th>關聯商品</th>
									<td>
										<a href="#" class="link-underline">{{ getProNames(selDoc.productInfo) }}</a>
									</td>
								</tr>
								<tr>
									<th>通知對象</th>
									<td>
										<span v-if="hasHoldName">{{ selDoc.holdName }}</span>
										<span v-if="hasHoldName && hasSubsName"> 、 </span>
										<span v-if="hasSubsName">{{ selDoc.subsName }}</span>
									</td>
								</tr>
							</tbody>
						</table>
						<div class="caption">維護資訊</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p">建立人員</th>
									<td>{{ selDoc.createBy }}</td>
								</tr>
								<tr>
									<th>建立日期</th>
									<td>{{ selDoc.createDt }}</td>
								</tr>
								<tr>
									<th>最後維護人員</th>
									<td>{{ selDoc.modifyBy }}</td>
								</tr>
								<tr>
									<th>最後維護日期</th>
									<td>{{ selDoc.modifyDt }}</td>
								</tr>
							</tbody>
						</table>
					</div>

					<div class="modal-footer" id="appointmentFooter">
						<button @click.prevent="props.close()" type="button" class="btn btn-white">關閉</button>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
	<!-- Modal End -->
</template>
<script>
import { Field, Form } from 'vee-validate';
import _ from 'lodash';
import moment from 'moment';
import vueModal from '@/views/components/model.vue';
import vuePagination from '@/views/components/pagination.vue';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueModal,
		vuePagination
	},
	data: function () {
		return {
			selDocCat: [],
			selMainCat: [],
			selSubCat: [],
			pageData: {
				content: {}
			},
			selDoc: {},

			selectedTabCode: null,
			//傳入API參數
			mainCat: '',
			docCat: 'ISSUER',
			mainType: null,
			subType: null,
			bgnDt: null,
			endDt: null,
			docName: null,

			//分頁元件
			pageable: {
				page: 0,
				size: 10,
				sort: 'DOC_ID',
				direction: 'ASC'
			},
			//Modal
			isOpenModal: null,
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',
			buttonClass: 'btn-expand'
		};
	},
	watch: {
		docCat: function (newVal, oldVal) {
			var self = this;
			if (_.isEqual(newVal, 'MKTEVENT') || _.isEqual(newVal, 'MKTOUTLOOK')) {
				self.getMainCat(newVal);
			} else if (_.isEqual(newVal, 'PRODUCT')) {
				self.getProCat();
			} else {
				self.selMainCat = [];
			}
		},
		selectedTabCode(newVal) {
			if (newVal) {
				this.gotoPage(0);
			}
		},
		mainType: async function (newVal, oldVal) {
			var self = this;
			if (newVal) {
				self.selSubCat = await self.getMainCatType(self.docCat, newVal);
			} else {
				self.subType = null;
			}
		}
	},
	mounted: async function () {
		var self = this;
		self.selDocCat = await self.getAdmCodeDetail('DOC_CATS');
	},
	computed: {
		userInfo: function () {
			return this.$store.getters['userInfo/info'];
		},
		hasHoldName() {
			return !!this.selDoc.holdName; // 檢查 holdName 是否有值
		},
		hasSubsName() {
			return !!this.selDoc.subsName; // 檢查 subsName 是否有值
		}
	},
	methods: {
		async getProCat() {
			var self = this;
			const ret = await self.$api.getProCatApi();
			self.selProCat = ret.data;
		},
		getMainCat: async function (catCode) {
			var self = this;
			const ret = await self.$api.getMainDocTypeApi({
				catCode: catCode
			});
			if (ret.data) {
				self.selMainCat = ret.data;
			}
		},
		getMainCatType: async function (catCode, mainTypeCode) {
			var self = this;
			var data = { catCode: catCode, mainTypeCode: mainTypeCode };
			let reqData = _.omitBy(data, (value) => _.isNil(value) || value === '');
			const ret = await self.$api.getMainDocTypeApi(reqData);
			return ret.data;
		},
		getAdmCodeDetail: async function (codeType) {
			var self = this;
			const ret = await self.$api.getAdmCodeDetail({ codeType: codeType });
			return ret.data;
		},
		getDocument: function () {
			var self = this;
			$.when(self.$refs.queryForm.validate()).then(function (result) {
				if (result.valid) {
					if (_.isNil(self.docCat)) {
						self.$self.$swal.fire({
							icon: 'error',
							text: '文件類型為必填',
							showCloseButton: true,
							confirmButtonText: '確認',
							buttonsStyling: false, // remove default button style
							customClass: {
								confirmButton: 'btn btn-danger'
							}
						});
						return;
					}
					if (moment(self.bgnDt).isAfter(moment(self.endDt))) {
						self.$swal.fire({
							icon: 'error',
							text: '日期區間的起不能大於迄',
							showCloseButton: true,
							confirmButtonText: '確認',
							buttonsStyling: false, // remove default button style
							customClass: {
								confirmButton: 'btn btn-danger'
							}
						});
						return;
					}
					if (
						(self.bgnDt != null && self.endDt === null) ||
						(self.bgnDt === null && self.endDt != null) ||
						(self.bgnDt === null && self.endDt === null)
					) {
						self.$swal
							.fire({
								icon: 'error',
								text: '日期區間，起、迄都要設定',
								showCloseButton: true,
								confirmButtonText: '確認',
								buttonsStyling: false, // remove default button style
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							})
							.then(function () {
								self.bgnDt = null;
								self.endDt = null;
							});
						return;
					}
					self.getPageData(0);
				}
			});
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (page) {
			var self = this;
			var url = _.toPageUrl('', page, self.pageable);
			const ret = await self.$api.getDocProQueryPageData(
				{
					docCat: self.docCat,
					mainType: self.mainType,
					subType: self.subType,
					bgnDt: _.formatDate(self.bgnDt),
					endDt: _.formatDate(self.endDt),
					docName: self.docName
				},
				url
			);
			self.pageData = ret.data;
		},
		viewSelDoc: async function (docId, docCatName) {
			var self = this;

			var selDocItem = _.filter(self.selDocCat, {
				codeName: docCatName
			});
			var docCatParam = '';
			if (!_.isEmpty(selDocItem)) {
				docCatParam = selDocItem[0].codeValue;
			}

			const ret = await self.$api.getViewSelDoc({
				docCat: docCatParam,
				docId: docId
			});
			if (!ret.data?.length > 0) return;
			self.selDoc = ret.data[0];
			self.isOpenModal = true;
		},
		getProNames(productInfo) {
			if (!productInfo || productInfo.length === 0) {
				return '';
			}
			return _.chain(productInfo).map('proName').join('、 ').value();
		},
		changeModalSize: function () {
			var self = this;
			if (self.modalClass === 'modal-dialog modal-lg modal-dialog-centered') {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered fullscreen';
				self.buttonClass = 'btn-expand mini';
			} else {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered';
				self.buttonClass = 'btn-expand';
			}
		},
		closeModal: function () {
			var self = this;
			self.isOpenModal = false;
		},
		openModal: function () {
			var self = this;
			self.isOpenModal = true;
		},
		viewFile: async function (targetFileId) {
			var self = this;
			await self.$api.downloadGenOtherFileApi({ fileId: targetFileId });
		},
		redirectEditPage: function (docId, docCatName) {
			let self = this;
			if (docCatName == '行銷資訊') {
				self.docCat = 'MKTEVENT';
			} else if (docCatName == '商品資訊') {
				self.docCat = 'PRODUCT';
			}
			let url = self.config.contextPath + '/gen/docProParam?';
			url += 'docId=' + _.convertUrlSpecChar(docId);
			url += '&docCat=' + self.docCat;
			location.href = url;
		},
		deleteDoc: function (docId) {
			var self = this;
			self.$swal
				.fire({
					icon: 'warning',
					text: '是否要刪除此文件？',
					showCloseButton: true,
					confirmButtonText: '確認',
					showCancelButton: true,
					cancelButtonText: '取消',
					buttonsStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-danger',
						cancelButton: 'btn btn-secondary'
					}
				})
				.then(async function (result) {
					if (result.isConfirmed) {
						const ret = await self.$api.deleteDocApi({
							docId: docId
						});
						self.$swal
							.fire({
								icon: 'success',
								text: '刪除成功',
								showCloseButton: true,
								confirmButtonText: '確認',
								buttonsStyling: false,
								customClass: {
									confirmButton: 'btn btn-success'
								}
							})
							.then(function (ret) {
								self.getDocument();
							});
					}
				});
		}
	}
};
</script>
