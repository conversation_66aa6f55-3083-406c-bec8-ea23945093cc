<template>
	<dynamic-title></dynamic-title>
	<!-- 查詢條件-->
	<div class="card card-form-collapse">
		<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formsearch1">
			<h4>查詢條件</h4>
			<span class="tx-square-bracket">為必填欄位</span>
		</div>
		<div class="card-body collapse show" id="formsearch1">
			<form>
				<div class="form-row">
					<div class="form-group col-lg-6">
						<label class="form-label">功能模組</label>
						<select name="menuCode" id="menuCode" class="form-select" v-model="menuCode">
							<option value="" selected="selected">全部</option>
							<option v-for="functionMenu in functionMenuTree" :value="functionMenu.menuCode">
								{{ functionMenu.menuName }}
							</option>
						</select>
					</div>
				</div>
				<div class="form-footer">
					<button id="btnSave" type="button" class="btn btn-primary" @click="getPrograSet()">查詢</button>
				</div>
			</form>
		</div>
	</div>
	<!-- 查詢結果-->
	<div id="searchResult">
		<div class="card card-table">
			<div class="card-header">
				<h4>系統功能選單設定</h4>
			</div>
			<div class="table-responsive">
				<div class="m-2">
					<button type="button" class="btn btn-info btn-glow me-2" @click="toggleAll(true)">全部展開</button>
					<button type="button" class="btn btn-info btn-glow" @click="toggleAll(false)">全部收合</button>
				</div>
				<table class="bih-table table table-RWD table-expandable">
					<thead>
						<tr>
							<th>一級選單(模組)</th>
							<th>二級選單(功能)</th>
							<th>三級選單(子功能)</th>
							<th>四級選單(頁簽)</th>
							<th>五級選單(子頁簽)</th>
							<th>異動人員</th>
							<th>異動日期</th>
							<th>執行</th>
						</tr>
					</thead>
					<tbody>
						<template v-for="module in prograSet">
							<tr>
								<td class="text-start">
									<button
										class="btn"
										type="button"
										:style="module.nodes?.length ? '' : 'pointer-events: none;'"
										@click="toggle(module)"
									>
										<i
											class="bi"
											:class="module.isExpanded ? 'bi-dash-lg' : 'bi-plus-lg'"
											:style="module.nodes?.length ? '' : 'opacity: 0'"
										></i>
									</button>
									{{ module.name }}
								</td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td class="text-center" data-th="異動人員"></td>
								<td class="text-center" data-th="異動日期"></td>
								<td class="text-center" data-th="執行"></td>
							</tr>
							<template v-for="program in module.nodes">
								<tr v-show="module.isExpanded">
									<td></td>
									<td class="text-start">
										<button
											class="btn"
											type="button"
											:style="program.nodes?.length ? '' : 'pointer-events: none;'"
											@click="toggle(program)"
										>
											<i
												class="bi"
												:class="program.isExpanded ? 'bi-dash-lg' : 'bi-plus-lg'"
												:style="program.nodes?.length ? '' : 'opacity: 0'"
											></i>
										</button>
										{{ program.name }}
									</td>
									<td></td>
									<td></td>
									<td></td>
									<td class="text-center" data-th="異動人員">
										{{ program.modifyBy }}
										{{ program.userName }}
									</td>
									<td class="text-center" data-th="異動日期">
										{{ program.modifyDt }}
									</td>
									<td class="text-center" data-th="執行">
										<div class="form-check form-check-inline" v-if="!isExcludedMenu(program.menuCode)">
											<input
												class="form-check-input"
												@change="updateActiveArray(program, 'Y')"
												:key="program.menuCode"
												:id="program.menuCode + 'Y'"
												type="radio"
												:name="program.menuCode"
												:checked="program.activeYn == 'Y'"
											/>
											<label class="form-check-label" :for="program.menuCode + 'Y'">執行</label>
										</div>
										<div class="form-check form-check-inline" v-if="!isExcludedMenu(program.menuCode)">
											<input
												class="form-check-input"
												@change="updateActiveArray(program, 'N')"
												:key="program.menuCode"
												:id="program.menuCode + 'N'"
												type="radio"
												:name="program.menuCode"
												:checked="program.activeYn == 'N'"
											/>
											<label class="form-check-label" :for="program.menuCode + 'N'">停用</label>
										</div>
									</td>
								</tr>
								<template v-for="page in program.nodes">
									<tr v-show="program.isExpanded && module.isExpanded">
										<td></td>
										<td></td>
										<td class="text-start">
											<button
												class="btn"
												type="button"
												:style="page.nodes?.length ? '' : 'pointer-events: none;'"
												@click="toggle(page)"
											>
												<i
													class="bi"
													:class="page.isExpanded ? 'bi-dash-lg' : 'bi-plus-lg'"
													:style="page.nodes?.length ? '' : 'opacity: 0'"
												></i>
											</button>
											{{ page.name }}
										</td>
										<td></td>
										<td></td>
										<td class="text-center" data-th="異動人員">
											{{ page.modifyBy }}
											{{ page.userName }}
										</td>
										<td class="text-center" data-th="異動日期">
											{{ page.modifyDt }}
										</td>
										<td class="text-center" data-th="執行">
											<div class="form-check form-check-inline" v-if="!isExcludedMenu(page.menuCode)">
												<input
													class="form-check-input"
													@change="updateActiveArray(page, 'Y')"
													:key="page.id"
													:id="page.menuCode + 'Y'"
													type="radio"
													:name="page.menuCode"
													:checked="page.activeYn == 'Y'"
												/>
												<label class="form-check-label" :for="page.menuCode + 'Y'">執行</label>
											</div>
											<div class="form-check form-check-inline" v-if="!isExcludedMenu(page.menuCode)">
												<input
													class="form-check-input"
													@change="updateActiveArray(page, 'N')"
													:key="page.id"
													:id="page.menuCode + 'N'"
													type="radio"
													:name="page.menuCode"
													:checked="page.activeYn == 'N'"
												/>
												<label class="form-check-label" :for="page.menuCode + 'N'">停用</label>
											</div>
										</td>
									</tr>
									<template v-for="tab in page.nodes">
										<tr v-show="page.isExpanded && program.isExpanded && module.isExpanded">
											<td></td>
											<td></td>
											<td></td>
											<td class="text-start">
												<button
													class="btn"
													type="button"
													:style="tab.nodes?.length ? '' : 'pointer-events: none'"
													@click="toggle(tab)"
												>
													<i
														class="bi"
														:class="tab.isExpanded ? 'bi-dash-lg' : 'bi-plus-lg'"
														:style="tab.nodes?.length ? '' : 'opacity: 0'"
													></i>
												</button>
												{{ tab.name }}
											</td>
											<td></td>
											<td class="text-center" data-th="異動人員">
												{{ tab.modifyBy }}
												{{ tab.userName }}
											</td>
											<td class="text-center" data-th="異動日期">
												{{ tab.modifyDt }}
											</td>
											<td class="text-center" data-th="執行">
												<div class="form-check form-check-inline" v-if="!isExcludedMenu(tab.menuCode)">
													<input
														class="form-check-input"
														@change="updateActiveArray(tab, 'Y')"
														:key="tab.menuCode"
														:id="tab.menuCode + 'Y'"
														type="radio"
														:name="tab.menuCode"
														:checked="tab.activeYn == 'Y'"
													/>
													<label class="form-check-label" :for="tab.menuCode + 'Y'">執行</label>
												</div>
												<div class="form-check form-check-inline" v-if="!isExcludedMenu(tab.menuCode)">
													<input
														class="form-check-input"
														@change="updateActiveArray(tab, 'N')"
														:key="tab.menuCode"
														:id="tab.menuCode + 'N'"
														type="radio"
														:name="tab.menuCode"
														:checked="tab.activeYn == 'N'"
													/>
													<label class="form-check-label" :for="tab.menuCode + 'N'">停用</label>
												</div>
											</td>
										</tr>
										<template v-for="subTab in tab.nodes">
											<tr v-show="tab.isExpanded && page.isExpanded && program.isExpanded && module.isExpanded">
												<td></td>
												<td></td>
												<td></td>
												<td></td>
												<td class="text-start">
													<button class="btn" type="button" :style="subTab.nodes?.length ? '' : 'pointer-events: none'">
														<i
															class="bi"
															:class="subTab.isExpanded ? 'bi-dash-lg' : 'bi-plus-lg'"
															:style="subTab.nodes?.length ? '' : 'opacity: 0'"
														></i>
													</button>
													{{ subTab.name }}
												</td>
												<td class="text-center" data-th="異動人員">
													{{ subTab.modifyBy }}
													{{ subTab.userName }}
												</td>
												<td class="text-center" data-th="異動日期">
													{{ subTab.modifyDt }}
												</td>
												<td class="text-center" data-th="執行">
													<div class="form-check form-check-inline" v-if="!isExcludedMenu(subTab.menuCode)">
														<input
															class="form-check-input"
															@change="updateActiveArray(subTab, 'Y')"
															:key="subTab.menuCode"
															:id="subTab.menuCode + 'Y'"
															type="radio"
															:name="subTab.menuCode"
															:checked="subTab.activeYn == 'Y'"
														/>
														<label class="form-check-label" :for="subTab.menuCode + 'Y'">執行</label>
													</div>
													<div class="form-check form-check-inline" v-if="!isExcludedMenu(subTab.menuCode)">
														<input
															class="form-check-input"
															@change="updateActiveArray(subTab, 'N')"
															:key="subTab.menuCode"
															:id="subTab.menuCode + 'N'"
															type="radio"
															:name="subTab.menuCode"
															:checked="subTab.activeYn == 'N'"
														/>
														<label class="form-check-label" :for="subTab.menuCode + 'N'">停用</label>
													</div>
												</td>
											</tr>
										</template>
									</template>
								</template>
							</template>
						</template>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	<div class="btn-end">
		<button id="btnSave" type="button" class="btn btn-primary btn-lg" @click="save()">儲存</button>
	</div>
</template>

<script>
import dynamicTitle from '@/views/components/dynamicTitle.vue';
export default {
	components: {
		dynamicTitle
	},
	data: function () {
		return {
			//下拉選單
			functionMenuTree: null,
			//Api 用參數
			menuCode: '',
			menuUpdateReq: [],

			//Api邏輯判斷用參數
			orgCheckedIds: [],
			orgUnCheckedIds: [],

			//主要顯示資料
			prograSet: [],

			// 用來修改項目是否執行的陣列
			activeArray: [],

			//不可控制的功能
			excludeMenuCode: []
		};
	},
	computed: {
		menus: function () {
			return this.$store.getters['menus/menus'];
		},
		currMenuCode: function () {
			return this.$store.getters['menus/menuCode'];
		}
	},
	mounted: function () {
		this.getFunctionMenuTree();

		this.getExcludeMenuCode(this.menus);
	},
	methods: {
		toggle(menu) {
			menu.isExpanded = !menu.isExpanded;
		},
		toggleAll(isExpanded, menus = this.prograSet) {
			if (!Array.isArray(menus)) return; // Vuex meuns 預設從 [] 改為 () => ({}) 因此新增
			menus.forEach((m) => {
				m.isExpanded = isExpanded;
				if (m.nodes?.length) {
					this.toggleAll(isExpanded, m.nodes);
				}
			});
		},
		getFunctionMenuTree: async function () {
			let ret = await this.$api.getFunctionMenuTreeApi();
			this.functionMenuTree = ret.data;
		},
		getExcludeMenuCode: function (menus) {
			if (!Array.isArray(menus)) return; // Vuex meuns 預設從 [] 改為 () => ({}) 因此新增
			if (menus) {
				for (const m of menus) {
					if (m.code !== this.currMenuCode) {
						if (this.getExcludeMenuCode(m.nodes)) {
							this.excludeMenuCode.push(m.code);
						}
					} else {
						this.excludeMenuCode.push(m.code);
						return true;
					}
				}
			}
			return false;
		},
		isExcludedMenu: function (menuCode) {
			return this.excludeMenuCode.indexOf(menuCode) >= 0;
		},
		getPrograSet: async function () {
			this.prograSet = [];
			this.activeArray = [];

			let ret = await this.$api.getFunctionMenuTreeApi(this.menuCode);
			this.prograSet = ret.data;
		},
		updateActiveArray: function (menu, yn, direction) {
			if (menu) {
				var existingItem = this.activeArray.find((item) => item.menuCode === menu.menuCode);
				menu.activeYn = yn;
				if (!existingItem) {
					var newItem = { menuCode: menu.menuCode, activeYn: yn };
					this.activeArray.push(newItem);
				} else {
					existingItem.activeYn = yn;
				}

				if (direction === 'up' || !direction) {
					// 往上變更父menu的狀態
					const findParentMenu = (menus, targetMenuCode) => {
						const found = menus.find((m) => m.parentCode && m.menuCode === targetMenuCode);
						if (!found) {
							const foundList = menus.map((m) => findParentMenu(m.nodes, targetMenuCode)).filter((m) => m);
							return foundList[0] || undefined;
						}
						return found;
					};
					const parentMenu = findParentMenu(this.prograSet, menu.parentCode);
					const sameLevelActiveYn = parentMenu?.nodes.map((x) => x.activeYn).filter((item, idx, arr) => arr.indexOf(item) === idx);
					// 本層全部都是N => 把父menu也變成N
					// 本曾是Y但父menu是N => 把父menu也變成Y
					if ((sameLevelActiveYn?.length === 1 && sameLevelActiveYn[0] === 'N') || (menu.activeYn === 'Y' && parentMenu.activeYn === 'N')) {
						this.updateActiveArray(parentMenu, yn, 'up');
					}
				}
				if (direction === 'down' || !direction) {
					// 往下變更全部子menu的狀態
					menu.nodes?.forEach((m) => {
						this.updateActiveArray(m, yn, 'down');
					});
				}
			}
		},
		save: async function () {
			let ret = await this.$api.patchSaveMenuActive(this.activeArray);
			if (ret) {
				this.$swal.fire({
					title: '訊息',
					text: '修改成功。',
					showCloseButton: true,
					confirmButtonText: '確認',
					buttonsStyling: false,
					customClass: {
						confirmButton: 'btn btn-primary'
					}
				});
				this.getPrograSet();
			}
		}
	}
};
</script>
