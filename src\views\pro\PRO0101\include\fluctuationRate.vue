<template>
	<div class="col-12">
		<h4>
			漲跌機率分析
			<small class="small-text"
				>以下表格以近五年({{ $filters.defaultValue(minYear, '--') }}年~{{ $filters.defaultValue(maxYear, '--') }}年)進行計算</small
			>
		</h4>
		<div class="table-responsive">
			<table width="100%" class="table table-condensed text-right">
				<thead>
					<tr>
						<th width="4%">&nbsp;</th>
						<th width="7%">&nbsp;</th>
						<th width="5%">&nbsp;</th>
						<th width="7%" class="text-center">1月</th>
						<th width="7%" class="text-center">2月</th>
						<th width="7%" class="text-center">3月</th>
						<th width="7%" class="text-center">4月</th>
						<th width="7%" class="text-center">5月</th>
						<th width="7%" class="text-center">6月</th>
						<th width="7%" class="text-center">7月</th>
						<th width="7%" class="text-center">8月</th>
						<th width="7%" class="text-center">9月</th>
						<th width="7%" class="text-center">10月</th>
						<th width="7%" class="text-center">11月</th>
						<th width="7%" class="text-center">12月</th>
					</tr>
					<tr>
						<th rowspan="2">&nbsp;</th>
						<th rowspan="2">&nbsp;</th>
						<th rowspan="2">計算<br />區間<br />(月)</th>
						<th>上漲機率(%)</th>
						<th>上漲機率(%)</th>
						<th>上漲機率(%)</th>
						<th>上漲機率(%)</th>
						<th>上漲機率(%)</th>
						<th>上漲機率(%)</th>
						<th>上漲機率(%)</th>
						<th>上漲機率(%)</th>
						<th>上漲機率(%)</th>
						<th>上漲機率(%)</th>
						<th>上漲機率(%)</th>
						<th>上漲機率(%)</th>
					</tr>
					<tr>
						<th>平均報酬(%)</th>
						<th>平均報酬(%)</th>
						<th>平均報酬(%)</th>
						<th>平均報酬(%)</th>
						<th>平均報酬(%)</th>
						<th>平均報酬(%)</th>
						<th>平均報酬(%)</th>
						<th>平均報酬(%)</th>
						<th>平均報酬(%)</th>
						<th>平均報酬(%)</th>
						<th>平均報酬(%)</th>
						<th>平均報酬(%)</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td rowspan="2">名稱</td>
						<td rowspan="2" class="text-left">
							<a>{{ $filters.defaultValue(fundInfo && fundInfo.fundName, '--') }}</a>
						</td>
						<td rowspan="2">{{ period }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 1), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 2), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 3), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 4), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 5), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 6), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 7), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 8), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 9), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 10), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 11), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 12), '0,0.[00]'), '--') }}</td>
					</tr>
					<tr>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 1)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 2)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 3)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 4)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 5)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 6)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 7)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 8)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 9)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 10)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 11)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 12)), '--')"></p></td>
					</tr>
					<tr>
						<td rowspan="2">對應指數</td>
						<td rowspan="2" class="text-left">
							<a>{{ $filters.defaultValue(bmName, '--') }}</a>
						</td>
						<td rowspan="2">{{ period }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 1), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 2), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 3), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 4), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 5), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 6), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 7), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 8), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 9), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 10), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 11), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 12), '0,0.[00]'), '--') }}</td>
					</tr>
					<tr>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 1)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 2)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 3)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 4)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 5)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 6)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 7)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 8)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 9)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 10)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 11)), '--')"></p></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 12)), '--')"></p></td>
					</tr>
					<tr>
						<td rowspan="2">同類型指數</td>
						<td rowspan="2" class="text-left">
							<a>{{ $filters.defaultValue(fundInfo && fundInfo.globalClassBmName, '--') }}</a>
						</td>
						<td rowspan="2">{{ period }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 1), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 2), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 3), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 4), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 5), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 6), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 7), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 8), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 9), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 10), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 11), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 12), '0,0.[00]'), '--') }}</td>
					</tr>
					<tr>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 1)), '--')"></p>
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 2)), '--')"></p>
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 3)), '--')"></p>
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 4)), '--')"></p>
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 5)), '--')"></p>
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 6)), '--')"></p>
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 7)), '--')"></p>
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 8)), '--')"></p>
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 9)), '--')"></p>
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 10)), '--')"></p>
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 11)), '--')"></p>
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 12)), '--')"></p>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import moment from 'moment';
export default {
	props: {
		techCurrencyCode: String,
		fundInfo: Object
	},
	data: function () {
		return {
			period: 60,
			beginDate: moment().subtract(60, 'months').format('YYYY/MM/DD'),
			endDate: moment().format('YYYY/MM/DD'),
			monthPcts: [],
			bmMonthPcts: [],
			globalClassBmMonthPcts: [],
			bmName: null
		};
	},
	watch: {
		fundInfo: {
			handler: async function (newVal, oldVal) {
				await this.getMonthPcts();
				await this.getBmMonthPcts();
				await this.getGlobalClassBmMonthPcts();
			}
		}
	},
	computed: {
		maxYear: function () {
			var years = _.map(_.orderBy(this.monthPcts, ['dataDate'], ['desc']), function (item) {
				return moment(item.dataDate, 'YYYY-MM-DD').format('Y');
			});
			return years ? years[0] : null;
		},
		minYear: function () {
			var years = _.map(_.orderBy(this.monthPcts, ['dataDate'], ['asc']), function (item) {
				return moment(item.dataDate, 'YYYY-MM-DD').format('Y');
			});
			return years ? years[0] : null;
		}
	},
	mounted: function () {},
	methods: {
		getMonthPcts: async function () {
			var self = this;
			const ret = await this.$api.getMonthPctsApi({
				fundCode: self.fundInfo.fundCode,
				beginDate: self.beginDate,
				endDate: self.endDate,
				techCurrencyCode: self.techCurrencyCode
			});
			self.monthPcts = ret.data;
		},
		getBmMonthPcts: async function () {
			var self = this;
			var managerBmCode = self.fundInfo.managerBmCode;
			var analysisBmCode = self.fundInfo.analysisBmCode;
			var bmCode;

			if (managerBmCode && managerBmCode != '11000006' && managerBmCode != '11000000') {
				self.bmName = self.fundInfo.managerBmName;
				bmCode = managerBmCode;
			} else if (analysisBmCode) {
				self.bmName = self.fundInfo.analysisBmName;
				bmCode = analysisBmCode;
			}
			const ret = await this.$api.getMonthPctsApi({
				fundCode: bmCode,
				beginDate: self.beginDate,
				endDate: self.endDate,
				techCurrencyCode: self.techCurrencyCode
			});
			self.bmMonthPcts = ret.data;
		},
		getGlobalClassBmMonthPcts: async function () {
			var self = this;
			const ret = await this.$api.getMonthPctsApi({
				fundCode: self.fundInfo.globalClassBmCode,
				beginDate: self.beginDate,
				endDate: self.endDate,
				techCurrencyCode: self.techCurrencyCode
			});
			self.globalClassBmMonthPcts = ret.data;
		},
		getMonthPctUpRate: function (pcts, month) {
			var regex = /^\d+(\.\d+)?$/;
			var monthPcts = this.getSameMonthPcts(pcts, month);
			var upCount = _.filter(monthPcts, function (item) {
				return regex.test(item.dvalue);
			});
			return monthPcts.length === this.period / 12 ? (upCount.length / monthPcts.length) * 100 : null;
		},
		getAvgMonthPctRate: function (pcts, month) {
			var monthPcts = this.getSameMonthPcts(pcts, month);
			return monthPcts.length === this.period / 12 ? _.sumBy(monthPcts, 'dvalue') / monthPcts.length : null;
		},
		getSameMonthPcts: function (pcts, month) {
			var monthPcts = _.filter(pcts, function (item) {
				return moment(item.dataDate, 'YYYY-MM-DD').format('M') == month;
			});
			monthPcts = _.orderBy(monthPcts, ['dataDate'], ['desc']);
			var sameMonthPcts = []; // Fetch latest row in same month.
			_.forEach(monthPcts, function (item) {
				var yearMonth = moment(item.dataDate, 'YYYY-MM-DD').format('YYYYMM');
				var existSameMonthPct = _.find(sameMonthPcts, function (item) {
					return yearMonth == moment(item.dataDate, 'YYYY-MM-DD').format('YYYYMM');
				});
				if (!existSameMonthPct) {
					sameMonthPcts.push(item);
				}
			});
			return sameMonthPcts;
		}
	}
};
</script>
