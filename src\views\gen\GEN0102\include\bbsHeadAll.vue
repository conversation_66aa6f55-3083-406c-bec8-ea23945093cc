<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form">
			<div class="card-header">
				<h4>查詢條件</h4>
				<span class="tx-square-bracket">為必填欄位</span>
			</div>
			<vue-form v-slot="{ errors, validate }" ref="queryForm">
				<div class="card-body collapse show" id="formsearch1">
					<form>
						<div class="form-row">
							<div class="form-group col-lg-4">
								<label class="form-label">訊息類別</label>
								<select class="form-select" v-model="msgCode" name="msgCode">
									<option selected :value="null">全部</option>
									<option :value="selMessage.msgCode" v-for="selMessage in selMessageMap">
										{{ selMessage.msgName }}
									</option>
								</select>
							</div>
							<div class="form-group col-lg-4">
								<label class="form-label">主分類</label>
								<select v-model="mainCatCode" name="mainCatCode" class="form-select">
									<option :value="null">全部</option>
									<option :value="item.catCode" v-for="item in selMsgMainCat">
										{{ item.catName }}
									</option>
								</select>
							</div>
							<div class="form-group col-lg-4">
								<label class="form-label">次分類</label>
								<select v-model="subCatCode" name="subCatCode" class="form-select" :disabled="!mainCatCode">
									<option :value="item.catCode" v-for="item in selMsgSubCat">
										{{ item.catName }}
									</option>
								</select>
							</div>
						</div>

						<div class="form-row">
							<div class="form-group col-lg-4">
								<label class="form-label">公告標題</label>
								<vue-field name="msgTitle" v-slot="{ field, errors }">
									<input v-bind="field" class="form-control" type="text" v-model="msgTitle" maxlength="20" />
									<span class="text-danger">{{ errors[0] }}</span>
								</vue-field>
							</div>
							<div class="form-group col-lg-4">
								<label class="form-label">是否過期</label>
								<div class="form-check form-check-inline">
									<vue-field
										type="radio"
										id="expiredYnAll"
										name="expiredYn"
										class="form-check-input"
										:value="null"
										v-model="expiredYn"
										label="不分"
									>
									</vue-field>
									<label class="form-check-label" for="expiredYnAll">不分</label>
								</div>
								<template v-for="option in selOptionYn" :key="option.codeValue">
									<div class="form-check form-check-inline">
										<vue-field
											:id="generateId(option.codeValue)"
											name="expiredYn"
											type="radio"
											class="form-check-input"
											:value="option.codeValue"
											v-model="expiredYn"
											label="是否逾期"
										>
										</vue-field>
										<label class="form-check-label" :for="generateId(option.codeValue)">
											{{ option.codeName }}
										</label>
									</div>
								</template>
							</div>

							<div class="form-group col-lg-4">
								<label class="form-label tx-require">公告日期</label>

								<vue-field
									type="date"
									id="validBgnDt"
									name="validBgnDt"
									v-model="validBgnDt"
									size="13"
									label="有效日期起"
									class="form-control mn-wd-30p"
									maxlength="10"
									rules="required"
									:class="{ 'is-invalid': showErrors && errors.validBgnDt }"
								>
								</vue-field>

								<span class="input-group-text">~</span>

								<vue-field
									type="date"
									id="validEndDt"
									name="validEndDt"
									v-model="validEndDt"
									size="13"
									label="有效日期迄"
									class="form-control mn-wd-30p"
									maxlength="10"
									rules="required"
									:min="minValidEndDt"
									:class="{ 'is-invalid': showErrors && errors.validEndDt }"
								>
								</vue-field>
							</div>
							<span class="text-danger" v-show="showErrors && (errors.validBgnDt || errors.validEndDt)">
								{{ errors.validBgnDt }} {{ errors.validEndDt }}
							</span>
						</div>

						<div class="form-footer">
							<button class="btn btn-primary btn-glow" type="button" @click.prevent="submitForm">查詢</button>
						</div>
					</form>
				</div>
			</vue-form>
		</div>
		<div id="searchResult" v-if="pageData.totalElements > 0">
			<div class="card card-table mb-3">
				<div class="card-header">
					<h4>查詢結果</h4>
					<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
				</div>
				<div class="table-responsive">
					<table class="table table-RWD table-bordered text-center">
						<thead>
							<tr>
								<th>重要性</th>
								<th>是否過期</th>
								<th>公告日期</th>
								<th>訊息類別</th>
								<th>主分類</th>
								<th>次分類</th>
								<th>公告標題</th>
								<th>建立者</th>
								<th>建立日期</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="item in pageData.selMsgList">
								<td
									:class="{ 'text-center tx-red bi-exclamation': item.importantYn == 'Y' }"
									data-th="重要性"
									style="font-size: 24px"
								></td>
								<td data-th="是否過期">{{ item.expiredYn === 'Y' ? '是' : '否' }}</td>
								<td data-th="公告日期">{{ item.validBgnDt }}<br />~{{ item.validEndDt }}</td>
								<td data-th="訊息類別">{{ item.msgName }}</td>
								<td data-th="主分類">{{ item.mainCatName }}</td>
								<td data-th="次分類">{{ item.subCatName }}</td>
								<td data-th="公告標題">
									<a href="javascript:void(0);" class="link-underline" @click="getView(item.msgId)">{{ item.msgTitle }}</a>
								</td>
								<td data-th="建立者">{{ item.createUser }}</td>
								<td data-th="建立日期">{{ item.createDt }}</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
	<!-- Modal -->
	<vue-modal :is-open="isOpenModal" :before-close="closeModal">
		<template v-slot:content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">公告訊息</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()"><i class="bi bi-arrows-fullscreen"></i></button>
						<button type="button" class="btn-close" @click.prevent="props.close()"></button>
					</div>
					<div class="modal-body">
						<table class="table table-bordered">
							<caption>
								公告分類
							</caption>
							<tbody>
								<tr>
									<th>訊息類別</th>
									<td>{{ selMsg.msgName }}</td>
								</tr>
							</tbody>
						</table>

						<table class="table table-bordered">
							<caption>
								公告內容
							</caption>
							<tbody>
								<tr>
									<th class="wd-15p">重要性</th>
									<td>{{ subTypeName(selMsg.importantYn) }}</td>
								</tr>
								<tr>
									<th>主分類</th>
									<td>{{ selMsg.mainCatName }}</td>
								</tr>
								<tr>
									<th>次分類</th>
									<td>{{ selMsg.subCatName }}</td>
								</tr>
								<tr>
									<th>有效日期</th>
									<td>起 {{ selMsg.validBgnDt }} ~ 迄 {{ selMsg.validEndDt }}</td>
								</tr>
								<tr>
									<th><span>公告標題</span></th>
									<td>{{ selMsg.msgTitle }}</td>
								</tr>
								<tr>
									<th>公告內容</th>
									<td>{{ selMsg.msgContent }}</td>
								</tr>
								<tr>
									<th>首頁是否顯示</th>
									<td>{{ subTypeName(selMsg.showYn) }}</td>
								</tr>
								<tr>
									<th>上傳檔案</th>
									<td>
										<ul class="list-group list-inline-tags" v-for="file in selMsg.fileList">
											<li class="list-group-item">
												<a href="#" @click="viewFile(file.msgFileId)">
													<span>{{ file.showName }}</span>
												</a>
											</li>
										</ul>
									</td>
								</tr>
								<tr>
									<th>連結</th>
									<td>
										<a :href="selMsg.favoriteLink" target="_blank">{{ selMsg.favoriteLink }}</a>
									</td>
								</tr>
							</tbody>
						</table>

						<table class="table table-bordered">
							<caption>
								維護資訊
							</caption>
							<tbody>
								<tr>
									<th>建立人員</th>
									<td>{{ selMsg.createUser }}</td>
								</tr>
								<tr>
									<th>建立人員分機</th>
									<td>{{ selMsg.createUserExt }}</td>
								</tr>
								<tr>
									<th>建立日期</th>
									<td>{{ selMsg.createDt }}</td>
								</tr>
								<tr>
									<th>最後維護人員</th>
									<td>{{ selMsg.modifyUser }}</td>
								</tr>
								<tr>
									<th>維護人員分機</th>
									<td>{{ selMsg.modifyUserExt }}</td>
								</tr>
								<tr>
									<th>最後維護日期</th>
									<td>{{ selMsg.modifyDt }}</td>
								</tr>
							</tbody>
						</table>
					</div>
					<div class="modal-footer">
						<button @click.prevent="props.close()" type="button" class="btn btn-white">關閉視窗</button>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
	<!-- Modal End -->
</template>
<script>
import vueModal from '@/views/components/model.vue';
import { Field, Form } from 'vee-validate';
import VuePagination from '@/views/components/pagination.vue';
import _ from 'lodash';
export default {
	components: {
		vueModal,
		'vue-form': Form,
		'vue-field': Field,
		VuePagination
	},
	props: {
		msgId: String
	},
	data: function () {
		return {
			selMessageMap: [], // 公告類別資料
			selMsgMainCat: [], // 主分類資料
			selMsgSubCat: [], // 次分類資料
			selOptionYn: [
				{ codeName: '是', codeValue: 'Y' },
				{ codeName: '否', codeValue: 'N' }
			],

			msgCode: null,
			mainCatCode: null,
			subCatCode: null,
			validBgnDt: null,
			validEndDt: null,
			msgTitle: null,

			showErrors: false,
			selImportentYn: [], // 重要性參數
			expiredYn: 'N',

			pageData: {
				selMsgList: [] //查詢結果
			},
			pageable: {
				page: 0,
				size: 20,
				sort: 'MSG_ID',
				direction: 'ASC'
			},
			isOpenModal: null,
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',
			buttonClass: 'btn-expand',

			selMsg: {} //公告明細資料
		};
	},
	computed: {
		minValidEndDt() {
			return this.validBgnDt ? this.validBgnDt : null;
		}
	},
	mounted: async function () {
		var self = this;
		$.when(
			self.getMessageMap(),
			(self.selMsgMainCat = await self.getMessageCat('M', null)),
			(self.selImportentYn = await self.getAdmCodeDetail('GM_IMPORTANT_YN')),
			(self.selOptionYn = await self.getAdmCodeDetail('OPTION_YN'))
		).then(function () {
			if (self.msgId) {
				self.getMessage(self.msgId);
			}
		});
	},
	watch: {
		mainCatCode: function (newVal, oldVal) {
			var self = this;
			if (newVal) {
				self.getMessageCat('S', newVal, self.selMsgSubCat);
			} else {
				self.selMsg.subCatCode = null;
			}
		},
		validBgnDt: function (newVal, oldVal) {
			this.showErrors = false;
			if (newVal && this.validEndDt && newVal > this.validEndDt) {
				this.validEndDt = null;
			}
		}
	},
	methods: {
		generateId(value) {
			return 'expiredYn_' + (value || 'default');
		},
		getAdmCodeDetail: async function (codeType) {
			var self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: codeType
			});
			return ret.data;
		},
		getMessageMap: async function (selMessageMap) {
			var self = this;
			const ret = await self.$api.getMessageMapApi({});
			if (ret.data) {
				self.selMessageMap = ret.data;
			}
		},
		getMessageCat: async function (catType, mainCatCode) {
			var self = this;
			var data = { catType: catType, mainCatCode: mainCatCode };
			let reqData = _.omitBy(data, (value) => _.isNil(value) || value === '');
			const ret = await self.$api.getGenMessageCat(reqData);
			return ret.data;
		},
		subTypeName: function (codeValue) {
			var self = this;
			if (!_.isBlank(self.selOptionYn) && !_.isBlank(codeValue)) {
				return _.find(self.selOptionYn, { codeValue: codeValue }).codeName;
			} else {
				return codeValue;
			}
		},
		submitForm() {
			this.showErrors = true;
			this.gotoPage(0);
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: function (_page) {
			var self = this;
			self.$refs.queryForm.validate().then(async function (pass) {
				if (pass.valid) {
					var page = _.isNumber(_page) ? _page : self.pageable.page;
					var url = '';
					url += '?page=' + page + '&size=' + self.pageable.size;
					url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;
					const ret = await self.$api.getBbsMgtPageData(
						{
							msgCode: self.msgCode,
							mainCatCode: self.mainCatCode,
							subCatCode: self.subCatCode,
							validBgnDt: _.formatDate(self.validBgnDt),
							validEndDt: _.formatDate(self.validEndDt),
							msgTitle: self.msgTitle,
							expiredYn: self.expiredYn
						},
						url
					);
					self.pageData = ret.data;
					self.pageData.selMsgList = self.pageData.content;
				}
			});
		},
		getView: async function (msgId) {
			var self = this;
			const ret = await self.$api.getGenMessageApi({
				msgId: msgId
			});
			if (ret.data) {
				self.selMsg = _.clone(ret.data);
				self.isOpenModal = true;
			}
		},
		changeModalSize: function () {
			var self = this;
			if (self.modalClass === 'modal-dialog modal-lg modal-dialog-centered') {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered fullscreen';
				self.buttonClass = 'btn-expand mini';
			} else {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered';
				self.buttonClass = 'btn-expand';
			}
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		viewFile: function (targetFileId) {
			var self = this;
			self.$api.downloadBbsHeadFileApi({ fileId: targetFileId });
		}
	}
};
</script>
