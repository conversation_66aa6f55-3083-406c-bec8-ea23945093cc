<template>
	<div class="modal-dialog modal-xl">
		<!--  海外股票-->
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">信託-海外股票</h4>
				<button type="button" class="btn-close" @click.prevent="close()" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="card card-prointro shadow-none mb-3">
					<div class="pro_left">
						<div class="pro_name">
							<div class="avatar avatar-md avatar-structure"></div>
							<h4>
								<span>商品名稱</span> <br />{{ proInfo.proName }} <br /><span class="tx-black">{{ proInfo.proEName }}</span>
							</h4>
						</div>
						<h4 class="pro_value">
							<span>最新淨值</span>
							<br /><span>{{ proInfo.aprice }}</span> <br /><span>{{ proInfo.priceDt }}</span>
						</h4>
						<h4 class="pro_value">
							<span>最新市價</span>
							<br /><span>{{ proInfo.sprice }}</span> <br /><span>{{ proInfo.priceDt }}</span>
						</h4>
					</div>
					<div class="pro_class">
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品代碼</span> <br />{{ proInfo.bankProCode }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6>
								<span>資產類別 <br /></span>{{ proInfo.assetcatName }}
							</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品主類</span> <br />{{ proInfo.pfcatName }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品次類</span><br />{{ proInfo.proTypeName }}</h6>
						</div>
					</div>
				</div>
				<div class="tab-nav-pills">
					<ul class="nav nav-pills justify-content-center" role="tablist">
						<li class="nav-item"><a class="nav-link active" href="#Sectionpfd1" data-bs-toggle="pill">商品基本資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionpfd2" data-bs-toggle="pill">商品共同資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionpfd3" data-bs-toggle="pill">商品附加資料</a></li>
					</ul>

					<div class="tab-content">
						<div class="tab-pane fade show active" id="Sectionpfd1">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>商品資訊</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>風險等級</th>
											<td class="wd-30p">{{ proInfo.pfdInfo.riskName }}</td>
											<th>計價幣別</th>
											<td class="wd-30p">{{ proInfo.pfdInfo.curCode }}</td>
										</tr>
										<tr>
											<th>是否可銷售</th>
											<td>{{ proInfo.pfdInfo.buyYn }}</td>
											<th>交易單位</th>
											<td>{{ proInfo.pfdInfo.txUnit }}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionpfd2">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>銷售相關資料</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>銷售地區</th>
											<td v-if="proInfo.allYn == 'Y'">全行</td>
											<td v-else></td>
											<th>限PI申購</th>
											<td>{{ proInfo.profInvestorYn }}</td>
										</tr>
										<tr>
											<th>是否開放申購</th>
											<td>{{ proInfo.buyYn }}</td>
											<th>是否開放贖回</th>
											<td>{{ proInfo.sellYn }}</td>
										</tr>
										<tr>
											<th><span>保本要求</span></th>
											<td>
												<span v-if="actionType !== 'EDIT'">{{ proInfo.principalGuarYn }}</span>
												<span v-else>
													<div v-for="item in principalGuarMenu" class="form-check form-check-inline">
														<input
															class="form-check-input"
															:id="'principalGuar' + item.codeValue"
															v-model="proInfo.principalGuarYn"
															type="radio"
															:value="item.codeValue"
															name="fastCode"
														/>
														<label class="form-check-label" :for="'principalGuar' + item.codeValue">{{
															$filters.defaultValue(item.codeName, '--')
														}}</label>
													</div>
												</span>
											</td>
											<th>配息率</th>
											<td>
												<template v-if="actionType !== 'EDIT' && proInfo.intRate">
													{{ proInfo.intRate * 100 }}%<span v-if="proInfo.intRate * 100 > 5">(高配息率)</span
													><span v-else>(一般配息率)</span>
												</template>
												<template v-if="actionType === 'EDIT'">
													<input
														class="form-control"
														id="intRate"
														maxlength="5"
														name="intRate"
														type="text"
														v-model="intRateEdit"
													/>%
												</template>
											</td>
										</tr>
										<tr>
											<th><span>理財需求</span></th>
											<td colspan="3">
												<div class="form-check form-check-inline" v-for="(item, index) in finReqCodeMenu">
													<input
														class="form-check-input"
														name="finReqCodes"
														:disabled="actionType == 'EDIT' ? false : true"
														:id="'finReqCodes_' + index"
														v-model="finReqCodes"
														:value="item.codeValue"
														type="checkbox"
													/>
													<label class="form-check-label" :for="'finReqCodes_' + index">{{ item.codeName }}</label>
												</div>
											</td>
										</tr>
										<tr>
											<th>商品標籤</th>
											<td colspan="3" disabled="disabled">
												{{ proInfo.selprocatNames }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionpfd3">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>其他設定</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>備註</span></th>
											<td>
												<textarea
													class="form-control"
													cols="80"
													rows="4"
													size="200"
													maxlength="200"
													v-model="proInfo.memo"
													:readonly="actionType !== 'EDIT'"
												></textarea>
												<div class="tx-note" v-if="proInfo.memo">{{ 200 - proInfo.memo.length }} 個字可輸入</div>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>商品說明書</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_A"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['A']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="pfdUploadFileA"
																@change="triggerFile($event, 'A')"
																accept=".xlsx,.xls"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('A', url['A'])">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['A'] && uploadFiles['A'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['A'].url" target="_blank">{{ uploadFiles['A'].url }}</a
													><br v-if="uploadFiles['A']" />
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['A'] && uploadFiles['A'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['A'])"
														>{{ uploadFiles['A'].showName }}</span
													>
													<span
														v-show="uploadFiles['A'] && uploadFiles['A'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('A', uploadFiles['A'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
										<tr>
											<th>投資人須知</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_D"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['D']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="pfdUploadFileD"
																@change="triggerFile($event, 'D')"
																accept=".xlsx,.xls"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('D', url['D'])">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['D'] && uploadFiles['D'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['D'].url" target="_blank">{{ uploadFiles['D'].url }}</a
													><br v-if="uploadFiles['D']" />
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['D'] && uploadFiles['D'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['D'])"
														>{{ uploadFiles['D'].showName }}</span
													>
													<span
														v-show="uploadFiles['D'] && uploadFiles['D'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('D', uploadFiles['D'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
										<tr>
											<th>DM</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_F"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['F']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="pfdUploadFileF"
																@change="triggerFile($event, 'F')"
																accept=".xlsx,.xls"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('F', url['F'])">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['F'] && uploadFiles['F'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['F'].url" target="_blank">{{ uploadFiles['F'].url }}</a
													><br v-if="uploadFiles['F']" />
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['F'] && uploadFiles['F'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['F'])"
														>{{ uploadFiles['F'].showName }}</span
													>
													<span
														v-show="uploadFiles['F'] && uploadFiles['F'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('F', uploadFiles['F'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
										<tr>
											<th>其他</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_G"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['G']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="pfdUploadFileG"
																@change="triggerFile($event, 'G')"
																accept=".xlsx,.xls"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('G', url['G'])">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['G'] && uploadFiles['G'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['G'].url" target="_blank">{{ uploadFiles['G'].url }}</a
													><br v-if="uploadFiles['G']" />
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['G'] && uploadFiles['G'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['G'])"
														>{{ uploadFiles['G'].showName }}</span
													>
													<span
														v-show="uploadFiles['G'] && uploadFiles['G'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('G', uploadFiles['G'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>其他相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tr>
										<td>
											<span v-for="(item, index) in otherFileList">
												<a
													v-if="index === otherFileList.length - 1"
													v-show="item.show"
													href="#"
													class="tx-link"
													@click="downloadOtherFile(item.docFileId)"
													>{{ $filters.defaultValue(item.showName, '--') }}</a
												>
												<a v-else href="#" class="tx-link" v-show="item.show" @click="downloadOtherFile(item.docFileId)"
													>{{ $filters.defaultValue(item.showName, '--') }}、</a
												>
											</span>
										</td>
									</tr>
								</table>
							</div>
							<div class="tx-note">其他相關附件為文件管理內所上傳與本商品相關的文件。</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<input id="modalCloseButton" type="button" @click.prevent="close()" class="btn btn-white" value="關閉" />
				<input
					type="button"
					class="btn btn-primary"
					value="傳送主管審核"
					v-if="actionType == 'EDIT' && config.btnEdit"
					@click="
						updateProduct();
						close();
					"
				/>
			</div>
		</div>
	</div>
	<!-- Modal 2 End -->
</template>
<script>
import moment from 'moment';
import _ from 'lodash';
export default {
	props: {
		finReqCodeMenu: Array,
		close: Function
	},
	data: function () {
		const now = moment();
		return {
			proInfo: {
				pfdInfo: {},
				pfdInfo: {},
				bondInfo: {},
				pfdInfo: {},
				spInfo: {},
				insInfo: {},
				dciInfo: {},
				secInfo: {}
			},
			finReqCodes: [],
			uploadFiles: [],
			actionType: '',
			otherFileList: []
		};
	},
	watch: {},
	mounted: function () {},
	methods: {
		getProInfo: async function (bankProCode, pfcatCode) {
			var self = this;
			const ret = await this.$api.getProductInfoApi({
				proCode: bankProCode,
				pfcatCode: pfcatCode
			});
			if (_.isEmpty(ret.data)) {
				ret.data = {};
				this.$bi.alert('資料不存在');
				return;
			}
			if (_.isEmpty(ret.data.pfdInfo)) {
				ret.data.pfdInfo = {};
			}

			self.proInfo = ret.data;
			this.$forceUpdate();

			var selectYnList = [];
			const ret2 = await this.$api.getAdmCodeDetail({
				codeType: 'SELECT_YN'
			});
			selectYnList = ret2.data;
			if (!_.isUndefined(self.proInfo.pfdInfo.buyYn)) {
				var buyYnObjs = _.filter(selectYnList, {
					codeValue: self.proInfo.pfdInfo.buyYn
				});
				self.proInfo.pfdInfo.buyYn = buyYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.buyYn)) {
				var buyYnObjs = _.filter(selectYnList, {
					codeValue: self.proInfo.buyYn
				});
				self.proInfo.buyYn = buyYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.sellYn)) {
				var sellYnObjs = _.filter(selectYnList, {
					codeValue: self.proInfo.sellYn
				});
				self.proInfo.sellYn = sellYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.profInvestorYn)) {
				var profInvestorYnObjs = _.filter(selectYnList, {
					codeValue: self.proInfo.profInvestorYn
				});
				self.proInfo.profInvestorYn = profInvestorYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.principalGuarYn)) {
				var principalGuarYnList = [];
				const ret = await this.$api.getAdmCodeDetail({
					codeType: 'GUAR_YN'
				});
				principalGuarYnList = ret.data;
				var principalGuarYnObjs = _.filter(principalGuarYnList, {
					codeValue: self.proInfo.principalGuarYn
				});
				self.proInfo.principalGuarYn = principalGuarYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.finReqCode)) {
				self.finReqCodes = self.proInfo.finReqCode.split(',');
			}

			if (!_.isUndefined(self.proInfo.selprocatNames)) {
				var selprocatNames = self.proInfo.selprocatNames.replaceAll(',', '、');
				self.proInfo.selprocatNames = selprocatNames;
			}

			this.$forceUpdate();
		}
	} // methods end
};
</script>
