<template>
	<div>
		<!-- Modal 4 常用句設定 start -->
		<div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
			<div class="modal-content">
				<div class="modal-header">
					<h4 class="modal-title">常用句設定</h4>
					<button type="button" class="btn-close" @click.prevent="close()" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<div id="reuserWordMsg" v-show="showMsg">
						<div id="reuseWordAlert" class="alert alert-success alert-dismissible" role="alert">
							<span class="ico-ok"></span> 儲存成功
							<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
						</div>
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-bordered table-striped">
							<thead>
								<tr>
									<th width="8%" class="text-center">序號</th>
									<th>常用句</th>
								</tr>
							</thead>
							<tbody v-if="wobReuseWords && wobReuseWords.length > 0">
								<tr v-for="(item, index) in wobReuseWords" :key="index">
									<td data-th="序號" class="text-center">{{ index + 1 }}</td>
									<td data-th="常用句">
										<input
											:name="'wobReuseWords[' + index + '].words'"
											class="form-control"
											type="text"
											size="20"
											maxlength="20"
											v-model="item.words"
										/>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="modal-footer" id="reuseWordSettingFooter">
					<input
						class="btn btn-white"
						type="button"
						value="關閉"
						@click.prevent="
							close();
							showSuperModalName();
						"
					/>
					<input class="btn btn-primary" id="save3" type="button" value="儲存" @click="updateReuseWords()" />
				</div>
			</div>
		</div>
		<!-- Modal 4 End -->
	</div>
</template>
<script>
import _ from 'lodash';

export default {
	props: {
		close: Function,
		id: String,
		wobReuseWords: Array,
		superModalName: String
	},
	data: function () {
		return {
			showMsg: false,
			//常用句機制
			reuseWord: null,
			newReuseWord: null,
			isOpenModal: null
		};
	},
	mounted: function () {
		var self = this;
		self.getReuseWords();
	},
	methods: {
		openModal: function () {
			this.isOpenModal = true;
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		getReuseWords: async function () {
			var self = this;
			const ret = await self.$api.getReuseWordsApi();
			ret.data.forEach(function (item) {
				var index = item.wordsId - 1;
				if (self.wobReuseWords[index]) {
					self.wobReuseWords[index].words = item.words;
				}
			});
		},
		updateReuseWords: async function () {
			var self = this;
			var reuseWordsUpdateReq = [];
			for (var i = 0; i < 10; i++) {
				var wordObj = {};
				if (!_.isBlank(self.wobReuseWords[i].words)) {
					wordObj.wordsId = i + 1;
					wordObj.words = self.wobReuseWords[i].words;
					reuseWordsUpdateReq.push(wordObj);
				}
			}

			const ret = await self.$api.updateReuseWordsApi(reuseWordsUpdateReq);
			self.getReuseWords();

			self.showMsg = true;
		},
		showSuperModalName: function () {
			var self = this;
			if (self.superModalName) {
				var superModal = new bootstrap.Modal(document.getElementById(self.superModalName));
				superModal.show();
			}
		}
	}
};
</script>
