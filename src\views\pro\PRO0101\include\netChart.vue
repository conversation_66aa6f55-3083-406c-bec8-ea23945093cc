<template>
	<div :ref="chartId" :id="chartId" style="height: 500px"></div>
</template>
<script>
import _ from 'lodash';
import * as am5 from '@amcharts/amcharts5';
import * as am5xy from '@amcharts/amcharts5/xy';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import { toRaw } from 'vue';
export default {
	props: {
		chartId: String,
		proPriceRangeMenu: Array
	},
	data: function () {
		return {
			chartData: [],
			am5Obj: {}
		};
	},
	watch: {},
	mounted: function () {},
	methods: {
		initChart: function () {
			var self = this;

			let { chartData, am5Obj } = self;
			let firstLoad = false;

			let { series, xAxis, yAxis, root, chart } = toRaw(am5Obj);

			if (root) {
				// 如果 root 存在，先銷毀現有的圖表和 root
				root.dispose();
				am5Obj.root = null; // 確保 root 重新建立
			}

			// Create root element
			// https://www.amcharts.com/docs/v5/getting-started/#Root_element
			root = am5.Root.new(self.chartId);
			root._logo.dispose();

			// Set themes
			// https://www.amcharts.com/docs/v5/concepts/themes/
			root.setThemes([am5themes_Animated.new(root)]);

			// Create chart
			// https://www.amcharts.com/docs/v5/charts/xy-chart/
			chart = root.container.children.push(
				am5xy.XYChart.new(root, {
					panX: true,
					panY: true,
					wheelX: 'panX',
					wheelY: 'zoomX',
					layout: root.verticalLayout
				})
			);

			// Create axes X軸
			// https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
			xAxis = chart.xAxes.push(
				am5xy.DateAxis.new(root, {
					maxDeviation: 0.1,
					groupData: false,
					baseInterval: {
						timeUnit: 'day',
						count: 1
					},
					renderer: am5xy.AxisRendererX.new(root, {
						minGridDistance: 50
					}),
					tooltip: am5.Tooltip.new(root, {})
				})
			);

			// Y軸
			yAxis = chart.yAxes.push(
				am5xy.ValueAxis.new(root, {
					maxDeviation: 0.1,
					renderer: am5xy.AxisRendererY.new(root, {})
				})
			);

			var cursor = chart.set(
				'cursor',
				am5xy.XYCursor.new(root, {
					xAxis: xAxis
				})
			);
			cursor.lineY.set('visible', false);

			// add scrollbar
			chart.set(
				'scrollbarX',
				am5.Scrollbar.new(root, {
					orientation: 'horizontal'
				})
			);

			series = chart.series.push(
				am5xy.LineSeries.new(root, {
					name: chartData[0]?.name || '',
					xAxis: xAxis,
					yAxis: yAxis,
					valueYField: 'value',
					valueXField: 'date',
					tooltip: am5.Tooltip.new(root, {
						pointerOrientation: 'horizontal',
						labelText: '{valueY}'
					})
				})
			);

			if (!_.isEmpty(chartData)) {
				series.data.setAll(chartData);
			}

			series.bullets.push(function () {
				var bulletCircle = am5.Circle.new(root, {
					radius: 5,
					fill: series.get('fill')
				});
				return am5.Bullet.new(root, {
					sprite: bulletCircle
				});
			});

			// 設定圖表和 series 的出現動畫
			series.appear(1000, 100);
			chart.appear(1000, 100);

			// 更新 am5Obj
			Object.assign(am5Obj, {
				series,
				xAxis,
				yAxis,
				root,
				chart
			});
		},
		getNets: async function (proCode, rangeType, rangeFixed) {
			console.log('getNets', proCode);
			var self = this;
			var proCodeArray = [proCode];
			var data = [];
			const ret = await this.$api.getPriceAnaApi({
				proCodes: proCodeArray,
				freqType: rangeType,
				freqFixed: rangeFixed
			});

			if (!_.isNil(ret.data) && !_.isNil(ret.data.priceHist)) {
				ret.data.priceHist.forEach((d) => {
					var lineData = { date: Date.parse(d.priceDt), value: d.aprice };
					data.push(lineData);
				});
				self.chartData = data;
				console.log('data', self.chartData);
			}
			self.initChart();
		}
	} // methods end
};
</script>
