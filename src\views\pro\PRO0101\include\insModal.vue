<template>
	<!-- Modal 2 保險-->
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">人身保險</h4>
				<button type="button" class="btn-close" @click.prevent="close()" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="card card-prointro shadow-none mb-3">
					<div class="pro_left">
						<div class="pro_name">
							<div class="avatar avatar-md avatar-insurance"></div>
							<h4>
								<span>商品名稱</span> <br />{{ $filters.defaultValue(proInfo.proName, '--') }} <br /><span class="tx-black">{{
									$filters.defaultValue(proInfo.proEName, '--')
								}}</span>
							</h4>
						</div>
					</div>
					<div class="pro_class">
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6>
								<span>險種代碼</span>
								<br />{{ $filters.defaultValue(proInfo.bankProCode, '--') }}
							</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>資產類別</span><br />{{ $filters.defaultValue(proInfo.assetcatName, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品主類</span><br />{{ $filters.defaultValue(proInfo.pfcatName, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品次類</span><br />{{ $filters.defaultValue(proInfo.proTypeName, '--') }}</h6>
						</div>
					</div>
				</div>

				<div class="tab-nav-pills">
					<ul class="nav nav-pills justify-content-center" role="tablist">
						<li class="nav-item"><a class="nav-link active" href="#Sectionins1" data-bs-toggle="pill">商品基本資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionins2" data-bs-toggle="pill">商品共同資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionins3" data-bs-toggle="pill">商品附加資料</a></li>
						<!--                <li class="nav-item"><a class="nav-link" href="#Sectionins4" data-bs-toggle="pill">投資標的資訊</a></li>-->
					</ul>
					<div class="tab-content">
						<div class="tab-pane fade show active" id="Sectionins1">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>保險商品資訊</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>險種代碼</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.bankProCode, '--') }}
											</td>
											<th>保險商品類別</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.insInfo.insType, '--') }}
											</td>
										</tr>
										<tr>
											<th>保險公司名稱</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.insInfo.inscmpName, '--') }}
											</td>
											<th>主/附約</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.insInfo.baseRider === 'Y' ? '主約' : '附約', '--') }}
											</td>
										</tr>
										<tr>
											<th>商品幣別</th>
											<td class="wd-30p">{{ $filters.defaultValue(proInfo.curCode, '--') }}</td>
											<th>商品風險等級</th>
											<td class="wd-30p">
												<span v-if="proInfo.riskCode === '1'">RR1</span>
												<span v-else-if="proInfo.riskCode === '2'">RR2</span>
												<span v-else-if="proInfo.riskCode === '3'">RR3</span>
												<span v-else-if="proInfo.riskCode === '4'">RR4</span>
												<span v-else-if="proInfo.riskCode === '5'">RR5</span>
											</td>
										</tr>
										<tr>
											<th>繳費年期類型（首期）</th>
											<td>{{ $filters.defaultValue(proInfo.insInfo.payTermName, '--') }}</td>
											<th>繳費年期類型(數值)</th>
											<td>{{ $filters.formatNumber(proInfo.insInfo.payTerm, '--') }}</td>
										</tr>
										<tr>
											<th>保障年期類型</th>
											<td>{{ $filters.defaultValue(proInfo.insInfo.insTermName, '--') }}</td>
											<th>保障年期類型(數值)</th>
											<td>{{ $filters.formatNumber(proInfo.insInfo.insTerm, '--') }}</td>
										</tr>
										<tr>
											<th>承保年齡(起)</th>
											<td>{{ $filters.formatNumber(proInfo.insInfo.ageMin, '--') }}</td>
											<th>承保年齡(迄)</th>
											<td>{{ $filters.formatNumber(proInfo.insInfo.ageMax, '--') }}</td>
										</tr>
										<tr>
											<th>保險商品年期</th>
											<td class="wd-30p">{{ $filters.formatNumber(proInfo.insInfo.insTerm, '--') }}年</td>
											<th>商品期間</th>
											<td class="wd-30p">{{ $filters.formatNumber(proInfo.insInfo.payTerm, '--') }}年</td>
										</tr>
										<tr>
											<th>銷售起始日</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.insInfo.stdDt, '--') }}
											</td>
											<th>銷售結束日</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.insInfo.endDt, '--') }}
											</td>
										</tr>
										<tr>
											<th>商品屬性</th>
											<td class="wd-30p">
												<span v-if="proInfo.protypeCode === 'I01'">儲蓄</span>
												<span v-else-if="proInfo.protypeCode === 'I02'">保障</span>
												<span v-else-if="proInfo.protypeCode === 'I03'">投資</span>
												<span v-else-if="proInfo.protypeCode === 'I04'">房貸</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div class="tab-pane fade" id="Sectionins2">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>銷售相關資料</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th width="20%">銷售地區</th>
											<td width="30%" v-if="proInfo.allYn == 'Y'">全行</td>
											<td width="30%" v-else>--</td>
											<th width="20%">限PI申購</th>
											<td width="30%">
												{{ $filters.defaultValue(proInfo.profInvestorYn, '--') }}
											</td>
										</tr>
										<tr>
											<th width="20%">銷售對象</th>
											<td width="30%" colspan="3">
												{{ $filters.defaultValue(proInfo.targetCusBu, '--') }}
											</td>
										</tr>
										<tr>
											<th><span>理財需求</span></th>
											<td colspan="3">
												<div class="form-check form-check-inline" v-for="item in finReqCodeMenu">
													<input
														class="form-check-input"
														name="finReqCodes"
														disabled
														id="c1"
														v-model="finReqCodes"
														:value="item.codeValue"
														type="checkbox"
													/>
													<label class="form-check-label" for="c1">{{ $filters.defaultValue(item.codeName, '--') }}</label>
												</div>
											</td>
										</tr>
										<tr>
											<th>商品標籤</th>
											<td disabled="disabled" colspan="3">
												{{ $filters.defaultValue(proInfo.selprocatNames, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div class="tab-pane fade" id="Sectionins3">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>其他設定</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>商品投資標的</span></th>
											<td class="wd-80p">
												<span>{{ $filters.defaultValue(proInfo.sectorCode, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>商品投資地區</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.geoFocusCode, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>比較基準設定</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.benchmarkCode, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>商品簡介</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.memo, '--') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>保單條款</th>
											<td class="wd-80p">
												<a v-if="proFileC && proFileC.url" :href="proFileC.url" target="_blank">{{
													$filters.defaultValue(proFileC.url, '--')
												}}</a
												><br v-if="proFileC && proFileC.url" />
												<a v-if="proFileC" class="tx-link" href="#" @click="downloadFile(proFileC)">{{
													$filters.defaultValue(proFileC.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>DM</th>
											<td class="wd-80p">
												<a v-if="proFileF && proFileF.url" :href="proFileF.url" target="_blank">{{
													$filters.defaultValue(proFileF.url, '--')
												}}</a
												><br v-if="proFileF && proFileF.url" />
												<a v-if="proFileF" class="tx-link" href="#" @click="downloadFile(proFileF)">{{
													$filters.defaultValue(proFileF.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>其他</th>
											<td class="wd-80p">
												<a v-if="proFileG && proFileG.url" :href="proFileG.url" target="_blank">{{
													$filters.defaultValue(proFileG.url, '--')
												}}</a
												><br v-if="proFileG && proFileG.url" />
												<a v-if="proFileG" class="tx-link" href="#" @click="downloadFile(proFileG)">{{
													$filters.defaultValue(proFileG.showName, '--')
												}}</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>其他相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<td>
												<span v-for="(item, index) in otherFileList">
													<a
														v-if="index === otherFileList.length - 1"
														v-show="item.show"
														href="#"
														class="tx-link"
														@click="downloadOtherFile(item.docFileId)"
														>{{ $filters.defaultValue(item.showName, '--') }}</a
													>
													<a v-else href="#" class="tx-link" v-show="item.show" @click="downloadOtherFile(item.docFileId)"
														>{{ $filters.defaultValue(item.showName, '--') }}、</a
													>
												</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="tx-note">其他相關附件為文件管理內所上傳與本商品相關的文件。</div>
						</div>

						<div class="tab-pane fade" id="Sectionins4">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>投資標的資訊</h4>
								</div>
								<table class="table table-RWD table-bordered" id="invTrgTbl">
									<thead>
										<tr>
											<th width="15%">Lipper Code</th>
											<th width="15%">銀行標的代碼</th>
											<th width="15%">保險公司商品代碼</th>
											<th width="35%">標的名稱</th>
											<th width="10%">國內基金</th>
											<th width="10%">風險屬性</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="item in insInvTargetList">
											<td data-th="Lipper Code">
												{{ $filters.defaultValue(item.lipperId, '--') }}
											</td>
											<td data-th="銀行標的代碼">{{ $filters.defaultValue(item.invtgtCode, '--') }}</td>
											<td data-th="保險公司商品代碼">
												{{ $filters.defaultValue(item.inscmpProCode, '--') }}
											</td>
											<td data-th="標的名稱">{{ $filters.defaultValue(item.invTgtName, '--') }}</td>
											<td data-th="國內基金">
												<span v-if="item.localYn == 'Y'">境內</span>
												<span v-else>境外</span>
											</td>
											<td data-th="風險屬性">{{ $filters.defaultValue(item.riskName, '--') }}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="modal-footer">
				<button @click.prevent="close()" type="button" class="btn btn-white">關閉視窗</button>
			</div>
		</div>
	</div>
	<!-- Modal 2 End -->
</template>
<script>
import _ from 'lodash';
import moment from 'moment';
import pagination from '@/views/components/pagination.vue';

export default {
	components: {
		'vue-pagination': pagination
	},
	props: {
		downloadFile: Function,
		downloadOtherFile: Function,
		finReqCodeMenu: Array,
		close: Function
	},
	data: function () {
		const now = moment();
		return {
			proInfo: {
				fundInfo: {},
				etfInfo: {},
				bondInfo: {},
				spInfo: {},
				insInfo: {},
				dciInfo: {},
				secInfo: {}
			},
			finReqCodes: [],
			proFileC: {},
			proFileF: {},
			proFileG: {},
			otherFileList: [],
			insInvTargetList: []
		};
	},
	watch: {},
	mounted: function () {},
	methods: {
		async getProInfo(bankProCode, pfcatCode) {
			// 獲取商品基本資料
			const productInfo = await this.$api.getProductInfoApi({
				proCode: bankProCode,
				pfcatCode: pfcatCode
			});

			if (_.isNil(productInfo.data)) {
				productInfo.data = {};
				this.$bi.alert('資料不存在');
				return;
			}
			if (_.isNil(productInfo.data.insInfo)) {
				productInfo.data.insInfo = {};
			}

			this.proInfo = productInfo.data;

			// 獲取 SELECT_YN 代碼
			const selectYnList = await this.$api.getAdmCodeDetail({
				codeType: 'SELECT_YN'
			});

			if (!_.isEmpty(selectYnList.data)) {
				if (!_.isUndefined(this.proInfo.profInvestorYn)) {
					const profInvestorYnObjs = _.filter(selectYnList.data, {
						codeValue: this.proInfo.profInvestorYn
					});
					this.proInfo.profInvestorYn = profInvestorYnObjs[0].codeName;
				}
			}

			// 獲取 CUS_BU 代碼
			if (!_.isUndefined(this.proInfo.targetCusBu)) {
				const targetCusBuList = await this.$api.getAdmCodeDetail({
					codeType: 'CUS_BU'
				});
				const targetCusBuObjs = _.filter(targetCusBuList.data, {
					codeValue: this.proInfo.targetCusBu
				});
				this.proInfo.targetCusBu = targetCusBuObjs[0].codeName;
			}

			if (!_.isUndefined(this.proInfo.selprocatNames)) {
				const selprocatNames = this.proInfo.selprocatNames.replaceAll(',', '、');
				this.proInfo.selprocatNames = selprocatNames;
			}

			if (!_.isUndefined(this.proInfo.finReqCode)) {
				this.finReqCodes = this.proInfo.finReqCode.split(',');
			}

			// 獲取商品附加資料
			const productsCommInfo = await this.$api.getProductsCommInfo({
				proCode: bankProCode,
				pfcatCode: pfcatCode
			});

			if (!_.isNil(productsCommInfo.data)) {
				if (productsCommInfo.data.proDocs) {
					this.otherFileList = productsCommInfo.data.proDocs;
					this.otherFileList.forEach((item) => {
						item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
					});
				}

				const proFileList = productsCommInfo.data.proFiles;
				if (!_.isNil(proFileList)) {
					this.proFileC = proFileList.filter((proFile) => proFile.fileType === 'C')[0];
					this.proFileF = proFileList.filter((proFile) => proFile.fileType === 'F')[0];
					this.proFileG = proFileList.filter((proFile) => proFile.fileType === 'G')[0];
				}
			}
		}
	} // methods end
};
</script>
