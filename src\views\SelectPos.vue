<template>
	<div class="container-body">
		<div class="container">
			<div class="logo">
				<img src="../assets/images/logo/logo-ifa.svg" alt="" />
			</div>

			<h2>使用者選擇</h2>

			<div class="input-group">
				<label>使用者</label>
				<input :value="userName" readonly />
			</div>
			<div class="input-group">
				<label for="pos-select">選擇角色</label>
				<select id="pos-select" class="form-select wd-100p" v-model="selectedPosCode">
					<option v-for="(item, i) in userPositions" :key="i" :value="item.posCode">{{ item.posName }}</option>
				</select>
			</div>
			<button class="btn btn-primary" type="button" @click="login">登入</button>
		</div>
	</div>
</template>

<script>
import { getToken, setToken } from '@/utils/auth.js';
import axios from 'axios';
// import userCodeComplement from '../utils/mixin/userCodeComplement.js';

export default {
	// mixins: [userCodeComplement],
	data: function () {
		return {
			userName: '王大明',
			jwtToken: '',
			// userCode: null, //[[${userCode ?: null}]],
			// valid: false, //[[${valid ?: false}]],
			// isUserAccountSwitch: false, //[[${isUserAccountSwitch ?: false}]],
			// pwd: null,
			// userDeputies: null,
			userPositions: [],
			// selectedUserCode: null,
			selectedPosCode: null
		};
	},
	created() {
		this.getUserRoles();
	},
	// mounted: function () {
	// 	if (this.userCode && this.valid) {
	// 		this.getUserDeputies();
	// 	}
	// },
	watch: {
		// selectedUserCode: function (newVal, oldVal) {
		// 	if (newVal) {
		// 		this.getUserPositions();
		// 	}
		// },
		// valid: function (newVal, oldVal) {
		// 	if (newVal) {
		// 		this.getUserDeputies();
		// 	} else {
		// 		this.pwd = null;
		// 		this.userDeputies = null;
		// 		this.userPositions = null;
		// 		this.selectedUserCode = null;
		// 		this.selectedPosCode = null;
		// 	}
		// }
	},
	methods: {
		getUserRoles: async function () {
			let res = await this.$api.getUserRolesApi();
			this.userPositions = res.data;
			this.selectedPosCode = this.userPositions[0].posCode;
		},
		login: async function () {
			let ret = await this.$api.authenticate(this.selectedPosCode);

			if (ret.data.accessToken && ret.data.refreshToken) {
				setToken('accessToken', ret.data.accessToken);
				setToken('refreshToken', ret.data.refreshToken);
				this.$router.push('/');
			} else {
				this.$swal.fire({
					title: 'error',
					text: '此帳號或密碼錯誤！',
					icon: 'error'
				});
			}
		}
	}
};
</script>

<style scoped>
@import '../assets/css/bi/login.css';
</style>
