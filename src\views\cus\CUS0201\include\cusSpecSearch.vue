<template>
	<div>
		<div class="card card-form">
			<div class="card-header">
				<h4>特殊條件查詢</h4>
			</div>
			<div class="card-body">
				<div class="row g-2 align-items-end">
					<div class="col-md-12 bg-ligntblue p-3">
						<label class="form-label d-flex justify-content-between"
							>重點客戶群組查詢：<a class="tx-link" id="cusGroupLink" @click="next()">編輯重點客戶</a></label
						>
						<vue-form v-slot="{ errors }" ref="groupCode">
							<div class="input-group">
								<vue-field
									as="select"
									name="groupCode"
									class="form-select"
									:class="{ 'is-invalid': errors.groupCode }"
									id="groupCode"
									v-model="groupCode"
									rules="required"
									label="重點顧客"
								>
									<option value="" disabled>請選擇</option>
									<option value="ALL">全部群組顧客</option>
									<option value="NONE">未分類群組顧客</option>
									<option v-for="cusGroup in cusGroupMenu" :value="cusGroup.groupCode">{{ cusGroup.groupName }}</option>
								</vue-field>
								<button
									class="btn btn-primary btn-glow JQ-singleSearch"
									type="button"
									@click.prevent="queryByGroup()"
									data-rel="byGroupCode"
								>
									<i class="bi bi-search"></i>
								</button>
							</div>
							<div style="height: 25px">
								<span class="text-danger" v-show="errors.groupCode">{{ errors.groupCode }}</span>
							</div>
						</vue-form>
					</div>
					<div class="divider"></div>
					<div class="col-md-12 bg-ligntblue p-3">
						<vue-form v-slot="{ errors }" ref="balanceWatch">
							<label class="form-label d-flex justify-content-between">持有商品市值(折合台幣)查詢：</label>
							<label class="form-label">商品餘額查詢</label><br />
							<div class="form-check form-check-inline" v-for="productBalance in productBalanceMenu">
								<vue-field
									class="form-check-input"
									name="balanceItems"
									id="balanceItems"
									type="checkbox"
									v-model="balanceItems"
									:value="productBalance.codeValue"
									rules="required"
									label="商品"
									:class="{ 'is-invalid': errors.balanceItems }"
									data-vv-scope="balanceWatch"
								></vue-field>
								<label class="form-check-label">{{ productBalance.codeName }}</label>
							</div>
							<div class="row justify-content-between">
								<div class="col-10">
									<div class="input-group">
										<!--									<select class="form-select" v-model="curCode" rules="required" :class="{'is-invalid': errors.balanceItems}" label="查詢幣別" data-vv-scope="balanceWatch">-->
										<!--										<option value="ALL" selected="selected">全部折台</option>-->
										<!--										<option v-for="item in currenciesMenu" :value="item.value">{{item.name}}</option>-->
										<!--									</select>-->
										<vue-field
											name="balanceBegin"
											class="form-control text-end"
											id="balanceBegin"
											type="number"
											size="20"
											v-model="balanceBegin"
											:rules="balanceBeginRules"
											label="餘額起"
											data-vv-scope="balanceWatch"
											:class="{ 'is-invalid': errors.balanceBegin }"
										></vue-field>
										<span class="input-group-text">~</span>
										<vue-field
											name="balanceEnd"
											class="form-control text-end"
											id="balanceEnd"
											:class="{ 'is-invalid': errors.balanceEnd }"
											type="number"
											size="20"
											v-model="balanceEnd"
											:rules="balanceEndRules"
											label="餘額迄"
											data-vv-scope="balanceWatch"
										></vue-field>
										<span class="input-group-text">元</span>
									</div>
								</div>
								<div class="col-2 text-end">
									<button class="btn btn-primary btn-glow JQ-singleSearch" type="button" @click.prevent="queryByBalance()">
										<i class="bi bi-search"></i>
									</button>
								</div>
								<div class="col-10" style="height: 3px">
									<span class="text-danger" v-show="errors.balanceItems">{{ errors.balanceItems }}</span>
									<span class="text-danger" v-show="errors.balanceBegin">{{ errors.balanceBegin }}</span>
									<span class="text-danger" v-show="errors.balanceEnd">{{ errors.balanceEnd }}</span>
								</div>
							</div>
						</vue-form>
					</div>

					<div class="divider"></div>
					<div class="col-md-12 bg-ligntblue p-3">
						<vue-form v-slot="{ errors }" ref="expireItems">
							<label class="form-label">持有商品到期查詢：</label><br />
							<span class="input-group-text">持有商品主類</span>
							<div class="form-check form-check-inline" v-for="productExpire in productExpireMenu">
								<vue-field
									class="form-check-input"
									name="expireItems"
									:class="{ 'is-invalid': errors.expireItems }"
									type="checkbox"
									:value="productExpire.codeValue"
									v-model="expireItems"
									rules="required"
									label="商品"
								></vue-field>
								<label class="form-check-label">{{ productExpire.codeName }}</label>
							</div>

							<span class="input-group-text">近到期天數</span>
							<div class="d-flex align-items-center justify-content-between">
								<div class="form-check form-check-inline" v-for="expireDays in expireDayList">
									<vue-field
										class="form-check-input"
										name="expireDays"
										:class="{ 'is-invalid': errors.expireDays }"
										type="radio"
										:value="expireDays.codeValue"
										v-model="expireDay"
										rules="required"
										label="近到期天數"
									></vue-field>
									<label class="form-check-label">{{ expireDays.codeName }}</label>
								</div>

								<div>
									<button class="btn btn-primary btn-glow" type="button" @click.prevent="queryByProductExpire">
										<i class="bi bi-search"></i>
									</button>
								</div>
							</div>
							<div class="col-10" style="height: 3px">
								<span class="text-danger" v-show="errors.expireItems">{{ errors.expireItems }}</span>
							</div>
						</vue-form>
					</div>
					<div class="divider"></div>
					<div class="col-md-12 bg-ligntblue p-3">
						<vue-form v-slot="{ errors }" ref="trustCertificate">
							<label class="form-label">單一信託憑證損益查詢</label><br />
							<div class="row align-items-end justify-content-between">
								<div class="col-10">
									<span class="input-group-text">損益區間</span>
									<div class="form-check form-check-inline" v-for="certificate in trustCertificateMenu">
										<vue-field
											class="form-check-input"
											:class="{ 'is-invalid': errors.trustCertificate }"
											name="trustCertificate"
											type="radio"
											:value="certificate.codeValue"
											v-model="trustCertificate"
											rules="required"
											label="單一信託憑證損益"
										></vue-field>
										<label class="form-check-label">{{ certificate.codeName }}</label>
									</div>
								</div>
								<div class="col-2 text-end">
									<button class="btn btn-primary btn-glow" type="button" @click.prevent="queryByTrustCertificate()">
										<i class="bi bi-search"></i>
									</button>
								</div>
							</div>
							<div style="height: 25px">
								<span class="text-danger" v-show="errors.trustCertificate">{{ errors.trustCertificate }}</span>
							</div>
						</vue-form>
					</div>
				</div>
			</div>
		</div>

		<vue-fav-customer-model :refresh-fun="getCusGroup"></vue-fav-customer-model>
	</div>
</template>
<script>
import { Field, Form } from 'vee-validate';
import vueFavCustomerModel from './favCustomerModal.vue';
import _ from 'lodash';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueFavCustomerModel
	},
	props: {
		queryReq: Object,
		gotoPage: Function
	},
	data: function () {
		return {
			groupCode: '',
			balanceItems: [],
			balanceBegin: '',
			balanceBeginRules: {
				required: true,
				min_value: 0
			},
			balanceEnd: '',
			curCode: 'ALL', // 查詢幣別代碼
			trustCertificate: null,

			expireItems: [],
			expireDay: 0,
			expireDays: [],
			//畫面顯示用邏輯參數
			isGroupNotNull: false,
			//下拉選單
			cusGroupMenu: [],
			productBalanceMenu: [],
			productExpireMenu: [],
			trustCertificateMenu: [],
			expireDayList: [],
			currenciesMenu: [] // 查詢幣別
		};
	},
	computed: {
		balanceEndRules() {
			return {
				required: true,
				min_value: this.balanceBegin || 0
			};
		}
	},
	mounted: function () {
		var self = this;
		self.getCusGroup();
		self.getProductBalance();
		self.getProductExpire();
		self.getTrustCertificate();
		// self.getCurrMenu();
		self.getExpireDayList();
	},
	methods: {
		next: function () {
			var self = this;
			self.$router.push('/cus/favCusSetup');
		},
		getCusGroup: async function () {
			var self = this;
			const ret = await self.$api.getCusGroupMenuApi();
			if (!_.isEmpty(ret.data)) {
				self.cusGroupMenu = ret.data;
				self.isGroupNotNull = true;
			}
		},
		getProductBalance: async function () {
			var self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'MKT_AMT_FIELD'
			});
			self.productBalanceMenu = ret.data;
		},
		getProductExpire: async function () {
			var self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'PRO_EXPIRE_SRH'
			});
			self.productExpireMenu = ret.data;
		},
		getTrustCertificate: async function () {
			var self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'PL_RATE_RANGE'
			});
			self.trustCertificateMenu = ret.data;
		},
		getExpireDayList: async function () {
			var self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'PRO_EXPIRE_TIME'
			});
			self.expireDayList = ret.data;
		},
		queryByGroup: function () {
			var self = this;
			self.$refs.groupCode.validate().then(function (pass) {
				if (pass) {
					self.clearQueryReq();
					self.queryReq.queryType = 'GROUP_CODE';
					self.queryReq.groupCode = self.groupCode;
					self.gotoPage(0);
					// self.groupCode = null;
				}
			});
		},
		clearQueryReq: function () {
			var self = this;

			for (var key in self.queryReq) {
				delete self.queryReq[key];
			}
		},
		queryByBalance: function () {
			var self = this;
			self.$refs.balanceWatch.validate().then(function (pass) {
				if (pass.valid) {
					self.clearQueryReq();
					self.queryReq.queryType = 'BALANCE';
					self.queryReq.mktProList = self.balanceItems;
					self.queryReq.mktValueStart = self.balanceBegin;
					self.queryReq.mktValueEnd = self.balanceEnd;
					self.gotoPage(0);
					// self.balanceItems = [];
					// self.balanceEnd = null;
					// self.balanceBegin = null;
				}
			});
		},
		queryByProductExpire: function () {
			var self = this;
			self.$refs.expireItems.validate().then(function (pass) {
				if (pass) {
					self.clearQueryReq();
					self.queryReq.queryType = 'EXPIRE_ITEMS';
					self.queryReq.expProList = self.expireItems;
					self.queryReq.expireDay = self.expireDay;
					self.gotoPage(0);
					// self.expireItems = [];
					// self.expireDay = 0;
				}
			});
		},
		queryByTrustCertificate: function () {
			var self = this;
			self.$refs.trustCertificate.validate().then(function (pass) {
				if (pass) {
					self.clearQueryReq();
					self.queryReq.queryType = 'TRUST_CERTIFICATE';
					self.queryReq.plRange = self.trustCertificate;
					self.gotoPage(0);
					// self.trustCertificate = null;
				}
			});
		}
		// 取得「幣別」下拉選單
		// getCurrMenu: function () {
		// 	var self = this;
		// 	var url = self.config.apiPath + '/cus/currenciesMenu/';
		// 	$.bi
		// 		.ajax({
		// 			url: url,
		// 			method: 'GET'
		// 		})
		// 		.then(function (ret) {
		// 			if (ret.data) {
		// 				ret.data.forEach(function (item) {
		// 					var obj = { value: item.curCode, name: item.curName };
		// 					self.currenciesMenu.push(obj);
		// 				});
		// 			}
		// 		});
		// }
	}
};
</script>
