<template>
	<!-- Modal 1-->
	<modal ref="modal" :before-close="closeModal">
		<template v-slot:content="props">
			<div class="modal-dialog modal-dialog-centered modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title" id="detailModal">申請明細</h4>
						<button type="button" class="btn-close" @click="props.close()" aria-label="Close"></button>
					</div>
					<div class="modal-body" style="overflow: scroll">
						<table class="table table-bordered">
							<caption>
								功能選單
							</caption>
							<tbody>
								<tr>
									<th colspan="8">系統角色：{{ roleName }}</th>
								</tr>
								<tr>
									<th>一級選單(模組)</th>
									<th>二級選單(功能)</th>
									<th>三級選單(子功能)</th>
									<th>四級選單(頁簽)</th>
									<th>五級選單(子頁簽)</th>
									<th>查詢</th>
									<th>編輯</th>
									<th>覆核</th>
									<th>匯出</th>
								</tr>
								<tr v-for="item in roleMenuLogs">
									<td data-th="一級選單(模組)">
										{{ item.firMenuName }}
									</td>
									<td data-th="二級選單(功能)" class="text-start">
										{{ item.secMenuName }}
									</td>
									<td data-th="三級選單(子功能)">
										{{ item.thiMenuName }}
									</td>
									<td data-th="四級選單(頁簽)">
										{{ item.fouMenuName }}
									</td>
									<td data-th="五級選單(子頁簽)">
										{{ item.fifMenuName }}
									</td>
									<td data-th="查詢">
										<div class="form-check form-check-inline">
											<input class="form-check-input" type="checkbox" disabled :checked="item.view" />
										</div>
									</td>
									<td data-th="編輯">
										<div class="form-check form-check-inline">
											<input class="form-check-input" type="checkbox" disabled :checked="item.edit" />
										</div>
									</td>
									<td data-th="覆核">
										<div class="form-check form-check-inline">
											<input class="form-check-input" type="checkbox" disabled :checked="item.verify" />
										</div>
									</td>
									<td data-th="匯出">
										<div class="form-check form-check-inline bi-tree-removeY">
											<input class="form-check-input" type="checkbox" disabled :checked="item.export" />
										</div>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
					<div class="modal-footer">
						<button @click.prevent="props.close()" type="button" class="btn btn-white">關閉視窗</button>
					</div>
				</div>
			</div>
		</template>
	</modal>
</template>

<script>
import modal from '@/views/components/model.vue';

export default {
	components: {
		modal
	},
	props: {},
	data: function () {
		return {
			roleName: String
		};
	},
	methods: {
		getDetail: async function (eventId, roleName) {
			if (this.$_.isBlank(eventId)) {
				return;
			}
			this.roleName = roleName;
			let ret = this.$api.getDetailApi(eventId);
			this.roleMenuLogs = ret.data;
			this.$refs.modal.open();
		},
		closeModal: function () {
			this.$refs.modal.close();
		}
	}
};
</script>
