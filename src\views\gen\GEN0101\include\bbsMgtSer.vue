<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form">
			<div class="card-header">
				<h4>公告設定</h4>
				<span class="tx-square-bracket">為必填欄位</span>
			</div>
			<div class="card-body collapse show" id="card-body">
				<div class="row g-3 align-items-end">
					<vue-form v-slot="{ errors, validate }" ref="queryForm">
						<div class="form-row">
							<div class="form-group col-md-4">
								<label class="form-label">訊息類別</label>
								<select class="form-select" v-model="msgCode" name="msgCode">
									<option selected :value="null">全部</option>
									<option v-for="selMessage in selMessageMap" :value="selMessage.msgCode">
										{{ selMessage.msgName }}
									</option>
								</select>
							</div>
							<div class="form-group col-md-4">
								<label class="form-label">主分類</label>
								<select v-model="mainCatCode" name="mainCatCode" class="form-select">
									<option :value="null">全部</option>
									<option :value="item.catCode" v-for="item in selMsgMainCat">
										{{ item.catName }}
									</option>
								</select>
							</div>
							<div class="form-group col-md-4">
								<label class="form-label">次分類</label>
								<select v-model="subCatCode" name="subCatCode" class="form-select" :disabled="!mainCatCode">
									<option :value="null">請選擇</option>
									<option :value="item.catCode" v-for="item in selMsgSubCat">
										{{ item.catName }}
									</option>
								</select>
							</div>
							<div class="form-group col-md-12">
								<label class="form-label tx-require">有效日期</label>
								<span class="input-group-text">起</span>
								<vue-field
									type="date"
									id="validBgnDt"
									name="validBgnDt"
									v-model="validBgnDt"
									size="13"
									label="有效日期起"
									class="form-control"
									maxlength="10"
									rules="required"
									:class="{ 'is-invalid': errors.validBgnDt }"
								></vue-field>
								<span class="input-group-text">~</span>
								<span class="input-group-text">迄</span>
								<vue-field
									type="date"
									id="validEndDt"
									name="validEndDt"
									v-model="validEndDt"
									size="13"
									label="有效日期迄"
									class="form-control"
									maxlength="10"
									rules="required"
									:class="{ 'is-invalid': errors.validEndDt }"
								></vue-field>
							</div>
							<span class="text-danger" v-show="errors.validBgnDt || errors.validEndDt"
								>{{ errors.validBgnDt }} {{ errors.validEndDt }}</span
							>
							<div class="form-group col-md-4">
								<label class="form-label">公告標題</label>
								<vue-field
									id="msgTitle"
									name="msgTitle"
									label="公告標題"
									type="text"
									class="form-control form-input"
									v-model="msgTitle"
								>
								</vue-field>
							</div>
						</div>
						<div class="form-footer">
							<button class="btn btn-primary btn-glow" type="button" @click.prevent="gotoPage(0)">查詢</button>
						</div>
					</vue-form>
				</div>
			</div>
			<div id="searchResult">
				<div class="card card-table">
					<div class="card-header">
						<h4>查詢結果</h4>
						<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-bordered text-center">
							<thead>
								<tr>
									<th>重要性</th>
									<th>訊息類別</th>
									<th>主分類</th>
									<th>次分類</th>
									<th>有效日期</th>
									<th>公告標題</th>
									<th>建立者</th>
									<th>執行</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="item in pageData.content">
									<td :class="{ 'text-center tx-red bi-exclamation': item.importantYn == 'Y' }" data-th="重要性"></td>
									<td data-th="訊息類別">{{ item.msgName }}</td>
									<td data-th="主分類">{{ item.mainCatName }}</td>
									<td data-th="次分類">{{ item.subCatName }}</td>
									<td data-th="有效日期">{{ item.validBgnDt }} ~ {{ item.validEndDt }}</td>
									<td data-th="公告標題">{{ item.msgTitle }}</td>
									<td data-th="建立者">{{ item.createUser }}</td>
									<td data-th="執行">
										<button type="button" class="btn btn-dark btn-glow btn-icon" title="檢視" @click="getView(item.msgId)">
											<i class="bi bi-search"></i>
										</button>
										<button
											type="button"
											class="btn btn-info btn-glow btn-icon btn-edit"
											data-bs-original-title="編輯"
											@click="editPage(item)"
										>
											<i class="bi bi-pen"></i>
										</button>
										<button
											type="button"
											class="btn btn-danger btn-glow btn-icon"
											data-bs-original-title="刪除"
											@click="doDelete(item)"
										>
											<i class="bi bi-trash"></i>
										</button>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!--Detail Modal-->
	<vue-modal :is-open="isOpenModal" :before-close="closeModal">
		<template v-slot:content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">公告訊息</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()"><i class="bi bi-arrows-fullscreen"></i></button>
						<button type="button" class="btn-close" @click.prevent="props.close()"></button>
					</div>
					<div class="modal-body">
						<table class="table table-bordered">
							<caption>
								公告分類
							</caption>
							<tbody>
								<tr>
									<th>訊息類別</th>
									<td>{{ selMsg.msgName }}</td>
								</tr>
							</tbody>
						</table>

						<table class="table table-bordered">
							<caption>
								公告內容
							</caption>
							<tbody>
								<tr>
									<th class="wd-15p">重要性</th>
									<td>{{ subTypeName(selMsg.importantYn) }}</td>
								</tr>
								<tr>
									<th>主分類</th>
									<td>{{ selMsg.mainCatName }}</td>
								</tr>
								<tr>
									<th>次分類</th>
									<td>{{ selMsg.subCatName }}</td>
								</tr>
								<tr>
									<th>有效日期</th>
									<td>{{ selMsg.validBgnDt }} ~ {{ selMsg.validEndDt }}</td>
								</tr>
								<tr>
									<th><span>公告標題</span></th>
									<td>{{ selMsg.msgTitle }}</td>
								</tr>
								<tr>
									<th>公告內容</th>
									<td>{{ selMsg.msgContent }}</td>
								</tr>
								<tr>
									<th>首頁是否顯示</th>
									<td>{{ subTypeName(selMsg.showYn) }}</td>
								</tr>
								<tr>
									<th>上傳檔案</th>
									<td>
										<ul class="list-group list-inline-tags" v-for="file in selMsg.fileList">
											<li class="list-group-item">
												<a href="#" @click="viewFile(file.msgFileId)">
													<span>{{ file.showName }}</span>
												</a>
											</li>
										</ul>
									</td>
								</tr>
								<tr>
									<th>連結</th>
									<td>
										<a :href="selMsg.favoriteLink">{{ selMsg.favoriteLink }}</a>
									</td>
								</tr>
							</tbody>
						</table>

						<table class="table table-bordered">
							<caption>
								維護資訊
							</caption>
							<tbody>
								<tr>
									<th>建立人員</th>
									<td>{{ selMsg.createUser }}</td>
								</tr>
								<tr>
									<th>建立人員分機</th>
									<td>{{ selMsg.createUserExt }}</td>
								</tr>
								<tr>
									<th>建立日期</th>
									<td>{{ selMsg.createDt }}</td>
								</tr>
								<tr>
									<th>最後維護人員</th>
									<td>{{ selMsg.modifyUser }}</td>
								</tr>
								<tr>
									<th>維護人員分機</th>
									<td>{{ selMsg.modifyUserExt }}</td>
								</tr>
								<tr>
									<th>最後維護日期</th>
									<td>{{ selMsg.modifyDt }}</td>
								</tr>
							</tbody>
						</table>
					</div>
					<div class="modal-footer">
						<button @click.prevent="props.close()" type="button" class="btn btn-white">關閉視窗</button>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
</template>
<script>
import { Field, Form } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import _ from 'lodash';
import vuePagination from '@/views/components/pagination.vue';
import moment from 'moment';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueModal,
		vuePagination
	},
	computed: {
		userInfo: function () {
			return this.$store.getters['userInfo/info'];
		}
	},
	data: function () {
		return {
			selMessageMap: [], // 公告類別資料
			selMsgMainCat: [], // 主分類資料
			selMsgSubCat: [], // 次分類資料
			selOptionYn: [], // convert option name

			msgCode: null,
			mainCatCode: null,
			subCatCode: null,
			validBgnDt: null,
			validEndDt: null,
			msgTitle: null,

			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: 'MSG_ID',
				direction: 'ASC'
			},
			isOpenModal: null,
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',
			buttonClass: 'btn-expand',
			//getView
			selMsg: {}
		};
	},
	mounted: async function () {
		var self = this;
		// initial
		self.getMessageMap();
		self.getMessageCat('M', null, self.selMsgMainCat);
		self.selOptionYn = await self.getAdmCodeDetail('OPTION_YN');
	},
	watch: {
		mainCatCode: async function (newVal, oldVal) {
			var self = this;
			if (newVal) {
				self.selMsgSubCat = await self.getMessageCat('S', newVal);
			} else {
				self.selMsg.subCatCode = null;
			}
		}
	},
	methods: {
		getAdmCodeDetail: async function (codeType) {
			var self = this;
			const ret = await self.$api.getAdmCodeDetail({ codeType: codeType });
			return ret.data;
		},
		getMessageMap: async function () {
			var self = this;
			const ret = await self.$api.getMessageMapApi();
			self.selMessageMap = ret.data;
		},
		getMessageCat: async function (catType, mainCatCode) {
			var self = this;
			var data = { catType: catType, mainCatCode: mainCatCode };
			let reqData = _.omitBy(data, (value) => _.isNil(value) || value === '');
			const ret = await self.$api.getGenMessageCat(reqData);
			return ret.data;
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: function (_page) {
			var self = this;
			self.$refs.queryForm.validate().then(async function (pass) {
				if (pass.valid) {
					if (moment(self.validBgnDt).isAfter(moment(self.validEndDt))) {
						this.$bi.alert('日期區間的起不能大於迄');
						return;
					}
					var page = _.isNumber(_page) ? _page : self.pageable.page;
					var url = '';
					url += '?page=' + page + '&size=' + self.pageable.size;
					url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;

					const ret = await self.$api.getBbsMgtPageData(
						{
							msgCode: self.msgCode,
							mainCatCode: self.mainCatCode,
							subCatCode: self.subCatCode,
							validBgnDt: _.formatDate(self.validBgnDt),
							validEndDt: _.formatDate(self.validEndDt),
							msgTitle: self.msgTitle
						},
						url
					);
					self.pageData = ret.data;
				}
			});
		},
		getView: async function (msgId) {
			var self = this;
			const ret = await self.$api.getGenMessageApi({
				msgId: msgId
			});
			if (ret.data) {
				self.selMsg = _.clone(ret.data);
				self.isOpenModal = true;
			}
		},
		editPage: function (item) {
			if (item.lockYn === 'Y') {
				this.$bi.alert('此公告為待審核狀態，不可修改。');
				return;
			}
			let self = this;
			let url = self.config.contextPath + '/gen/bbsMgtParam?';
			url += 'msgId=' + _.convertUrlSpecChar(item.msgId);
			location.href = url;
		},
		doDelete: async function (item) {
			if (item.lockYn === 'Y') {
				this.$bi.alert('此公告為待審核狀態，不可修改。');
				return;
			}
			var msgId = item.msgId;
			var self = this;
			const ret = await self.$api.getGenMessageApi({
				msgId: msgId
			});
			if (ret.data) {
				var msgData = _.clone(ret.data);
				msgData.validBgnDt = moment(ret.data.validBgnDt).format('yyyy-MM-DD');
				msgData.validEndDt = moment(ret.data.validEndDt).format('yyyy-MM-DD');
				msgData.actionCode = 'D';
				msgData.fileObject = _.clone(ret.data.fileList);
				_.forEach(msgData.fileObject, function (file) {
					file.fileNo = file.msgFileId;
				});
				self.$swal
					.fire({
						icon: 'warning',
						text: '確認刪除',
						showCloseButton: true,
						confirmButtonText: '確認',
						cancelButtonText: '取消',
						showCancelButton: true,
						buttonsStyling: false,
						customClass: {
							confirmButton: 'btn btn-success',
							cancelButton: 'btn btn-danger'
						},
						willOpen: function () {
							// 添加自定義的樣式來增加按鈕之間的間距
							$('.swal2-confirm').css('margin-right', '20px');
						}
					})
					.then(function (result) {
						if (result.isConfirmed) {
							// 提交送審
							self.bbmMaintainSave(msgData);
						}
					});
			}
		},
		bbmMaintainSave: async function (msgData) {
			var self = this;
			self.$api.getBbmMaintainSaveApi();
			if (ret.data && ret.data.length > 0) {
				var bbma = ret.data[0];
				if (bbma.wfgRoleKind !== 'rule4' && bbma.wfgsRole !== self.userInfo.roleCode) {
					this.$bi.alert('無使用權限，請洽系統管理人員！');
					return;
				} else {
					// 送審
					var formData = new FormData();
					var jsonStr = JSON.stringify(msgData);
					formData.append('json', new Blob([jsonStr], { type: 'application/json' }));
					var confirmText = '刪除';
					self.$api.postMsgLogApi(formData);
					var confirmStatus = 'success';
					if (typeof ret.data.msgId === 'undefined' || ret.data.msgId === null) {
						confirmText = confirmText + '失敗，請聯絡管理員';
						confirmStatus = 'error';
					} else {
						confirmText = confirmText + '成功';
					}
					self.$swal
						.fire({
							icon: confirmStatus,
							text: confirmText,
							showCloseButton: true,
							confirmButtonText: '確認',
							buttonsStyling: false,
							customClass: {
								confirmButton: 'btn btn-success'
							}
						})
						.then(function (ret) {
							self.gotoPage(0);
						});
				}
			}
		},
		subTypeName: function (codeValue) {
			var self = this;
			if (!_.isBlank(self.selOptionYn) && !_.isBlank(codeValue)) {
				return _.find(self.selOptionYn, { codeValue: codeValue }).codeName;
			} else {
				return codeValue;
			}
		},
		changeModalSize: function () {
			var self = this;
			if (self.modalClass === 'modal-dialog modal-lg modal-dialog-centered') {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered fullscreen';
				self.buttonClass = 'btn-expand mini';
			} else {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered';
				self.buttonClass = 'btn-expand';
			}
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		viewFile: function (targetFileId) {
			var self = this;
			self.$api.downloadBbsHeadFileApi({ fileId: targetFileId });
		}
	}
};
</script>
