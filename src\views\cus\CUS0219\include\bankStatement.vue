<template>
	<vue-cus-bank-statement v-if="tabCode == 1" ref="bankStatement" :cus-code="cusCode"></vue-cus-bank-statement>
</template>
<script>
import vueCusBankStatement from './bankStatementReport.vue';
export default {
	components: {
		vueCusBankStatement
	},
	props: {
		cusCode: null,
		hasAuth: <PERSON><PERSON><PERSON>,
		customer: Object
	},
	data: function () {
		return {
			//畫面邏輯判斷用參數
			customTitle: null,
			tabCode: 0,

			userName: '', // 操作者姓名
			userRoleName: '', // 操作者角色名稱
			queryDt: null, // 查詢時間
			dataDt: null // 資料時間
		};
	},
	computed: {
		userInfo: function () {
			return this.$store.getters['userInfo/info'];
		}
	},
	watch: {
		userInfo: {
			immediate: true,
			handler: function (newVal, oldVal) {
				var self = this;
				if (newVal) {
					if (newVal.deputyUserName) {
						self.userName = newVal.deputyUserName;
					} else {
						self.userName = newVal.userName;
					}
					self.userRoleName = newVal.roleName;
				}
			}
		}
	},
	mounted: function () {
		var self = this;
		if (self.hasAuth) {
			self.tabCode = 1;
		} else {
			self.$bi.alert('該顧客非屬您歸屬私銀中心/組轄下顧客。');
		}
	},
	methods: {
		changeTab: function (tabCode) {
			var self = this;
			self.tabCode = tabCode;
		}
	}
};
</script>
