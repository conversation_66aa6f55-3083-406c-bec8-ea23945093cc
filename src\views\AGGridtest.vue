<template>
	<!-- The AG Grid component -->
	<div class="container py-5">
		<ag-grid-vue
			:rowData="testData"
			:columnDefs="columnDefs"
			style="width: 100%"
			:domLayout="'autoHeight'"
			:enableCharts="true"
			:cellSelection="true"
		>
		</ag-grid-vue>
	</div>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue3';
export default {
	data: function () {
		return {
			autoSizeStrategy: {
				type: 'fitCellContents'
			},
			testData: [
				{
					dataYm: '2024-10-02',
					buyAmtLc: 15000,
					sellAmtLc: 2000,
					totAmtLc: 13000,
					rplLc: 400,
					uplLc: 100,
					invAmtLc: 8000,
					mktAmtLc: 8100
				},
				{
					dataYm: '2024-11-11',
					buyAmtLc: 12000,
					sellAmtLc: 3000,
					totAmtLc: 9000,
					rplLc: 300,
					uplLc: 200,
					invAmtLc: 9000,
					mktAmtLc: 9200
				},
				{
					dataYm: '2024-12-13',
					buyAmtLc: 9000,
					sellAmtLc: 4000,
					totAmtLc: 5000,
					rplLc: 200,
					uplLc: 300,
					invAmtLc: 10000,
					mktAmtLc: 10300
				},
				{
					dataYm: '2025-01-11',
					buyAmtLc: 6000,
					sellAmtLc: 5000,
					totAmtLc: 1000,
					rplLc: 100,
					uplLc: 400,
					invAmtLc: 11000,
					mktAmtLc: 11400
				}
			],
			columnDefs: [
				{
					field: 'dataYm',
					headerName: '日期',
					filter: 'agDateColumnFilter',
					filterParams: {
						comparator: (filterLocalDateAtMidnight, cellValue) => {
							// 把 YYYYMM 格式字串轉換成 Date 物件
							const year = parseInt(cellValue.substring(0, 4));
							const month = parseInt(cellValue.substring(4, 6)) - 1; // 月份從 0 開始
							const cellDate = new Date(year, month);

							// 與過濾器設置的日期進行比較
							if (cellDate < filterLocalDateAtMidnight) {
								return -1;
							} else if (cellDate > filterLocalDateAtMidnight) {
								return 1;
							}
							return 0; // 相等
						}
					}
				},
				{ field: 'buyAmtLc', headerName: '資金流入', filter: 'agNumberColumnFilter', sortable: true, sort: 'asc' },
				{ field: 'sellAmtLc', headerName: '資金流出' },
				{ field: 'totAmtLc', headerName: '資金淨增減' },
				{ field: 'rplLc', headerName: '實現損益' },
				{ field: 'uplLc', headerName: '未實現損益' },
				{ field: 'invAmtLc', headerName: '非投資商品庫存餘額' },
				{ field: 'mktAmtLc', headerName: '投資商品庫存本金' }
			]
		};
	},
	components: {
		AgGridVue
	}
};
</script>
