<template>
	<div class="card card-form-collapse">
		<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
			<h4>查詢條件</h4>
		</div>
		<div class="collapse show" id="collapseListGroup1">
			<div class="card-body">
				<vue-form v-slot="{ errors, validate, handleReset }" ref="invTranForm">
					<div class="form-row">
						<div class="form-group col-12 col-lg-6">
							<label class="form-label tx-require">查詢商品</label>
							<div class="form-check-group">
								<div v-for="(item, i) in pfcatList" class="form-check form-check-inline">
									<vue-field
										class="form-check-input"
										name="pfcat_code"
										:id="'pfcatCodes-' + i"
										v-model="form.pfcatCode"
										type="checkbox"
										:value="item.pfcatCode"
									>
									</vue-field>
									<label class="form-check-label" :for="'pfcatCodes-' + i">{{ item.pfcatName }}</label>
								</div>
								<span class="text-danger" v-show="errors.pfcat_code">{{ errors.pfcat_code }}</span>
							</div>
						</div>
						<div class="form-group col-12 col-lg-6">
							<label class="form-label tx-require">交易類型</label>
							<div class="form-check-group">
								<div v-for="(item, i) in trantypeList" class="form-check form-check-inline">
									<vue-field
										class="form-check-input"
										name="tranTypeCode"
										v-model="form.tranTypeCode"
										:id="'tranTypeCode-' + i"
										type="checkbox"
										:value="item.trantypeCode"
									>
									</vue-field>
									<label class="form-check-label" :for="'tranTypeCode-' + i">{{ item.trantypeName }}</label>
								</div>
								<span class="text-danger" v-show="errors.tranTypeCode">{{ errors.tranTypeCode }}</span>
							</div>
						</div>
						<div class="form-group col-12 col-lg-3">
							<label class="form-label">商品代號</label>
							<input name="bankProCode" class="form-control" id="bankProCode" type="text" v-model="form.bankProCode" />
						</div>
						<div class="form-group col-12 col-lg-3">
							<label class="form-label">憑證編號</label>
							<input name="refNo" class="form-control" id="refNo" type="text" v-model="form.refNo" />
						</div>
						<div class="form-group col-md-10 col-lg-6">
							<label class="form-label">交易日期區間</label>
							<div class="input-group">
								<input
									name="startDate"
									class="form-control"
									id="startDate"
									type="date"
									size="13"
									maxlength="10"
									v-model="form.tranDtB"
								/>
								<span class="input-group-text">~</span>
								<input name="endDate" class="form-control" id="endDate" type="date" size="13" maxlength="10" v-model="form.tranDtE" />
							</div>
						</div>
					</div>
					<div class="form-footer">
						<button type="button" class="btn btn-primary btn-search" @click="search">查詢</button>
					</div>
				</vue-form>
			</div>
		</div>
	</div>
	<div class="tx-note mb-3">證券台股僅顯示現股資料。</div>
	<div class="searchResult">
		<div class="card card-table card-collapse" v-if="fundTranList.length > 0">
			<div class="card-header">
				<h4>信託-基金</h4>
				<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup4"></div>
			</div>
			<div class="collapse show" id="collapseListGroup4">
				<div class="table-responsive">
					<table class="table table-RWD table-bordered">
						<thead>
							<tr>
								<th rowspan="2">交易日</th>
								<th>帳號</th>
								<th rowspan="2">交易類型</th>
								<th rowspan="2">交易幣別</th>
								<th rowspan="2">匯率</th>
								<th rowspan="2">交易金額</th>
								<th rowspan="2">淨值</th>
								<th rowspan="2">交易單位數</th>
								<th rowspan="2">手續費</th>
								<th rowspan="2">其他費用</th>
								<th rowspan="2">備註</th>
							</tr>
							<tr>
								<th>商品代號/投資標的</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="tran in fundTranList">
								<td data-th="交易日">{{ $filters.formatDate(tran.tranDt) }}</td>
								<td data-th="帳號　商品代號/投資標的">
									{{ tran.refNo }}<br /><a class="tx-link" href="#">{{ tran.proName }}({{ tran.bankProCode }})</a>
								</td>
								<td data-th="交易類型">{{ tran.trantypeName }}</td>
								<td class="" data-th="幣別">
									<label class="num">{{ tran.tranCurCode }}</label>
								</td>
								<td class="text-end" data-th="匯率">
									<label class="num">{{ $filters.formatAmt(tran.fxRate) }}</label>
								</td>
								<td class="text-end" data-th="交易金額">
									<label class="num">{{ $filters.formatAmt(tran.invAmtFc) }}</label
									>&nbsp;{{ tran.tranCurCode }}
								</td>
								<td class="text-end" data-th="淨值">
									<label class="num">{{ $filters.formatAmt(tran.price) }}</label>
								</td>
								<td class="text-end" data-th="交易單位數">
									<label class="num">{{ $filters.formatAmt(tran.unit) }}</label>
								</td>
								<td class="text-end" data-th="手續費">
									<label class="num">{{ $filters.formatAmt(tran.feeFc) }}</label
									>&nbsp;{{ tran.fCurCode }}
								</td>
								<td class="text-end" data-th="其他費用">
									<label class="num">{{ $filters.formatAmt(tran.oFeeFc) }}</label
									>&nbsp;{{ tran.oCurCode }}
								</td>
								<td data-th="備註">{{ tran.memo }}</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<div class="card card-table card-collapse mb-3" v-if="fbTranList.length > 0">
			<div class="card-header">
				<h4>信託-海外債</h4>
				<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup6"></div>
			</div>
			<div class="collapse show" id="collapseListGroup6">
				<div class="table-responsive">
					<table class="table table-RWD table-bordered">
						<thead>
							<tr>
								<th rowspan="2">交易日</th>
								<th>帳號</th>
								<th rowspan="2">交易類型</th>
								<th rowspan="2">交易金額</th>
								<th rowspan="2">交易面額</th>
								<th rowspan="2">匯率</th>
								<th rowspan="2">價格(%)</th>
								<th rowspan="2">前手息</th>
								<th>通路服務費實際費率</th>
							</tr>
							<tr>
								<th>商品代號/投資標的</th>
								<th>通路服務費年化費率</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="tran in fbTranList">
								<td data-th="交易日">{{ $filters.formatDate(tran.tranDt) }}</td>
								<td data-th="帳號　商品代號/投資標的">
									{{ tran.refNo }}<br /><a class="tx-link" href="#">{{ tran.proName }}({{ tran.bankProCode }})</a>
								</td>
								<td data-th="交易類型">{{ tran.trantypeName }}</td>
								<td class="text-end" data-th="交易金額">
									<label class="num">{{ $filters.formatAmt(tran.invAmtFc) }}</label
									>&nbsp;{{ tran.tranCurCode }}
								</td>
								<td class="text-end" data-th="交易面額"></td>
								<td class="text-end" data-th="匯率">
									<label class="num">{{ $filters.formatAmt(tran.fxRate) }}</label>
								</td>
								<td class="text-end" data-th="價格(%)">
									<label class="num">{{ $filters.formatAmt(tran.price) }}</label>
								</td>
								<td class="text-end" data-th="前手息">
									<label class="num">{{ $filters.formatAmt(tran.uFeeFc) }}</label
									>&nbsp;{{ tran.tranCurCode }}
								</td>
								<td class="text-end" data-th="通路服務費實際費率　通路服務費年化費率">
									<label class="num">{{ $filters.formatPct(tran.channelServiceRate) }}%</label><br />
									<label class="num">{{ $filters.formatPct(tran.channelServiceRateYear) }}%</label>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<div class="card card-table card-collapse mb-3" v-if="etfTranList.length > 0">
			<div class="card-header">
				<h4>信託-ETF</h4>
				<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup5"></div>
			</div>
			<div class="collapse show" id="collapseListGroup5">
				<div class="table-responsive">
					<table class="table table-RWD table-bordered">
						<thead>
							<tr>
								<th rowspan="2">交易日</th>
								<th>成交序號</th>
								<th rowspan="2">交易類型</th>
								<th rowspan="2">交易金額</th>
								<th rowspan="2">匯率</th>
								<th rowspan="2">成交價格</th>
								<th rowspan="2">股數</th>
								<th rowspan="2">手續費</th>
								<th rowspan="2">稅費</th>
								<th rowspan="2">信託管理費</th>
							</tr>
							<tr>
								<th>商品代號/投資標的</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="tran in etfTranList">
								<td data-th="交易日">{{ $filters.formatDate(tran.tranDt) }}</td>
								<td data-th="帳號　商品代號/投資標的">
									{{ tran.refNo }}<br /><a class="tx-link" href="#">{{ tran.proName }}({{ tran.bankProCode }})</a>
								</td>
								<td data-th="交易類型">{{ tran.trantypeName }}</td>
								<td class="text-end" data-th="交易金額">
									<label class="num">{{ $filters.formatAmt(tran.invAmtFc) }}</label
									>&nbsp;{{ tran.tranCurCode }}
								</td>
								<td class="text-end" data-th="匯率">
									<label class="num">{{ $filters.formatAmt(tran.fxRate) }}</label>
								</td>
								<td class="text-end" data-th="成交價格">
									<label class="num">{{ $filters.formatAmt(tran.price) }}</label>
								</td>
								<td class="text-end" data-th="股數">
									<label class="num">{{ $filters.formatAmt(tran.unit) }}</label>
								</td>
								<td class="text-end" data-th="手續費">
									<label class="num">{{ $filters.formatAmt(tran.fFeeFc) }}</label
									>&nbsp;{{ tran.fCurCode }}
								</td>
								<td class="text-end" data-th="稅費">
									<label class="num">{{ $filters.formatAmt(tran.sFeeFc) }}</label
									>&nbsp;{{ tran.tranCurCode }}
								</td>
								<td class="text-end" data-th="信託管理費">
									<label class="num">{{ $filters.formatAmt(tran.mFeeFc) }}</label
									>&nbsp;{{ tran.tranCurCode }}
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<div class="card card-table card-collapse mb-3" v-if="pfdTranList.length > 0">
			<div class="card-header">
				<h4>信託-海外股票</h4>
				<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup5"></div>
			</div>
			<div class="collapse show" id="collapseListGroup5">
				<div class="table-responsive">
					<table class="table table-RWD table-bordered">
						<thead>
							<tr>
								<th rowspan="2">交易日</th>
								<th>委託書號</th>
								<th rowspan="2">交易類型</th>
								<th rowspan="2">投資本金</th>
								<th rowspan="2">參考匯率</th>
								<th rowspan="2">成交價格</th>
								<th rowspan="2">成交股數</th>
								<th rowspan="2">預估手續費</th>
								<th rowspan="2">稅費</th>
								<th rowspan="2">預估管理費</th>
							</tr>
							<tr>
								<th>商品代號/投資標的</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="tran in pfdTranList">
								<td data-th="交易日">{{ $filters.formatDate(tran.tranDt) }}</td>
								<td data-th="委託書號　商品代號/投資標的">
									{{ tran.refNo }}<br /><a class="tx-link" href="#">{{ tran.proName }}({{ tran.bankProCode }})</a>
								</td>
								<td data-th="交易類型">{{ tran.trantypeName }}</td>
								<td class="text-end" data-th="投資本金">
									<label class="num">{{ $filters.formatAmt(tran.invAmtFc) }}</label
									>&nbsp;{{ tran.tranCurCode }}
								</td>
								<td class="text-end" data-th="參考匯率">
									<label class="num">{{ $filters.formatAmt(tran.fxRate) }}</label>
								</td>
								<td class="text-end" data-th="成交價格">
									<label class="num">{{ $filters.formatAmt(tran.price) }}</label>
								</td>
								<td class="text-end" data-th="成交股數">
									<label class="num">{{ $filters.formatAmt(tran.unit) }}</label>
								</td>
								<td class="text-end" data-th="預估手續費">
									<label class="num">{{ $filters.formatAmt(tran.fFeeFc) }}</label
									>&nbsp;{{ tran.fCurCode }}
								</td>
								<td class="text-end" data-th="稅費">
									<label class="num">{{ $filters.formatAmt(tran.sFeeFc) }}</label
									>&nbsp;{{ tran.tranCurCode }}
								</td>
								<td class="text-end" data-th="預估管理費">
									<label class="num">{{ $filters.formatAmt(tran.mFeeFc) }}</label
									>&nbsp;{{ tran.tranCurCode }}
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<div class="alert alert-warning mt-3" role="alert">
			<span class="ico-alert"></span>
			本功能畫面提供的有關資訊是截止統計日期，由系統根據客戶歷史投資資料自動生成的，僅供參考。
		</div>
	</div>
</template>
<script>
import { Field, Form } from 'vee-validate';
import moment from 'moment';
export default {
	props: {
		cusCode: null
	},
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	data: function () {
		return {
			form: {
				cusCode: null,
				pfcatCode: [],
				tranTypeCode: [],
				bankProCode: null,
				refNo: null,
				tranDtB: null,
				tranDtE: null
			},
			pfcatList: [],
			trantypeList: [],
			pageData: [],
			fundTranList: [],
			fbTranList: [],
			etfTranList: [],
			pfdTranList: []
		};
	},
	watch: {},
	mounted: function () {
		var self = this;
		self.form.cusCode = self.cusCode;
		self.form.tranDtB = moment().add(-31, 'days').format('YYYY-MM-DD'); // 交易日期起 (預設一個月前)
		self.form.tranDtE = moment().format('YYYY-MM-DD'); // 交易日期迄
		self.getPfcatList();
		self.getTrantypeList();
	},
	methods: {
		getPfcatList: async function () {
			var self = this;
			const resp = await self.$api.getProCatApi({
				tranYn: 'Y'
			});
			self.pfcatList = resp.data;
		},
		getTrantypeList: async function () {
			var self = this;
			const resp = await self.$api.getTranTypeListApi();
			self.trantypeList = resp.data;
		},
		search: function () {
			var self = this;
			self.$refs.invTranForm.validate().then(function (pass) {
				if (pass.valid) {
					self.getPageData();
				}
			});
		},
		gotoPage: function (page) {
			this.getPageData(page);
		},
		// 呼叫後端 API 取得「客戶交易明細」
		getPageData: async function () {
			var self = this;
			const resp = await self.$api.getTransactionLogApi(self.form);
			self.pageData = resp.data;
			self.fundTranList = self.pageData.filter((tran) => {
				return tran.pfcatCode === 'FUND';
			});
			self.fbTranList = self.pageData.filter((tran) => {
				return tran.pfcatCode === 'FB';
			});
			self.etfTranList = self.pageData.filter((tran) => {
				return tran.pfcatCode === 'ETF';
			});
			self.pfdTranList = self.pageData.filter((tran) => {
				return tran.pfcatCode === 'PFD';
			});
		},
		tranName: function (tranYn) {
			switch (tranYn) {
				case 'Y':
					return '是';
				case 'N':
					return '否';
				default:
					return '';
			}
		},
		productModal: function (item) {
			this.$refs.productModal.open(item.proCode, item.pfcatCode);
		}
	}
};
</script>
