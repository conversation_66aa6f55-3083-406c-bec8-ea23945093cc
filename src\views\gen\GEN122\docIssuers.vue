<template>
	<dynamicTitle />
	<div>
		<div class="row">
			<div class="col-12">
				<div class="tab-nav-main">
					<div class="tab-content">
						<div class="tab-pane fade active show">
							<div class="card card-table mb-3">
								<div class="card-header">
									<h4>文件列表</h4>
									<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
								</div>
								<div class="table-responsive">
									<table class="table table-RWD table-hover table-bordered table-padding">
										<thead>
											<tr>
												<th width="40%">文件標題</th>
												<th width="10%">生效日</th>
												<th width="10%">到期日</th>
												<th width="12%">可否提供給客戶</th>
												<th width="9%">緊急程度</th>
												<th width="9%">發行機構</th>
												<th width="9%">檢視</th>
												<th width="10%">相關附件</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="data in pageData.content">
												<td data-th="文件標題">{{ data.docName }}</td>
												<td data-th="生效日">{{ $filters.formatDate(data.validDt) }}</td>
												<td data-th="到期日">{{ $filters.formatDate(data.expireDt) }}</td>
												<td data-th="可否提供給客戶">{{ data.showCusName }}</td>
												<td data-th="緊急程度">{{ data.priorityName }}</td>
												<td data-th="發行機構">{{ data.issuerList?.map((item) => item.issuerName).join('；') }}</td>
												<td data-th="檢視" class="text-center">
													<button type="button" class="btn btn-dark btn-icon" @click="viewDoc(data)" title="檢視">
														<i class="bi bi-search"></i>
													</button>
												</td>
												<td data-th="相關附件" class="text-center">
													<a v-for="file in data.fileList" href="#" @click="viewFile(file.docFileId)">{{
														file.showName
													}}</a>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Modal -->
	<vue-modal :is-open="isOpenModal" :before-close="closeModal">
		<template v-slot:content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">文件檢視</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()"><i class="bi bi-arrows-fullscreen"></i></button>
						<button type="button" class="btn-close" @click.prevent="props.close()" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="caption">文件分類</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p">文件類型</th>
									<td>{{ selectedItem.docCatName }}</td>
								</tr>
							</tbody>
						</table>
						<div class="caption">文件內容</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p">文件標題</th>
									<td>{{ selectedItem.docName }}</td>
								</tr>
								<tr>
									<th>生效日</th>
									<td>{{ $filters.formatDate(selectedItem.validDt) }}</td>
								</tr>
								<tr>
									<th>到期日</th>
									<td>{{ $filters.formatDate(selectedItem.expireDt) }}</td>
								</tr>
								<tr>
									<th>緊急程度</th>
									<td>{{ selectedItem.priorityName }}</td>
								</tr>
								<tr>
									<th>可否提供給客戶</th>
									<td>{{ selectedItem.showCusName }}</td>
								</tr>
								<tr>
									<th>摘要</th>
									<td>{{ selectedItem.docDesc }}</td>
								</tr>
								<tr>
									<th>附加文檔</th>
									<td>
										<a v-for="file in selectedItem.fileInfo" href="#" @click="viewFile(file.docFileId)" class="link-underline">{{
											file.showName
										}}</a
										><br />
									</td>
								</tr>
								<tr>
									<th>發行機構</th>
									<td>{{ selectedItem.issuerInfo?.map((item) => item.issuerName).join('、') }}</td>
								</tr>
							</tbody>
						</table>
						<div class="caption">維護資訊</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p">建立人員</th>
									<td>{{ selectedItem.createBy }}</td>
								</tr>
								<tr>
									<th>建立日期</th>
									<td>{{ $filters.formatDate(selectedItem.createDt) }}</td>
								</tr>
								<tr>
									<th>最後維護人員</th>
									<td>{{ selectedItem.modifyBy }}</td>
								</tr>
								<tr>
									<th>最後維護日期</th>
									<td>{{ $filters.formatDate(selectedItem.modifyDt) }}</td>
								</tr>
							</tbody>
						</table>
					</div>

					<div class="modal-footer" id="appointmentFooter">
						<input
							name="btnClose"
							class="btn btn-white"
							id="appointmentCloseButton"
							type="button"
							value="關閉"
							@click.prevent="props.close()"
						/>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
	<!-- Modal End -->
</template>
<script>
import vueModal from '@/views/components/model.vue';
import vuePagination from '@/views/components/pagination.vue';
import dynamicTitle from '@/views/components/dynamicTitle.vue';
import _ from 'lodash';
export default {
	components: {
		vueModal,
		vuePagination,
		dynamicTitle
	},
	data: function () {
		return {
			//Data
			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: 'DOC_ID',
				direction: 'ASC'
			},
			selectedItem: {},
			//Modal
			isOpenModal: null,
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',
			buttonClass: 'btn-expand'
		};
	},
	computed: {
		userInfo: function () {
			return this.$store.getters['userInfo/info'];
		}
	},
	watch: {},
	mounted: function () {
		var self = this;
		this.getPageData();
	},
	methods: {
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData();
		},
		getPageData: async function () {
			var page = this.pageable.page;
			var self = this;
			var url = _.toPageUrl('', page, self.pageable);
			const ret = await self.$api.getDocIssuersPageData({ queryString: url });
			self.pageData = ret.data;
		},
		viewDoc: async function (docIssuer) {
			var self = this;
			const ret = await self.$api.getViewSelDoc({
				docCat: 'ISSUER',
				docId: docIssuer.docId
			});
			if (!ret.data?.length > 0) return;
			self.selectedItem = ret.data[0];
			self.isOpenModal = true;
		},
		viewFile: function (fileId) {
			var self = this;
			var url = self.config.apiPath + '/com/fileView?fileType=GenDocFiles&fileId=' + fileId;

			var previewWindow = window.open(url, '_blank');
			previewWindow.addEventListener('beforeunload', () => {
				URL.revokeObjectURL(url);
			});
		},
		changeModalSize: function () {
			var self = this;
			if (self.modalClass === 'modal-dialog modal-lg modal-dialog-centered') {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered fullscreen';
				self.buttonClass = 'btn-expand mini';
			} else {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered';
				self.buttonClass = 'btn-expand';
			}
		},
		closeModal: function () {
			var self = this;
			self.isOpenModal = false;
		},
		openModal: function () {
			var self = this;
			self.isOpenModal = true;
		}
	}
};
</script>
