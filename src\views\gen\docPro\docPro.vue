<template>
	<dynamicTitle />
	<div>
		<div class="row">
			<div class="col-12">
				<vue-bi-tabs :menu-code="'M41-00'" @change-tab="changeTab">
					<template #default="{ id }">
						<component :is="id"></component>
					</template>
				</vue-bi-tabs>
			</div>
		</div>
	</div>
</template>
<script>
import dynamicTitle from '@/views/components/dynamicTitle.vue';
import vueBiTabs from '@/views/components/biTabs.vue';
import vueDocPro from './include/docPro.vue';
import vueDocProQuery from './include/docProQuery.vue';
export default {
	components: {
		vueBiTabs,
		dynamicTitle,
		vueDocProQuery,
		vueDocPro
	},
	data: function () {
		return {
			//畫面顯示用參數
			customTitle: null,
			//畫面邏輯判斷用參數
			tabCode: 0,
			tabs: [
				{ tabCode: 1, label: '文件維護' },
				{ tabCode: 2, label: '文件查詢' }
			]
		};
	},
	mounted: function () {
		var self = this;
		self.tabCode = self.tabs[0].tabCode;
		self.customTitle = self.tabs[0].label;
	},
	methods: {
		changeTab: function (tabCode) {
			var self = this;
			self.tabCode = tabCode;
			for (var i = 0; i < self.tabs.length; i++) {
				var tab = self.tabs[i];
				if (tab.tabCode == tabCode) {
					self.customTitle = tab.label;
				}
			}
		}
	}
};
</script>
