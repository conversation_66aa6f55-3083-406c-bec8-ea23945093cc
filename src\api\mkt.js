import request from '@/utils/request';
const apiPath = import.meta.env.VITE_API_URL_V1;

export function getActivityEventDetail({ eventId }) {
	return request({
		url: apiPath + '/mkt/queryActivityEventDetail',
		method: 'get',
		data: {
			eventId
		}
	});
}
export function downloadMktOtherFileApi({ fileId }) {
	const url = apiPath + '/com/fileView?fileType=MktFiles&fileId=' + fileId;
	var previewWindow = window.open(url, '_blank');
	previewWindow.addEventListener('beforeunload', () => {
		URL.revokeObjectURL(url);
	});
}
