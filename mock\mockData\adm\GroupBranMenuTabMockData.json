{"status": 200, "data": [{"code": "M00-070", "name": "分行分區設定", "parentCode": "M00-07", "url": "vue-group-bran-fp-upload", "order": 1, "leafYn": "Y", "leaf": false}, {"code": "M00-071", "name": "分行分區查詢", "parentCode": "M00-07", "url": "vue-group-bran-fp-search", "order": 2, "leafYn": "Y", "leaf": false}], "timestamp": "2025/04/14", "sqlTracer": [{"data": [{"code": "M00-070", "name": "分行分區設定", "parentCode": "M00-07", "url": "vue-group-bran-fp-upload", "order": 1, "leafYn": "Y", "leaf": false}, {"code": "M00-071", "name": "分行分區查詢", "parentCode": "M00-07", "url": "vue-group-bran-fp-search", "order": 2, "leafYn": "Y", "leaf": false}], "sqlInfo": " SELECT DISTINCT      M<PERSON><PERSON><PERSON>_CODE CODE,      <PERSON><PERSON>ME<PERSON>_NAME NAME,      M.PARENT_MENU_CODE PARENT_CODE,      M<PERSON>SHOW_ORDER 'ORDER',      M.MENU_ICON ICON,      <PERSON><PERSON>TAB_YN LEAF_YN,      AP.PROG_CLASSNAME URL  FROM ADM_MENUS M      JOIN ADM_ROLE_MENU_MAP ARMM ON ARMM.MENU_CODE = M.MENU_CODE      LEFT JOIN ADM_PROGS AP ON AP.PROG_CODE = M.PROG_CODE  WHERE ARMM.ROLE_CODE = :roleCode    AND M.ACTIVE_YN = 'Y'    AND M.PARENT_MENU_CODE = :parentMenuCode    AND M.TAB_YN = :tabYn ORDER BY M.SHOW_ORDER ASC, M.MENU_CODE ASC ,class com.bi.frame.menu.model.MenuTree,{roleCode=98, tabYn=Y, parentMenuCode=M00-04}"}]}