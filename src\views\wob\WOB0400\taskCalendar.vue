<template>
	<dynamic-title></dynamic-title>
	<!--頁面內容 start-->
	<div class="container-fluid">
		<div v-if="!isShowSummary">
			<!--頁面內容 start-->

			<!--行事曆 Start-->
			<div class="mb-3">
				<button
					id="cal-visit"
					class="btn btn-week cal-visit me-2"
					type="button"
					v-if="userInfo?.roleType === 'HQ'"
					@click="changeDisplayParam('A')"
				>
					<p class="mb-0 ellipsis">約訪事件</p>
				</button>
				<button id="cal-bank" class="btn btn-week cal-bank me-2" type="button" @click="changeDisplayParam('P')">
					<p class="mb-0 ellipsis">個人記事</p>
				</button>
				<button id="porduct-expire" class="btn btn-week cal-expire me-2" type="button" @click="changeDisplayParam('PRO')">
					<p class="mb-0 ellipsis">商品到期</p>
				</button>
				<button
					id="cal-FC"
					class="btn btn-week cal-FC me-2"
					type="button"
					v-if="userInfo?.roleType === 'HQ'"
					@click="changeDisplayParam('C')"
				>
					<p class="mb-0 ellipsis">客戶重要日子</p>
				</button>
				<button
					id="cal-rec"
					class="btn btn-week cal-rec me-2"
					type="button"
					v-if="userInfo?.roleType === 'HQ'"
					@click="changeDisplayParam('CNT')"
				>
					<p class="mb-0 ellipsis">聯繫紀錄</p>
				</button>
			</div>
			<div id="calendar" ref="calendar"></div>
			<!--行事曆 END-->

			<!-- 到期商品檢視 -->
			<vue-modal :is-open="isOpenModal['fund']" :before-close="closeModal('fund')">
				<template v-slot:content="props">
					<vue-fund-modal
						ref="fundModalRef"
						:is-open-fund-modal="isOpenModal['fund']"
						:fin-req-code-menu="finReqCodeMenu"
						:download-file="downloadFile"
						:download-other-file="downloadOtherFile"
						:close="props.close"
					></vue-fund-modal>
				</template>
			</vue-modal>
			<vue-modal :is-open="isOpenModal['etf']" :before-close="closeModal('etf')">
				<template v-slot:content="props">
					<vue-etf-modal
						ref="etfModalRef"
						:is-open-etf-modal="isOpenModal['etf']"
						:fin-req-code-menu="finReqCodeMenu"
						:pro-price-range-menu="proPriceRangeMenu"
						:download-file="downloadFile"
						:download-other-file="downloadOtherFile"
						:close="props.close"
					></vue-etf-modal>
				</template>
			</vue-modal>
			<vue-modal :is-open="isOpenModal['bond']" :before-close="closeModal('bond')">
				<template v-slot:content="props">
					<vue-bond-modal
						ref="bondModalRef"
						:is-open-bond-modal="isOpenModal['bond']"
						:fin-req-code-menu="finReqCodeMenu"
						:pro-price-range-menu="proPriceRangeMenu"
						:download-file="downloadFile"
						:download-other-file="downloadOtherFile"
						:close="props.close"
					></vue-bond-modal>
				</template>
			</vue-modal>
			<!-- <vue-modal :is-open="isOpenModal['pfd']" :before-close="closeModal('pfd')">
				<template v-slot:content="props">
					<vue-pfd-modal
						ref="pfdModalRef"
						:is-open-pfd-modal="isOpenModal['pfd']"
						:fin-req-code-menu="finReqCodeMenu"
						:close="props.close"
					></vue-pfd-modal>
				</template>
			</vue-modal> -->
			<vue-modal :is-open="isOpenModal['sp']" :before-close="closeModal('sp')">
				<template v-slot:content="props">
					<vue-sp-modal
						ref="spModalRef"
						:is-open-structured-product-modal="isOpenModal['sp']"
						:fin-req-code-menu="finReqCodeMenu"
						:pro-price-range-menu="proPriceRangeMenu"
						:download-file="downloadFile"
						:download-other-file="downloadOtherFile"
						:close="props.close"
					></vue-sp-modal>
				</template>
			</vue-modal>
			<vue-modal :is-open="isOpenModal['ins']" :before-close="closeModal('ins')">
				<template v-slot:content="props">
					<vue-ins-modal
						ref="insModalRef"
						:is-open-ins-modal="isOpenInsModal"
						:fin-req-code-menu="finReqCodeMenu"
						:download-file="downloadFile"
						:download-other-file="downloadOtherFile"
						:close="props.close"
					></vue-ins-modal>
				</template>
			</vue-modal>

			<!-- Modal 新增行事曆記事 -->
			<vue-wob-new-task-modal
				ref="newTaskModal"
				:get-calendar-tasks="getCalendarTasks"
				:get-select-reuse-word="getSelectReuseWord"
			></vue-wob-new-task-modal>
			<!-- Modal 2 檢視個人記事 -->
			<vue-wob-personal-task-modal
				:rec-code="personalTaskRecCode"
				:get-calendar-tasks="getCalendarTasks"
				ref="personalTaskModal"
			></vue-wob-personal-task-modal>
			<!-- Modal 5 檢視約訪紀錄 -->
			<vue-wob-appointment-task-modal
				:rec-code="apptTaskRecCode"
				:role-show-edit-btn="roleShowEditBtn"
				:get-calendar-tasks="getCalendarTasks"
				:user-info="userInfo"
				ref="appointmentTaskModal"
			></vue-wob-appointment-task-modal>
			<!-- Modal 7 檢視顧客重要日子 -->
			<vue-wob-cus-momory-task-modal
				:cus-memory-task-rec-code="cusMemoryTaskRecCode"
				:get-calendar-tasks="getCalendarTasks"
				ref="cusMemoryTaskModal"
			>
			</vue-wob-cus-momory-task-modal>
			<!-- Model 檢視聯繫紀錄 -->
			<vue-wob-contact-task-modal
				:rec-code="contactTaskRecCode"
				:get-calendar-tasks="getCalendarTasks"
				:get-select-reuse-word="getSelectReuseWord"
				:user-info="userInfo"
				ref="contactTaskModal"
			></vue-wob-contact-task-modal>

			<vue-cus-summary ref="cusSummaryRef" :set-is-show-summary="setIsShowSummary"></vue-cus-summary>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import dynamicTitle from '@/views/components/dynamicTitle.vue';
import vueModal from '@/views/components/model.vue';
import vueWobNewTaskModal from './include/newTaskModal.vue';
import vueWobPersonalTaskModal from './include/personalTaskModal.vue';
import vueWobAppointmentTaskModal from './include/appointmentTaskModal.vue';
import vueWobCusMomoryTaskModal from './include/cusMemoryTaskModal.vue';
import vueWobContactTaskModal from './include/contactTaskModal.vue';

import vueFundModal from '@/views/pro/PRO0101/include/fundModal.vue';
import vueEtfModal from '@/views/pro/PRO0101/include/etfModal.vue';
import vueBondModal from '@/views/pro/PRO0101/include/bondModal.vue';
import vueSpModal from '@/views/pro/PRO0101/include/spModal.vue';
import vueInsModal from '@/views/pro/PRO0101/include/insModal.vue';

//行事曆套件
import { Calendar } from '@fullcalendar/core';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';

export default {
	components: {
		vueWobNewTaskModal,
		vueWobPersonalTaskModal,
		vueWobAppointmentTaskModal,
		vueWobCusMomoryTaskModal,
		vueWobContactTaskModal,
		vueModal,
		vueFundModal,
		vueEtfModal,
		vueBondModal,
		vueSpModal,
		vueInsModal,
		dynamicTitle
	},
	data: function () {
		return {
			wobReuseWords: null,
			// searchScope: 'SELF',
			startDate: null,
			endDate: null,
			loginRoleType: null,
			range: 'month',
			//檢視Modal用參數
			personalTaskRecCode: null, // 個人記事
			contactTaskRecCode: null, // 聯繫紀錄
			apptTaskRecCode: null, // 約訪事件
			cusMemoryTaskRecCode: null, // 客戶重要日子

			roleShowEditBtn: true,
			//畫面顯示用參數
			calendarTasks: null,
			events: [],
			displayEvents: [],
			displayParam: '',

			fullCalendar: null, // 日曆元件
			fullCalendarStartDt: null, // 日曆元件顯示起始日期,
			//畫面控制參數
			isShowSummary: false,
			isShowSubordinate: false,

			isOpenModal: {
				pro: false
			}
		};
	},
	watch: {
		userInfo: {
			immediate: true,
			handler: function (newVal, oldVal) {
				var self = this;
				if (newVal) {
					self.loginRoleType = newVal.roleType;
					if (
						newVal.roleCode == 'A05' ||
						newVal.roleCode == 'A06' ||
						newVal.roleCode == 'A08' ||
						newVal.roleCode == 'A09' ||
						newVal.roleCode == 'A10' ||
						newVal.roleCode == 'A11' ||
						newVal.roleCode == 'A12'
					) {
						self.roleShowEditBtn = false;
					} else {
						self.roleShowEditBtn = true;
					}
					self.showSubordinate(newVal.roleType);
				}
			}
		}
		// searchScope: function () {
		// 	this.getCalendarTasks();
		// 	this.getSelectReuseWord();
		// }
	},
	computed: {
		userInfo: function () {
			return this.$store.getters['userInfo/info'].data;
		},
		isShowPageTitle: function () {
			return !this.isShowSummary;
		}
	},
	mounted: function () {
		var self = this;
		self.getCalendarTasks();
		self.getSelectReuseWord();
	},
	methods: {
		closeModal(modalName) {
			var self = this;
			self.isOpenModal[modalName] = false;
		},
		// 取得行事曆資訊
		getCalendarTasks: function () {
			var self = this;

			var calendar = self.fullCalendar;
			if (_.isNil(calendar)) {
				calendar = new Calendar(this.$refs.calendar, {
					plugins: [dayGridPlugin, timeGridPlugin],
					initialView: 'dayGridMonth'
				});
			}
			self.startDate = self.formatDate(calendar.view.currentStart);
			self.endDate = self.formatDate(calendar.view.currentEnd);

			self.$api
				.getCalendarTasksApi({
					startDate: _.formatDate(self.startDate),
					endDate: _.formatDate(self.endDate)
				})
				.then(function (ret) {
					self.calendarTasks = ret.data;
					// empty events
					self.events = [];

					// 個人記事
					if (self.calendarTasks.personalTasks) {
						self.calendarTasks.personalTasks.forEach(function (item) {
							var event = {};
							event.id = 'P';

							event.title = item.title;
							event.recCode = item.recCode;
							event.classNames = ['btn-week', 'cal-bank'];

							if (_.isBlank(item.nextRemindDt)) {
								event.start = item.nextRemindDt;
							} else {
								event.start = item.nextRemindDt + 'T' + item.nextRemindTime;
							}
							event.eventName = '個人記事';
							event.item = item;
							self.events.push(event);
						});
					}
					// 約訪事件
					if (self.calendarTasks.appointmentTasks) {
						self.calendarTasks.appointmentTasks.forEach(function (item) {
							var event = {};
							event.id = 'A';
							event.title = item.title;
							event.recCode = item.recCode;
							event.classNames = ['btn-week', 'cal-visit'];
							if (_.isBlank(item.nextRemindTime)) {
								event.start = item.nextRemindDt;
							} else {
								event.start = item.nextRemindDt + 'T' + item.nextRemindTime;
							}
							if (_.isBlank(item.nextRemindTimee)) {
								event.end = item.nextRemindDte;
							} else {
								event.end = item.nextRemindDte + 'T' + item.nextRemindTimee;
							}
							event.eventName = '約訪事件';
							event.item = item;
							self.events.push(event);
						});
					}
					// 客戶重要日子
					if (self.calendarTasks.cusMemoryTasks) {
						self.calendarTasks.cusMemoryTasks.forEach(function (item) {
							var event = {};
							event.id = 'C';
							event.title = item.cusName + ' ' + item.note;
							event.recCode = item.id;
							event.classNames = ['btn-week', 'cal-FC'];
							if (!_.isBlank(item.dateDt)) {
								event.start = item.dateDt;
							}
							event.eventName = '客戶重要日子';
							event.item = item;
							self.events.push(event);
						});
					}
					// 聯繫紀錄
					if (self.calendarTasks.contactTasks) {
						self.calendarTasks.contactTasks.forEach(function (item) {
							var event = {};
							event.id = 'CNT';
							event.title = item.cusName + ' ' + item.title;
							event.recCode = item.recCode;
							event.classNames = ['btn-week', 'cal-rec'];
							if (_.isBlank(item.nextRemindTime)) {
								event.start = item.nextRemindDt;
							} else {
								event.start = item.nextRemindDt + 'T' + item.nextRemindTime;
							}
							event.eventName = '聯繫紀錄';
							event.item = item;
							self.events.push(event);
						});
					}
					// 到期商品通知
					if (self.calendarTasks.expiredTasks) {
						self.calendarTasks.expiredTasks.forEach(function (item) {
							var event = {};
							event.id = 'PRO';
							event.title = item.bankProCode + ' ' + item.proName + '到期';
							event.start = item.expireDt;
							event.proCode = item.proCode;
							event.pfcatCode = item.pfcatCode;
							event.classNames = ['btn-week', 'cal-expire'];
							event.eventName = '商品到期';
							event.item = item;
							self.events.push(event);
						});
					}
					self.displayEvents = self.events;

					if (self.fullCalendar != null) {
						self.reloadView();
					} else {
						self.renderCalendar();
					}
				});
		},
		reloadView: function () {
			var self = this;
			_.forEach(self.fullCalendar.getEventSources(), function (e) {
				e.remove();
			});
			self.fullCalendar.addEventSource(self.displayEvents);
		},
		renderCalendar: function () {
			var self = this;
			var calendarEl = document.getElementById('calendar');

			self.fullCalendar = new Calendar(calendarEl, {
				plugins: [dayGridPlugin, timeGridPlugin],
				initialDate: self.fullCalendarStartDt,
				nowIndicator: true,
				headerToolbar: {
					right: 'prev,next today addnew',
					center: 'title',
					left: 'dayGridMonth,dayGridWeek,timeGridDay'
				},
				customButtons: {
					addnew: {
						text: '新增工作項目',
						click: function () {
							self.$refs.newTaskModal.show();
						}
					},
					prev: {
						click: function () {
							self.fullCalendar.prev();
							self.fullCalendarStartDt = self.fullCalendar.view.currentStart;
							self.getCalendarTasks();
						}
					},
					next: {
						click: function () {
							self.fullCalendar.next();
							self.fullCalendarStartDt = self.fullCalendar.view.currentStart;
							self.getCalendarTasks();
						}
					},
					today: {
						text: '今日',
						click: function () {
							self.fullCalendar.today();
							self.fullCalendar.changeView('dayGridMonth');
							self.fullCalendarStartDt = self.fullCalendar.view.currentStart;
							self.getCalendarTasks();
						}
					},
					dayGridMonth: {
						text: '月',
						click: function () {
							self.range = 'month';
							self.fullCalendar.changeView('dayGridMonth');
							self.getCalendarTasks();
						}
					},
					dayGridWeek: {
						text: '週',
						click: function () {
							self.range = 'week';
							self.fullCalendar.changeView('dayGridWeek');
							self.getCalendarTasks();
						}
					},
					timeGridDay: {
						text: '日',
						click: function () {
							self.range = 'day';
							self.fullCalendar.changeView('timeGridDay');
							self.getCalendarTasks();
						}
					}
				},
				navLinks: true,
				editable: true,
				selectable: true,
				selectMirror: true,
				dayMaxEvents: 5,
				events: self.displayEvents,
				eventDidMount: function (info) {
					if (info.event.title.length > 20) {
						info.el.querySelector('.fc-event-title').innerHTML = info.event.title.substring(0, 20) + '...';
					}
					if (info.event.end && info.event.start.getDate() !== info.event.end.getDate()) {
						info.el.classList.add('multi-day-event');
					}
				},

				eventClick: function (info) {
					if ('P' == info.event.id) {
						console.log('點 個人記事');
						// 個人記事
						// self.personalTaskRecCode = info.event.extendedProps.recCode;
						// self.$refs.personalTaskModal.modalStates.modal1 = true;
						// 在開啟 modal 前，先強制關閉
						self.$refs.personalTaskModal.modalStates.modal1 = false;
						self.personalTaskRecCode = info.event.extendedProps.recCode;
						self.$nextTick(() => {
							self.$refs.personalTaskModal.modalStates.modal1 = true;
						});
					} else if ('A' == info.event.id) {
						console.log('點 約訪事件');
						//約訪事件
						// self.apptTaskRecCode = info.event.extendedProps.recCode;
						// self.$refs.appointmentTaskModal.modalStates.modal1 = true;
						self.$refs.appointmentTaskModal.modalStates.modal1 = false;
						self.apptTaskRecCode = info.event.extendedProps.recCode;
						self.$nextTick(() => {
							self.$refs.appointmentTaskModal.modalStates.modal1 = true;
						});
					} else if ('C' == info.event.id) {
						console.log('點 客戶重要日子');
						// 客戶重要日子
						// self.cusMemoryTaskRecCode = info.event.extendedProps.recCode;
						// self.$refs.cusMemoryTaskModal.modalStates.modal1 = true;
						self.$refs.cusMemoryTaskModal.modalStates.modal1 = false;
						self.cusMemoryTaskRecCode = info.event.extendedProps.recCode;
						self.$nextTick(() => {
							self.$refs.cusMemoryTaskModal.modalStates.modal1 = true;
						});
					} else if ('CNT' == info.event.id) {
						console.log('點 聯繫紀錄');
						// 聯繫紀錄
						// self.contactTaskRecCode = info.event.extendedProps.recCode;
						// self.$refs.contactTaskModal.modalStates.modal1 = true;
						self.$refs.contactTaskModal.modalStates.modal1 = false;
						self.contactTaskRecCode = info.event.extendedProps.recCode;
						self.$nextTick(() => {
							self.$refs.contactTaskModal.modalStates.modal1 = true;
						});
					} else if ('PRO' == info.event.id) {
						//到期商品
						var proCode = info.event.extendedProps.proCode;
						var pfcatCode = info.event.extendedProps.pfcatCode;

						console.log('點 商品到期pfcatCode:' + pfcatCode);
						switch (pfcatCode) {
							case 'FUND':
								self.fundModalHandler(proCode, pfcatCode);
								break;
							case 'ETF':
								self.etfModalHandler(proCode, pfcatCode);
								break;
							case 'FB':
								self.bondModalHandler(proCode, pfcatCode);
								break;
							case 'SP':
								self.spModalHandler(proCode, pfcatCode);
								break;
							case 'INS':
								self.insModalHandler(proCode, pfcatCode);
								break;
							// case 'DCI':
							// 	self.dciModalHandler(proCode, pfcatCode);
							// 	break;
							// case 'SEC':
							// 	self.secModalHandler(proCode, pfcatCode);
							// 	break;
							// case 'PFD':
							// 	self.pfdModalHandler(proCode, pfcatCode);
							// 	break;
						}
					}
				}
			});
			self.fullCalendar.render();
		},
		changeDisplayParam: function (displayParam) {
			var self = this;
			if (self.displayParam === displayParam) {
				self.displayParam = '';
				self.displayEvents = self.events;
			} else {
				self.displayParam = displayParam;
				self.displayEvents = self.events.filter((event) => event.id !== displayParam);
			}
			self.renderCalendar();
		},
		formatDate: function (date) {
			var d = new Date(date),
				month = '' + (d.getMonth() + 1),
				day = '' + d.getDate(),
				year = d.getFullYear();

			if (month.length < 2) month = '0' + month;
			if (day.length < 2) day = '0' + day;

			return [year, month, day].join('-');
		},
		setIsShowSummary: function (val) {
			var self = this;
			self.isShowSummary = val;
		},
		doViewSummary: function (cusCode) {
			var self = this;
			self.isShowSummary = true;
			self.$refs.cusSummaryRef.initPage(cusCode);
		},
		getSelectReuseWord: function () {
			var self = this;
			self.$api.getReuseWordsApi().then(function (ret) {
				var result = ret.data;
				var needAppend = 5 - result.length;
				if (result.length < 5) {
					for (var i = 0; i < needAppend; i++) {
						result.push({ words: '', wordsId: ret.data.length + i + 1 });
					}
				}
				self.$refs.newTaskModal.selectReuseWord = result;
				self.$refs.newTaskModal.wobReuseWords = result;
				self.$refs.contactTaskModal.selectReuseWord = result;
				self.$refs.contactTaskModal.wobReuseWords = result;
			});
		},
		showSubordinate: function (roleType) {
			if (roleType != 'RM') {
				this.isShowSubordinate = true;
			}
		},
		// 到期商品資訊顯示控制
		fundModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.$refs.fundModalRef.getProInfo(proCode, pfcatCode);
			self.isOpenModal['fund'] = true;
		},
		etfModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.$refs.etfModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.etfModalRef.getEtfDetail(proCode); // 商品資訊/基金資料
			self.$refs.etfModalRef.getEtfStockHold(proCode); // 商品資訊/ETF持股
			self.$refs.etfModalRef.getEtfPrice(proCode); // 商品資訊/價格分析資料
			self.$refs.etfModalRef.getPricesChartData(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.etfModalRef.getEtfProfileNameMenu(proCode); // 商品資訊/績效分析
			self.$refs.etfModalRef.getEtfProfileBenchmarksMenu(); // 商品資訊/績效分析
			self.isOpenModal['etf'] = true;
		},
		bondModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.$refs.bondModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.bondModalRef.getBondPriceAna(proCode);
			self.$refs.bondModalRef.getPricesChartData(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.bondModalRef.getBondPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal['bond'] = true;
		},
		// pfdModalHandler: function (proCode, pfcatCode) {
		// 	var self = this;
		// 	self.$refs.pfdModalRef.getProInfo(proCode, pfcatCode);
		// 	//			self.$refs.pfdModalRef.getEtfDetail(proCode); // 商品資訊/基金資料
		// 	self.isOpenModal['pfd'] = true;
		// },
		spModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.isOpenModal['sp'] = true;
			self.$refs.spModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.spModalRef.getSpPriceAna(proCode); // 商品資訊/淨值分析資料
			self.$refs.spModalRef.getSpNets(proCode, 'Y', -1.0); // 商品資訊/淨值分析圖表
			self.$refs.spModalRef.getSpPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
		},
		insModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.$refs.insModalRef.getProInfo(proCode, pfcatCode);
			self.isOpenModal['ins'] = true;
		}
		// dciModalHandler: function (proCode, pfcatCode) {
		// 	var self = this;
		// 	self.$refs.dciModalRef.getProInfo(proCode, pfcatCode); //商品基本資料
		// 	self.$refs.dciModalRef.getDciPriceAna(proCode, 'M', -1.0); // 商品資訊/價格分析資料
		// 	self.$refs.dciModalRef.getDciNets(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
		// 	self.$refs.dciModalRef.getDciPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
		// 	self.isOpenModal['dci'] = true;
		// },
		// secModalHandler: function (proCode, pfcatCode) {
		// 	var self = this;
		// 	self.isOpenModal['sec'] = true;
		// 	self.$refs.secModalRef.getProInfo(proCode, pfcatCode);
		// 	self.$refs.secModalRef.getSecPriceAna(proCode); // 商品資訊/淨值分析資料
		// 	self.$refs.secModalRef.getSecNets(proCode, 'Y', -1.0); // 商品資訊/淨值分析圖表
		// 	self.$refs.secModalRef.getSecPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
		// }
	}
};
</script>
