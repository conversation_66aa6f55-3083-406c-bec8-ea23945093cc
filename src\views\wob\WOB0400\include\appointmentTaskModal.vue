<template>
	<div>
		<!-- Modal 1  -->
		<vue-modal :is-open="modalStates.modal1" :before-close="() => closeModal('modal1')">
			<template v-slot:content="props">
				<div class="modal-dialog modal-lg modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">約訪</h4>
							<button type="button" class="btn-close" @click.prevent="closeModal('modal1')" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<div class="alert alert-info" role="alert">
								[ {{ tdRecTask.cusName }} ] {{ tdRecTask.nextRemindDt }}, {{ tdRecTask.nextRemindTime }}
							</div>
							<div class="card card-form">
								<div class="card-header">
									<h4>約訪紀錄</h4>
								</div>
								<table class="biv-table table table-bordered table-RWD table-horizontal-RWD">
									<tbody>
										<tr>
											<th class="w20">到期通知設定</th>
											<td class="w80">{{ tdRecTask.advNceName }}</td>
										</tr>
										<tr>
											<th>訪談目的</th>
											<td>{{ tdRecTask.visitPurName }}</td>
											<th>訪談方式</th>
											<td>{{ tdRecTask.visitAprName }}</td>
										</tr>
										<tr>
											<th>約訪主旨</th>
											<td>{{ tdRecTask.title }}</td>
										</tr>
										<tr>
											<th>約訪內容</th>
											<td>{{ tdRecTask.content }}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div class="modal-footer">
							<button name="btnClose" type="button" class="btn btn-white" @click.prevent="closeModal('modal1')"
								aria-label="Close">
								關閉
							</button>
							<button name="btnDelete" type="button" class="btn btn-danger" @click="deleteTdRec()">刪除</button>
							<button name="btnModify" type="button" class="btn btn-primary" @click.prevent="doUpdate()">修改</button>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- Modal 1 end -->

		<!-- Modal 2 約訪事件維護 -->
		<vue-modal :is-open="modalStates.modal2" :before-close="() => closeModal('modal2')">
			<template v-slot:content="props">
				<div class="modal-dialog modal-lg modal-dialog-scrollable">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">約訪</h4>
							<button type="button" class="btn-close" @click.prevent="closeModal('modal2')" aria-label="Close"></button>
						</div>
						<div class="modal-body overflow-scroll">
							<div class="card-clientCard">
								<div class="card shadow-none mb-3">
									<table>
										<tbody>
											<tr>
												<td width="10%" class="clientCard-icon">
													<div class="avatar avatar-male">
														<img :src="getImgURL('avatar', 'man-1.png')" class="rounded-circle bg-info" />
														<!-- <img th:src="@{/images/avatar/man-3.png}" class="rounded-circle bg-info" v-if="gender =='F'">-->
													</div>
													<h5 class="mb-0">{{ cusInfo.cusName || '--' }}</h5>
												</td>
												<td width="90%">
													<div class="caption tx-black">
														<sapn class=" ">最近通聯日期：{{ $filters.formatDate(cusInfo.lastConnectionDt) || '--' }}</sapn>
													</div>
													<div class="row">
														<div class="col-lg-4">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-clipboard-data mg-xl-e-5-f"></i>投資屬性：{{
																		cusInfo.rankName || '--'
																	}}
																</li>
																<li><i class="bi bi-gift mg-xl-e-5-f"></i>生日：{{ cusInfo.birth || '--' }}</li>
																<li>
																	<i class="bi bi-envelope mg-xl-e-5-f"></i>電子郵件：{{ cusInfo.email || '--' }}
																</li>
															</ul>
														</div>
														<div class="col-lg-3">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-house-door mg-xl-e-5-f"></i>聯絡電話(住)：{{
																		cusInfo.phoneH || '--'
																	}}
																</li>
																<li>
																	<i class="bi bi-building mg-xl-e-5-f"></i>聯繫電話(公司)：{{
																		cusInfo.phoneO || '--'
																	}}
																</li>
																<li>
																	<i class="bi bi-telephone mg-xl-e-5-f"></i>聯絡電話(行動)：{{
																		cusInfo.phoneM || '--'
																	}}
																</li>
															</ul>
														</div>
														<div class="col-lg-5">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-journal-medical mg-xl-e-5-f"></i>未成年辦理財富管理業務與投資商品同意書：
																	<span :class="cusInfo.childInvYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ cusInfo.childInvYn === 'Y' ? '是' : '否' }}
																	</span>
																</li>
																<li>
																	<i class="bi bi-journal-text mg-xl-e-5-f"></i>特定金錢信託客戶投資有價證券推介同意書：
																	<span :class="cusInfo.specRecommYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ cusInfo.specRecommYn === 'Y' ? '是' : '否' }}
																	</span>
																</li>
																<li>
																	<i class="bi bi-journal-bookmark mg-xl-e-5-f"></i>財富特定客戶(不得主動推介)：
																	<span :class="cusInfo.specCusYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ cusInfo.specCusYn === 'Y' ? '是' : '否' }}
																	</span>
																</li>
															</ul>
														</div>
													</div>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
							<!-- 約訪紀錄 -->

							<div class="card card-form shadow-none">
								<div class="card-header">
									<h4>約訪行程</h4>
									<span class="tx-square-bracket">為必填欄位</span>
								</div>
								<div class="card-body">
									<vue-form v-slot="{ errors, validate }" ref="appointmentTask">
										<div class="row g-3 align-items-end">
											<div class="col-lg-6 col-xl-6">
												<label class="form-label tx-require">約訪日期</label>
												<vue-field type="date" name="visitDate" class="form-control" v-model="visitDate"
													:class="{ 'is-invalid': errors.visitDate }" label="約訪日期" rules="required"></vue-field>
												<div>
													<span class="text-danger" v-show="errors.visitDate">{{ errors.visitDate }}</span>
												</div>
											</div>
											<div class="col-lg-3 col-xl-3">
												<label class="form-label tx-require">約訪時間</label>
												<div class="input-group">
													<vue-field as="select" class="form-select" id="visitHour" name="visitHour" v-model="visitHour"
														:class="{ 'is-invalid': errors.visitHour }" rules="required" label="約訪時間">
														<option value="00">00</option>
														<option value="01">01</option>
														<option value="02">02</option>
														<option value="03">03</option>
														<option value="04">04</option>
														<option value="05">05</option>
														<option value="06">06</option>
														<option value="07">07</option>
														<option value="08">08</option>
														<option value="09">09</option>
														<option value="10">10</option>
														<option value="11">11</option>
														<option value="12">12</option>
														<option value="13">13</option>
														<option value="14">14</option>
														<option value="15">15</option>
														<option value="16">16</option>
														<option value="17">17</option>
														<option value="18">18</option>
														<option value="19">19</option>
														<option value="20">20</option>
														<option value="21">21</option>
														<option value="22">22</option>
														<option value="23">23</option>
													</vue-field>
													<span class="input-group-text">時</span>
													<div>
														<span class="text-danger" v-show="errors.visitHour">{{ errors.visitHour }}</span>
													</div>
													<vue-field as="select" class="form-select" id="visitMin" name="visitMin" v-model="visitMin"
														:class="{ 'is-invalid': errors.visitMin }" rules="required" label="約訪時間">
														<option selected="selected" value="00">00</option>
														<option v-for="minute in selectMinutes" :value="minute">{{ minute }}</option>
													</vue-field>
													<span class="input-group-text">分</span>
													<div>
														<span class="text-danger" v-show="errors.visitMin">{{ errors.visitMin }}</span>
													</div>
												</div>
											</div>
											<div class="col-lg-3 col-xl-3">
												<label class="form-label tx-require">訪談目的</label>
												<vue-field as="select" class="form-select" id="apptVisitPurCode" name="apptVisitPurCode"
													v-model="apptVisitPurCode" :class="{ 'is-invalid': errors.apptVisitPurCode }" rules="required"
													label="訪談目的">
													<option disabled selected value="">請選擇</option>
													<option v-for="visitPurpose in visitPurMenu" :value="visitPurpose.codeValue">
														{{ visitPurpose.codeName }}
													</option>
												</vue-field>
												<div style="height: 3px">
													<span class="text-danger" v-show="errors.apptVisitPurCode">{{ errors.apptVisitPurCode
													}}</span>
												</div>
											</div>
											<div class="col-lg-3 col-xl-3">
												<label class="form-label tx-require">訪談方式</label>
												<vue-field as="select" class="form-select" id="apptVisitAprCode" name="apptVisitAprCode"
													v-model="apptVisitAprCode" :class="{ 'is-invalid': errors.apptVisitAprCode }" rules="required"
													label="訪談方式">
													<option disabled selected value="">請選擇</option>
													<option v-for="visitType in visitAprMenu" :value="visitType.codeValue">
														{{ visitType.codeName }}
													</option>
												</vue-field>
												<div style="height: 3px">
													<span class="text-danger" v-show="errors.apptVisitAprCode">{{ errors.apptVisitAprCode
													}}</span>
												</div>
											</div>
											<div class="col-lg-3 col-xl-3">
												<label class="form-label tx-require">訪談主旨</label>
												<vue-field class="form-control" name="apptTitle" type="text" size="30" value="apptTitle"
													v-model="apptTitle" :class="{ 'is-invalid': errors.apptTitle }" label="訪談主旨"
													rules="required"></vue-field>
												<div style="height: 3px">
													<span class="text-danger" v-show="errors.apptTitle">{{ errors.apptTitle }}</span>
												</div>
											</div>
											<div class="col-lg-6 col-xl-6">
												<label class="form-label tx-require">到期通知設定</label>
												<br />
												<div class="form-check form-check-inline">
													<vue-field type="radio" name="apptAdvNceYn" class="form-check-input" id="apptAdvNce_NN"
														value="N" v-model="apptAdvNce" :class="{ 'is-invalid': errors.apptAdvNce }" rules="required"
														label="到期通知設定"></vue-field>
													<label class="form-check-label">不提前通知</label>
												</div>
												<div class="form-check form-check-inline">
													<vue-field type="radio" name="apptAdvNceYn" class="form-check-input" id="apptAdvNce_YY"
														value="Y" v-model="apptAdvNce" :class="{ 'is-invalid': errors.apptAdvNce }" rules="required"
														label="到期通知設定">
													</vue-field>
													<label class="form-check-label">提前</label>
												</div>

												<div class="d-inline-block">
													<div class="input-group">
														<vue-field class="form-control" name="apptAdvNceDay" type="text" size="30"
															v-model="apptAdvNceDay" :class="{ 'is-invalid': errors.apptAdvNceDay }" label="提前通知天/週數"
															:rules="apptAdvNce === 'Y' ? 'required' : ''">
														</vue-field>
														<div style="height: 3px">
															<span class="text-danger" v-show="errors.apptAdvNceDay">{{ errors.apptAdvNceDay }}</span>
														</div>
														<vue-field as="select" class="form-select" id="apptAdvNcePrd" name="apptAdvNcePrd"
															v-model="apptAdvNcePrd" :rules="apptAdvNce === 'Y' ? 'required' : ''"
															:class="{ 'is-invalid': errors.apptAdvNcePrd }" label="到期通知設定">
															<option value="D">日</option>
															<option value="W">週</option>
														</vue-field>
														<span class="input-group-text">通知</span>
														<div style="height: 3px">
															<span class="text-danger" v-show="errors.apptAdvNce">{{ errors.apptAdvNce }}</span>
														</div>
													</div>
												</div>
											</div>
											<div class="col-lg-12">
												<label class="form-label tx-require">訪談內容</label>
												<vue-field as="textarea" class="form-control" id="content" name="content" rows="5" cols="50"
													size="400" v-model="content" :class="{ 'is-invalid': errors.content }" rules="required"
													label="訪談內容"></vue-field>
												<div style="height: 3px">
													<span class="text-danger" v-show="errors.content">{{ errors.content }}</span>
												</div>
											</div>
											<div class="col-lg-6">
												<div class="input-group">
													<span class="input-group-text">常用句</span>
													<select name="reuseWord" class="form-select" id="reuseWord" v-model="reuseWord">
														<option v-for="selectWords in wobReuseWords" v-show="selectWords.words"
															:value="selectWords.words">
															{{ selectWords.words }}
														</option>
													</select>
													<button type="button" class="btn btn-primary" id="setContent" @click="appendReuseWord()">
														加入
													</button>
												</div>
											</div>
											<div class="col-lg-6">
												<div class="input-group">
													<span class="input-group-text">常用句設定</span>
													<input class="form-control" id="words" type="text" size="20" maxlength="20"
														v-model="newReuseWord" />
													<button type="button" class="btn btn-primary" id="wordAdd" @click="insertReuseWord()">
														新增
													</button>
													<button type="button" class="btn btn-primary" id="wordSetting"
														@click.prevent="isOpenReuseWordModal = true">
														設定
													</button>
												</div>
											</div>
										</div>
									</vue-form>
								</div>
							</div>
						</div>
						<div class="modal-footer" id="modifyTaskFooter">
							<input class="btn btn-white" id="apptEditModalCloseButton" type="button" value="關閉"
								@click.prevent="closeModal('modal2')" />
							<input class="btn btn-primary" id="btnSave" type="button" value="儲存" @click="updateTdRec('N')" />
							<input class="btn btn-primary" id="btnSaveAndClose" type="button" value="儲存並結案"
								@click="updateTdRec('Y')" />
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- Modal 2 end -->
		<vue-modal :is-open="isOpenReuseWordModal" :before-close="isOpenReuseWordModal = false">
			<template v-slot:content="props">
				<vue-cus-reuse-word-modal :close="props.close" :id="'appoReuseWordModal'" :wob-reuse-words="wobReuseWords"
					:super-modal-name="'updateAppointmentTaskModal'"></vue-cus-reuse-word-modal>
			</template>
		</vue-modal>
	</div>
</template>
<script>
import _ from 'lodash';
import { Field, Form } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import vueCusReuseWordModal from '@/views/cus/include/reuseWordModal.vue';
import { getImgURL } from '@/utils/imgURL.js';

export default {
	components: {
		vueModal,
		vueCusReuseWordModal,
		'vue-form': Form,
		'vue-field': Field
	},
	props: {
		recCode: String,
		apptTaskTdRec: Object,
		getCalendarTasks: Function,
		roleShowEditBtn: Boolean,
		userInfo: Object
	},
	data: function () {
		var minutes = [];
		for (var i = 1; i < 60; i++) {
			minutes.push(String(i).padStart(2, '0'));
		}
		return {
			isOpenReuseWordModal: false,
			modalStates: {
				modal1: false,
				modal2: false
			},
			tdRecTask: {},

			// 選單
			selectMinutes: minutes,
			visitPurMenu: [],
			visitAprMenu: [],

			// 客戶資料
			cusCode: null,
			cusInfo: {
				cusName: null,
				birth: null,
				email: null,
				phoneH: '',
				phoneO: '',
				phoneM: '',
				rankName: null,
				childInvYn: null,
				specRecommYn: null,
				specCusYn: null,
				logsCreateDt: null
			},

			//API用參數
			visitDate: null, // 約訪日期
			visitHour: null, // 約訪時間(時)
			visitMin: null, // 約訪時間(分)
			apptVisitPurCode: '', // 訪談目的
			apptVisitAprCode: '', // 訪談方式
			apptTitle: null, // 訪談主旨
			content: '', // 訪談內容
			apptAdvNce: null, // 到期通知設定
			apptAdvNceDay: null, // 到期通知天數
			apptAdvNcePrd: null, // 到期通知週期（日/週）

			//畫面顯示用參數
			showEditBtn: false,
			//常用句機制
			reuseWord: null,
			newReuseWord: null,
			wobReuseWords: null,
			editModal: null
		};
	},
	watch: {
		recCode: function (val) {
			var self = this;
			if (self.recCode) {
				self.getTdRec();
			}
		}
	},
	mounted: function () {
		var self = this;
		self.getVisitPurMenu();
		self.getVisitAprMenu();
		self.getReuseWords();
		self.setDefaultReuseWords();
		self.showEditBtn = false;
		self.editModal = null; //new bootstrap.Modal(self.$refs.editModal);
	},
	methods: {
		getImgURL,
		closeModal: function (modalName) {
			this.modalStates[modalName] = false;
			if (modalName == 'modal2') {
				this.modalStates.modal1 = true;
			}
		},
		openModal: function (modalName) {
			this.modalStates[modalName] = true;
		},
		getTdRec: function () {
			var self = this;
			self.$api
				.getAppointmentTdRecApi({
					recCode: self.recCode
				})
				.then(function (ret) {
					self.tdRecTask = ret.data;
					self.cusInfo.cusCode = self.tdRecTask.cusCode;
					self.cusInfo.birth = _.formatDate(ret.data.birth);

					// 設定編輯資料
					self.visitDate = self.tdRecTask.nextRemindDt.replaceAll('/', '-');
					let nextVisitTime = self.tdRecTask.nextRemindTime.split(':');
					self.visitHour = nextVisitTime[0];
					self.visitMin = nextVisitTime[1];
					self.apptVisitPurCode = self.tdRecTask.visitPurCode;
					self.apptVisitAprCode = self.tdRecTask.visitAprCode;
					self.apptTitle = self.tdRecTask.title;
					self.content = self.tdRecTask.content;
					self.apptAdvNce = self.tdRecTask.advNce;
					self.apptAdvNceDay = self.tdRecTask.advNceDay;
					self.apptAdvNcePrd = self.tdRecTask.advNcePrd;
				});
		},
		deleteTdRec: function () {
			var self = this;
			self.$bi.confirm('確定要刪除此筆資料嗎?', {
				event: {
					confirmOk: function () {
						self.$api
							.deleteTdRecApi({
								recCode: self.recCode
							})
							.then(function (ret) {
								self.$bi.alert('刪除成功。');
								self.modalStates.modal1 = false;
								if (self.getCalendarTasks) {
									self.getCalendarTasks();
								}
							});
					}
				}
			});
		},
		doUpdate: function () {
			var self = this;
			self.modalStates.modal1 = false;
			// 開啟第二個modal
			self.modalStates.modal2 = true;
			// self.editModal.show();
			self.getCusInfo();

			// 設定編輯資料
			self.visitDate = self.tdRecTask.nextRemindDt.replaceAll('/', '-');
			let nextVisitTime = self.tdRecTask.nextRemindTime.split(':');
			self.visitHour = nextVisitTime[0];
			self.visitMin = nextVisitTime[1];
			self.apptVisitPurCode = self.tdRecTask.visitPurCode;
			self.apptVisitAprCode = self.tdRecTask.visitAprCode;
			self.apptTitle = self.tdRecTask.title;
			self.content = self.tdRecTask.content;
			self.apptAdvNce = self.tdRecTask.advNce;
			self.apptAdvNceDay = self.tdRecTask.advNceDay;
			self.apptAdvNcePrd = self.tdRecTask.advNcePrd;
		},
		// 取得訪談目的選單
		getVisitPurMenu: function () {
			var self = this;
			self.$api
				.getCodeDetailApi({
					codeType: 'VISIT_PUR_CODE'
				})
				.then(function (ret) {
					self.visitPurMenu = ret.data;
				});
		},
		// 取得訪談方式選單
		getVisitAprMenu: function () {
			var self = this;
			self.$api
				.getCodeDetailApi({
					codeType: 'VISIT_APR_CODE'
				})
				.then(function (ret) {
					self.visitAprMenu = ret.data;
				});
		},
		getCusInfo: function () {
			var self = this;
			self.$api
				.getCustomer({
					cusCode: self.cusInfo.cusCode
				})
				.then(function (ret) {
					if (!ret.data) {
						self.$bi.alert('查無顧客，請確認是否為久未往來顧客。');
						self.clearValues();
						return;
					}
					self.cusInfo.cusName = ret.data.cusName;
					self.cusInfo.birth = ret.data.birth;
					self.cusInfo.email = ret.data.email;
					self.cusInfo.rankName = ret.data.rankName;
					self.cusInfo.childInvYn = ret.data.childInvYn;
					self.cusInfo.specRecommYn = ret.data.specRecommYn;
					self.cusInfo.specCusYn = ret.data.specCusYn;
					self.cusInfo.lastConnectionDt = ret.data.logCreateDt;

					// 將聯絡方式分類
					ret.data.contactInfoList.forEach(function (item) {
						switch (item.contactType) {
							case 'E': // email
								self.cusInfo.email = item.email;
								break;
							case 'H': // 住家電話
								self.cusInfo.phoneH = item.phone1;
								break;
							case 'O': // 公司電話
								self.cusInfo.phoneO = item.phone1;
								break;
							case 'M': // 手機
								self.cusInfo.phoneM = item.phone1;
								break;
						}
					});
				});
		},
		updateTdRec: function (doneYn) {
			var self = this;
			self.$refs.appointmentTask.validate().then(function (pass) {
				if (pass.valid) {
					var content = _.isNil(self.content) ? '' : self.content;
					self.$api
						.patchAppointmentTdRecApi({
							recCode: self.recCode,
							cusCode: self.cusInfo.cusCode,
							nextRemindDt: self.visitDate,
							nextRemindTime: self.visitHour + ':' + self.visitMin,
							advNce: self.apptAdvNce,
							advNceDay: self.apptAdvNce === 'Y' ? self.apptAdvNceDay : null,
							advNcePrd: self.apptAdvNce === 'Y' ? self.apptAdvNcePrd : null,
							visitAprCode: self.apptVisitAprCode,
							visitPurCode: self.apptVisitPurCode,
							title: self.apptTitle,
							content: content,
							doneYn: doneYn
						})
						.then(function (ret) {
							self.$bi.alert('更新成功');
							// self.editModal.hide();
							self.modalStates.modal2 = false;

							if (self.getCalendarTasks) {
								self.getCalendarTasks();
							}
							self.getTdRec();
						});
				}
			});
		},
		setDefaultReuseWords: function () {
			var self = this;
			self.wobReuseWords = [];
			for (var i = 0; i < 10; i++) {
				var tempWordObj = {
					wordsId: i + 1,
					words: null
				};
				self.wobReuseWords.push(tempWordObj);
			}
		},
		getReuseWords: function () {
			var self = this;
			self.$api.getReuseWordsApi().then(function (ret) {
				ret.data.forEach(function (item) {
					var index = item.wordsId - 1;
					self.wobReuseWords[index].words = item.words;
				});
			});
		},
		insertReuseWord: function () {
			var self = this;
			if (self.newReuseWord) {
				var wordsId = null;
				for (var i = 0; i < 10; i++) {
					if (!self.wobReuseWords[i].words) {
						wordsId = i + 1;
						break;
					}
				}

				if (wordsId) {
					self.$api
						.postReuseWordsApi({
							wordsId: wordsId,
							words: self.newReuseWord
						})
						.then(function (ret) {
							self.getReuseWords();
						});
				} else {
					if (!self.memo) {
						self.memo = '';
					}
					self.memo = self.memo + self.newReuseWord;
					self.$bi.alert('常句已超過設定個數，請由常用句設定頁面進行調整。');
				}
			}
		},
		appendReuseWord: function () {
			var self = this;
			if (!self.content) {
				self.content = '';
			}
			self.content = self.content + self.reuseWord;
		},
		doClose: function () {
			var self = this;
			self.reuseWord = null;
			self.newReuseWord = null;
		},
		clearValues: function () {
			var self = this;

			self.cusInfo.cusName = null;
			self.cusInfo.birth = null;
			self.cusInfo.email = null;
			self.cusInfo.phoneH = '';
			self.cusInfo.phoneO = '';
			self.cusInfo.phoneM = '';
			self.cusInfo.rankName = null;
			self.cusInfo.childInvYn = null;
			self.cusInfo.specRecommYn = null;
			self.cusInfo.specCusYn = null;
			self.cusInfo.logsCreateDt = null;
		}
	}
};
</script>
