<template>
	<div class="content ht-100v pd-0">
		<content-header></content-header>
		<aside-menu
			@click-side="
				() => {
					editableTabsValue = '首頁';
				}
			"
		></aside-menu>
		<edit-tab-name-modal ref="editTabNameModal" v-model:tabNameProp="editableTabsValue"></edit-tab-name-modal>
		<div class="content content-fixed content-body">
			<el-tabs v-model="editableTabsValue" type="card" class="demo-tabs" @tab-remove="removeTab" @tab-click="editTabName">
				<el-tab-pane label="首頁" name="首頁">
					<RouterView />
				</el-tab-pane>
				<el-tab-pane v-for="(tab, index) in tabsList" :key="index" :label="tab.key" :name="tab.key" closable class="h-100">
					<div :class="this.getContainerCSS(tab.layoutId)" class="h-100">
						<ChildTabPane
							v-for="(pane, childIndex) in tab.components"
							:key="pane.WindowName"
							:childIndex="childIndex"
							:pane="pane"
							:windowId="pane.WindowId"
							:layoutId="tab.layoutId"
						></ChildTabPane>
					</div>
				</el-tab-pane>
			</el-tabs>
		</div>
	</div>
</template>

<script>
import { LAYOUT_LIST } from '@/views/layoutComponent/layoutData.js';
import { findTabUrl } from '@/utils/findTabUrl';

import contentHeader from './components/contentHeader.vue';
import asideMenu from './components/asideMenu.vue';
import ChildTabPane from '@/views/layoutComponent/ChildTabPane.vue';
import editTabNameModal from '@/views/components/editTabNameModal.vue';

export default {
	components: {
		contentHeader,
		asideMenu,
		ChildTabPane,
		editTabNameModal
	},
	data() {
		return {
			tabList: [],
			editableTabsValue: '',
			tabClickCount: 0
		};
	},
	methods: {
		getContainerCSS(layoutId) {
			const layout = LAYOUT_LIST.find((layout) => layout.LayoutId === layoutId);
			if (layout) {
				return layout.LayoutGrid.Container;
			}
			return null;
		},
		removeTab(targetName) {
			this.$store.commit('tabStatus/removeTab', { name: targetName });
			if (this.editableTabsValue === targetName) {
				const tabs = this.$store.state.tabStatus.tabsList;
				if (tabs.length > 0) {
					this.editableTabsValue = tabs[tabs.length - 1].key;
				} else {
					this.editableTabsValue = '首頁';
				}
			}
		},
		editTabName(tab) {
			this.editableTabsValue = tab.paneName;
			if (tab.paneName === '首頁') return; // 如果是首頁，則不處理
			this.tabClickCount++;
			if (this.tabClickCount >= 2) {
				this.$refs.editTabNameModal.show();
				this.tabClickCount = 0; // 重置點擊計數
			} else {
				setTimeout(() => {
					this.tabClickCount = 0; // 如果在兩次點擊之間沒有再次點擊，則重置計數
				}, 300); // 設定一個延遲時間來區分單擊和雙擊
			}
		}
	},
	computed: {
		tabsList() {
			return this.$store.state.tabStatus.tabsList;
		}
	},
	watch: {
		tabsList: {
			handler(newVal) {
				if (newVal && newVal.length > 0) {
					this.editableTabsValue = newVal[newVal.length - 1].key;
				} else {
					this.editableTabsValue = '首頁';
				}
			},
			immediate: true
		}
	}
};
</script>
<style lang="css">
.el-tabs .el-tabs__content {
	height: calc(100% - 70px);
	overflow: auto;
}

.el-tabs.el-tabs--top {
	height: 100%;
}
</style>
