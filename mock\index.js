import GenInternalMsgMockData from './mockData/index/GenInternalMsgMockData.json';
import MessageListMockData from './mockData/index/MessageListMockData.json';
import SwitchIdentityListMockData from './mockData/index/SwitchIdentityListMockData.json';
import ChangeRoleMockData from './mockData/index/ChangeRoleMockData.json';

// contentHeader.vue
export function getGenInternalMsgApi() {
	return GenInternalMsgMockData;
}

export function getMessageListApi() {
	return MessageListMockData;
}

export function getSwitchIdentityListApi() {
	return SwitchIdentityListMockData;
}

export function postChangeRoleApi() {
	return ChangeRoleMockData;
}

// msg.vue
// getGenInternalMsg() 呼叫了與 contentHeader.vue 的 getGenInternalMsgApi 一樣的Api
