<template>
	<!-- Modal group Target start -->
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">選擇連結標的</h4>
				<button type="button" class="btn-close" @click="close()" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="card card-form-collapse">
					<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formsearch1">
						<h4>選擇連結標的</h4>
					</div>

					<div class="card-body collapse show" id="formsearch1">
						<form>
							<div class="form-row">
								<div class="form-group col-lg-4">
									<label class="form-label"> 股票代碼</label><br />
									<input class="form-control" type="text" size="15" maxlength="20" v-model="stockCode" />
								</div>
							</div>

							<div class="form-row">
								<div class="col-lg-12 text-end">
									<a href="#" class="btn btn-primary btn-search" @click="groupTargetsMenu()">查詢</a>
								</div>
							</div>
						</form>
					</div>
				</div>
				<p class="tx-note mb-3">[股票代碼]使用模糊查詢</p>
				<div v-if="targetsMenu.length > 0">
					<div class="card card-table">
						<div class="card-header">
							<h4>查詢結果</h4>
						</div>
						<div class="table-responsive">
							<table class="table table-RWD table-bordered">
								<thead>
									<tr>
										<th width="10%" class="text-center">選擇</th>
										<th width="10%">股票代碼</th>
										<th width="20%">國際編號</th>
										<th width="10%">幣別</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="item in targetsMenu">
										<td class="text-center">
											<input
												type="checkbox"
												class="form-check-input"
												:id="item.stockCode"
												v-model="targetCode"
												:value="item.stockCode"
											/>
										</td>
										<td>{{ item.stockCode }}</td>
										<td>{{ item.isinCode }}</td>
										<td>{{ item.currencyCode }}</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<input id="modaladdButton" type="button" class="btn btn-primary" value="加入" @click="addTarget()" />
			</div>
		</div>
	</div>
	<!-- Modal group Target End -->
</template>
<script>
export default {
	props: {
		targetProp: Array, // 已選擇項目
		close: Function
	},
	data: function () {
		return {
			stockCode: null,
			targetsMenu: [], // 發行機構 選項
			targetCode: [], // 發行機構 選擇項目
			targetItem: [] // 發行機構 選擇項目代碼與中文名稱
		};
	},
	watch: {},
	mounted: function () {
		var self = this;
		self.targetItem = self.targetProp || [];
		self.targetCode = self.targetItem.map((item) => item.stockCode);
	},
	methods: {
		groupTargetsMenu: async function () {
			// 發行機構來源資料
			var self = this;
			const ret = await this.$api.groupTargetMenuApi({
				stockCode: self.stockCode
			});

			self.targetsMenu = ret.data;
		},
		addTarget() {
			// 增加發行機構
			var self = this;
			if (self.targetCode.length === 0) {
				thi.$bi.alert('請至少選擇一個');
			} else {
				let allCodes = new Set([...self.targetItem.map((item) => item.stockCode), ...self.targetCode]);

				self.targetItem = Array.from(allCodes)
					.map((code) => {
						return _.find(self.targetsMenu, { stockCode: code }) || _.find(self.targetItem, { stockCode: code });
					})
					.filter((item) => item); // 移除空值

				self.targetCode = Array.from(allCodes);
				self.$emit('selected', self.targetItem);
				self.close();
			}
		},
		targetPropItem(v) {
			var self = this;
			if (v.length > 0) {
				self.targetItem = v;
				self.targetCode = Array.from(new Set([...self.targetCode, ...v.map((item) => item.stockCode)]));
			} else {
				self.targetItem = [];
				self.targetCode = [];
			}
		}
	}
	// methods end
};
</script>
