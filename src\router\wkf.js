const wkf = [
	{
		// 系統使用者管理審核 wfgId: WFG20141112001
		// 系統角色權限審核 wfgId: WFG20121005008
		// 活動審核 wfgId: WFG20241118001
		// 新商品臨時上架審核 wfgId: WFG20241203002

		// 商品資料審核 wfgId: WFG20241203001
		path: 'wkf/proMgt/:wfgId',
		name: 'wkfProcessor',
		component: () => import('../views/wkf/wkfProcessor.vue'),
		beforeEnter: (to, from, next) => {
			const allowedIds = ['WFG20241203001']; // 合法的 wfgId
			const wfgId = to.params.wfgId;

			if (allowedIds.includes(wfgId)) {
				next(); // 通過
			} else {
				next({ name: '401' }); // 導向 401 頁面
			}
		}
	},
	{
		// 新商品臨時上架審核 wfgId: WFG20241203002
		path: 'wkf/proNew/:wfgId',
		name: 'wkfProcessorNewPro',
		component: () => import('../views/wkf/wkfProcessor.vue'),
		beforeEnter: (to, from, next) => {
			const allowedIds = ['WFG20241203002']; // 合法的 wfgId
			const wfgId = to.params.wfgId;

			if (allowedIds.includes(wfgId)) {
				next(); // 通過
			} else {
				next({ name: '401' }); // 導向 401 頁面
			}
		}
	},
	{
		// 系統使用者管理審核 wfgId: WFG20141112001
		path: 'wkf/userAccount/:wfgId',
		name: 'wkfProcessorUserAccount',
		component: () => import('../views/wkf/wkfProcessor.vue'),
		beforeEnter: (to, from, next) => {
			const allowedIds = ['WFG20141112001']; // 合法的 wfgId
			const wfgId = to.params.wfgId;

			if (allowedIds.includes(wfgId)) {
				next(); // 通過
			} else {
				next({ name: '401' }); // 導向 401 頁面
			}
		}
	},
	{
		// 系統角色權限審核 wfgId: WFG20121005008
		path: 'wkf/admRole/:wfgId',
		name: 'wkfProcessorAdmRole',
		component: () => import('../views/wkf/wkfProcessor.vue'),
		beforeEnter: (to, from, next) => {
			const allowedIds = ['WFG20121005008']; // 合法的 wfgId
			const wfgId = to.params.wfgId;

			if (allowedIds.includes(wfgId)) {
				next(); // 通過
			} else {
				next({ name: '401' }); // 導向 401 頁面
			}
		}
	},
	{
		//  精選推薦商品審核 wfgId: WFG20241203003
		path: 'wkf/proPfcats/:wfgId',
		name: 'wkfProcessorProPfcats',
		component: () => import('../views/wkf/wkfProcessor.vue'),
		beforeEnter: (to, from, next) => {
			const allowedIds = ['WFG20241203003']; // 合法的 wfgId
			const wfgId = to.params.wfgId;

			if (allowedIds.includes(wfgId)) {
				next(); // 通過
			} else {
				next({ name: '401' }); // 導向 401 頁面
			}
		}
	}
];

export default wkf;
