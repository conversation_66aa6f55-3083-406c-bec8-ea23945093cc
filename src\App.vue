<template>
	<RouterView />
</template>

<script>
import { RouterView } from 'vue-router';
import { reactive, readonly } from 'vue';

export default {
	data() {
		return {
			registries: [],
			apiPath: null,
			contextPath: null,
			csrfToken: null,
			csrfHeaderName: null,
			notice: null,
			loader: null,
			loadCount: 0,
			knowledgeCloudHomeUrl: null
		};
	},
	created() {
		// this.loader = this.$loading.show({
		//     container: null,
		//     loader: 'spinner',
		//     color: '#3459e6'
		// })
		// setTimeout(() => {
		//   this.loader.hide()
		// }, 2000)
	},
	mounted() {
		this.initAjax();
		// this.getMenus()
	},
	methods: {
		getMenus: async function () {
			var menuTreeUrl = import.meta.env.VITE_API_URL_V1 + '/adm/roleMenuMapPreview?roleCode=00';
			// var menuTreeUrl = 'http://*************:8081/pbs-api/api/v1/rpt/searchCondition?menuCode=ADM101'
			const res = await this.$bi.ajax({
				url: menuTreeUrl,
				method: 'GET'
			});
			console.log(res);
		},
		showAlert() {
			this.$swal.fire('Hello!', 'This is a2 SweetAlert2 alert!', 'success');
		},
		shuffleArray() {
			console.log(this.$_.formatTwUnit(12345678, 2));
		},
		initAjax() {
			let self = this;
			let headers = {};
			// if(self.data.csrfToken){
			//   headers[self.data.csrfHeaderName] = self.data.csrfToken;
			// }
			// headers[self.data.csrfHeaderName] = self.data.csrfToken;
			this.$bi.headers = headers;

			this.$bi.ajaxStackError({
				throwMsg: function (error) {
					let self = this;
					let grid = '<table class="table">{0}</table>';
					let row = '<tr><td>{0}</td></tr>';
					let traceBt = '<a class="traceAlert" href="#">(問題追蹤)</a><p style="display: none">{0}</p>';
					let msgs = this.$_.map(error, function (item, i) {
						return row.format(item.msg + (item.trace ? traceBt.format(item.trace) : ''));
					}).join('');
					let msg = error[0].msg + (error[0].trace ? traceBt.format(error[0].trace) : '');

					this.$swal.fire({
						title: '訊息',
						html: error.length > 1 ? grid.format(msgs) : msg,
						showConfirmButton: false,
						showCloseButton: true,
						cancelButtonText: '<i class="fa fa-thumbs-down"></i>'
					});

					function handleTraceAlertClick(event) {
						if (event.target.matches('.traceAlert')) {
							const trace = event.target.parentElement.querySelector('p').textContent;
							Swal.fire({
								width: 1200,
								html: `<div class="text-start text-sm">${trace}</div>`,
								showConfirmButton: false,
								showCloseButton: true,
								cancelButtonText: '<i class="fa fa-thumbs-down"></i>',
								didClose: function () {
									self.throwMsg(error);
								}
							});
						}
					}
					document.removeEventListener('click', handleTraceAlertClick);
					document.addEventListener('click', handleTraceAlertClick);
				},
				statusCallback: {
					401: function () {
						location.href = self.data.contextPath + '/login';
					},
					403: function () {
						location.href = self.data.contextPath + '/login';
					}
				}
			});
		}
	}
};
</script>

<style scoped>
.modal .form-control:disabled {
	background: #fff;
}
</style>
