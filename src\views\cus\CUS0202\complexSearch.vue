<template>
	<div>
		<div class="row">
			<div class="col-12">
				<div id="search" class="row gx-2" v-show="!isShowSearchBar">
					<vue-complex-search-condition title="綜合條件查詢" ref="searchPage" :parent-search="search"> </vue-complex-search-condition>
					<vue-complex-search-history ref="history" :search="historySearch"> </vue-complex-search-history>
				</div>
				<div v-show="isShowSearchBar">
					<vue-complex-search-result
						title="查詢結果"
						ref="resultPage"
						:parent-search="gotoPage"
						:parent-search-yn="parentSearchYn"
						:query-req="queryReq"
						:set-is-show-search-bar="setIsShowSearchBar"
						:get-search-history="reloadHistory"
						:set-is-show-title="setIsShowTitle"
					>
					</vue-complex-search-result>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import vueComplexSearchResult from './include/complexSearchResult.vue';
import vueComplexSearchCondition from './include/complexSearchCondition.vue';
import vueComplexSearchHistory from './include/complexSearchHistory.vue';
import _ from 'lodash';
export default {
	components: {
		vueComplexSearchResult,
		vueComplexSearchCondition,
		vueComplexSearchHistory
	},
	data: function () {
		return {
			isShowSearchBar: false,
			parentSearchYn: true,
			isShowTitle: true,
			queryReq: {}
		};
	},
	computed: {
		isShowPageTitle() {
			return this.isShowTitle;
		}
	},
	methods: {
		search(req) {
			this.queryReq = req;
			this.parentSearchYn = true;
			this.$nextTick(() => {
				this.$refs.resultPage.gotoPage(0);
			});
		},
		async gotoPage(pageable) {
			var url = _.toPageUrl('', pageable.page, pageable);
			await this.$api.getMultiCusApi(this.queryReq, url);
		},
		historySearch(req) {
			this.queryReq = req;
			this.parentSeaerchYn = false;
			this.$nextTick(() => {
				this.$refs.resultPage.singleQuery(0);
			});
		},
		reloadHistory() {
			this.$refs.history.getSearchHistory();
		},
		setIsShowSearchBar(val) {
			this.isShowSearchBar = val;
			window.scrollTo(0, 0);
		},
		setIsShowTitle(val) {
			this.isShowTitle = val;
		}
	}
};
</script>
