<template>
	<div>
		<div class="tab-content">
			<div class="tab-pane fade show active">
				<div class="card card-table card-collapse">
					<div class="card-header">
						<h4>公司資料</h4>
					</div>
					<div class="table-responsive">
						<table class="table table-RWD">
							<thead>
								<tr>
									<th>公司名單</th>
									<th class="text-end">執行</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="company in companies">
									<td>
										<a href="#" @click="doUpdateCompany(company, 'READONLY')">{{ company.comName }}</a>
									</td>
									<td class="text-end">
										<button
											type="button"
											class="btn btn-info btn-glow btn-icon"
											data-bs-toggle="tooltip"
											title="編輯"
											@click="doUpdateCompany(company, 'UPDATE')"
											v-if="auth"
										>
											<i class="bi bi-pen"></i>
										</button>
										<button
											type="button"
											class="btn btn-danger btn-glow btn-icon"
											data-bs-toggle="tooltip"
											title="刪除"
											@click="deleteCompany(company)"
											v-if="auth"
										>
											<i class="bi bi-trash"></i>
										</button>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="text-end my-3">
					<button class="btn btn-lg btn-glow btn-primary" id="addCompanyInfo" @click="doInsertCompany()" v-if="auth">新增公司資料</button>
				</div>

				<!-- 新增公司資料 -->
				<div id="companyInfo" v-if="reqType">
					<div class="tab-step">
						<ul class="nav steps steps-tab justify-content-center mb-3" role="tablist">
							<li class="step-item">
								<a href="#basic" class="step-link" :class="{ active: step == 1 }" @click="gotoStep(1)">
									<span class="step-number">1</span> <span class="step-title">基本資料</span>
								</a>
							</li>
							<li class="step-item">
								<a href="#business" class="step-link" :class="{ active: step == 2 }" @click="gotoStep(2)">
									<span class="step-number">2</span> <span class="step-title">營運資料</span>
								</a>
							</li>
							<li class="step-item">
								<a href="#other" class="step-link" :class="{ active: step == 3 }" @click="gotoStep(3)">
									<span class="step-number">3</span> <span class="step-title">其他資料</span>
								</a>
							</li>
						</ul>
						<div class="tab-content mt-2">
							<!-- 公司基本資料 -->
							<div role="tabpanel" class="tab-pane fade" id="basic" :class="{ 'show active': step == 1 }" v-show="step == 1">
								<div class="card card-form-collapse">
									<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
										<h4>公司基本資料</h4>
									</div>
									<div class="collapse show" id="collapseListGroup1">
										<div class="card-body">
											<vue-form v-slot="{ errors, validate, handleReset }" ref="companyBasicInfo">
												<div class="form-row">
													<div class="form-group col-lg-4">
														<label class="form-label"> 公司類別</label><br />
														<div class="form-check form-check-inline" v-for="(item, i) in compOwnType">
															<vue-field
																type="radio"
																class="form-check-input"
																:id="'own_' + i"
																name="own_type"
																:value="item.codeValue"
																v-model="ownType"
																rules="required"
																label="公司類別"
																:class="{ 'is-invalid': errors.own_type }"
															>
															</vue-field>
															<label :for="'own_' + i" class="form-check-label">{{ item.codeName }}</label>
														</div>
														<div style="height: 25px">
															<span class="text-danger" v-show="errors.own_type">{{ errors.own_type }}</span>
														</div>
													</div>
													<div class="form-group col-lg-4">
														<label class="form-label tx-require"> 公司名稱</label><br />
														<vue-field
															id="comName"
															name="comName"
															type="text"
															class="form-control"
															size="40"
															maxlength="60"
															v-model="comName"
															:class="{ 'is-invalid': errors.comName }"
															rules="required"
															label="公司名稱"
														>
														</vue-field>
														<span class="text-danger" v-show="errors.comName">{{ errors.comName }}</span>
													</div>
													<div class="form-group col-lg-4">
														<label class="form-label"> 公司英文名稱：</label><br />
														<input
															name="com_ename"
															type="text"
															class="form-control JQ-keyup"
															size="40"
															maxlength="60"
															v-model="comEname"
														/>
													</div>
													<div class="form-group col-lg-4">
														<label class="form-label"> 公司統一編號</label><br />
														<vue-field
															id="vatNum"
															name="vatNum"
															type="text"
															class="form-control JQ-intOnly"
															size="40"
															maxlength="25"
															v-model="vatNum"
															:class="{ 'is-invalid': errors.vatNum }"
															label="公司統一編號"
															data-vv-scope="companyBasicInfo"
														>
														</vue-field>
														<span class="text-danger" v-show="errors.vatNum">{{ errors.vatNum }}</span>
													</div>
													<div class="form-group col-lg-4">
														<label class="form-label"> 公司負責人</label><br />
														<input
															name="owner"
															type="text"
															class="form-control"
															size="40"
															maxlength="60"
															v-model="owner"
														/>
													</div>
													<div class="form-group col-lg-4">
														<label class="form-label"> 公司成立日期</label><br />
														<input
															v-if="reqType == 'READONLY'"
															type="input"
															class="form-control"
															size="10"
															maxlength="10"
															readonly="true"
															v-model="establishDt"
														/>
														<input
															v-else
															name="establish_dt"
															type="date"
															class="form-control"
															size="10"
															maxlength="10"
															v-model="establishDt"
														/>
													</div>
													<div class="form-group col-lg-4">
														<label class="form-label">公司網站</label><br />
														<input
															name="url"
															type="text"
															class="form-control JQ-keyup"
															size="40"
															maxlength="60"
															v-model="url"
														/>
													</div>
													<div class="form-group col-lg-4">
														<label class="form-label"> 公司電話</label><br />
														<input
															name="phone"
															type="text"
															class="form-control JQ-intOnly"
															size="40"
															maxlength="20"
															v-model="phone"
														/>
													</div>
													<div class="form-group col-lg-4">
														<label class="form-label">聯絡人</label><br />
														<input
															name="contact_person"
															type="text"
															class="form-control"
															size="40"
															maxlength="60"
															v-model="contactPerson"
														/>
													</div>
													<div class="form-group col-lg-4">
														<label class="form-label">聯絡人電話</label><br />
														<input
															name="contact_phone"
															type="text"
															class="form-control JQ-intOnly"
															size="40"
															maxlength="20"
															v-model="contactPhone"
														/>
													</div>
													<div class="form-group col-lg-4">
														<label class="form-label">產業別</label><br />
														<select name="indus_code" id="indus_code" class="form-select" v-model="indusCode">
															<option value="" selected="selected">--</option>
															<option v-for="cusIndustry in cusIndustriesMenu" :value="cusIndustry.codeValue">
																{{ cusIndustry.codeName }}
															</option>
														</select>
													</div>
													<div class="form-group col-lg-4">
														<label class="form-label">資本額</label><br />
														<label
															v-if="reqType == 'READONLY'"
															type="number"
															class="form-control JQ-keydown"
															size="20"
															maxlength="25"
															>{{ $filters.formatNum(capital, '0,0') }}</label
														>
														<input
															v-else
															name="capital"
															type="number"
															class="form-control JQ-keydown"
															size="20"
															maxlength="25"
															v-model="capital"
														/>
													</div>
													<div class="form-group col-lg-12">
														<label class="form-label tx-require">公司註冊地</label><br />
														<vue-field
															name="reg_cun_code"
															id="reg_cun_code"
															class="form-select"
															v-model="regCunCode"
															rules="required"
															label="公司註冊地"
															as="select"
															:class="{ 'is-invalid': errors.reg_cun_code }"
															data-vv-scope="companyBasicInfo"
														>
															<option v-for="regCountry in countriesMenu" :value="regCountry.cunCode">
																{{ regCountry.cunName }}
															</option>
														</vue-field>
														<span class="text-danger" v-show="errors.reg_cun_code">{{ errors.reg_cun_code }}</span>
													</div>
													<div class="form-group col-lg-12">
														<label class="form-label">公司地址</label><br />
														<div class="row g-1">
															<div class="col-md-7">
																<div class="input-group">
																	<span class="input-group-text">國家</span>
																	<select name="cun_code" id="cun_code" class="form-select" v-model="cunCode">
																		<option v-for="country in countriesMenu" :value="country.cunCode">
																			{{ country.cunName }}
																		</option>
																	</select>
																	<span class="input-group-text"> 郵遞區號</span>
																	<input
																		name="zip"
																		type="text"
																		class="form-control JQ-intOnly"
																		size="10"
																		maxlength="10"
																		v-model="zip"
																	/>
																</div>
															</div>
															<div class="col-md-5">
																<input
																	name="caddress"
																	type="text"
																	class="form-control"
																	size="100"
																	maxlength="66"
																	v-model="caddress"
																/>
															</div>
														</div>
													</div>
													<div class="form-group col-lg-4">
														<label class="form-label">員工人數</label><br />
														<vue-field
															name="employeeNum"
															type="number"
															class="form-control"
															size="10"
															maxlength="10"
															v-model="employeeNum"
															label="員工人數"
															:class="{ 'is-invalid': errors.employeeNum }"
															data-vv-scope="companyBasicInfo"
															rules="min_value:0|max:10"
														>
														</vue-field>
														<div style="height: 25px">
															<span class="text-danger" v-show="errors.employeeNum">{{ errors.employeeNum }}</span>
														</div>
													</div>
													<div class="form-group col-lg-4">
														<label class="form-label">是否上市</label><br />
														<div class="form-check form-check-inline" v-for="(item, i) in optionYn">
															<input
																type="radio"
																class="form-check-input"
																:id="'listedYn_' + i"
																name="listedYn"
																:value="item.codeValue"
																v-model="listedYn"
															/>
															<label :for="'listedYn_' + i" class="form-check-label">{{ item.codeName }}</label>
														</div>
													</div>
													<div class="form-group col-lg-12">
														<label class="form-label">註記</label><br />
														<vue-field
															as="textarea"
															class="form-control"
															name="note"
															rows="2"
															cols="100"
															maxlength="200"
															v-model="note"
															:rules="{ max: 200 }"
														></vue-field>
														<div class="tx-note">200字可輸入</div>
														<div style="height: 25px">
															<span class="text-danger" v-show="errors.note">{{ errors.note }}</span>
														</div>
													</div>
												</div>
											</vue-form>
										</div>
									</div>
								</div>
								<div class="text-end mt-3">
									<input
										type="button"
										class="btn btn-lg btn-glow btn-primary"
										value="儲存 &amp; 下一步"
										id="next-business"
										v-if="reqType != 'READONLY'"
										@click="saveCompany()"
									/>
								</div>
							</div>

							<!-- 公司營運資料 -->
							<div role="tabpanel" class="tab-pane fade" :class="{ 'show active': step == 2 }" v-show="step == 2">
								<div class="card card-form mb-3">
									<div class="card-header">
										<h4>公司營運資料</h4>
									</div>
									<table class="table table-RWD table-horizontal-RWD table-bordered">
										<tbody>
											<tr>
												<td>
													<vue-form v-slot="{ errors, validate }" ref="ownCompaniesWatch">
														<div class="form-group">
															<label class="form-label">年度:</label>
															<vue-field
																id="year1"
																name="annual"
																type="text"
																class="form-control w-10 mx-1"
																size="6"
																maxlength="4"
																v-model="annual"
																:class="{ 'is-invalid': errors.sales }"
																:rules="{ required: true, min: 4, min_value: 1900, numeric: true }"
																label="年度"
															/>
															<span class="text-danger" v-show="errors.annual">{{ errors.annual }}</span>
														</div>
														<div class="form-group">
															<label class="form-label">營業額:</label>
															<span class="text-danger" v-show="errors.sales">{{ errors.sales }}</span>
															<vue-field
																id="text1"
																name="sales"
																type="text"
																class="form-control w-10 mx-1"
																size="10"
																maxlength="15"
																v-model="sales"
																:class="{ 'is-invalid': errors.sales }"
																:rules="{ required: true, numeric: true, max: 8, min: 0 }"
																label="營業額"
															>
															</vue-field>
															<label class="form-label">萬元</label>
															<label class="form-label">損益:</label>
															<span class="text-danger" v-show="errors.gainLoss">{{ errors.gainLoss }}</span>
															<vue-field
																id="text1"
																name="gainLoss"
																type="text"
																class="form-control w-10 mx-1"
																size="10"
																maxlength="15"
																v-model="gainLoss"
																:class="{ 'is-invalid': errors.gainLoss }"
																:rules="{ required: true, numeric: true, max: 8, min: 0 }"
																label="損益"
															>
															</vue-field>
															<label class="form-label">萬元</label>
															<label class="form-label">淨值:</label>
															<span class="text-danger" v-show="errors.netValue">{{ errors.netValue }}</span>
															<vue-field
																id="text1"
																name="netValue"
																type="text"
																class="form-control w-10 mx-1"
																size="10"
																maxlength="15"
																v-model="netValue"
																:class="{ 'is-invalid': errors.netValue }"
																:rules="{ required: true, numeric: true, max: 8, min: 0 }"
																label="淨值"
															>
															</vue-field>
															<label class="form-label">萬元</label>
															<a
																data-bs-toggle="tooltip"
																href="#"
																class="table-icon ms-1"
																data-bs-original-title=""
																title=""
															>
																<button
																	:disabled="reqType == 'READONLY'"
																	@click="insertOwnCompanie()"
																	type="button"
																	class="btn btn-info btn-icon"
																	title=""
																	data-bs-original-title="新增"
																	aria-label="新增"
																>
																	<i class="fa-solid fa-plus"></i>
																</button>
															</a>
														</div>
													</vue-form>
													<table class="table table-RWD table-horizontal-RWD table-bordered">
														<thead>
															<tr>
																<th class="wd-20p">年度</th>
																<th>營業額</th>
																<th>損益</th>
																<th>淨值</th>
																<th>執行</th>
															</tr>
														</thead>
														<tbody>
															<tr v-for="(ownCompanie, i) in ownCompanies">
																<td>{{ ownCompanie.annual }}</td>
																<td>{{ ownCompanie.sales }} 萬元</td>
																<td>{{ ownCompanie.gainLoss }} 萬元</td>
																<td>{{ ownCompanie.netValue }} 萬元</td>
																<td class="text-end">
																	<button
																		type="button"
																		class="btn btn-danger btn-glow btn-icon"
																		data-bs-toggle="tooltip"
																		title="刪除"
																		:disabled="reqType == 'READONLY'"
																		@click="deleteOwnCompanie(i)"
																	>
																		<i class="bi bi-trash"></i>
																	</button>
																</td>
															</tr>
														</tbody>
													</table>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
								<div class="text-end">
									<input
										type="button"
										class="btn btn-lg btn-info"
										value="上一步"
										id="back-basic"
										v-if="reqType != 'READONLY'"
										@click="gotoStep(1)"
									/>
									<input
										type="button"
										class="btn btn-lg btn-primary"
										value="儲存並下一步"
										id="next-other"
										v-if="reqType != 'READONLY'"
										@click="saveOwnCompanies()"
									/>
								</div>
							</div>
							<!-- 其他資料 -->
							<div id="other" class="tab-pane fade" :class="{ 'show active': step == 3 }" v-show="step == 3">
								<div class="card card-form mb-3">
									<div class="card-header">
										<h4>公司其他資料</h4>
									</div>
									<table class="table table-RWD table-horizontal-RWD table-bordered">
										<tbody>
											<tr>
												<th class="wd-15p">海外分公司</th>
												<td>
													<vue-form v-slot="{ errors, validate }" ref="compOverseas">
														<div class="form-check-group">
															<div class="form-group">
																地點:
																<vue-field
																	id="place"
																	name="place"
																	type="text"
																	class="form-control w-25 mx-1"
																	size="20"
																	maxlength="13"
																	v-model="place"
																	rules="required"
																	label="地點"
																	:class="{ 'is-invalid': errors.text - dist }"
																	data-vv-scope="compOverseas"
																>
																</vue-field>
																<span class="text-danger" v-show="errors.place">{{ errors.place }}</span>
																海外分公司:
																<vue-field
																	id="overseaName"
																	name="overseaName"
																	type="text"
																	class="form-control"
																	size="20"
																	maxlength="20"
																	v-model="overseaName"
																	rules="required"
																	:class="{ 'is-invalid': errors.text - oversea }"
																	label="海外分公司"
																	data-vv-scope="compOverseas"
																/>
																<span class="text-danger" v-show="errors.overseaName">{{ errors.overseaName }}</span>
																<button
																	:disabled="reqType == 'READONLY'"
																	@click="addCompOverseas()"
																	type="button"
																	class="btn btn-info btn-icon"
																	data-bs-toggle="tooltip"
																	title=""
																	data-bs-original-title="新增"
																	aria-label="新增"
																>
																	<i class="fa-solid fa-plus"></i>
																</button>
															</div>
														</div>
													</vue-form>
													<table class="table table-RWD table-horizontal-RWD table-bordered">
														<thead>
															<tr>
																<th>地點</th>
																<th>分公司名稱</th>
																<th>執行</th>
															</tr>
														</thead>
														<tbody>
															<tr v-for="(compOversea, i) in compOverseas">
																<td>{{ compOversea.place }}</td>
																<td>{{ compOversea.overseaName }}</td>
																<td class="text-end">
																	<button
																		type="button"
																		class="btn btn-danger btn-glow btn-icon"
																		data-bs-toggle="tooltip"
																		title="刪除"
																		:disabled="reqType == 'READONLY'"
																		@click="deleteCompOversea(i)"
																	>
																		<i class="bi bi-trash"></i>
																	</button>
																</td>
															</tr>
														</tbody>
													</table>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
								<div class="text-end">
									<input
										type="button"
										class="btn btn-info btn-lg"
										value="上一步"
										data-bs-toggle="tab"
										v-if="reqType != 'READONLY'"
										@click="gotoStep(2)"
									/>
									<input
										type="button"
										class="btn btn-primary btn-lg"
										value="儲存"
										v-if="reqType != 'READONLY'"
										@click="updateCompOtherDatas()"
									/>
								</div>
							</div>
						</div>
						<div class="text-end my-3"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { Field, Form } from 'vee-validate';
import _ from 'lodash';
export default {
	props: {
		cusCode: null
	},
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	data: function () {
		return {
			companies: [],
			authYn: null,
			compOwnType: [],
			comId: null,
			//Step 1:公司基本資料
			ownType: null,
			comName: null,
			comEname: null,
			vatNum: null,
			owner: null,
			establishDt: null,
			url: null,
			phone: null,
			contactPerson: null,
			contactPhone: null,
			indusCode: null,
			cunCode: 'TW',
			regCunCode: 'TW',
			zip: null,
			caddress: null,
			note: null,
			capital: null,
			createBy: null,
			//Step 2:公司營運資料
			ownCompanies: {},
			annual: null,
			sales: null,
			gainLoss: null,
			netValue: null,
			//Step 3:公司其他資料
			place: null,
			overseaName: null,
			employeeNum: null,
			listedYn: null,
			listedPlace: null,
			compOverseas: [],
			//下拉選單
			cusIndustriesMenu: [],
			countriesMenu: [],
			//畫面邏輯用
			reqType: null,
			step: 1,
			optionYn: []
		};
	},
	computed: {
		auth: function () {
			return this.authYn === 'Y';
		}
	},
	watch: {
		comId: function () {
			var self = this;
		}
	},
	beforeMount: function () {},
	created: function () {},
	mounted: async function () {
		var self = this;
		self.getCompanies();
		self.compOwnType = await self.getAdmCodeDetail('COMP_OWN_TYPE');
		self.getIndustriesMenu();
		self.getCountriesMenu();
		self.optionYn = await self.getAdmCodeDetail('OPTION_YN');
		self.chkCustomerAuth();
	},
	methods: {
		getCompanies: async function () {
			var self = this;
			const ret = await self.$api.getCompaniesApi({
				cusCode: self.cusCode
			});
			self.companies = ret.data;
		},
		getIndustriesMenu: async function () {
			var self = this;
			self.cusIndustriesMenu = await self.getAdmCodeDetail('CUS_INDUSTRIES');
		},
		getCountriesMenu: async function () {
			var self = this;
			const ret = await self.$api.getCountriesMenuApi();
			self.countriesMenu = ret.data;
		},
		gotoStep: function (step) {
			var self = this;
			if (step != 1 && self.comId == null) {
				self.$bi.alert('請先建立或選擇公司。');
				return;
			}

			if (step == 2) {
				self.getOperationDatas();
			}

			if (step == 3) {
				self.getCompOverseas();
			}
			self.step = step;
		},
		doInsertCompany: function () {
			var self = this;
			self.comId = null;
			self.clearData();
			self.reqType = 'INSERT';
			self.gotoStep(1);
			self.maskInputComponents(); // 遮罩輸入類型元件

			if (self.$refs.companyBasicInfo != null) {
				setTimeout(function () {
					self.$refs.companyBasicInfo.resetForm();
				}, 50);
			}
		},
		doUpdateCompany: function (company, reqType) {
			var self = this;
			self.clearData();
			self.reqType = reqType; // READONLY: 唯獨模式, UPDATE: 編輯模式
			self.comId = company.comId;
			self.ownType = company.ownType;
			self.comName = company.comName;
			self.comEname = company.comEname;
			self.vatNum = company.vatNum;
			self.owner = company.owner;
			self.establishDt = company.establishDt;
			self.url = company.url;
			self.phone = company.phone;
			self.contactPerson = company.contactPerson;
			self.contactPhone = company.contactPhone;
			self.indusCode = company.indusCode;
			self.cunCode = company.cunCode;
			self.regCunCode = company.regCunCode;
			self.zip = company.zip;
			self.caddress = company.caddress;
			self.note = company.note;
			self.capital = company.capital;
			self.createBy = company.createBy;

			self.employeeNum = company.employeeNum;
			self.listedYn = company.listedYn;
			self.listedPlace = company.listedPlace;

			self.annual = null;
			self.sales = null;
			self.gainLoss = null;
			self.netValue = null;
			self.place = null;
			self.overseaName = null;

			self.gotoStep(1);
			self.maskInputComponents(); // 遮罩輸入類型元件
		},
		saveCompany: function () {
			var self = this;

			if (self.reqType == 'INSERT') {
				self.insertCompany();
			} else if (self.reqType == 'UPDATE') {
				self.updateCompany();
			}
		},
		insertCompany: async function () {
			var self = this;
			var companyBasicInfo = self.$refs.companyBasicInfo;
			companyBasicInfo.validate().then(async function (pass) {
				if (Number(self.employeeNum) && !(self.employeeNum % 1 === 0 && self.employeeNum > 0)) {
					companyBasicInfo.setFieldError('employeeNum', '員工人數須為正整數。');
					return;
				}
				if (pass.valid) {
					const ret = await self.$api.postCompanyApi({
						cusCode: self.cusCode,
						ownType: self.ownType,
						comName: self.comName,
						comEname: self.comEname,
						vatNum: self.vatNum,
						owner: self.owner,
						establishDt: _.formatDate(self.establishDt),
						url: self.url,
						phone: self.phone,
						contactPerson: self.contactPerson,
						contactPhone: self.contactPhone,
						indusCode: self.indusCode,
						cunCode: self.cunCode,
						regCunCode: self.regCunCode,
						zip: self.zip,
						caddress: self.caddress,
						employeeNum: self.employeeNum,
						listedYn: self.listedYn,
						note: self.note,
						capital: self.capital
					});
					self.comId = ret.data;
					self.$bi.alert('新增成功。');
					self.reqType = 'UPDATE';
					self.getCompanies();
					self.gotoStep(2);
				}
			});
		},
		updateCompany: function () {
			var self = this;
			var url = self.config.apiPath + '/cus/companies';

			self.$refs.companyBasicInfo.validate().then(async function (pass) {
				if (Number(self.employeeNum) && !(self.employeeNum % 1 === 0 && self.employeeNum > 0)) {
					self.errors.add({
						field: 'companyBasicInfo.employeeNum',
						msg: '員工人數須為正整數。'
					});
					return;
				}

				if (pass) {
					const ret = await self.$api.patchCompanyApi({
						comId: self.comId,
						cusCode: self.cusCode,
						ownType: self.ownType,
						comName: self.comName,
						comEname: self.comEname,
						vatNum: self.vatNum,
						owner: self.owner,
						establishDt: _.formatDate(self.establishDt),
						url: self.url,
						phone: self.phone,
						contactPerson: self.contactPerson,
						contactPhone: self.contactPhone,
						indusCode: self.indusCode,
						cunCode: self.cunCode,
						regCunCode: self.regCunCode,
						zip: self.zip,
						caddress: self.caddress,
						employeeNum: self.employeeNum,
						listedYn: self.listedYn,
						note: self.note,
						capital: self.capital
					});
					self.$bi.alert('更新成功。');
					self.getCompanies();
					self.gotoStep(2);
				}
			});
		},
		deleteCompany: async function (company) {
			var self = this;
			self.$bi.confirm('您確定要將此公司資料全部刪除？', {
				event: {
					confirmOk: async function () {
						const ret = await self.$api.deleteCompanyApi({
							comId: company.comId,
							cusCode: self.cusCode
						});
						self.$bi.alert('刪除成功。');
						self.getCompanies();
						self.clearData();
						self.reqType = null;
					}
				}
			});
		},
		clearData: function () {
			var self = this;
			self.comId = null;
			self.ownType = null;
			self.comName = null;
			self.comEname = null;
			self.vatNum = null;
			self.owner = null;
			self.establishDt = null;
			self.url = null;
			self.phone = null;
			self.contactPerson = null;
			self.contactPhone = null;
			self.indusCode = null;
			self.cunCode = null;
			self.regCunCode = null;
			self.zip = null;
			self.caddress = null;
			self.employeeNum = null;
			self.listedYn = null;
			self.note = null;
			self.capital = null;
			self.createBy = null;
			self.annual = null;
			self.sales = null;
			self.gainLoss = null;
			self.netValue = null;
			self.place = null;
			self.overseaName = null;
		},
		getOperationDatas: async function () {
			var self = this;
			const ret = await self.$api.getOwnCompanies({
				comId: self.comId
			});
			self.ownCompanies = ret.data;
		},
		deleteOwnCompanie: function (i) {
			var self = this;
			self.ownCompanies.splice(i, 1);
		},
		insertOwnCompanie: function () {
			var self = this;
			self.$refs.ownCompaniesWatch.validate().then(function (pass) {
				if (pass.valid) {
					self.ownCompanies.push({
						annual: self.annual,
						sales: self.sales,
						gainLoss: self.gainLoss,
						netValue: self.netValue
					});

					self.annual = null;
					self.sales = null;
					self.gainLoss = null;
					self.netValue = null;

					setTimeout(function () {
						self.$refs.ownCompaniesWatch.resetForm();
					}, 50);
				}
			});
		},
		saveOwnCompanies: async function () {
			var self = this;
			const resp = await self.$api.saveOwnCompaniesApi({
				comId: self.comId,
				ownComList: self.ownCompanies
			});
			self.$bi.alert('儲存成功。');
			self.gotoStep(3);
		},
		getCompOverseas: async function () {
			var self = this;
			const resp = await self.$api.getCompOverseasApi({
				comId: self.comId
			});
			self.compOverseas = resp.data;
		},
		addCompOverseas: function () {
			var self = this;
			self.$refs.compOverseas.validate().then(function (pass) {
				if (pass.valid) {
					self.compOverseas.push({
						place: self.place,
						overseaName: self.overseaName
					});

					self.place = null;
					self.overseaName = null;
					setTimeout(function () {
						self.$refs.compOverseas.resetForm();
					}, 50);
				}
			});
		},
		deleteCompOversea: function (i) {
			var self = this;
			self.compOverseas.splice(i, 1);
		},
		updateCompOtherDatas: async function () {
			var self = this;
			const ret = await self.$api.updateCompOtherDatasApi({
				comId: self.comId,
				overseaList: self.compOverseas
			});
			self.$bi.alert('更新成功。');
			self.getCompanies();
			self.getCompOverseas();
			self.clearData();
			self.reqType = null;
		},
		// 全面遮罩 UI 輸入類型元件
		maskInputComponents: function () {
			var self = this;
			// Code that will run only after the entire view has been re-rendered
			self.$nextTick(function () {
				if (self.reqType == 'READONLY') {
					// 唯讀模式
					$('input, textarea').prop('readonly', true);
					$('input').prop('disabled', true);
					$('select').prop('disabled', true);
				} else {
					$('input, textarea').prop('readonly', false);
					$('input').prop('disabled', false);
					$('select').prop('disabled', false);
				}
			});
		},
		chkCustomerAuth: async function () {
			var self = this;
			const resp = await self.$api.chkCustomerAuthApi({
				cusCode: self.cusCode,
				progCode: 'ACUS_001'
			});
			self.authYn = resp.data.authYn;
		},
		getAdmCodeDetail: async function (codeType) {
			var self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: codeType
			});
			return ret.data;
		}
	}
};
</script>
