<template>
	<dynamic-title></dynamic-title>
	<div>
		<div class="row">
			<div class="col-12">
				<vue-bi-tabs :menu-code="'M40-01'" @change-tab="changeTab">
					<template #default="{ id }">
						<component :is="id" :msg-id="msgId"></component>
					</template>
				</vue-bi-tabs>
			</div>
		</div>
		<!--頁面內容 end-->
	</div>
</template>
<script>
import vueBiTabs from '@/views/components/biTabs.vue';
import vueBbsMgt from './include/bbsMgt.vue';
import vueBbsMgtSer from './include/bbsMgtSer.vue';
import dynamicTitle from '@/views/components/dynamicTitle.vue';
export default {
	components: {
		vueBiTabs,
		vueBbsMgt,
		vueBbsMgtSer,
		dynamicTitle
	},
	data: function () {
		return {
			msgId: null,
			//畫面顯示用參數
			customTitle: null,
			//畫面邏輯判斷用參數
			tabCode: 0,
			tabs: [
				{ tabCode: 1, label: '公告事項設定' },
				{ tabCode: 2, label: '公告事項查詢' }
			]
		};
	},
	mounted: function () {
		var self = this;
		self.tabCode = self.tabs[0].tabCode;
		self.customTitle = self.tabs[0].label;
	},
	methods: {
		changeTab: function (tabCode) {
			var self = this;
			self.tabCode = tabCode;
			self.msgId = null;
			for (var i = 0; i < self.tabs.length; i++) {
				var tab = self.tabs[i];
				if (tab.tabCode == tabCode) {
					self.customTitle = tab.label;
				}
			}
		},
		editPage: function (item) {
			var self = this;
			if (item.lockYn === 'Y') {
				self.$bi.alert('此公告為待審核狀態，不可修改。');
				return;
			}
			self.changeTab(1);
			self.msgId = item.msgId;
		}
	}
};
</script>
