import * as login from './login.js';
import * as loginMock from '../../mock/login.js';

import * as index from './index.js';
import * as indexMock from '../../mock/index.js';

import * as adm from './adm.js';
import * as admMock from '../../mock/adm.js';

import * as store from './stores.js';
import * as storeMock from '../../mock/stores.js';

import * as wkf from './wkf.js';
import * as wkfMock from '../../mock/wkf.js';

import * as pro from './pro.js';
import * as proMock from '../../mock/pro.js';
import * as common from './common.js';

import * as gen from './gen.js';
import * as genMock from '../../mock/gen.js';

import * as cus from './cus.js';
import * as cusMock from '../../mock/cus.js';

import * as wob from './wob.js';
import * as wobMock from '../../mock/wob.js';

let api = {};

if (import.meta.env.VITE_API_MOCK_ENABLED === 'true') {
	api = {
		...loginMock,
		...indexMock,
		...storeMock,
		...admMock,
		...wkfMock,
		...proMock,
		...genMock,
		...cusMock,
		...wobMock
	};
} else {
	api = {
		...login,
		...indexMock,
		...common,
		...store,
		...adm,
		...pro,
		...wkf,
		...gen,
		...cus,
		...wob
	};
}

export default api;
