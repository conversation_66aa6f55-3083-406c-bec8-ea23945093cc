<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form mb-3">
			<div class="card-header">
				<h4>分行分區查詢</h4>
				<span class="tx-square-bracket">為必填欄位</span>
			</div>
			<div class="card-body">
				<div class="row g-3 align-items-end">
					<table class="table table-RWD table-horizontal-RWD table-bordered">
						<tbody>
							<tr>
								<th class="wd-15p tx-require">分行區別</th>
								<td class="wd-85p">
									<select name="groupCode" class="form-select" v-model="groupCode">
										<option selected="selected" value="">全部</option>
										<option v-for="(item, index) in groupBranNameList" :value="item.groupCode">{{ item.groupName }}</option>
									</select>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<div class="form-footer d-flex justify-content-end gap-2">
			<button class="btn btn-primary btn-glow btn-search" @click.prevent="getPageData(0)">查詢</button>
			<button class="btn btn-primary btn-glow" @click="exportPageData()">Excel下載</button>
		</div>

		<div class="col-12">
			<div id="searchResult">
				<div class="card card-table">
					<div class="card-header">
						<h4>名單上傳結果清單</h4>
						<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
					</div>
					<template v-if="pageData.totalElements > 0">
						<div class="table-responsive">
							<table class="table table-RWD table-bordered text-center">
								<thead>
									<tr>
										<th class="text-center">項次</th>
										<th class="text-center">分區代號</th>
										<th class="text-center">分區名稱</th>
										<th class="text-center">分行代號</th>
										<th class="text-center">分行名稱</th>
										<th class="text-center">生效日期</th>
										<th class="text-center">上傳人員</th>
										<th class="text-center">異動時間</th>
									</tr>
								</thead>
								<tbody id="TopTenList">
									<tr v-for="(item, i) in pageData.content">
										<td data-th="項次">{{ item.orderBy }}</td>
										<td data-th="分區代號">{{ item.groupCode }}</td>
										<td data-th="分區名稱">{{ item.groupName }}</td>
										<td data-th="分行代號">{{ item.branCode }}</td>
										<td data-th="分行名稱">{{ item.branName }}</td>
										<td data-th="生效日期">{{ item.validDate }}</td>
										<td data-th="上傳人員">{{ item.createBy }} {{ item.createUserName }}</td>
										<td data-th="異動時間">{{ item.createDt }}</td>
									</tr>
								</tbody>
							</table>
						</div>
					</template>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import vuePagination from '@/views/components/pagination.vue';
import _ from 'lodash';
import moment from 'moment';
export default {
	components: {
		vuePagination
	},
	data: function () {
		return {
			groupBranNameList: [], //下拉選單
			groupCode: '',
			pageData: {},
			pageable: {
				page: 0,
				size: 20,
				sort: 'ORDER_BY',
				direction: 'ASC'
			}
		};
	},
	mounted: function () {
		var self = this;
		self.getGroupBranName();
	},
	methods: {
		getGroupBranName: function () {
			var self = this;

			self.$api.getGroupBranNameApi().then(function (ret) {
				self.groupBranNameList = ret.data;
			});
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (_page) {
			var self = this;

			var page = _.isNumber(_page) ? _page : self.pageable.page;
			var url = '';
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=GROUP_CODE' + ',' + self.pageable.direction;
			const ret = await self.$api.getBranPageData(
				{
					groupCode: self.groupCode
				},
				url
			);
			self.pageData = ret.data;
		},
		exportPageData: function () {
			var self = this;
			var dateStr = moment().format('YYYYMMDD');
			var fileName = '分行分區查詢_' + dateStr;

			self.$api
				.getBranExportPageData({
					groupCode: self.groupCode
				})
				.then(function (ret) {
					var link = document.createElement('a');
					var url = URL.createObjectURL(ret);
					link.download = fileName; // FIXME bi.ajax沒有返回xhr，故無法從header中取回檔名
					link.href = url;
					document.body.appendChild(link);
					link.click();
					link.remove();
					setTimeout(() => URL.revokeObjectURL(url), 500);
				});
		}
	}
};
</script>
