import api from '@/api/apiService.js';

export const menus = {
	namespaced: true,
	state: {
		menus: [],
		menuCode: () => ({}),
		contentPath: () => ({})
	},
	mutations: {
		SET_MENUS: function (state, info) {
			state.menus = info;
		},
		SET_MENU_CODE: function (state, info) {
			state.menuCode = info;
		},
		SET_CONTENT_PATH: function (state, info) {
			state.contentPath = info;
		}
	},
	actions: {
		getMenus: async function (context) {
			const res = await api.getMenusApi();
			context.commit('SET_MENUS', res.data);

			if (res) return true;
		},
		setContentPath: function (context, contentPath) {
			const path = contentPath && contentPath !== '/' ? contentPath : null;
			const cleanedPath = path ? path.split('?')[0] : null;
			const menu = context.state.menus;
			const menuCode = getMenuCode(cleanedPath, menu);

			context.commit('SET_MENU_CODE', menuCode);
			context.commit('SET_CONTENT_PATH', path);
		}
	},
	getters: {
		menus: function (state) {
			return state.menus;
		},
		menuCode: function (state) {
			return state.menuCode;
		},
		contentPath: function (state) {
			return state.contentPath;
		}
	}
};

function getMenuCode(path, menu) {
	for (let m of menu) {
		if (m.url == path) {
			return m.code;
		}
		if (m.nodes) {
			const foundMenuCode = getMenuCode(path, m.nodes);
			if (foundMenuCode) {
				return foundMenuCode;
			}
		}
	}
	return null;
}
