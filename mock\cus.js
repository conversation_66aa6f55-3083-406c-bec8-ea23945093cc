import checkCusAuthMockData from './mockData/cus/checkCusAuthMockData.json';
import getCusInfoMockData from './mockData/cus/getCusInfoMockData.json';
import getSearchHistoryApiMockData from './mockData/cus/getSearchHistoryApi.json';
import cusSingleQueryMockData_01 from './mockData/cus/cusSingleQueryMockData_01.json';
import cusSingleQueryMockData_02 from './mockData/cus/cusSingleQueryMockData_02.json';
import cusSingleQueryMockData_03 from './mockData/cus/cusSingleQueryMockData_03.json';
import chkCusAuthApiMockData from './mockData/cus/chkCusAuthApiMockData.json';
import getCusInfoMockData_02 from './mockData/cus/getCusInfoMockData_02.json';
import getCusSummariesMenuApiMockData from './mockData/cus/getCusSummariesMenuApiMockData.json';
import getAssetTrendApiMockData from './mockData/cus/getAssetTrendApiMockData.json';
import getTdListsDataApiMockData from './mockData/cus/getTdListsDataApiMockData.json';
import getAssetLoansDataApiMockData from './mockData/cus/getAssetLoansDataApiMockData.json';
import getCustomerInfoMockData from './mockData/cus/getCustomerInfoMockData.json';
import getAssetReportListMockData from './mockData/cus/getAssetReportListMockData.json';
import { get } from 'lodash';


export function getCusGradesApi() {}
export function checkCusAuthApi({ cusCode }) {
	switch (cusCode) {
		case '#100027533':
			return checkCusAuthMockData;
		case '00956829':
			return chkCusAuthApiMockData;
	}
}

export function getCusInfoApi({ cusCode }) {
	switch (cusCode) {
		case '#100027533':
			return getCusInfoMockData;
		case '00956829':
			return getCusInfoMockData_02;
	}
}
export function cusSingleQuery(queryReq, queryString) {
	switch (queryReq.resultCode) {
		case 'RC20250415000004':
			return cusSingleQueryMockData_01;
		case 'RC20250415000002':
			return cusSingleQueryMockData_02;
		case 'RC20250415000001':
			return cusSingleQueryMockData_03;
	}
}

export function getCustomersApi({ cusCode }) {
	return;
}

export function getUserRoleMenuApi() {
	return;
}
export function getAssetTrendApi({ cusCode }) {
	return getAssetTrendApiMockData;
}

export function getTdListsDataApi({ cusCode }) {
	return getTdListsDataApiMockData;
}

export function getAssetLoansDataApi({ cusCode }) {
	return getAssetLoansDataApiMockData;
}
export function getCusGroupMenuApi() {}
export function postGroupApi({ groupName }) {}
export function deleteGroupApi({ groupCode }) {}
export function getCusSummariesMenuApi({ pbStatus }) {
	return getCusSummariesMenuApiMockData;
}
export function getCusSummariesApi({ idn, graCode, cusName }, queryString) {}

export function getDisabledDimensionsApi({ cusCode }) {}

export function getCustomerInfoApi({ cusCode }) {
	switch (cusCode) {
		case '00956829':
			return getCustomerInfoMockData;
	}
}

export function getCusAoHistoryApi({ cusCode }, queryString) {}

export function getAoChangeHistApi({ cusCode }) {}

export function getCompaniesApi({ cusCode }) {}

export function getCountriesMenuApi() {}

export function postCompanyApi({
	cusCode,
	ownType,
	comName,
	comEname,
	vatNum,
	owner,
	establishDt,
	url,
	phone,
	contactPerson,
	contactPhone,
	indusCode,
	cunCode,
	regCunCode,
	zip,
	caddress,
	employeeNum,
	listedYn,
	note,
	capital
}) {}

export function patchCompanyApi({
	comId,
	cusCode,
	ownType,
	comName,
	comEname,
	vatNum,
	owner,
	establishDt,
	url,
	phone,
	contactPerson,
	contactPhone,
	indusCode,
	cunCode,
	regCunCode,
	zip,
	caddress,
	employeeNum,
	listedYn,
	note,
	capital
}) {}
export function deleteCompanyApi({ comId, cusCode }) {}

export function getOwnCompanies({ comId }) {}

export function saveOwnCompaniesApi({ comId, ownComList }) {}

export function getCompOverseasApi({ comId }) {}

export function updateCompOtherDatasApi({ comId, overseaList }) {}
export function chkCustomerAuthApi({ cusCode, progCode }) {}

export function getRelativeFriendsApi({ cusCode }) {}

export function insertRelativeFriendsApi({ cusCode, idn, relName, reltypeCode, gender, birthday, cphone, education, organization, post, note }) {}

export function updateRelativeFriendsApi({ id, cusCode, idn, relName, reltypeCode, gender, birthday, cphone, education, organization, post, note }) {}

export function deleteRelativeFriendsApi({ id, cusCode }) {}

export function getMemoryDateApi({ cusCode }) {}
export function postMemoryDateApi({ cusCode, dateDt, remindYn, remindDays, note }) {}
export function updateMemoryDateApi({ id, cusCode, dateDt, remindYn, remindDays, note }) {}

export function deleteMemoryDateApi({ id }) {}

export function getExtDataItemApi() {}

export function getExtDataAnswersApi({ cusCode }) {}
export function getExtDataLogApi({ cusCode }) {}

export function updateExtDataAnsApi({ cusCode, queitem }) {}

export function getTranTypeListApi() {}

export function getTransactionLogApi({ cusCode, pfcatCode, tranTypeCode, bankProCode, refNo, tranDtB, tranDtE }) {}

export function getInvTargetApi({ cusCode }) {}

export function getInvTargetGeoFocusApi({ cusCode }) {}

export function getInvTargetRiskApi({ cusCodes }) {}

export function getInvTargetProTypes({ cusCodes }) {}

export function getCusFilesList({ cusCode, fileType }) {}

export function generateCuaAssetReportApi({ cusCode }) {}

export function downloadCusFile(fileId) {}
export function getGroupCustomers({ groupCode }) {}
export function postGroupCustomers({ groupCode, groupName, cusCodes }) {}

export function getCustomer({ cusCode }) {}

export function getSearchHistoryApi({ userCode }) {
	return getSearchHistoryApiMockData;
}

export function postCusSearchLog({ userCode, resultCode, logType, deputyUserCode }) {}

export function deleteSearchResult({ resultCode }) {}

export function getAssetReportList({ userCode }) {
	return getAssetReportListMockData;
}