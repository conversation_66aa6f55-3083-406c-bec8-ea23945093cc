<template>
	<!--頁面內容 start-->
	<header>
		<h2>系統角色權限維護</h2>
		<nav>
			<ol class="breadcrumb breadcrumb-style2">
				<li class="breadcrumb-item">系統管理</li>
				<li class="breadcrumb-item">權限管理</li>
				<li class="breadcrumb-item active" aria-current="page">系統角色權限維護</li>
			</ol>
		</nav>
	</header>

	<!-- <dynamic-title></dynamic-title>  -->

	<div class="card card-table">
		<div class="card-header">
			<h4>功能權限維護</h4>
		</div>
		<table class="table mb-0">
			<tbody>
				<tr>
					<th colspan="4">系統角色：{{ roleName }}</th>
					<th colspan="4" class="text-center">權限範圍</th>
				</tr>
				<!--程式的start-->
				<tr>
					<td>
						<button type="button" class="btn btn-info" id="btnExpandAll" @click="toggleAll(true)">全部展開</button>
						<button type="button" class="btn btn-info" id="btnCollapseAll" @click="toggleAll(false)">全部收合</button>
					</td>
				</tr>
				<tr>
					<th>一級選單(模組)</th>
					<th>二級選單(功能)</th>
					<th>三級選單(子功能)</th>
					<th>四級選單(頁簽)</th>
					<th>五級選單(子頁簽)</th>
					<th>查詢</th>
					<th>編輯</th>
					<th>覆核</th>
					<th>匯出</th>
				</tr>
				<template v-for="(item, index) in menuEnableData">
					<tr class="accordion-header">
						<td data-th="一級選單(模組)" class="text-start">
							<span class="" :class="{ 'accordion-toggle': !$_.isEmpty(item.nodes), collapsed: !item.show }" @click="toggle(item)">
								<label class="text-start">{{ item.menuName }}</label>
							</span>
						</td>
						<td data-th="二級選單(功能)"></td>
						<td data-th="三級選單(子功能)"></td>
						<td data-th="四級選單(頁簽)"></td>
						<td data-th="五級選單(子頁簽)"></td>
						<td data-th="查詢">
							<div class="form-check form-check-inline" v-if="$_.isEmpty(item.nodes)">
								<input
									class="form-check-input"
									:name="item.menuCode"
									type="checkbox"
									:disabled="!item.progView"
									:checked="item.view"
									v-model="item.view"
								/>
							</div>
						</td>
						<td data-th="編輯">
							<div class="form-check form-check-inline" v-if="$_.isEmpty(item.nodes)">
								<input
									class="form-check-input"
									:name="item.menuCode"
									type="checkbox"
									:disabled="!item.progEdit"
									:checked="item.edit"
									v-model="item.edit"
								/>
							</div>
						</td>
						<td data-th="覆核">
							<div class="form-check form-check-inline" v-if="$_.isEmpty(item.nodes)">
								<input
									class="form-check-input"
									:name="item.menuCode"
									type="checkbox"
									:disabled="!item.progVerify"
									:checked="item.verify"
									v-model="item.verify"
								/>
							</div>
						</td>
						<td data-th="匯出">
							<div class="form-check form-check-inline" v-if="$_.isEmpty(item.nodes)">
								<input
									class="form-check-input"
									:name="item.menuCode"
									type="checkbox"
									:disabled="!item.progExport"
									:checked="item.export"
									v-model="item.export"
								/>
							</div>
						</td>
					</tr>
					<template v-for="(item2, index2) in item.nodes">
						<tr v-if="item.show">
							<td data-th="一級選單(模組)"></td>
							<td data-th="二級選單(功能)" class="text-start">
								<span :class="{ 'accordion-toggle': !$_.isEmpty(item2.nodes), collapsed: !item2.show }" @click="toggle(item2)">
									<label class="text-start">{{ item2.menuName }}</label>
								</span>
							</td>
							<td data-th="三級選單(子功能)"></td>
							<td data-th="四級選單(頁簽)"></td>
							<td data-th="五級選單(子頁簽)"></td>
							<td data-th="查詢">
								<div class="form-check form-check-inline" v-if="$_.isEmpty(item2.nodes)">
									<input
										class="form-check-input"
										:name="item2.menuCode"
										type="checkbox"
										:disabled="!item2.progView"
										:checked="item2.view"
										v-model="item2.view"
									/>
								</div>
							</td>
							<td data-th="編輯">
								<div class="form-check form-check-inline" v-if="$_.isEmpty(item2.nodes)">
									<input
										class="form-check-input"
										:name="item2.menuCode"
										type="checkbox"
										:disabled="!item2.progEdit"
										:checked="item2.edit"
										v-model="item2.edit"
									/>
								</div>
							</td>
							<td data-th="覆核">
								<div class="form-check form-check-inline" v-if="$_.isEmpty(item2.nodes)">
									<input
										class="form-check-input"
										:name="item2.menuCode"
										type="checkbox"
										:disabled="!item2.progVerify"
										:checked="item2.verify"
										v-model="item2.verify"
									/>
								</div>
							</td>
							<td data-th="匯出">
								<div class="form-check form-check-inline" v-if="$_.isEmpty(item2.nodes)">
									<input
										class="form-check-input"
										:name="item2.menuCode"
										type="checkbox"
										:disabled="!item2.progExport"
										:checked="item2.export"
										v-model="item2.export"
									/>
								</div>
							</td>
						</tr>
						<template v-for="(item3, index3) in item2.nodes">
							<tr v-if="item.show && item2.show">
								<td data-th="一級選單(模組)"></td>
								<td data-th="二級選單(功能)"></td>
								<td data-th="三級選單(子功能)" class="text-start">
									<span :class="{ 'accordion-toggle': !$_.isEmpty(item3.nodes), collapsed: !item3.show }" @click="toggle(item3)">
										<label class="text-start">{{ item3.menuName }}</label>
									</span>
								</td>
								<td data-th="四級選單(頁簽)"></td>
								<td data-th="五級選單(子頁簽)"></td>
								<td data-th="查詢">
									<div class="form-check form-check-inline" v-if="$_.isEmpty(item3.nodes)">
										<input
											class="form-check-input"
											:name="item3.menuCode"
											type="checkbox"
											:disabled="!item3.progView"
											:checked="item3.view"
											v-model="item3.view"
										/>
									</div>
								</td>
								<td data-th="編輯">
									<div class="form-check form-check-inline" v-if="$_.isEmpty(item3.nodes)">
										<input
											class="form-check-input"
											:name="item3.menuCode"
											type="checkbox"
											:disabled="!item3.progEdit"
											:checked="item3.edit"
											v-model="item3.edit"
										/>
									</div>
								</td>
								<td data-th="覆核">
									<div class="form-check form-check-inline" v-if="$_.isEmpty(item3.nodes)">
										<input
											class="form-check-input"
											:name="item3.menuCode"
											type="checkbox"
											:disabled="!item3.progVerify"
											:checked="item3.verify"
											v-model="item3.verify"
										/>
									</div>
								</td>
								<td data-th="匯出">
									<div class="form-check form-check-inline" v-if="$_.isEmpty(item3.nodes)">
										<input
											class="form-check-input"
											:name="item3.menuCode"
											type="checkbox"
											:disabled="!item3.progExport"
											:checked="item3.export"
											v-model="item3.export"
										/>
									</div>
								</td>
							</tr>
							<template v-for="(item4, index4) in item3.nodes">
								<tr v-if="item.show && item2.show && item3.show">
									<td data-th="一級選單(模組)"></td>
									<td data-th="二級選單(功能)"></td>
									<td data-th="三級選單(子功能)"></td>
									<td data-th="四級選單(頁簽)">
										<span
											:class="{ 'accordion-toggle': !$_.isEmpty(item4.nodes), collapsed: !item4.show }"
											@click="toggle(item4)"
										>
											<label class="text-start">{{ item4.menuName }}</label>
										</span>
									</td>
									<td data-th="五級選單(子頁簽)"></td>
									<td data-th="查詢">
										<div class="form-check form-check-inline" v-if="$_.isEmpty(item4.nodes)">
											<input
												class="form-check-input"
												:name="item4.menuCode"
												type="checkbox"
												:disabled="!item4.progView"
												:checked="item4.view"
												v-model="item4.view"
											/>
										</div>
									</td>
									<td data-th="編輯">
										<div class="form-check form-check-inline" v-if="$_.isEmpty(item4.nodes)">
											<input
												class="form-check-input"
												:name="item4.menuCode"
												type="checkbox"
												:disabled="!item4.progEdit"
												:checked="item4.edit"
												v-model="item4.edit"
											/>
										</div>
									</td>
									<td data-th="覆核">
										<div class="form-check form-check-inline" v-if="$_.isEmpty(item4.nodes)">
											<input
												class="form-check-input"
												:name="item4.menuCode"
												type="checkbox"
												:disabled="!item4.progVerify"
												:checked="item4.verify"
												v-model="item4.verify"
											/>
										</div>
									</td>
									<td data-th="匯出">
										<div class="form-check form-check-inline" v-if="$_.isEmpty(item4.nodes)">
											<input
												class="form-check-input"
												:name="item4.menuCode"
												type="checkbox"
												:disabled="!item4.progExport"
												:checked="item4.export"
												v-model="item4.export"
											/>
										</div>
									</td>
								</tr>
								<template v-for="(item5, index5) in item4.nodes">
									<tr v-if="item.show && item2.show && item3.show && item4.show">
										<td data-th="一級選單(模組)"></td>
										<td data-th="二級選單(功能)"></td>
										<td data-th="三級選單(子功能)"></td>
										<td data-th="四級選單(頁簽)"></td>
										<td data-th="五級選單(子頁簽)">
											<label class="text-start">{{ item5.menuName }}</label>
										</td>
										<td data-th="查詢">
											<div class="form-check form-check-inline" v-if="$_.isEmpty(item5.nodes)">
												<input
													class="form-check-input"
													:name="item5.menuCode"
													type="checkbox"
													:disabled="!item5.progView"
													:checked="item5.view"
													v-model="item5.view"
												/>
											</div>
										</td>
										<td data-th="編輯">
											<div class="form-check form-check-inline" v-if="$_.isEmpty(item5.nodes)">
												<input
													class="form-check-input"
													:name="item5.menuCode"
													type="checkbox"
													:disabled="!item5.progEdit"
													:checked="item5.edit"
													v-model="item5.edit"
												/>
											</div>
										</td>
										<td data-th="覆核">
											<div class="form-check form-check-inline" v-if="$_.isEmpty(item5.nodes)">
												<input
													class="form-check-input"
													:name="item5.menuCode"
													type="checkbox"
													:disabled="!item5.progVerify"
													:checked="item5.verify"
													v-model="item5.verify"
												/>
											</div>
										</td>
										<td data-th="匯出">
											<div class="form-check form-check-inline" v-if="$_.isEmpty(item5.nodes)">
												<input
													class="form-check-input"
													:name="item5.menuCode"
													type="checkbox"
													:disabled="!item5.progExport"
													:checked="item5.export"
													v-model="item5.export"
												/>
											</div>
										</td>
									</tr>
								</template>
							</template>
						</template>
					</template>
				</template>
				<!--程式的end-->
			</tbody>
		</table>
		<div>
			<div id="treeview-noborder" class=""></div>
		</div>
	</div>

	<div class="col-12 mt-3 text-end">
		<button class="btn btn-primary btn-lg" type="button" @click="backRolePage()">回系統角色定義</button>
		<button class="btn btn-primary btn-lg" type="button" @click="updateRolAuthority()">提交審核</button>
	</div>
	<!--頁面內容 end-->
</template>

<script>
import dynamicTitle from '@/views/components/dynamicTitle.vue';

export default {
	data: function () {
		return {
			roleName: String,
			//主要顯示資料
			menuEnableData: [],
			//原本勾選的選單資料
			originalEnableData: []
		};
	},
	components: {
		dynamicTitle
	},
	mounted: function () {
		this.getAdmRoles();
		this.getMenuEnableTree();
	},
	methods: {
		getAdmRoles: async function () {
			let ret = await this.$api.getAdmRolesApi(this.$route.params.rolecode);
			this.roleName = ret.data[0].roleName;
		},
		getMenuEnableTree: async function () {
			let ret = await this.$api.getMenuEnableTreeApi(this.$route.params.rolecode);
			this.menuEnableData = ret.data;
			// 保存原勾選的選單資料
			this.originalEnableData = this.$_.cloneDeep(this.menuEnableData);
			console.log(this.menuEnableData);
		},
		toggle(menu) {
			menu.show = !menu.show;
		},
		toggleAll(show, menus = this.menuEnableData) {
			menus.forEach((m) => {
				m.show = show;
				if (m.nodes?.length) {
					this.toggleAll(show, m.nodes);
				}
			});
		},
		backRolePage: function () {
			this.$router.push('/adm/admRole');
		},
		updateRolAuthority: async function () {
			let menuData = [];
			let orgDate = [];
			this.getCheckedMenuCodes(this.menuEnableData, menuData);
			this.getCheckedMenuCodes(this.originalEnableData, orgDate);
			let buffer = [];
			for (var i = 0; i < menuData.length; i++) {
				if (
					menuData[i].edit != orgDate[i].edit ||
					menuData[i].view != orgDate[i].view ||
					menuData[i].export != orgDate[i].export ||
					menuData[i].verify != orgDate[i].verify
				) {
					buffer.push(menuData[i]);
				}
			}

			if (buffer.length === 0) {
				this.$bi.alert('角色權限未異動，不須執行提交審核動作');
				return;
			}

			let ret = await this.$api.postUpdateRolAuthorityApi(this.$route.params.rolecode, buffer);
			this.$_.handleWkfResp(ret.data, false);
			setTimeout(function () {
				this.$router.push('/adm/admRole');
			}, 5000);
		},
		// 取得原勾選選單資料的 MENU_CODE
		getCheckedMenuCodes: function (nodes, buffer) {
			if (this.$_.isEmpty(nodes)) {
				return;
			}

			this.$_.forEach(nodes, (node) => {
				if (!this.$_.isNil(node.menuCode) && this.$_.size(node.nodes) > 0) {
					this.getCheckedMenuCodes(node.nodes, buffer);
				} else {
					buffer.push(node);
					return;
				}
			});
		}
	}
};
</script>
