{"status": 200, "data": {"cusCode": "#100027533", "pbStatus": "", "pbBranCode": "", "cusBranCode": "065", "authYn": "Y"}, "timestamp": "2025/07/04", "sqlTracer": [{"data": {"cusCode": "#100027533", "pbStatus": "", "pbBranCode": "", "cusBranCode": "065", "authYn": "Y"}, "sqlInfo": "SELECT CUS.CUS_CODE, COALESCE (CFI.PB_STATUS, '') AS PB_STATUS, ACD.CODE_NAME AS PB_STATUS_NAME, COALESCE (CFI.PB_<PERSON>AN_CODE, '') AS PB_BRAN_CODE, CAI.<PERSON>AN_CODE AS CUS_BRAN_CODE, CPFI.CREATE_BY FROM CUSTOMERS CUS JOIN CUS_AO_INFO CAI ON CUS.CUS_CODE = CAI.CUS_CODE LEFT JOIN CUS_FORM_INFO CFI ON CUS.CUS_CODE = CFI.CUS_CODE LEFT JOIN CUS_PBEXP_FORM_INFO CPFI ON CUS.CUS_CODE = CPFI.CUS_CODE LEFT JOIN ADM_CODE_DETAIL ACD ON COALESCE (CFI.PB_STATUS, 'N') = ACD.CODE_VALUE AND ACD.CODE_TYPE = 'PB_STATUS' WHERE CUS.CUS_CODE = :cusCode ,class com.bi.pbs.cus.web.model.CustomerAuthResp,{cusCode=#100027533}"}]}