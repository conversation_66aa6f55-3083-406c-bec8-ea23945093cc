<template>
	<div class="tab-view">
		<div class="tab-view-child">This is Component {{ this.$props.tabKey }}</div>
	</div>
</template>
<script>
export default {
	name: 'tabView',
	props: {
		tabKey: {
			type: String
		}
	}
};
</script>
<style scoped>
.tab-view {
	padding: 20px;
	background-color: #f9f9f9;
	border: 1px solid #ddd;
	border-radius: 4px;
	width: 100%;
	height: 100%;
	display: flex;
}

.tab-view-child {
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 18px;
	color: #333;
}
</style>
