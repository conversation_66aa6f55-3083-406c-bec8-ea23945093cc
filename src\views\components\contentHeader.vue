<template>
	<div class="navbar navbar-header navbar-header-fixed">
		<div class="marquee">
			<template v-for="item in messageList">
				<span>{{ $filters.formatDate(item.validBgnDt) }}</span>
				{{ item.msgTitle }}
			</template>
		</div>
		<div class="navbar-brand">
			<!-- 改寫aside-menu-link javascript -->
			<a href="" class="aside-menu-link">
				<div class="burger"><span></span><span></span><span></span></div>
			</a>
			<router-link to="/"><img :src="getImgURL('logo', 'bi_logo.svg')" alt="logo_img" /></router-link>
		</div>
		<nav class="navbar-right">
			<!-- <button type="button" class="btn btn-secondary btn-sm ms-30" @click="addTab">+</button> -->
			<div style="margin-bottom: 20px">
				<el-button size="small" @click="addTab"> add tab </el-button>
			</div>
			<router-link to="/" class="nav-link" data-bs-toggle="tooltip" title="首頁" data-bs-placement="bottom">
				<i class="bi bi-laptop-fill"></i>
			</router-link>
			<router-link to="/wob/taskCalendar" class="nav-link" data-bs-toggle="tooltip" title="工作日曆"
				data-bs-placement="bottom">
				<i class="fa-solid fa-blue fas fa-calendar" aria-hidden="true"></i>
			</router-link>
			<router-link to="/cus/cusSearch" class="nav-link" data-bs-toggle="tooltip" title="客戶查詢"
				data-bs-placement="bottom">
				<i class="fa-solid fa-user-tag"></i>
			</router-link>
			<div class="dropdown dropdown-message">
				<a class="nav-link dropdown-link" data-bs-toggle="dropdown" href="#">
					<i class="fa-solid fa-blue fa-comment"></i><span v-if="genInternalMsg.length > 0"
						class="badge bg-danger text-bg-dark badge-sm">{{ genInternalMsg.length }}</span></a>
				<div class="dropdown-menu dropdown-menu-right">
					<div class="dropdown-header">訊息通知</div>
					<a href="#" class="dropdown-item" v-for="item in genInternalMsg">
						<div class="media media-border align-items-start">
							<div class="avatar">
								<img :src="getImgURL('icon', 'icon-alert.png')" />
							</div>
							<div class="media-body">
								<h6 class="bold">
									{{ item.senderUserCode }}
									{{ item.userName }}
								</h6>
								{{ item.msgContent }}<br />
								<span>{{ $filters.formatDateTime(item.msgDatetime) }}</span>
							</div>
						</div>
					</a>
					<div class="dropdown-footer">
						<a href="#" @click="openInternalMsg()">更多訊息 <i class="fa fa-angle-right"></i></a>
					</div>
				</div>
			</div>
			<div class="dropdown dropdown-notification">
				<a class="nav-link dropdown-link" data-bs-toggle="dropdown" href="#"><i
						class="fa-solid fa-blue fa-square-plus"></i></a>
				<div class="dropdown-menu dropdown-menu-right">
					<div class="dropdown-header">快速新增</div>
					<a @click.prevent="newTask(1)" class="dropdown-item">
						<i class="fa-solid fa-address-card"></i>
						<b> 新增聯繫紀錄</b>
					</a>
					<a @click.prevent="newTask(2)" class="dropdown-item"> <i class="fas fa-calendar"></i> <b> 新增約訪行程</b> </a>
				</div>
			</div>
			<div class="dropdown dropdown-profile">
				<a href="" class="dropdown-link" data-bs-toggle="dropdown">
					<div class="tx-user">
						<span id="current-user"> {{ userName }} {{ userCode }} {{ userRoleName }}</span>
						<i class="bi bi-caret-down-fill"></i>
					</div>
				</a>
				<div class="dropdown-menu dropdown-menu-right tx-13 dropdown-menu-work">
					<router-link to="/selectpos" class="dropdown-item">
						<p class="mb-2 tx-12 tx-color-03">切換代理人</p>
					</router-link>
					<div class="dropdown-divider"></div>
					<a href="#" class="mb-2 tx-12 tx-color-03" data-bs-toggle="tooltip" title="切換系統角色">切換系統角色</a>
					<a v-for="identity in switchIdentityList" href="#" class="nav-link" @click="changeRole(identity.posCode)">{{
						identity.posName
					}}</a>
					<div class="dropdown-divider"></div>
					<a href="#" class="nav-link" data-bs-toggle="tooltip" title="登出" @click="clickLogout()"><i></i>Sign Out</a>
				</div>
			</div>
		</nav>
	</div>

	<layout-list-modal ref="layoutListModal" v-model:selectedLayoutId="selectedLayoutId"
		@next-step="() => (this.selectedLayoutId == 0 ? this.$refs.featureListModal.show() : this.$refs.layoutDisplayModal.show())"></layout-list-modal>
	<component-list-modal ref="componentListModal" v-model:selectedLayoutId="selectedLayoutId"></component-list-modal>
	<layout-display-modal ref="layoutDisplayModal" v-model:selectedLayoutId="selectedLayoutId"></layout-display-modal>
	<feature-list-modal ref="featureListModal"></feature-list-modal>

	<!-- <vue-wob-new-task-modal ref="newTaskModal"></vue-wob-new-task-modal> -->
	<msg ref="internalMsg"></msg>

	<!-- <th:block th:insert="/wob/WOB0400/include/taskCalendarModal :: newTaskModal"></th:block>
	<th:block th:insert="/include/common :: modal"></th:block> -->
</template>

<script>
import { RouterLink } from 'vue-router';
import msg from '@/views/components/msg.vue';
import layoutListModal from './layoutListModal.vue';
import componentListModal from './componentListModal.vue';
import layoutDisplayModal from './layoutDisplayModal.vue';
import featureListModal from './featureListModal.vue';
import { getImgURL } from '@/utils/imgURL.js';

export default {
	components: {
		layoutListModal,
		msg,
		componentListModal,
		layoutDisplayModal,
		featureListModal
	},
	data() {
		return {
			userName: null,
			userRoleName: null,
			userCode: null,
			genInternalMsg: [],
			messageList: [],
			switchIdentityList: [],
			selectedLayoutId: null
		};
	},
	async mounted() {
		this.$store.dispatch('userInfo/getInfo');
		let ret = await this.getGenInternalMsg('Y');
		this.genInternalMsg = ret.data;
		this.getMessageList();
	},
	computed: {
		userInfo: function () {
			return this.$store.getters['userInfo/info'];
		}
	},
	watch: {
		userInfo: function (newVal, oldVal) {
			if (newVal) {
				if (newVal.deputyUserName) {
					this.userName = newVal.deputyUserName;
				} else {
					this.userName = newVal.userName;
				}
				this.userRoleName = newVal.roleName;
				this.userCode = newVal.userCode;
			}
		},
		userCode: function (newVal, oldVal) {
			if (newVal) {
				this.getSwitchIdentityList(newVal);
			}
		}
	},
	methods: {
		getImgURL,
		clickLogout: function () {
			this.$bi.confirm('您確定要登出嗎?', {
				event: {
					confirmOk: () => {
						sessionStorage.setItem('beforeShutDown', 'false');
						this.logout();
					}
				}
			});
		},
		logout: function () {
			this.$router.push('/login');
		},
		getGenInternalMsg: async function (forceYn) {
			let ret = await this.$api.getGenInternalMsgApi(forceYn);
			return ret;
		},
		getMessageList: async function () {
			let ret = await this.$api.getMessageListApi('M');
			this.messageList = ret.data;
		},
		// todo: 待新增 newTaskModal
		// newTask: function (sectionCode) {
		// 	this.$refs.newTaskModal.getSelectVisitPurpose();
		// 	this.$refs.newTaskModal.getSelectIdentity();
		// 	this.$refs.newTaskModal.getSelectVisitType();
		// 	this.$refs.newTaskModal.getSelectPlaceType();
		// 	this.$refs.newTaskModal.getSelectAttBranches();
		// 	this.$refs.newTaskModal.getSelectContactPurpose();
		// 	this.$refs.newTaskModal.getSelectContactType();
		// 	this.$refs.newTaskModal.getSelectReuseWordSelf();
		// 	this.$refs.newTaskModal.show(sectionCode);
		// },
		getSwitchIdentityList: async function (userCode) {
			let ret = await this.$api.getSwitchIdentityListApi(userCode);
			this.switchIdentityList = ret.data;
		},
		changeRole: async function (posCode) {
			if (!posCode) return;
			await this.$api.postChangeRoleApi(this.userCode, this.userInfo, posCode);
			this.$store.dispatch('userInfo/getInfo').then(() => {
				this.$router.push('/');
			});
		},
		openInternalMsg: async function () {
			await this.$refs.internalMsg.getGenInternalMsg();
			this.$refs.internalMsg.show();
		},
		addTab: function () {
			this.$refs.layoutListModal.show();
		}
	}
};
</script>
