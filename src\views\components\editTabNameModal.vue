<template>
	<div class="modal fade show" ref="editTabNameModal" aria-modal="true" tabindex="-1">
		<div class="modal-dialog modal-dialog-centered">
			<div class="modal-content">
				<div class="modal-header">
					<h4 class="modal-title">編輯分頁名稱</h4>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<el-input v-model="newTabName" placeholder="請輸入分頁名稱"></el-input>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
					<button type="button" class="btn btn-primary" @click="handleConfirm">確認</button>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { Modal } from 'bootstrap';

export default {
	props: {
		tabNameProp: {
			type: String,
			default: ''
		}
	},
	data: function () {
		return {
			editTabNameModal: {},
			oldTabName: '',
			newTabName: ''
		};
	},
	emits: ['update:tabNameProp'],
	mounted: function () {
		// 確保在 Vue 完成渲染後再初始化 Modal
		this.$nextTick(() => {
			this.editTabNameModal = new Modal(this.$refs.editTabNameModal); // 在 Vue 渲染完成後初始化
		});
	},
	methods: {
		show: function () {
			this.editTabNameModal.show();
			this.oldTabName = this.tabNameProp;
			this.newTabName = this.checkKeyIsNumber(this.tabNameProp) ? `分頁${this.tabNameProp}` : this.tabNameProp;
		},
		handleConfirm: function () {
			console.log('handleConfirm old:', this.oldTabName, 'new:', this.newTabName);
			if (this.newTabName.trim() === '') {
				this.$swal.fire({
					text: '分頁名稱不能為空',
					icon: 'error'
				});
				return;
			}
			if (this.checkIsExistTabName(this.newTabName)) {
				this.$swal.fire({
					text: '分頁名稱已存在',
					icon: 'error'
				});
				return;
			}
			this.$store.commit('tabStatus/editTabName', { oldTabName: this.oldTabName, newTabName: this.newTabName });
			this.$emit('update:tabNameProp', this.newTabName);
			this.editTabNameModal.hide();
		},
		checkIsExistTabName(tabName) {
			return this.$store.state.tabStatus.tabsList.some((tab) => tab.name === tabName);
		},
		checkKeyIsNumber(key) {
			return !isNaN(Number(key));
		}
	},
	watch: {}
};
</script>
