<template>
	<!--頁面內容 start-->
	<div v-if="doUpdateCode != null">
		<div class="card-header">
			<h4>請輸入下列資料</h4>
		</div>
		<div class="card-body">
			<div class="row g-3">
				<div class="col-md-4">
					<label class="form-label">依顧客會員註記</label>
					<div class="input-group">
						<select class="form-select" id="selectCusGrade" v-model="graCode">
							<option value="">全部</option>
							<option v-for="aumData in aumMenu" :value="aumData.graCode">{{ aumData.graName }}</option>
						</select>
						<button class="btn btn-primary btn-glow" id="searchByGraBtn" type="button" @click="queryByGraCode()">
							<i class="bi bi-search"></i>
						</button>
					</div>
				</div>
				<div class="col-md-4">
					<label class="form-label">依顧客身份證字號/統編</label>
					<div class="input-group">
						<input name="inputCusCode" class="form-control" id="inputCusCode" type="text" size="30" maxlength="10" v-model="idn" />
						<button class="btn btn-primary btn-glow" id="searchByIdnBtn" type="button" @click="queryByIdn()">
							<i class="bi bi-search"></i>
						</button>
					</div>
				</div>
				<div class="col-md-4">
					<label class="form-label">依顧客姓名</label>
					<div class="input-group">
						<input name="inputCusName" class="form-control" id="inputCusName" type="text" size="30" maxlength="10" v-model="cusName" />
						<button class="btn btn-primary btn-glow" id="searchByNameBtn" type="button" @click="queryByCusName()">
							<i class="bi bi-search"></i>
						</button>
					</div>
				</div>
			</div>

			<div class="divider"></div>
			<div class="row g-0 text-center align-items-center justify-content-center">
				<div class="col-5" id="ShowList">
					<div class="tx-title">所屬顧客</div>
					<select name="availableCusPool" class="form-select" id="availableCusPool" size="13" multiple v-model="queryCusCodes">
						<option v-for="cusItem in queryCus" :value="cusItem.cusCode">
							{{ cusItem.cusName }}({{ cusItem.idn }}){{ cusItem.graName }}
						</option>
					</select>
				</div>
				<div class="col-2">
					<button type="button" class="btn btn-info mb-2" id="addCus" @click="addCus()">
						加入 <i class="bi bi-arrow-right-circle"></i>
					</button>
					<br />
					<button type="button" class="btn btn-info" id="removeCus" @click="removeCus()">
						移除 <i class="bi bi-arrow-left-circle"></i>
					</button>
				</div>
				<div class="col-5">
					<div class="tx-title">已加入群組顧客</div>
					<select name="selectedCusPool" class="form-select" id="selectedCusPool" size="13" multiple v-model="selectCusCodes">
						<option v-for="selectedCusItem in selectCus" :value="selectedCusItem.cusCode">
							{{ selectedCusItem.cusName }}({{ selectedCusItem.idn }}){{ selectedCusItem.graName }}
						</option>
					</select>
				</div>
			</div>
		</div>
		<div class="text-end mt-3">
			<button type="button" class="btn btn-lg btn-glow btn-secondary" @click="clearEditForm()">關閉</button>
			<button type="button" class="btn btn-lg btn-glow btn-primary" @click="updateGroupCus()">儲存</button>
		</div>
	</div>
	<!--頁面內容 end-->
</template>
<script>
export default {
	props: {
		groupName: String,
		doUpdateCode: Number,
		gotoPage: Function,
		refreshUpdateCode: Function
	},
	data: function () {
		return {
			//API 用參數
			graCode: null,
			idn: null,
			cusName: null,
			selectCusCodes: [],

			//畫面邏輯用參數
			// doUpdateCode: null,
			queryCusCodes: [],
			//下拉選單
			aumMenu: [],
			//畫面顯示用參數
			cusGroups: [],

			//主要顯示資料
			queryCus: [],
			selectCus: [],
			pageable: {
				page: 0,
				size: 500,
				sort: 'CUS_CODE',
				direction: 'ASC'
			}
		};
	},
	watch: {
		doUpdateCode: function () {
			var self = this;
			if (!_.isBlank(self.doUpdateCode)) {
				self.getGroupCus();
			}
		}
	},
	mounted: function () {
		var self = this;
		self.getAumMenu();
	},
	methods: {
		getAumMenu: async function () {
			var self = this;
			const ret = await self.$api.getCusGradesApi();
			self.aumMenu = ret.data;
		},
		getCusGroup: async function () {
			var self = this;
			const ret = await self.$api.getCusGroupMenuApi();
			self.cusGroups = ret.data;
		},
		queryByGraCode: function () {
			var self = this;
			var queryReq = { graCode: self.graCode };
			self.singleQuery(queryReq);
		},
		queryByIdn: function () {
			var self = this;
			var queryReq = { idn: self.idn };
			self.singleQuery(queryReq);
		},
		queryByCusName: function () {
			var self = this;
			var queryReq = { cusName: self.cusName };
			self.singleQuery(queryReq);
		},
		singleQuery: async function (queryReq) {
			var self = this;
			var url = _.toPageUrl('', page, self.pageable);
			const ret = await self.$api.getCusSummariesApi(
				{
					idn: queryReq.idn,
					graCode: queryReq.graCode,
					cusName: queryReq.cusName
				},
				url
			);

			if (ret.data.content.length == 0) {
				self.queryCus = [];
				self.$bi.alert('查無顧客，請確認是否為久未往來顧客。');
			} else {
				self.queryCus = ret.data.content;

				self.selectCus.forEach(function (selectCus) {
					self.queryCus.forEach(function (item, index, arr) {
						if (selectCus.cusCode == item.cusCode) {
							arr.splice(index, 1);
						}
					});
				});
			}
		},
		getGroupCus: async function () {
			var self = this;
			const ret = await self.$api.getGroupCustomers({
				groupCode: self.doUpdateCode
			});
			self.selectCus = ret.data;
		},
		clearEditForm: function () {
			var self = this;
			self.groupName = '';
			self.queryCus = [];
			self.selectCus = [];
			self.doUpdateCode = null;
			self.refreshUpdateCode(self.doUpdateCode);
		},
		addCus: function () {
			var self = this;

			self.queryCusCodes.forEach(function (queryCusCode) {
				self.queryCus.forEach(function (item, index, arr) {
					if (queryCusCode == item.cusCode) {
						self.selectCus.push(item);
						arr.splice(index, 1);
					}
				});
			});
		},
		removeCus: function () {
			var self = this;

			self.selectCusCodes.forEach(function (selectCusCode) {
				self.selectCus.forEach(function (item, index, arr) {
					if (selectCusCode == item.cusCode) {
						self.queryCus.push(item);
						arr.splice(index, 1);
					}
				});
			});
		},
		updateGroupCus: async function () {
			var self = this;

			var selectCusCodes = [];
			self.selectCus.forEach(function (item) {
				selectCusCodes.push(item.cusCode);
			});

			const ret = await self.$api.postGroupCustomers({
				groupName: self.groupName,
				groupCode: self.doUpdateCode,
				cusCodes: selectCusCodes
			});
			self.$bi.alert('修改成功');
			self.getCusGroup();
		}
	}
};
</script>
