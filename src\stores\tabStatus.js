/* 
data structure :
user_layout: [{
  key: 1, // 唯一鍵, 會依據此鍵命名分頁
  layoutId: 1, // 版面ID
  components: [
    {
      WindowId: 1, // block ID, 每個block的唯一鍵
      WindowName: 'Group1', // block名稱 目前不會用到
      Components: ['ComponentA', 'ComponentB'] // 元件列表
    },
    {
      WindowId: 2,
      WindowName: 'Group2',
      Components: ['ComponentC']
    }
  ]
},
{
  key: 2,
  layoutId: 2,
  components: [
    {
      WindowId: 1,
      WindowName: 'Group1',
      Components: ['ComponentD', 'ComponentE']
    }
  ]
}] */

export const tabStatus = {
	namespaced: true,
	state: () => ({
		tabsList: [],
		keyIndex: 1
	}),
	mutations: {
		setTabList(state, payload) {
			const leng = state.tabsList.length;
			let newTab = {};
			if (payload.layoutId === 0) {
				newTab = {
					key: payload.tabName,
					layoutId: payload.layoutId,
					components: payload.components
				};
			} else {
				newTab = {
					key: generateDefaultName(state, state.tabsList),
					layoutId: payload.layoutId,
					components: payload.components.sort((a, b) => a.WindowId - b.WindowId)
				};
			}
			state.tabsList = [...state.tabsList, newTab];
		},
		removeTab(state, payload) {
			state.tabsList = state.tabsList.filter((tab) => tab.key !== payload.name);
		},
		editTabName(state, payload) {
			const tab = state.tabsList.find((tab) => tab.key === payload.oldTabName);
			if (tab) {
				tab.key = payload.newTabName;
			}
		}
	},
	actions: {},
	getters: {}
};

function generateDefaultName(state, pages) {
	let index = state.keyIndex++;
	let nameSet = new Set(pages.map((p) => p.key));
	let candidate = `分頁${index}`;
	while (nameSet.has(candidate)) {
		index++;
		candidate = `分頁${index}`;
	}
	return candidate;
}
