<template>
	<div>
		<vue-bi-tabs :menu-code="'M20-058'">
			<template #default="{ id }">
				<div>
					<component :is="id" :cus-code="cusCode" :customer="customer"></component>
				</div>
			</template>
		</vue-bi-tabs>
	</div>
</template>
<script>
import vueBiTabs from '@/views/components/biTabs.vue';
import vuePagination from '@/views/components/pagination.vue';
import vueCusInvestAnalysisInvTransaction from './cusInvestAnalysisInvTransaction.vue';
import vueCusInvestAnalysisInvTarget from './invTarget.vue';
export default {
	components: {
		vueBiTabs,
		vuePagination,
		vueCusInvestAnalysisInvTransaction,
		vueCusInvestAnalysisInvTarget
	},
	props: {
		cusCode: null,
		hasAuth: <PERSON><PERSON><PERSON>,
		customer: Object
	},
	data: function () {
		return {
			//畫面邏輯判斷用參數
			customTitle: null,
			tabCode: 0,

			userName: '', // 操作者姓名
			userRoleName: '', // 操作者角色名稱
			queryDt: null, // 查詢時間
			dataDt: null // 資料時間
		};
	},
	computed: {
		userInfo: function () {
			return this.$store.getters['userInfo/info'];
		}
	},
	watch: {
		userInfo: {
			immediate: true,
			handler: function (newVal, oldVal) {
				var self = this;
				if (newVal) {
					if (newVal.deputyUserName) {
						self.userName = newVal.deputyUserName;
					} else {
						self.userName = newVal.userName;
					}
					self.userRoleName = newVal.roleName;
				}
			}
		}
	},
	mounted: function () {
		var self = this;
		if (self.hasAuth) {
			self.getQueryDt();
			self.tabCode = 1;
		} else {
			self.$bi.alert('該顧客非屬您歸屬私銀中心/組轄下顧客。');
		}
	},
	methods: {
		changeTab: function (tabCode) {
			var self = this;
			self.tabCode = tabCode;
			self.getQueryDt();
		},
		// 取得查詢時間
		getQueryDt: async function () {
			var self = this;
			self.queryDt = moment().format('YYYY/MM/DD HH:mm');

			const ret = await self.$api.getLastAssetAmount({
				cusCode: self.cusCode
			});
			if (ret.data) {
				self.dataDt = ret.data.dataDt;
			}
		}
	}
};
</script>
