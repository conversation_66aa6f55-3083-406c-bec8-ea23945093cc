const pro = [
	{
		// 商品資料查詢
		path: 'pro/productSearch',
		name: 'productSearch',
		component: () => import('../views/pro/PRO0101/productSearch.vue'),
		props: {
			bankPdtCode: null,
			pdtName: null,
			proListSource: null
		}
	},
	{
		// 商品資料維護
		path: 'pro/productMgt',
		name: 'productMgt',
		component: () => import('../views/pro/productInfo/productMgt.vue')
	},
	{
		// 新商品臨時上架維護
		path: '/pro/productAdd',
		name: 'productAdd',
		component: () => import('../views/pro/PrdAdd/productAdd.vue')
	},
	{
		// 精選推薦商品查詢
		path: '/pro/packagePrdSearch',
		name: 'packagePrdSearch',
		component: () => import('../views/pro/PRO0401/packagePrdSearch.vue')
	},
	{
		// 精選推薦商品設定
		path: '/pro/packagePrd',
		name: 'packagePrd',
		component: () => import('../views/pro/PRO0402/packagePrd.vue')
	}
];

export default pro;
