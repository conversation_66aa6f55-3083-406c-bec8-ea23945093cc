{"status": 200, "data": [{"order": 0, "leaf": false, "menuCode": "M0", "menuName": "系統管理", "depths": 1, "firMenuName": "系統管理", "edit": true, "view": true, "export": false, "verify": true, "roleName": "分行幹部"}, {"order": 0, "leaf": false, "menuCode": "M02", "menuName": "代理人", "depths": 2, "firMenuName": "系統管理", "secMenuName": "代理人", "edit": true, "view": true, "export": false, "verify": true, "roleName": "分行幹部"}, {"order": 0, "leaf": false, "menuCode": "M02-00", "menuName": "代理人設定", "depths": 3, "firMenuName": "系統管理", "secMenuName": "代理人", "thiMenuName": "代理人設定", "edit": true, "view": true, "export": false, "verify": true, "roleName": "分行幹部"}], "timestamp": "2025/04/10", "sqlTracer": [{"data": [{"order": 0, "leaf": false, "menuCode": "M0", "menuName": "系統管理", "depths": 1, "firMenuName": "系統管理", "edit": true, "view": true, "export": false, "verify": true, "roleName": "分行幹部"}, {"order": 0, "leaf": false, "menuCode": "M02", "menuName": "代理人", "depths": 2, "firMenuName": "系統管理", "secMenuName": "代理人", "edit": true, "view": true, "export": false, "verify": true, "roleName": "分行幹部"}, {"order": 0, "leaf": false, "menuCode": "M02-00", "menuName": "代理人設定", "depths": 3, "firMenuName": "系統管理", "secMenuName": "代理人", "thiMenuName": "代理人設定", "edit": true, "view": true, "export": false, "verify": true, "roleName": "分行幹部"}], "sqlInfo": "SELECT AM.MENU_NAME,         AM.MENU_CODE,         AM.DEPTHS,         CASE WHEN LEN(AM.STRSET)>=2 THEN (SELECT MENU_NAME FROM ADM_MENUS PAM WHERE PAM.STRSET=SUBSTRING(AM.STRSET, 1,2))END AS'FIR_MENU_NAME',         CASE WHEN LEN(AM.STRSET)>=4 THEN (SELECT MENU_NAME FROM ADM_MENUS PAM WHERE PAM.STRSET=SUBSTRING(AM.STRSET, 1,4))END AS'SEC_MENU_NAME',         CASE WHEN LEN(AM.STRSET)>=6 THEN (SELECT MENU_NAME FROM ADM_MENUS PAM WHERE PAM.STRSET=SUBSTRING(AM.STRSET, 1,6))END AS'THI_MENU_NAME',         CASE WHEN LEN(AM.STRSET)>=8 THEN (SELECT MENU_NAME FROM ADM_MENUS PAM WHERE PAM.STRSET=SUBSTRING(AM.STRSET, 1,8))END AS'FOU_MENU_NAME',         CASE WHEN LEN(AM.STRSET)>=10 THEN (SELECT MENU_NAME FROM ADM_MENUS PAM WHERE PAM.STRSET=SUBSTRING(AM.STRSET, 1,10))END AS'FIF_MENU_NAME',  \t\tCOALESCE(JSON_VALUE(ML.PERMISSION, '$.Edit'), 'false') AS 'EDIT',  \t\tCOALESCE(JSON_VALUE(ML.PERMISSION, '$.View'), 'false') AS 'VIEW',  \t\tCOALESCE(JSON_VALUE(ML.PERMISSION, '$.Export'), 'false') AS 'EXPORT',  \t\tCOALESCE(JSON_VALUE(ML.PERMISSION, '$.Verify'), 'false') AS 'VERIFY',         AR.ROLE_NAME FROM ADM_ROLE_MENU_LOG RML JOIN ADM_ROLE_MENU_MAP_LOG ML ON RML.ROLE_MAP_ID=ML.ROLE_MAP_ID JOIN ADM_MENUS AM ON AM.MENU_CODE = ML.MENU_CODE JOIN ADM_ROLES AR ON ML.ROLE_CODE=AR.ROLE_CODE WHERE RML.EVENT_ID = :eventId ,class com.bi.pbs.adm.web.model.RoleMenuNameResp,{eventId=EVN20250410000014}"}]}