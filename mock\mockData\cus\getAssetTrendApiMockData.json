{"status": 200, "data": [{"name": "總資產", "datas": []}, {"name": "投資", "datas": []}, {"name": "現金", "datas": []}], "timestamp": "2025/07/09", "sqlTracer": [{"data": [], "sqlInfo": "SELECT CA.CUS_CODE ,CA.DATA_YM, SUM(MKT_AMT_LC)/1000 VALUE  FROM CUS_ASSETAMOUNT_MONTH CA LEFT JOIN PRO_PFCATS PP ON CA.PFCAT_CODE = PP.PFCAT_CODE  WHERE (PP.AU_TYPE = 'AUM' OR CA.PFCAT_CODE = 'SAVING' ) AND CUS_CODE = :cusCode  GROUP BY DATA_YM, CUS_CODE ,class com.bi.pbs.cus.web.model.AssetTrendResp,{cusCode=00956829}"}, {"data": [], "sqlInfo": "SELECT CA.CUS_CODE ,CA.DATA_YM, SUM(MKT_AMT_LC)/1000 VALUE  FROM CUS_ASSETAMOUNT_MONTH CA LEFT JOIN PRO_PFCATS PP ON CA.PFCAT_CODE = PP.PFCAT_CODE  WHERE PP.AU_TYPE = 'AUM' AND CUS_CODE = :cusCode  GROUP BY DATA_YM, CUS_CODE ,class com.bi.pbs.cus.web.model.AssetTrendResp,{cusCode=00956829}"}, {"data": [], "sqlInfo": "SELECT CA.CUS_CODE ,CA.DATA_YM, SUM(MKT_AMT_LC)/1000 VALUE  FROM CUS_ASSETAMOUNT_MONTH CA LEFT JOIN PRO_PFCATS PP ON CA.PFCAT_CODE = PP.PFCAT_CODE  WHERE  CA.PFCAT_CODE = 'SAVING' AND CUS_CODE = :cusCode  GROUP BY DATA_YM, CUS_CODE ,class com.bi.pbs.cus.web.model.AssetTrendResp,{cusCode=00956829}"}]}