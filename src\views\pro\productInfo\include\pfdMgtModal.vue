<template>
	<div class="modal-dialog modal-xl">
		<!--  海外股票-->
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">信託-海外股票</h4>
				<button type="button" class="btn-close" @click.prevent="close()" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="card card-prointro shadow-none mb-3">
					<div class="pro_left">
						<div class="pro_name">
							<div class="avatar avatar-md avatar-structure"></div>
							<h4>
								<span>商品名稱</span> <br />{{ proInfo.proName }} <br /><span class="tx-black">{{ proInfo.proEName }}</span>
							</h4>
						</div>
						<h4 class="pro_value">
							<span>最新淨值</span>
							<br /><span>{{ proInfo.aprice }}</span> <br /><span>{{ proInfo.priceDt }}</span>
						</h4>
						<h4 class="pro_value">
							<span>最新市價</span>
							<br /><span>{{ proInfo.sprice }}</span> <br /><span>{{ proInfo.priceDt }}</span>
						</h4>
					</div>
					<div class="pro_class">
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品代碼</span> <br />{{ proInfo.bankProCode }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6>
								<span>資產類別 <br /></span>{{ proInfo.assetcatName }}
							</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品主類</span> <br />{{ proInfo.pfcatName }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品次類</span><br />{{ proInfo.proTypeName }}</h6>
						</div>
					</div>
				</div>
				<div class="tab-nav-pills">
					<ul class="nav nav-pills justify-content-center" role="tablist">
						<li class="nav-item"><a class="nav-link active" href="#Sectionpfd1" data-bs-toggle="pill">商品基本資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionpfd2" data-bs-toggle="pill">商品共同資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionpfd3" data-bs-toggle="pill">商品附加資料</a></li>
					</ul>

					<div class="tab-content">
						<div class="tab-pane fade show active" id="Sectionpfd1">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>商品資訊</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>風險等級</th>
											<td class="wd-30p">{{ proInfo.pfdInfo.riskName }}</td>
											<th>計價幣別</th>
											<td class="wd-30p">{{ proInfo.pfdInfo.curCode }}</td>
										</tr>
										<tr>
											<th>是否可銷售</th>
											<td>{{ proInfo.pfdInfo.buyYn }}</td>
											<th>交易單位</th>
											<td>{{ proInfo.pfdInfo.txUnit }}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionpfd2">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>銷售相關資料</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>銷售地區</th>
											<td v-if="proInfo.allYn == 'Y'">全行</td>
											<td v-else></td>
											<th>限PI申購</th>
											<td>{{ proInfo.profInvestorYn }}</td>
										</tr>
										<tr>
											<th>是否開放申購</th>
											<td>{{ proInfo.buyYn }}</td>
											<th>是否開放贖回</th>
											<td>{{ proInfo.sellYn }}</td>
										</tr>
										<tr>
											<th><span>保本要求</span></th>
											<td>
												<span v-if="actionType !== 'EDIT'">{{ proInfo.principalGuarYn }}</span>
												<span v-else>
													<div v-for="item in principalGuarMenu" class="form-check form-check-inline">
														<input
															class="form-check-input"
															:id="'principalGuar' + item.codeValue"
															v-model="proInfo.principalGuarYn"
															type="radio"
															:value="item.codeValue"
															name="fastCode"
														/>
														<label class="form-check-label" :for="'principalGuar' + item.codeValue">{{
															$filters.defaultValue(item.codeName, '--')
														}}</label>
													</div>
												</span>
											</td>
											<th>配息率</th>
											<td>
												<template v-if="actionType !== 'EDIT' && proInfo.intRate">
													{{ proInfo.intRate * 100 }}%<span v-if="proInfo.intRate * 100 > 5">(高配息率)</span
													><span v-else>(一般配息率)</span>
												</template>
												<template v-if="actionType === 'EDIT'">
													<input
														class="form-control"
														id="intRate"
														maxlength="5"
														name="intRate"
														type="text"
														v-model="intRateEdit"
													/>%
												</template>
											</td>
										</tr>
										<tr>
											<th><span>理財需求</span></th>
											<td colspan="3">
												<div class="form-check form-check-inline" v-for="(item, index) in finReqCodeMenu">
													<input
														class="form-check-input"
														name="finReqCodes"
														:disabled="actionType == 'EDIT' ? false : true"
														:id="'finReqCodes_' + index"
														v-model="finReqCodes"
														:value="item.codeValue"
														type="checkbox"
													/>
													<label class="form-check-label" :for="'finReqCodes_' + index">{{ item.codeName }}</label>
												</div>
											</td>
										</tr>
										<tr>
											<th>商品標籤</th>
											<td colspan="3" disabled="disabled">
												{{ proInfo.selprocatNames }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionpfd3">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>其他設定</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>備註</span></th>
											<td>
												<textarea
													class="form-control"
													cols="80"
													rows="4"
													size="200"
													maxlength="200"
													v-model="proInfo.memo"
													:readonly="actionType !== 'EDIT'"
												></textarea>
												<div class="tx-note" v-if="proInfo.memo">{{ 200 - proInfo.memo.length }} 個字可輸入</div>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>商品說明書</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_A"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['A']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="pfdUploadFileA"
																@change="triggerFile($event, 'A')"
																accept=".xlsx,.xls"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('A', url['A'])">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['A'] && uploadFiles['A'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['A'].url" target="_blank">{{ uploadFiles['A'].url }}</a
													><br v-if="uploadFiles['A']" />
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['A'] && uploadFiles['A'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['A'])"
														>{{ uploadFiles['A'].showName }}</span
													>
													<span
														v-show="uploadFiles['A'] && uploadFiles['A'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('A', uploadFiles['A'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
										<tr>
											<th>投資人須知</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_D"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['D']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="pfdUploadFileD"
																@change="triggerFile($event, 'D')"
																accept=".xlsx,.xls"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('D', url['D'])">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['D'] && uploadFiles['D'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['D'].url" target="_blank">{{ uploadFiles['D'].url }}</a
													><br v-if="uploadFiles['D']" />
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['D'] && uploadFiles['D'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['D'])"
														>{{ uploadFiles['D'].showName }}</span
													>
													<span
														v-show="uploadFiles['D'] && uploadFiles['D'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('D', uploadFiles['D'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
										<tr>
											<th>DM</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_F"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['F']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="pfdUploadFileF"
																@change="triggerFile($event, 'F')"
																accept=".xlsx,.xls"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('F', url['F'])">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['F'] && uploadFiles['F'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['F'].url" target="_blank">{{ uploadFiles['F'].url }}</a
													><br v-if="uploadFiles['F']" />
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['F'] && uploadFiles['F'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['F'])"
														>{{ uploadFiles['F'].showName }}</span
													>
													<span
														v-show="uploadFiles['F'] && uploadFiles['F'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('F', uploadFiles['F'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
										<tr>
											<th>其他</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														type="text"
														name="url_G"
														class="form-control"
														size="40"
														maxlength="100"
														v-model="url['G']"
													/>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																class="form-control form-file"
																type="file"
																size="30"
																id="pfdUploadFileG"
																@change="triggerFile($event, 'G')"
																accept=".xlsx,.xls"
															/>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('G', url['G'])">
																上傳
															</button>
														</div>
													</div>
													<br />
												</template>
												<span v-if="uploadFiles['G'] && uploadFiles['G'].url && actionType !== 'EDIT'"
													>連結位址： <a :href="uploadFiles['G'].url" target="_blank">{{ uploadFiles['G'].url }}</a
													><br v-if="uploadFiles['G']" />
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['G'] && uploadFiles['G'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['G'])"
														>{{ uploadFiles['G'].showName }}</span
													>
													<span
														v-show="uploadFiles['G'] && uploadFiles['G'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														@click="deleteFiles('G', uploadFiles['G'].proFileId)"
														title="刪除"
													></span>
												</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>其他相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tr>
										<td>
											<span v-for="(item, index) in otherFileList">
												<a
													v-if="index === otherFileList.length - 1"
													v-show="item.show"
													href="#"
													class="tx-link"
													@click="downloadOtherFile(item.docFileId)"
													>{{ $filters.defaultValue(item.showName, '--') }}</a
												>
												<a v-else href="#" class="tx-link" v-show="item.show" @click="downloadOtherFile(item.docFileId)"
													>{{ $filters.defaultValue(item.showName, '--') }}、</a
												>
											</span>
										</td>
									</tr>
								</table>
							</div>
							<div class="tx-note">其他相關附件為文件管理內所上傳與本商品相關的文件。</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<input id="modalCloseButton" type="button" @click.prevent="close()" class="btn btn-white" value="關閉" />
				<input
					type="button"
					class="btn btn-primary"
					value="傳送主管審核"
					v-if="actionType == 'EDIT'"
					@click="
						updateProduct();
						close();
					"
				/>
			</div>
		</div>
	</div>
	<!-- Modal 2 End -->
</template>
<script>
import moment from 'moment';
import _ from 'lodash';
export default {
	props: {
		actionType: String,
		gotoPage: Function,
		finReqCodeMenu: Array,
		downloadFile: Function,
		downloadOtherFile: Function,
		close: Function
	},
	data: function () {
		const now = moment();
		return {
			proInfo: {
				fundInfo: {},
				etfInfo: {},
				dciInfo: {},
				spInfo: {},
				insInfo: {},
				pfdInfo: {}
			},
			finReqCodes: [],
			intRateEdit: '', // 配息率
			proFileList: [], // 相關附件檔案清單
			otherFileList: [], // 其他相關附件
			commInfo: {
				proFiles: []
			},
			proCode: '',
			pfcatCode: '',
			selectYnList: [],
			intFreqUnitypeList: [],
			principalGuarMenu: [],

			//File 用參數
			url: [],
			uploadFile: {}, //上傳檔案
			uploadFiles: [] // 已上傳檔案陣列
		};
	},
	watch: {
		intRateEdit: function (newVal, oldVal) {
			var self = this;
			if (newVal) {
				self.intRateEdit = parseFloat(newVal);
				self.proInfo.intRate = self.intRateEdit / 100;
			}
		}
	},
	mounted: function () {},
	methods: {
		getIntFreqUnitypeList() {
			let self = this;
			self.$api
				.getAdmCodeDetail({
					codeType: 'INT_FREQ_UNITTYPE'
				})
				.then(function (ret) {
					self.intFreqUnitypeList = ret.data;
				});
		},
		getProInfo: function (bankProCode, pfcatCode, eventId) {
			var self = this;
			self.resetModalVaule();
			Promise.all([self.getIntFreqUnitypeList()]).then(() => {
				if (eventId) {
					self.doViewProLog(bankProCode, pfcatCode, eventId); // //審核資料
				} else {
					self.getProductInfo(bankProCode, pfcatCode); // 基本資料 共用資料
					self.getProductCommInfo(bankProCode, pfcatCode); // 附加資料
				}
			});
		},
		getProductInfo: function (bankProCode, pfcatCode, callback) {
			var self = this;
			self.$api
				.getProductInfoApi({
					proCode: bankProCode,
					pfcatCode: pfcatCode
				})
				.then(function (ret) {
					if (_.isNil(ret.data)) {
						ret.data = {};
						self.$bi.alert('資料不存在');
						return;
					}
					if (_.isNil(ret.data.pfdInfo)) {
						ret.data.pfdInfo = {};
					}

					self.proInfo = ret.data;
					self.proCode = bankProCode;
					self.pfcatCode = pfcatCode;

					var selectYnList = [];
					self.$api
						.getAdmCodeDetail({
							codeType: 'SELECT_YN'
						})
						.then(function (ret) {
							selectYnList = ret.data;
							if (!_.isUndefined(self.proInfo.pfdInfo.buyYn)) {
								var buyYnObjs = _.filter(selectYnList, {
									codeValue: self.proInfo.pfdInfo.buyYn
								});
								self.proInfo.pfdInfo.buyYn = buyYnObjs[0].codeName;
							}

							if (!_.isUndefined(self.proInfo.buyYn)) {
								var buyYnObjs = _.filter(selectYnList, {
									codeValue: self.proInfo.buyYn
								});
								self.proInfo.buyYn = buyYnObjs[0].codeName;
							}

							if (!_.isUndefined(self.proInfo.sellYn)) {
								var sellYnObjs = _.filter(selectYnList, {
									codeValue: self.proInfo.sellYn
								});
								self.proInfo.sellYn = sellYnObjs[0].codeName;
							}

							if (!_.isUndefined(self.proInfo.profInvestorYn)) {
								var profInvestorYnObjs = _.filter(selectYnList, {
									codeValue: self.proInfo.profInvestorYn
								});
								self.proInfo.profInvestorYn = profInvestorYnObjs[0].codeName;
							}
						});

					if (!_.isUndefined(self.proInfo.pfdInfo.guaranteeStatus)) {
						var dcdGuaranteeTypeList = [];
						self.$api
							.getAdmCodeDetail({
								codeType: 'DCD_GUARANTEE_TYPE'
							})
							.then(function (ret) {
								dcdGuaranteeTypeList = ret.data;
								var dcdGuaranteeTypeObjs = _.filter(dcdGuaranteeTypeList, {
									codeValue: self.proInfo.pfdInfo.guaranteeStatus
								});
								self.proInfo.pfdInfo.guaranteeStatus = dcdGuaranteeTypeObjs[0].codeName;
							});
					}

					if (!_.isUndefined(self.proInfo.pfdInfo.linkTargetDesc)) {
						var dcdLinkTargetList = [];
						self.$api
							.getAdmCodeDetail({
								codeType: 'DCD_LINK_TARGET'
							})
							.then(function (ret) {
								dcdLinkTargetList = ret.data;
								var dcdLinkTargetObjs = _.filter(dcdLinkTargetList, {
									codeValue: self.proInfo.pfdInfo.linkTargetDesc
								});
								self.proInfo.pfdInfo.linkTargetDesc = dcdLinkTargetObjs[0].codeName;
							});
					}

					if (!_.isUndefined(self.proInfo.pfdInfo.capitalRaiseStatusCode)) {
						var dcdRaiseStatusList = [];
						self.$api
							.getAdmCodeDetail({
								codeType: 'DCD_RAISE_STATUS'
							})
							.then(function (ret) {
								dcdRaiseStatusList = ret.data;
								var dcdRaiseStatusObjs = _.filter(dcdRaiseStatusList, {
									codeValue: self.proInfo.pfdInfo.capitalRaiseStatusCode
								});
								self.proInfo.pfdInfo.capitalRaiseStatusCode = dcdRaiseStatusObjs[0].codeName;
							});
					}

					if (!_.isUndefined(self.proInfo.targetCusBu)) {
						var targetCusBuList = [];
						self.$api
							.getAdmCodeDetail({
								codeType: 'CUS_BU'
							})
							.then(function (ret) {
								targetCusBuList = ret.data;
								var targetCusBuObjs = _.filter(targetCusBuList, {
									codeValue: self.proInfo.targetCusBu
								});
								if (targetCusBuObjs.length > 0) {
									self.proInfo.targetCusBu = targetCusBuObjs[0].codeValue;
									self.proInfo.targetCusBuName = targetCusBuObjs[0].codeName;
								}
							});
					}

					// 波動類型
					if (!_.isUndefined(self.proInfo.volatilityType)) {
						self.$api
							.getAdmCodeDetail({
								codeType: 'VOLATILITY_TYPE'
							})
							.then(function (ret) {
								self.volatilityTypeList = ret.data;
								var volatilityTypeObjs = _.filter(self.volatilityTypeList, {
									codeValue: self.proInfo.volatilityType
								});
								// 區分編輯與(檢視、審核)
								if ((volatilityTypeObjs.length > 0) & (self.actionType === 'EDIT')) {
									self.proInfo.volatilityType = volatilityTypeObjs[0].codeValue;
								} else {
									self.proInfo.volatilityTypeName = volatilityTypeObjs[0].codeName;
								}
							});
					}

					// 配息頻率
					if (!_.isUndefined(self.proInfo.intFreqUnitype)) {
						self.$api
							.getAdmCodeDetail({
								codeType: 'INT_FREQ_UNITTYPE'
							})
							.then(function (ret) {
								self.intFreqUnitypeList = ret.data;
								var intFreqUnitypeObjs = _.filter(self.intFreqUnitypeList, {
									codeValue: self.proInfo.intFreqUnitype
								});
								if (self.actionType === 'EDIT') {
									self.proInfo.intFreqUnitype = intFreqUnitypeObjs[0].codeValue;
								} else {
									self.proInfo.intFreqUnitypeName = intFreqUnitypeObjs[0].codeName;
								}
							});
					}

					// 保本要求
					self.$api
						.getAdmCodeDetail({
							codeType: 'GUAR_YN'
						})
						.then(function (ret) {
							self.principalGuarMenu = ret.data;
							var principalGuarYnObjs = _.filter(self.principalGuarMenu, {
								codeValue: self.proInfo.principalGuarYn
							});
							// 區分編輯與(檢視、審核)
							if (self.actionType === 'EDIT') {
								self.proInfo.principalGuarYn = principalGuarYnObjs[0].codeValue;
							} else {
								self.proInfo.principalGuarYn = principalGuarYnObjs[0].codeName;
							}
						});

					// 配息率
					if (!_.isUndefined(self.proInfo.intRate)) {
						self.intRateEdit = self.proInfo.intRate * 100;
					}

					// 理財需求
					if (!_.isUndefined(self.proInfo.finReqCode)) {
						self.finReqCodes = self.proInfo.finReqCode.split(',');
					}

					if (!_.isUndefined(self.proInfo.selprocatNames)) {
						var selprocatNames = self.proInfo.selprocatNames.replaceAll(',', '、');
						self.proInfo.selprocatNames = selprocatNames;
					}

					callback && callback();
				});
		},
		getProductCommInfo: function (bankProCode, pfcatCode) {
			var self = this;
			// 商品附加資料
			self.$api
				.getProductsCommInfo({
					proCode: bankProCode,
					pfcatCode: pfcatCode
				})
				.then(function (ret) {
					if (!_.isNil(ret.data)) {
						if (ret.data.proDocs) {
							self.otherFileList = ret.data.proDocs; // 其他相關附件
							self.otherFileList.forEach(function (item) {
								// 其他相關附件 檔案顯示時間範圍
								item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
							});
						}

						self.proFileList = ret.data.proFiles;
						if (!_.isNil(self.proFileList)) {
							self.uploadFiles['A'] = self.proFileList.filter((proFile) => proFile.fileType === 'A')[0];
							self.uploadFiles['D'] = self.proFileList.filter((proFile) => proFile.fileType === 'D')[0];
							self.uploadFiles['F'] = self.proFileList.filter((proFile) => proFile.fileType === 'F')[0];
							self.uploadFiles['G'] = self.proFileList.filter((proFile) => proFile.fileType === 'G')[0];
							self.url['A'] = self.uploadFiles['A'] ? self.uploadFiles['A'].url : null;
							self.url['D'] = self.uploadFiles['D'] ? self.uploadFiles['D'].url : null;
							self.url['F'] = self.uploadFiles['F'] ? self.uploadFiles['F'].url : null;
							self.url['G'] = self.uploadFiles['G'] ? self.uploadFiles['G'].url : null;
						}
					}
				});
		},
		updateProduct: function () {
			var self = this;
			var url = self.config.apiPath + '/pro/product';
			self.commInfo.finReqCode = self.finReqCodes.join(',');

			Object.keys(self.url).forEach((key) => {
				// 檢查是否只有輸入url 沒有上傳檔案的type
				if (self.url[key] !== null) {
					// 有輸入url
					let typeInclude = self.proFileList.some((obj) => obj.fileType === key); // 找出是否有存在fileList
					if (!typeInclude) {
						var proFile = {};
						proFile.fileType = key;
						self.proFileList.push(proFile);
					}
				}
			});

			self.proFileList.forEach((e) => {
				e.createDt = null; // 移除日期避免轉型錯誤
				e.url = self.url[e.fileType];
			});
			self.commInfo = self.proInfo;
			self.commInfo.proFiles = self.proFileList;
			var formData = new FormData();
			// json model
			formData.append('model', JSON.stringify(self.commInfo));

			// upload file
			for (const key in self.uploadFiles) {
				let item = self.uploadFiles[key];
				if (item) {
					formData.append('files', item);
				}
			}
			self.$api.patchProductApi(formData).then(function (ret) {
				self.$bi.alert('提交審核成功。');
				self.gotoPage(0);
				self.resetModalVaule();
			});
		},
		resetModalVaule: function () {
			var self = this;
			$('[type="file"]').val(null);
			self.url = [];
			self.proFile = [];
			self.uploadFile = {};
			self.uploadFiles = [];
		},
		doViewProLog: function (proCode, pfcatCode, eventId) {
			//審核資料
			var self = this;
			self.resetModalVaule();
			if (proCode) {
				const callback = () => {
					self.getProductCommLogInfo(eventId);
				};

				self.getProductInfo(proCode, pfcatCode, callback);
			}
		},
		getProductCommLogInfo: function (eventId) {
			var self = this;
			self.$api
				.getProductLogApi({
					eventId: eventId
				})
				.then(function (ret) {
					if (!_.isNil(ret.data)) {
						// 取得維護資料帶入
						// 共同資料
						// 配息頻率
						if (!_.isUndefined(ret.data.intFreqUnitype)) {
							self.$api
								.getAdmCodeDetail({
									codeType: 'INT_FREQ_UNITTYPE'
								})
								.then(function (r) {
									self.intFreqUnitypeList = r.data;
									var intFreqUnitypeObjs = _.filter(self.intFreqUnitypeList, {
										codeValue: ret.data.intFreqUnitype
									});
									self.proInfo.intFreqUnitypeName = intFreqUnitypeObjs[0].codeName;
								});
						}
						// 保本要求
						if (!_.isUndefined(ret.data.principalGuarYn)) {
							self.$api
								.getAdmCodeDetail({
									codeType: 'GUAR_YN'
								})
								.then(function (r) {
									self.principalGuarMenu = r.data;
									var principalGuarYnObjs = _.filter(self.principalGuarMenu, {
										codeValue: ret.data.principalGuarYn
									});
									self.proInfo.principalGuarYn = principalGuarYnObjs[0].codeName;
								});
						}
						// 配息率
						self.proInfo.intRate = ret.data.intRate;

						//附加資料
						self.proInfo.memo = ret.data.memo; // 備註

						if (ret.data.proDocs) {
							self.otherFileList = ret.data.proDocs; // 其他相關附件
							self.otherFileList.forEach(function (item) {
								// 其他相關附件 檔案顯示時間範圍
								item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
							});
						}

						var proFileList = ret.data.proFiles;
						if (!_.isNil(proFileList)) {
							self.uploadFiles['A'] = proFileList.filter((proFile) => proFile.fileType === 'A')[0];
							self.uploadFiles['D'] = proFileList.filter((proFile) => proFile.fileType === 'D')[0];
							self.uploadFiles['F'] = proFileList.filter((proFile) => proFile.fileType === 'F')[0];
							self.uploadFiles['G'] = proFileList.filter((proFile) => proFile.fileType === 'G')[0];
						}
					}
				});
		},
		triggerFile: function (event, fileType) {
			var self = this;
			self.uploadFile[fileType] = event.target.files[0];
			self.uploadFile[fileType].showName = event.target.files[0].name;
		},
		doUploadFile: function (fileType) {
			var self = this;
			if (self.uploadFile[fileType]) {
				if (self.uploadFile[fileType].size > 10485760) {
					this.$bi.alert('檔案大小不得超過10MB！');
					return;
				}
				var proFile = {};

				proFile.fileName = self.uploadFile[fileType].name;
				proFile.showName = self.uploadFile[fileType].name;

				proFile.contentType = self.uploadFile[fileType].type;
				proFile.fileSize = self.uploadFile[fileType].size;
				proFile.fileType = fileType;

				if (!self.proFileList || self.proFileList.length <= 0) {
					self.proFileList.push(proFile);
				} else {
					// 有資料先刪除就檔案再新增
					self.proFileList.forEach((e, index) => {
						if (e.fileType === fileType) {
							self.proFileList.splice(index, 1);
						}
					});
					self.proFileList.push(proFile);
				}

				self.uploadFiles[fileType] = null; // 先將原先檔案清除
				self.uploadFiles[fileType] = self.uploadFile[fileType];

				$('#pfdUploadFile' + fileType).val(''); // 清空上傳區域檔案
				self.commInfo.isUpdateProFiles = true;
				self.uploadFile[fileType] = null;
			}
		},
		deleteFiles: function (fileType, proFileId) {
			var self = this;
			if (self.proFileList.length > 0) {
				self.proFileList.forEach((e, index, arr) => {
					if (e.proFileId === proFileId) {
						arr.splice(index, 1);
					}
				});
			}

			self.uploadFiles[fileType] = null;
		}
	} // methods end
};
</script>
