/*
 * Dashforge File Manager Page
 *
 * This style is use in file manager page.
 *
 */
.app-filemgr {
  padding: 0 !important;
}

.aside-fixed.minimize~.app-filemgr.content-fixed {
  width: calc(100% - 60px);
}

.aside-fixed~.app-filemgr.content-fixed {
  width: calc(100% - 240px);
}

@media (min-width:375px) and (max-width: 991px) {
  .aside-fixed~.app-filemgr.content-fixed {
    width: 100%;
  }
  .aside-fixed.minimize~.app-filemgr.content-fixed {
    width: 100%;
  }
}

#filemgrMenuclose {
  position: absolute;
  z-index: 38;
  left: 210px;
  top: 28px;
  font-size: 28px;
  line-height: 1;
  color: var(--bg-primary);
  font-weight: bold;
  box-shadow: 0px 0 8px rgba(149, 186, 253, 0.4);
  transition: all 0.4s ease 0s;
}

#filemgrMenuclose.close {
  transform: rotate(180deg);
}


/* @media (max-width: 991.98px) {
   .filemgr-sidebar-show .filemgr-sidebar {
    left: 0;
    opacity: 1;
    visibility: visible; }
   .filemgr-sidebar-show .filemgr-content {
    transform: translateX(220px); } } */

.filemgr-wrapper {
  position: fixed;
  top: 70px;
  bottom: 0;
  left: 0;
  right: 0;

}

.filemgr-wrapper.filemgr-sidebar-close {
  margin-left: -220px;
}

.filemgr-wrapper-two {
  position: relative;
  height: calc(100vh - 90px);
  top: auto;
}

.filemgr-wrapper-two.filemgr-sidebar-close {
  margin-left: -220px;
}

@media (min-width: 992px) {
  .filemgr-wrapper-two {
    height: calc(100vh - 70px);
  }
}



.filemgr-sidebar {
  position: absolute;
  top: 10px;
  bottom: 12px;
  left: 10px;
  width: 210px; 
  opacity: 1;
  visibility: visible;
  border-radius: 16px;
  box-shadow: 0 3px 4px rgba(0,0,0,.075);
  border: 1px solid var(--border-card); 
  background-color: #fff;
}

/* @media (prefers-reduced-motion: reduce) {
    .filemgr-sidebar {
      transition: none; } }
  @media (min-width: 992px) {
    .filemgr-sidebar {
      left: 0;
      opacity: 1;
      visibility: visible; } } */

.filemgr-sidebar-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100px;
  padding: 20px 15px 10px;
  background-color: #dceae7 ;
  border-radius: 16px 16px 0 0;
}

.filemgr-sidebar-header h5 {
  font-size: 17px
}

.bi-cart {
  font-size: 20px;
  color: #1e477c;
  font-weight: 900;
}

.filemgr-sidebar-header .cart-container .bi-cart{
  position: relative;
}
.filemgr-sidebar-header h5{
  margin-right: 5px;
}
/* .filemgr-sidebar-header .cart-container::after {
  position: absolute;
  content: "8";
  color: #fff;
  top: -56%;
  right: -38%;
  font-size: 8px;
  background: #1e477c;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  padding-left: 7%;
  padding-bottom: 2%;
  border: 2px solid #FFF;
  font-weight: normal;
} */
.cart-container .badge {
  position: absolute;
  bottom: 27%;
  right: 27%;
}
.filemgr-sidebar-header .dropdown-link {

  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 15px;
  position: relative;
  z-index: 5;
}

.filemgr-sidebar-header .dropdown-menu {
  box-shadow: none;
  width: 100%;
  max-width: 220px;
  border-width: 0 0 1px;
  border-color: rgba(72, 94, 144, 0.16);
  padding: 10px;
  margin: 70px 0 0 !important;
  border-radius: 0;
  transform: none !important;
  z-index: 30;
  background: #f8f9fa;
}

.filemgr-sidebar-header .dropdown-menu::before {
  content: '';
  position: absolute;
  top: -10px;
  right: 28px;
  border-bottom: 10px solid rgba(192, 204, 218, 0.53);
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
}

.filemgr-sidebar-header .dropdown-menu::after {
  content: '';
  position: absolute;
  top: -8.5px;
  right: 29px;
  border-bottom: 9px solid #f8f9fa;
  border-left: 9px solid transparent;
  border-right: 9px solid transparent;
}

.filemgr-sidebar-header .dropdown-item {
  display: flex;
  align-items: center;
  padding: 4px 10px;
}

.filemgr-sidebar-header .dropdown-item+.dropdown-item {
  margin-top: 4px;
}

.filemgr-sidebar-header .dropdown-item:hover:after {
  content: '→';
  position: absolute;
  right: 20px;
}

.filemgr-sidebar-header .dropdown-item i {
  font-size: 18px;
  margin-right: 10px;
}

@media (min-width: 1600px) {
  .section-nav~.container-fluid {
    padding-right: 160px;
  }
}


#navSection {
  border-left: 1px solid rgba(72, 94, 144, 0.16);
  padding-left: 15px;
}

.section-nav {
  position: fixed;
  top: 150px;
  bottom: 150px;
  right: 0;
  width: 160px;
  padding: 20px 15px;
  display: none;
}

@media (min-width: 1600px) {
  .section-nav {
    display: block;
  }
}

.section-nav .nav-label {
  font-size: 10px;
  font-weight: 500;
  font-family: -apple-system, BlinkMacSystemFont, "Inter UI", Roboto, sans-serif;
  text-transform: uppercase;
  letter-spacing: .7px;
  color: #8392a5;
  display: block;
  margin-bottom: 15px;
}

.section-nav .nav-link {
  color: #1b2e4b;
  padding: 0;
  display: block;
  position: relative;
  font-size: 13px;
}

.section-nav .nav-link:hover {
  color: #04539e;
}

.section-nav .nav-link+.nav-link {
  margin-top: 8px;
}

.section-nav .nav-link.active {
  color: #04539e;
  font-weight: 500;
  letter-spacing: -.12px;
}

.section-nav .nav-link.active::after {
  content: '';
  position: absolute;
  top: 50%;
  right: -31px;
  margin-top: -.5px;
  background-color: #04539e;
  height: 1px;
  width: 30px;
}


.filemgr-sidebar-body {
  position: absolute;
  top: 100px;
  left: 0;
  right: 0;
  bottom: 0; 
  overflow-y: auto;
}


.heighter-cusblock .filemgr-sidebar-header {
  height: 235px;
}

.heighter-cusblock .filemgr-sidebar-body {
  top: 235px;
}
  
.btn-fast-block {
  padding: 10px 10px 4px;
  margin-top: 16px;
}

.btn-fast-block label {
  font-weight: bold;
  display: flex;
  align-items: center;
  margin-left: 10px;
  margin-bottom: 4px;
}

.btn-fast-block label i {
  font-size: 16px;
  margin-right: 8px;
}

.btn-fast-block i {
  font-size: 24px;
}

.btn-fast-block .btn {
  border-radius: 0;
  font-size: 12px;
  padding: 0.1rem 0 0;
  width: calc(96% / 3);
  margin: 0px;
  margin-bottom: 4px;
  height: 64px;
}


.filemgr-content {
  position: absolute;
  top: 0;
  left: 220px;
  right: 0;
  bottom: 0;
  padding: 10px;
  background: #fff;
  overflow-y: auto;
}
.filemgr-content header{
  margin-top: 10px;
}

@media (prefers-reduced-motion: reduce) {
  .filemgr-content {
    transition: none;
  }
}

/* @media (min-width: 992px) {
    .filemgr-content {
      left: 220px;
    } }  */
@media (min-width: 1400px) {

  /* .filemgr-content{
          left: 220px;
        } */
  .filemgr-content.with-sectionnav {
    left: 220px;
    right: 160px;
  }
}



/*客戶清單*/
/******forindex******/
#navclose {
  position: absolute;
  z-index: 999;
  display: block;
  top: 102px;
  left: 204px;
  transition: all .3s;
}

#navclose::before {
  font-family: bootstrap-icons !important; 
  content: '\F4DA';
  font-size: 20px;
  padding: 4px;
  color: #EEEFF3;
  background: var(--bg-info);
  z-index: 999px;
  border-radius: 5px;
}
  
.filemgr-navleft {
  background-color: var(--bg-filemgr-sidebar-body);
  position: absolute;
  z-index: 99;
  top: 100px;
  bottom: 0;
  left: 0; 
  width: 220px;
  padding: 0px 0 10px;
  border-radius: 0 20px 20px 0;
}

@media (min-width: 768px) {
  #navclose {
    top: 70px;
  }

  .filemgr-navleft {
    top: 70px;
  }
}


.filemgr-cuslist-header {
  padding: 4px 10px;
}

.filemgr-cuslist-header h5 {
  font-size: 16px;
  font-weight: 700;
  letter-spacing: .5px; 
  color: var(--font);
  margin-top: 16px;
}
.filemgr-cuslist-body .tab-nav-line ul{
  margin-bottom: 4px;
}
.filemgr-cuslist-body .tab-nav-line .nav-line .nav-link {
  padding: 6px 6px;
  font-size: 13px;
}

.filemgr-cuslist-body .tab-nav-line .nav-line .nav-item+.nav-item {
  margin-left: 0;
}

.filemgr-navleft~.content-fixed {
  margin-left: 220px;
  width: calc(100% - 220px)
}

.navcolse .filemgr-navleft~.content-fixed {
  transform: translateX(-220px);
  transition: all .3s;
  width: 100%;
}

.navcolse .filemgr-navleft {
  transform: translateX(-220px);
  transition: all .3s;
}

.navcolse #navclose {
  left: -10px;
  transition: all .3s;
}
/******forcus-overview  顧客側選單******/
.app-filemgr .filemgr-navleft {
  top: 10px;  bottom: 10px;
  border-radius: 8px 16px 16px 8px;
}

.app-filemgr #navclose {
  top: 28px;
}

.app-filemgr .filemgr-navleft~.filemgr-wrapper-two {
  margin-left: 220px;
}

.navcolse .app-filemgr .filemgr-navleft~.filemgr-wrapper-two {
  margin-left: 0px;
  transition: all .3s;
}

.app-filemgr .filemgr-navleft~.filemgr-wrapper-two.filemgr-sidebar-close {
  margin-left: 0px;
  transition: all .3s;
}

.app-filemgr .filemgr-navleft~.filemgr-wrapper-two #filemgrMenuclose {
  top: 60px;
}

.navcolse .app-filemgr .filemgr-navleft~.filemgr-wrapper-two.filemgr-sidebar-close {
  margin-left: -220px;
}

.nav-sidebar.list-cus {
  display: flex;
}

  
.nav-sidebar.list-cus .nav-link:after {
  font-family: "bootstrap-icons";
  content: '\F282';
  position: absolute;
  right: 14px;
  font-size: 50%;
  transform: rotate(180deg);
  display: none;
}

.nav-sidebar.list-cus .nav-link.collapsed:after {
  transform: rotate(0deg);
}

.nav-sidebar.list-cus .nav-link sapn:first-child {
  margin-left: 12px; 
}

.table-cus {
  margin-bottom: 0;
}

.table-cus tbody th,
.table-cus td {
  font-size: 12px !important;
  padding: 2px 4px !important;
  background: #fafcff;
  vertical-align: middle;
}

.table-cus tbody th {
  padding-left: 16px !important;
  padding-right: 10px;
  width: 80px;
  color: #333;
}

.table-cus td {
  height: 34px;
  padding-left: 10px !important;
}

.cus-data {
  font-weight: normal;
  color: #111;
}



.media-folder {
  position: relative;
  border: 1px solid rgba(72, 94, 144, 0.16);
  padding: 10px 12px;
  background-color: #fff;
  border-radius: 0.25rem;
  transition: all 0.2s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .media-folder {
    transition: none;
  }
}

.media-folder:hover,
.media-folder:focus {
  border-color: #c0ccda;
}

.media-folder>svg {
  width: 42px;
  height: 42px;
  stroke-width: 1.5px;
  color: #04539e;
  opacity: .7;
}

.media-folder .media-body {
  margin-top: 5px;
  margin-left: 10px;
}

.media-folder .media-body h6 {
  margin-bottom: 1px;
}

.media-folder .media-body span {
  font-size: 12px;
  color: #8392a5;
  display: block;
}

/* @media (min-width: 992px) {
  .aside-filemgr + .content {
    margin-left: 60px; } } */