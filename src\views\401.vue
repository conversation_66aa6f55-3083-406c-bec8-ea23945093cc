<template>
	<div class="content content-auth bg-image">
		<div class="brand">
			<img th:src="@{/images/logo/logo.png}" height="45" />
		</div>

		<div class="container">
			<div class="row justify-content-center">
				<div class="col-12"></div>
				<div class="col-md-7 col-lg-5 col-xl-4">
					<div class="block-shadow text-center">
						<div class="block-header">
							<h4>登出PBS</h4>
						</div>

						<form class="form-login p-5">
							<div class="block-header">
								<h5>您已登出PBS，請關閉視窗，或重新登入</h5>
							</div>
						</form>

						<a @click="logout()" class="btn btn-primary btn-login" type="submit">重新登入</a>
					</div>
				</div>
			</div>
		</div>
	</div>
	<footer class="footer justify-content-center bg-transparent">Copyright © 2022 E.SUN BANK All rights reserved.</footer>
</template>

<script>
export default {
	data: function () {
		return {};
	},
	methods: {
		logout: function () {
			this.$router.push('/login');
		}
	}
};
</script>

<style scoped>
body {
	background: 778899;
}
/* For background color that browser auto fill input. */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
	transition: background-color 5000s ease-in-out 0s;
}
</style>
