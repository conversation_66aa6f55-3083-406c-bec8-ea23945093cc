<template>
	<dynamic-title></dynamic-title>
	<div class="card card-form-collapse">
		<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formsearch1">
			<h4>查詢條件</h4>
			<span class="tx-square-bracket">為必填欄位</span>
		</div>
		<div class="card-body collapse show" id="formsearch1">
			<div class="form-row">
				<div class="form-group col-12 col-lg-6">
					<label class="form-label">選擇系統角色</label>
					<select name="roleCode" id="roleCode" class="form-select" v-model="roleCode">
						<option value="">全部</option>
						<option v-for="roleData in roleMenu" :value="roleData.roleCode">[{{ roleData.roleCode }}]{{ roleData.roleName }}</option>
					</select>
				</div>
				<div class="form-group col-12 col-lg-6 justify-content-lg-start justify-content-end">
					<button class="btn btn-primary btn-search" @click="getAdmRoles()">查詢</button>
					<button class="btn btn-primary ms-1" @click="exportMenuData()">Excel下載</button>
				</div>
			</div>
		</div>
	</div>
	<div class="card card-table">
		<div class="card-header">
			<h4>系統角色列表</h4>
		</div>
		<div class="table-responsive">
			<table class="table table-RWD table-hover table-bordered">
				<thead>
					<tr>
						<th width="10%">代碼</th>
						<th width="20%">名稱</th>
						<th width="10%">提交日期</th>
						<th width="10%">提交人員</th>
						<th width="10%">審核日期</th>
						<th width="10%">審核人員</th>
						<th width="10%">審核狀態</th>
						<th width="15%">權限設定</th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="item in admRoleData">
						<td data-th="代碼">
							{{ item.roleCode }}
						</td>
						<td data-th="名稱" class="text-start">
							{{ item.roleName }}
						</td>
						<td data-th="提交日期">
							{{ item.createDt }}
						</td>
						<td data-th="提交人員">
							{{ item.createUserCode }}
							{{ item.createUserName }}
						</td>
						<td data-th="審核日期">
							{{ item.verifyDt }}
						</td>
						<td data-th="審核人員">
							{{ item.verifyUserCode }}
							{{ item.verifyUserName }}
						</td>

						<td v-if="item.status == null" data-th="審核狀態"></td>
						<!-- Parker 檢查權限 -->
						<td v-if="item.status == 'R'" data-th="審核狀態">
							<a href="#" class="tx-link tx-danger" @click.prevent="showRejectContent(item)">退回修改</a>
						</td>
						<td v-if="item.status == 'A'" data-th="審核狀態">覆核完成</td>
						<td v-if="item.status == 'P'" data-th="審核狀態">待覆核</td>

						<td v-if="item.status != 'P'" data-th="權限設定" class="text-center">
							<button
								type="button"
								class="btn btn-info btn-icon"
								data-bs-toggle="tooltip"
								href="javascript: void(0)"
								title="編輯"
								@click="editRoleAuthority(item.roleCode)"
							>
								<i class="bi bi-pen"></i>
							</button>
						</td>

						<!-- 權限檢視 -->
						<td v-if="item.status == 'P'" data-th="權限設定" class="text-center">
							<button type="button" class="btn btn-dark btn-icon" @click="viewRoleAuthority(item.eventId, item.roleName)">
								<i class="bi bi-search"></i>
							</button>
						</td>
						<!-- <td data-th="權限設定" class="text-center">
	<button
		type="button"
		class="btn btn-info btn-icon"
		data-bs-toggle="tooltip"
		href="javascript: void(0)"
		title="編輯"
		@click="editRoleAuthority(item.roleCode)"
	>
		<i class="bi bi-pen"></i>
	</button>
</td>
<td data-th="權限設定" class="text-center">
	<button
		type="button"
		class="btn btn-dark btn-icon"
		@click="viewRoleAuthority(item.eventId, item.roleName)"
	>
		<i class="bi bi-search"></i>
	</button>
</td> -->
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<!--Detail Modal-->
	<adm-role-review-detail ref="admRoleReviewDetail"></adm-role-review-detail>
	<!--reject Modal-->
	<modal :before-close="closeAlertModal" ref="closeAlertModal">
		<template v-slot:content="props">
			<div class="modal-dialog modal-dialog-centered">
				<div class="modal-content">
					<div class="modal-body pt-5">
						<div class="icon-alert"></div>
						<input
							type="text"
							id="recipient-name"
							disabled
							class="form-control form-control-plaintext tx-20 tx-danger text-center"
							v-model="rejectMsg"
						/>
					</div>
					<div class="modal-footer">
						<button class="btn btn-primary btn-glow" @click.prevent="props.close()">確認</button>
					</div>
				</div>
			</div>
		</template>
	</modal>
</template>

<script>
import dynamicTitle from '@/views/components/dynamicTitle.vue';
import admRoleReviewDetail from './include/admRoleReviewDetail.vue';
import modal from '@/views/components/model.vue';

export default {
	data: function () {
		return {
			//Api 用參數
			roleCode: '',
			roleName: null,
			//顯示用參數
			rejectMsg: null,
			//主要顯示資料
			roleMenu: [],
			admRoleData: [],
			roleMenuLogs: []
			// isOpenModal: null,
			// isOpenAlertModal: null
		};
	},
	components: {
		dynamicTitle,
		admRoleReviewDetail,
		modal
	},
	mounted: function () {
		this.getRoleMenu();
		this.getAdmRoles();
	},
	methods: {
		getRoleMenu: async function () {
			let ret = await this.$api.getRoleMenuApi();
			this.roleMenu = ret.data;
		},
		getAdmRoles: async function () {
			let ret = await this.$api.getAdmRolesApi(this.roleCode);
			this.admRoleData = ret.data;
		},
		exportMenuData: function () {
			if (!this.roleCode) {
				this.$bi.alert('系統角色為必填。');
				return;
			}

			let url = import.meta.env.VITE_API_URL_V1 + '/adm/roleMenuTree/export?roleCode=' + this.roleCode;
			let fileName = 'MenuPreview.xls';
			let xhr = new XMLHttpRequest();
			xhr.open('GET', url, true);
			xhr.responseType = 'blob';
			xhr.onload = function () {
				if (this.status === 200) {
					let blob = this.response;
					let reader = new FileReader();
					reader.readAsDataURL(blob);
					reader.onload = function (e) {
						let a = document.createElement('a');
						a.download = fileName;
						a.href = e.target.result;
						document.body.appendChild(a);
						a.click();
						document.body.removeChild(a);
					};
				}
			};
			xhr.send();
		},
		viewRoleAuthority: function (eventId, roleName) {
			this.$refs.admRoleReviewDetail.getDetail(eventId, roleName);
		},
		editRoleAuthority: function (roleCode) {
			this.$router.push('/adm/admRole/AdmRoleAuthority/' + roleCode);
		},
		showRejectContent: function (item) {
			this.rejectMsg = item.reason;
			this.$refs.closeAlertModal.open();
		},
		closeAlertModal: function () {
			this.$refs.closeAlertModal.close();
		}
	}
};
</script>
