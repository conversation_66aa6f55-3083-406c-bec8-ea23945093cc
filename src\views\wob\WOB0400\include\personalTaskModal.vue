<template>
	<div>
		<!-- Modal 1 -->
		<vue-modal :is-open="modalStates.modal1" :before-close="() => closeModal('modal1')">
			<template v-slot:content="props">
				<div class="modal-dialog modal-xl modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">個人記事</h4>
							<button type="button" class="btn-close" @click.prevent="props.close()" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<table class="table table-RWD table-bordered table-horizontal-RWD">
								<tbody>
									<tr>
										<th class="wd-20p"><label>工作日期</label></th>
										<td class="wd-80p">
											<label>{{ tdRecTask.nextRemindDt }}</label>
										</td>
									</tr>
									<tr>
										<th class="wd-20p"><label>工作時間</label></th>
										<td class="wd-80p">
											<label>{{ tdRecTask.nextRemindTime }}</label>
										</td>
									</tr>
									<tr>
										<th class="wd-20p"><label>提前通知</label></th>
										<td class="wd-80p">
											<label>{{ tdRecTask.advNceName }}</label>

											<template v-if="tdRecTask.advNce === 'Y'">
												<label> {{ tdRecTask.advNceDay }}</label>
												<label v-if="tdRecTask.advNcePrd === 'W'">週</label>
												<label v-if="tdRecTask.advNcePrd === 'D'">天</label>
											</template>
										</td>
									</tr>
									<tr>
										<th class="wd-20p"><label>內容</label></th>
										<td class="wd-80p">
											<label>{{ tdRecTask.content }}</label>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
						<div class="modal-footer">
							<input class="btn btn-white" id="personalModalCloseButton" type="button" value="關閉" @click.prevent="props.close()" />
							<input class="btn btn-danger" id="btnDelete" type="button" value="刪除" @click="deleteTdRec(tdRecTask.recCode)" />
							<input class="btn btn-primary" id="btnModify" type="button" value="修改" @click="doUpdate()" />
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- Modal 1 end -->

		<!-- Modal 2 維護個人記事 -->
		<vue-modal :is-open="modalStates.modal2" :before-close="() => closeModal('modal2')">
			<template v-slot:content="props">
				<div class="modal-dialog modal-xl modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">個人記事維護</h4>
							<button type="button" class="btn-close" @click.prevent="closeModal('modal2')" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<div class="card card-form shadow-none">
								<div class="card-header">
									<h4>個人記事事項</h4>
									<span class="tx-square-bracket">為必填欄位</span>
								</div>

								<div class="card-content">
									<vue-form v-slot="{ errors, validate }" ref="queryForm">
										<table class="biv-table table table-RWD table-bordered">
											<tbody>
												<tr>
													<th>
														<label class="form-label tx-require">工作日期</label>
													</th>
													<td>
														<div class="input-group">
															<vue-field
																type="date"
																name="nextRemindDt"
																class="form-control"
																v-model="nextRemindDt"
																rules="required"
																:class="{ 'is-invalid': errors.nextRemindDt }"
																label="工作日期"
															></vue-field>
															<div>
																<span class="text-danger" v-show="errors.nextRemindDt">{{
																	errors.nextRemindDt
																}}</span>
															</div>
														</div>
													</td>
													<th>
														<label class="form-label tx-require">工作時間</label>
													</th>
													<td>
														<div class="input-group">
															<vue-field
																as="select"
																class="form-select"
																id="psnTaskHour"
																name="psnTaskHour"
																v-model="psnTaskHour"
																rules="required"
																:class="{ 'is-invalid': errors.psnTaskHour }"
																label="工作時間"
															>
																<option value="00">00</option>
																<option value="01">01</option>
																<option value="02">02</option>
																<option value="03">03</option>
																<option value="04">04</option>
																<option value="05">05</option>
																<option value="06">06</option>
																<option value="07">07</option>
																<option value="08">08</option>
																<option value="09">09</option>
																<option value="10">10</option>
																<option value="11">11</option>
																<option value="12">12</option>
																<option value="13">13</option>
																<option value="14">14</option>
																<option value="15">15</option>
																<option value="16">16</option>
																<option value="17">17</option>
																<option value="18">18</option>
																<option value="19">19</option>
																<option value="20">20</option>
																<option value="21">21</option>
																<option value="22">22</option>
																<option value="23">23</option>
															</vue-field>
															<span class="input-group-text">時</span>
															<div>
																<span class="text-danger" v-show="errors.psnTaskHour">{{ errors.psnTaskHour }}</span>
															</div>
															<vue-field
																as="select"
																class="form-select"
																id="psnTaskMin"
																name="psnTaskMin"
																v-model="psnTaskMin"
																rules="required"
																:class="{ 'is-invalid': errors.psnTaskMin }"
																label="工作時間"
															>
																<option selected="selected" value="00">00</option>
																<option v-for="minute in selectMinutes" :value="minute">{{ minute }}</option>
															</vue-field>
															<span class="input-group-text">分</span>
															<div>
																<span class="text-danger" v-show="errors.psnTaskMin">{{ errors.psnTaskMin }}</span>
															</div>
														</div>
													</td>
												</tr>
												<tr>
													<th>
														<label class="form-label tx-require">到期通知設定</label>
													</th>
													<td colspan="3">
														<div class="input-group">
															<div class="form-check form-check-inline">
																<vue-field
																	type="radio"
																	name="apptAdvNceYn"
																	class="form-check-input"
																	id="apptAdvNce_NN"
																	value="N"
																	v-model="advNce"
																	:class="{ 'is-invalid': errors.advNce }"
																	rules="required"
																	label="到期通知設定"
																></vue-field>
																<label class="form-check-label">不提前通知</label>
															</div>
															<div class="form-check form-check-inline">
																<vue-field
																	type="radio"
																	name="apptAdvNceYn"
																	class="form-check-input"
																	id="apptAdvNce_YY"
																	value="Y"
																	v-model="advNce"
																	:class="{ 'is-invalid': errors.advNce }"
																	rules="required"
																	label="到期通知設定"
																>
																</vue-field>
																<label class="form-check-label">提前</label>
															</div>

															<vue-field
																class="form-control"
																name="advNceDay"
																type="text"
																size="30"
																value="advNceDay"
																v-model="advNceDay"
																:class="{ 'is-invalid': errors.advNceDay }"
																label="提前通知天/週數"
																:rules="advNce === 'Y' ? 'required' : ''"
															>
															</vue-field>
															<div style="height: 3px">
																<span class="text-danger" v-show="errors.advNceDay">{{ errors.advNceDay }}</span>
															</div>

															<vue-field
																as="select"
																class="form-select"
																id="advNcePrd"
																name="advNcePrd"
																v-model="advNcePrd"
																:class="{ 'is-invalid': errors.advNcePrd }"
																:rules="advNcePrd === 'Y' ? 'required' : ''"
																label="提前通知週期"
															>
																<option value="D">日</option>
																<option value="W">週</option>
															</vue-field>
															<div style="height: 3px">
																<span class="text-danger" v-show="errors.advNcePrd">{{ errors.advNcePrd }}</span>
															</div>

															<span class="input-group-text">通知</span>
														</div>
													</td>
												</tr>
												<tr>
													<th><label class="form-label tx-require">主旨</label></th>
													<td colspan="3">
														<div class="input-group">
															<vue-field
																class="form-control"
																name="title"
																type="text"
																size="30"
																value="title"
																v-model="title"
																:class="{ 'is-invalid': errors.title }"
																label="主旨"
																rules="required"
															>
															</vue-field>
															<div style="height: 3px">
																<span class="text-danger" v-show="errors.title">{{ errors.title }}</span>
															</div>
														</div>
													</td>
												</tr>
												<tr>
													<th>
														<label class="form-label tx-require">內容</label>
													</th>
													<td colspan="3">
														<vue-field
															as="textarea"
															class="form-control"
															id="content"
															name="content"
															rows="5"
															cols="50"
															size="400"
															v-model="content"
															:class="{ 'is-invalid': errors.content }"
															label="內容"
															rules="required"
														>
														</vue-field>
														<div style="height: 3px">
															<span class="text-danger" v-show="errors.content">{{ errors.content }}</span>
														</div>
													</td>
												</tr>
												<tr>
													<th>
														<span class="form-label">常用句</span>
													</th>
													<td colspan="3">
														<div class="input-group">
															<select name="reuseWord" class="form-select" id="reuseWord" v-model="reuseWord">
																<option
																	v-for="selectWords in wobReuseWords"
																	v-show="selectWords.words"
																	:value="selectWords.words"
																>
																	{{ selectWords.words }}
																</option>
															</select>
															<button
																type="button"
																class="btn btn-primary"
																id="setContent"
																@click="appendReuseWord('content')"
															>
																加入
															</button>
															<input
																class="form-control"
																id="words"
																type="text"
																size="20"
																maxlength="20"
																v-model="newReuseWord"
															/>
															<button type="button" class="btn btn-primary" id="wordAdd" @click="insertReuseWord()">
																新增
															</button>
															<button
																type="button"
																class="btn btn-primary"
																id="wordSetting"
																@click.prevent="openModal('reuseWordModal')"
															>
																設定
															</button>
														</div>
													</td>
												</tr>
											</tbody>
										</table>
									</vue-form>
								</div>
							</div>
						</div>
						<div class="modal-footer text-alignRight" id="modifyTaskFooter">
							<input
								class="btn btn-white"
								id="personalTaskModalCloseButton"
								type="button"
								value="關閉"
								@click.prevent="closeModal('modal2')"
							/>
							<input class="btn btn-primary" id="btnSave" type="button" value="儲存" @click="updatePersonalTdRec()" />
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- Modal 2 end -->
		<vue-modal :is-open="modalStates.reuseWordModal" :before-close="closeModal('reuseWordModal')">
			<template v-slot:content="props">
				<vue-cus-reuse-word-modal
					:close="props.close"
					:id="'personalReuseWordModal'"
					:wob-reuse-words="wobReuseWords"
					:super-modal-name="'updatePersonalTaskModal'"
				></vue-cus-reuse-word-modal>
			</template>
		</vue-modal>
	</div>
</template>
<script>
import _ from 'lodash';
import moment from 'moment';
import { Field, Form } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import vueCusReuseWordModal from '@/views/cus/include/reuseWordModal.vue';

export default {
	components: {
		vueModal,
		vueCusReuseWordModal,
		'vue-form': Form,
		'vue-field': Field
	},
	props: {
		recCode: String,
		getCalendarTasks: Function
	},

	data: function () {
		var minutes = [];
		for (var i = 1; i < 60; i++) {
			minutes.push(String(i).padStart(2, '0'));
		}
		return {
			modalStates: {
				modal1: false,
				modal2: false,
				reuseWordModal: false
			},

			// 選單
			selectMinutes: minutes,

			//顯示用參數
			tdRecTask: {},

			//常用句機制
			reuseWord: null,
			newReuseWord: null,
			wobReuseWords: null,
			//API用參數
			// recCode: null,
			nextRemindDt: null, // 工作日期
			psnTaskHour: '00', // 工作時間(時)
			psnTaskMin: '00', // 工作時間(分)
			advNce: 'D', // 到期通知設定
			advNceDay: null, // 到期通知天數
			advNcePrd: null, // 到期通知單位
			title: null, // 工作主旨
			content: null, // 工作內容

			forbiddenContentWords: [
				'KYC',
				'借',
				'印章',
				'密碼',
				'貸',
				'對帳單',
				'跨境',
				'新加坡',
				'香港',
				'到期',
				'帳上',
				'保證',
				'停售',
				'建議',
				'解約',
				'高齡'
			], // 禁用保留字
			forbiddenContentMsg: null // 禁用保留字錯誤提示訊息
		};
	},
	watch: {
		recCode: function (val) {
			var self = this;
			if (self.recCode) {
				self.getPersonalTask();
			}
		},
		content: function (str) {
			var self = this;
			self.checkForbiddenContentWords(str);
		}
	},
	mounted: function () {
		var self = this;
		self.getReuseWords();
		self.setDefaultReuseWords();
	},
	methods: {
		setDefaultReuseWords: function () {
			var self = this;
			self.wobReuseWords = [];
			for (var i = 0; i < 10; i++) {
				var tempWordObj = {
					wordsId: i + 1,
					words: null
				};
				self.wobReuseWords.push(tempWordObj);
			}
		},
		getReuseWords: function () {
			var self = this;
			self.$api.getReuseWordsApi().then(function (ret) {
				ret.data.forEach(function (item) {
					var index = item.wordsId - 1;
					self.wobReuseWords[index].words = item.words;
				});
			});
		},
		getPersonalTask: function () {
			var self = this;
			self.$api
				.getTdRecApi({
					recCode: self.recCode
				})
				.then(function (ret) {
					self.tdRecTask = ret.data;
				});
		},
		deleteTdRec: function (recCode) {
			var self = this;
			if (!recCode) {
				self.$bi.alert('請選擇欲刪除事件。');
				return;
			}

			self.$bi.confirm('確定要刪除此筆資料嗎?', {
				event: {
					confirmOk: function () {
						self.$api
							.deleteTdRecApi({
								recCode: recCode
							})
							.then(function (ret) {
								self.$bi.alert('刪除成功。');
								$('#personalModalCloseButton').click();
								if (self.getCalendarTasks) {
									self.getCalendarTasks();
								}
							});
					}
				}
			});
		},
		doUpdate: function () {
			var self = this;
			self.modalStates.modal2 = true;
			self.nextRemindDt = moment(self.tdRecTask.nextRemindDt).format('YYYY-MM-DD');
			self.advNce = self.tdRecTask.advNce;
			self.advNceDay = self.tdRecTask.advNceDay;
			self.advNcePrd = self.tdRecTask.advNcePrd;
			self.title = self.tdRecTask.title;
			self.content = self.tdRecTask.content;
			var nextRemindTime = self.tdRecTask.nextRemindTime.split(':');
			self.psnTaskHour = nextRemindTime[0];
			self.psnTaskMin = nextRemindTime[1];

			//常用句機制
			self.getReuseWords();
		},
		updatePersonalTdRec: function () {
			var self = this;
			self.modalStates.modal1 = true;

			self.$refs.queryForm.validate().then(function (pass) {
				if (pass.valid) {
					var nextRemindTime = self.psnTaskHour + ':' + self.psnTaskMin;

					self.$api
						.patchTdPersonalRecApi({
							tdRec: self.recCode,
							nextRemindDt: moment(self.nextRemindDt).format('YYYY-MM-DD'),
							nextRemindTime: nextRemindTime,
							advNce: self.advNce,
							advNceDay: self.advNce === 'Y' ? self.advNceDay : null,
							advNcePrd: self.advNce === 'Y' ? self.advNcePrd : null,
							title: self.title,
							content: self.content
						})
						.then(function (ret) {
							self.$bi.alert('更新成功');
							$('#personalTaskModalCloseButton').click();
							self.getCalendarTasks();
							self.getPersonalTask();
						});
				}
			});
		},
		insertReuseWord: function () {
			var self = this;
			if (self.newReuseWord) {
				var wordsId = null;
				for (var i = 0; i < 10; i++) {
					if (!self.wobReuseWords[i].words) {
						wordsId = i + 1;
						break;
					}
				}

				if (wordsId) {
					self.$api
						.postReuseWordsApi({
							wordsId: wordsId,
							words: self.newReuseWord
						})
						.then(function (ret) {
							self.newReuseWord = null;
							self.getReuseWords();
						});
				} else {
					if (!self.memo) {
						self.memo = '';
					}
					self.memo = self.memo + self.newReuseWord;
					self.$bi.alert('常句已超過設定個數，請由常用句設定頁面進行調整。');
				}
			}
		},
		appendReuseWord: function () {
			var self = this;
			if (!self.content) {
				self.content = '';
			}
			self.content = self.content + self.reuseWord;
		},
		// 禁用字檢核
		checkForbiddenContentWords: function (content) {
			var self = this;
			var forbiddenWord = null;
			if (content) {
				_.forEach(self.forbiddenContentWords, function (word) {
					if (_.includes(content, word) || _.includes(content, word.toLowerCase())) {
						forbiddenWord = word;
						return false;
					}
				});
			}

			if (forbiddenWord) {
				self.forbiddenContentMsg = '輸入內容包含「' + forbiddenWord + '」字樣，可能涉及主動推介或不當行銷，請再次確認訪談內容登載的妥適性';
			} else {
				self.forbiddenContentMsg = null;
			}
		},
		// 關閉子視窗
		doClose: function () {
			var self = this;
			self.reuseWord = null;
			self.newReuseWord = null;
		},
		closeModal: function (modalName) {
			this.modalStates[modalName] = false;
		},
		openModal: function (modalName) {
			this.modalStates[modalName] = true;
		}
	}
};
</script>
