<template>
	<modal ref="modal" :before-close="closeModal">
		<template v-slot:content="props">
			<div class="modal-dialog modal-dialog-centered modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title" id="detailModal">申請明細</h4>
						<button type="button" class="btn-close" @click="props.close()" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<table class="table table-bordered">
							<caption>
								角色設定預覽
							</caption>
							<tbody>
								<tr>
									<th>角色</th>
									<th>帳號可用性</th>
								</tr>
								<tr v-for="item in userPosEventItem">
									<td data-th="角色">
										<span>{{ item.branName }} > {{ item.roleName }}</span>
									</td>
									<td data-th="帳號可用性">
										<span>狀態：{{ item.startFlag == 'Y' ? '啟用' : '關閉' }}</span>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</template>
	</modal>
</template>

<script>
import modal from '@/views/components/model.vue';
export default {
	components: {
		modal
	},
	data: function () {
		return {
			userPosEventItem: []
		};
	},
	methods: {
		getDetail: async function (eventId) {
			let ret = this.$api.getUserAccountDetailApi();
			if (ret.data) {
				this.userPosEventItem = ret.data;
				this.$refs.modal.open();
			}
		},
		closeModal: function () {
			this.$refs.modal.close();
		}
	}
};
</script>
