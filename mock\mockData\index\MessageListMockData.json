{"status": 200, "data": [], "timestamp": "2025/04/21", "sqlTracer": [{"data": [], "sqlInfo": " SELECT MSG.MSG_ID, MSG.MSG_CODE, MSG.BRAN_CODE, MSG.VALID_BGN_DT, MSG.VALID_END_DT, MSG.MSG_TITLE, MSG.MSG_CONTENT  FROM GEN_MESSAGES MSG  INNER JOIN GEN_MESSAGE_MAPS MAP ON MSG.MSG_CODE = MAP.MSG_CODE  WHERE MAP.MSG_TYPE = :msgType  AND CAST(MSG.VALID_END_DT AS DATE)>=CAST(GETDATE() AS DATE)  AND CAST(MSG.VALID_BGN_DT AS DATE)<=CAST(GETDATE() AS DATE)  ORDER BY MSG.VALID_BGN_DT ,class com.bi.pbs.gen.web.model.GenNewsMessageResp,{msgType=M}"}]}