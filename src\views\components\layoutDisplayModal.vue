<template>
	<div class="modal fade show" ref="layoutDisplayModal" aria-modal="true" tabindex="-1">
		<div class="modal-dialog modal-dialog-centered">
			<div class="modal-content">
				<div class="modal-header">
					<h4 class="modal-title">元件選擇</h4>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<!-- 元件列表 -->
					<div
						v-if="isShowComponentList"
						class="position-absolute top-0 start-100 h-100 p-2 bg-white border w-50 d-grid grid-cols-3 gap-2 grid-rows-5"
					>
						<div
							v-for="comp in COMPONENT_LIST"
							class="d-flex flex-column align-items-center justify-content-evenly bg-blue cursor-grab shadow-sm"
							draggable="true"
							:ondragstart="drag"
							:id="comp.Name"
						>
							<div class="d-flex flex-column align-items-center">
								<div class="">{{ comp.Name }}</div>
							</div>
						</div>
					</div>
					<div class="d-flex justify-content-between">
						<div>點擊...選擇元件並拖拉至區塊</div>
						<div><button type="button" class="mb-1 border-1" @click="handleToggle">...</button></div>
					</div>
					<div>
						<div :class="getLayoutGrid(this.selectedLayoutId).Container" style="min-height: 40vh">
							<div
								v-for="(child, index) in childrenList"
								:class="child.className"
								:key="index"
								class="border border-primary d-flex justify-content-center"
								:ondragover="dragover"
								:ondrop="(ev) => drop(ev, child.Comp.WindowId)"
							>
								<div class="d-flex flex-column justify-content-center align-items-center">
									<div class="d-flex gap-2 flex-wrap">
										<el-tag
											v-for="(comp, idx) in child.Comp.Components"
											:key="comp"
											closable
											type="success"
											@close="handleClose(comp, child.Comp.WindowId)"
										>
											{{ comp }}
										</el-tag>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
					<button type="button" class="btn btn-primary" @click="handleConfirm">確認</button>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { Modal } from 'bootstrap';
import { LAYOUT_LIST, COMPONENT_LIST } from '@/views/layoutComponent/layoutData.js';
import componentListModal from '@/views/components/componentListModal.vue';

export default {
	components: {
		componentListModal
	},
	props: {
		selectedLayoutId: {
			type: Number,
			default: null
		}
	},
	data: function () {
		return {
			layoutDisplayModal: {},
			componentsOfWindow: [], // 每個window的components
			selectedWindowId: null, // 目前選擇的windowId
			isShowComponentList: false,
			COMPONENT_LIST,
			compLimit: 3 // 每個區塊最多允許的元件數量
		};
	},
	mounted: function () {
		// 確保在 Vue 完成渲染後再初始化 Modal
		this.$nextTick(() => {
			this.layoutDisplayModal = new Modal(this.$refs.layoutDisplayModal); // 在 Vue 渲染完成後初始化
		});
		this.$refs.layoutDisplayModal.addEventListener('hidden.bs.modal', () => {
			this.componentsOfWindow = [];
			this.selectedWindowId = null;
			this.isShowComponentList = false;
		});
	},
	methods: {
		show: function () {
			this.layoutDisplayModal.show();
		},
		handleConfirm: function () {
			if (this.componentsOfWindow.length !== this.getLayoutGrid(this.selectedLayoutId).Children.length) {
				this.$swal.fire({
					text: '請為每個區塊選擇元件',
					icon: 'error'
				});
				return;
			}
			this.$store.commit('tabStatus/setTabList', {
				layoutId: this.selectedLayoutId,
				components: this.componentsOfWindow
			});
			this.layoutDisplayModal.hide();
			this.componentsOfWindow = [];
			this.selectedWindowId = null;
		},
		getLayoutGrid: function (layoutId) {
			const layout = LAYOUT_LIST.find((layout) => layout.LayoutId === layoutId);
			if (layout) {
				return layout.LayoutGrid;
			}
			return { Container: '', Children: [] };
		},
		getComponents: function (components) {
			if (this.componentsOfWindow.find((item) => item.WindowId === this.selectedWindowId)) {
				this.componentsOfWindow = this.componentsOfWindow.map((item) => {
					if (item.WindowId === this.selectedWindowId) {
						return { ...item, Components: components };
					}
					return item;
				});
			} else {
				this.componentsOfWindow.push({
					WindowId: this.selectedWindowId,
					Components: components,
					WindowName: `Group${this.selectedWindowId}`
				});
			}
			this.selectedWindowId = null;
		},
		handleToggle: function () {
			this.isShowComponentList = !this.isShowComponentList;
		},
		drag: function (event) {
			event.dataTransfer.setData('text/plain', event.target.id);
		},
		dragover: function (event) {
			event.preventDefault();
			event.dataTransfer.dropEffect = 'move';
		},
		drop: function (event, windowId) {
			const data = event.dataTransfer.getData('text');
			const block = this.componentsOfWindow.find((item) => item.WindowId === windowId);
			if (block) {
				if (this.checkExistComponent(windowId, data)) return;
				if (this.checkCompCount(block.Components.length)) return;
				this.componentsOfWindow = this.componentsOfWindow.map((item) => {
					if (item.WindowId === windowId) {
						return { ...item, Components: [...item.Components, data] };
					}
					return item;
				});
			} else {
				this.componentsOfWindow.push({
					WindowId: windowId,
					Components: [data],
					WindowName: `Group${windowId}`
				});
			}
		},
		checkExistComponent: function (windowId, componentName) {
			if (this.componentsOfWindow.some((item) => item.WindowId === windowId && item.Components.includes(componentName))) {
				this.$swal.fire({
					text: '重複的元件',
					icon: 'error'
				});
				return true;
			}
			return false;
		},
		checkCompCount: function (blockCompoLength) {
			if (blockCompoLength >= this.compLimit) {
				this.$swal.fire({
					text: `每個區塊最多允許 ${this.compLimit} 個元件`,
					icon: 'error'
				});
				return true;
			}
			return false;
		},
		handleClose: function (comp, windowId) {
			this.componentsOfWindow = this.componentsOfWindow.map((item) => {
				if (item.WindowId === windowId) {
					return { ...item, Components: item.Components.filter((c) => c !== comp) };
				}
				return item;
			});
		}
	},
	computed: {
		childrenList() {
			const childrenArr = [];
			for (let i = 1; i <= this.getLayoutGrid(this.$props.selectedLayoutId).Children.length; i++) {
				if (this.$data.componentsOfWindow.find((item) => item.WindowId === i)) {
					childrenArr.push({
						className: this.getLayoutGrid(this.$props.selectedLayoutId).Children[i - 1],
						Comp: this.$data.componentsOfWindow.find((item) => item.WindowId === i)
					});
				} else {
					childrenArr.push({
						className: this.getLayoutGrid(this.$props.selectedLayoutId).Children[i - 1],
						Comp: { WindowId: i, Components: [], WindowName: `Group${i}` }
					});
				}
			}
			return childrenArr;
		}
	}
};
</script>
<style scoped>
.grid-cols-3 {
	grid-template-columns: repeat(3, 1fr);
}

.cursor-grab {
	cursor: grab;
}

.grid-rows-5 {
	grid-template-rows: repeat(5, 1fr);
}

.shadow-sm {
	box-shadow: 0 1px 2px 0 #0f172a;
}
</style>
