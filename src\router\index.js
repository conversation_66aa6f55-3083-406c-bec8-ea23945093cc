import { createRouter, createWebHashHistory } from 'vue-router';
import { getToken } from '@/utils/auth.js';

import adm from './adm.js';
import pro from './pro.js';
import wkf from './wkf.js';
import gen from './gen.js';
import cus from './cus.js';
import wob from './wob.js';

const router = createRouter({
	history: createWebHashHistory(import.meta.env.BASE_URL),
	routes: [
		{
			path: '/',
			name: 'default',
			component: () => import('../views/Default.vue'),
			beforeEnter: (to, from, next) => {
				// 檢查是否完成登入流程
				if (!document.cookie) {
					return next({ name: 'login' }); // 回到登入頁
				} else {
					next();
				}
			},
			children: [...adm, ...pro, ...wkf, ...gen, ...cus, ...wob]
		},
		{
			path: '/login',
			name: 'login',
			component: () => import('../views/Login.vue')
		},
		{
			path: '/selectpos',
			name: 'SelectPos',
			component: () => import('../views/SelectPos.vue')
		},
		{
			path: '/:pathMatch(.*)*', // 捕獲所有無法匹配的路徑
			name: '401',
			component: () => import('../views/401.vue')
		},

		{
			path: '/aggridtest',
			component: () => import('../views/AGGridtest.vue')
		}
	]
});

router.beforeEach(async (to, from, next) => {
	try {
		// 設定每 1 分鐘最多呼叫一次
		const lastRefresh = Number(localStorage.getItem('lastRefresh')) || 0;
		const now = Date.now();
		const shouldRefresh = now - lastRefresh > 60 * 1000;

		// if (import.meta.env.VITE_API_MOCK_ENABLED !== 'true' && shouldRefresh && getToken()) {
		// 	await refreshToken();
		// }

		next();
	} catch (err) {
		console.error('refresh token 失敗', err);
		if (to.path !== '/login') {
			next('/login');
		} else {
			next(); // 如果已經在登入頁，就不要再 redirect
		}
	}
});

export default router;
