{"status": 200, "data": [{"code": "M4", "name": "一般管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M40", "name": "文件公告訊息管理", "parentCode": "M4", "order": 0, "leaf": false, "nodes": [{"code": "M40-02", "name": "公告及文件下載", "parentCode": "M40", "order": 0, "leaf": false, "nodes": [{"code": "M40-022", "name": "一般", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-022", "parentMenuCode": "M40-02", "menuName": "一般", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-020", "name": "公告及文件下載", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-020", "parentMenuCode": "M40-02", "menuName": "公告及文件下載", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-024", "name": "強制閱讀", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-024", "parentMenuCode": "M40-02", "menuName": "強制閱讀", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-021", "name": "產品", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-021", "parentMenuCode": "M40-02", "menuName": "產品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-023", "name": "跑馬燈", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-023", "parentMenuCode": "M40-02", "menuName": "跑馬燈", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M40-02", "parentMenuCode": "M40", "menuName": "公告及文件下載", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M40", "parentMenuCode": "M4", "menuName": "文件公告訊息管理", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41", "name": "金融訊息", "parentCode": "M4", "order": 0, "leaf": false, "nodes": [{"code": "M41-02", "name": "行銷活動", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [{"code": "M41-022", "name": "市場研討會", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-022", "parentMenuCode": "M41-02", "menuName": "市場研討會", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-020", "name": "行銷活動", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-020", "parentMenuCode": "M41-02", "menuName": "行銷活動", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-024", "name": "高資產客戶專屬", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-024", "parentMenuCode": "M41-02", "menuName": "高資產客戶專屬", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-023", "name": "專案活動", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-023", "parentMenuCode": "M41-02", "menuName": "專案活動", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-021", "name": "新產品研討會", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-021", "parentMenuCode": "M41-02", "menuName": "新產品研討會", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M41-02", "parentMenuCode": "M41", "menuName": "行銷活動", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-03", "name": "研究報告", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [{"code": "M41-037", "name": "人身保險", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-037", "parentMenuCode": "M41-03", "menuName": "人身保險", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-030", "name": "不分", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-030", "parentMenuCode": "M41-03", "menuName": "不分", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-032", "name": "信託-ETF", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-032", "parentMenuCode": "M41-03", "menuName": "信託-ETF", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-034", "name": "信託-海外股票", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-034", "parentMenuCode": "M41-03", "menuName": "信託-海外股票", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-033", "name": "信託-海外債", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-033", "parentMenuCode": "M41-03", "menuName": "信託-海外債", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-031", "name": "信託-基金", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-031", "parentMenuCode": "M41-03", "menuName": "信託-基金", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-035", "name": "信託-境外結構型商品", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-035", "parentMenuCode": "M41-03", "menuName": "信託-境外結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-036", "name": "信託-銀行結構型商品", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-036", "parentMenuCode": "M41-03", "menuName": "信託-銀行結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-038", "name": "黃金存摺", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-038", "parentMenuCode": "M41-03", "menuName": "黃金存摺", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M41-03", "parentMenuCode": "M41", "menuName": "研究報告", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-01", "name": "產品文件", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [{"code": "M41-016", "name": "人身保險", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-016", "parentMenuCode": "M41-01", "menuName": "人身保險", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-011", "name": "信託-ETF", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-011", "parentMenuCode": "M41-01", "menuName": "信託-ETF", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-013", "name": "信託-海外股票", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-013", "parentMenuCode": "M41-01", "menuName": "信託-海外股票", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-012", "name": "信託-海外債", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-012", "parentMenuCode": "M41-01", "menuName": "信託-海外債", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-010", "name": "信託-基金", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-010", "parentMenuCode": "M41-01", "menuName": "信託-基金", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-014", "name": "信託-境外結構型商品", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-014", "parentMenuCode": "M41-01", "menuName": "信託-境外結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-015", "name": "信託-銀行結構型商品", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-015", "parentMenuCode": "M41-01", "menuName": "信託-銀行結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M41-01", "parentMenuCode": "M41", "menuName": "產品文件", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-04", "name": "發行機構報告", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-04", "parentMenuCode": "M41", "menuName": "發行機構報告", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-05", "name": "新聞事件", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-05", "parentMenuCode": "M41", "menuName": "新聞事件", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M41", "parentMenuCode": "M4", "menuName": "金融訊息", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M43", "name": "證照管理", "parentCode": "M4", "order": 0, "leaf": false, "nodes": [{"code": "M43-01", "name": "證照登錄查詢", "parentCode": "M43", "order": 0, "leaf": false, "nodes": [], "menuCode": "M43-01", "parentMenuCode": "M43", "menuName": "證照登錄查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M43", "parentMenuCode": "M4", "menuName": "證照管理", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M4", "parentMenuCode": "", "menuName": "一般管理", "roleName": "理財專員", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M1", "name": "工作管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M10", "name": "行事曆", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [], "menuCode": "M10", "parentMenuCode": "M1", "menuName": "行事曆", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M11", "name": "事件通知", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [{"code": "M11-01", "name": "已完成工作事項", "parentCode": "M11", "order": 0, "leaf": false, "nodes": [], "menuCode": "M11-01", "parentMenuCode": "M11", "menuName": "已完成工作事項", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M11-00", "name": "待辦工作事項", "parentCode": "M11", "order": 0, "leaf": false, "nodes": [], "menuCode": "M11-00", "parentMenuCode": "M11", "menuName": "待辦工作事項", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M11", "parentMenuCode": "M1", "menuName": "事件通知", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M1B", "name": "事件通知TOP10自設", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [], "menuCode": "M1B", "parentMenuCode": "M1", "menuName": "事件通知TOP10自設", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M12", "name": "事件通知查詢", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [], "menuCode": "M12", "parentMenuCode": "M1", "menuName": "事件通知查詢", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M14", "name": "服務紀錄查詢", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [], "menuCode": "M14", "parentMenuCode": "M1", "menuName": "服務紀錄查詢", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M17", "name": "排行榜", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [{"code": "M17-00", "name": "排行榜-AO", "parentCode": "M17", "order": 0, "leaf": false, "nodes": [], "menuCode": "M17-00", "parentMenuCode": "M17", "menuName": "排行榜-AO", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M17", "parentMenuCode": "M1", "menuName": "排行榜", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M1", "parentMenuCode": "", "menuName": "工作管理", "roleName": "理財專員", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M5", "name": "行銷管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M51", "name": "一般活動行銷維護", "parentCode": "M5", "order": 0, "leaf": false, "nodes": [{"code": "M51-03", "name": "名單下載及出席登錄", "parentCode": "M51", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-03", "parentMenuCode": "M51", "menuName": "名單下載及出席登錄", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M51-02", "name": "客戶活動報名", "parentCode": "M51", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-02", "parentMenuCode": "M51", "menuName": "客戶活動報名", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M51-04", "name": "活動查詢", "parentCode": "M51", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-04", "parentMenuCode": "M51", "menuName": "活動查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M51", "parentMenuCode": "M5", "menuName": "一般活動行銷維護", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M50", "name": "商機名單行銷維護", "parentCode": "M5", "order": 0, "leaf": false, "nodes": [{"code": "M50-09", "name": "行銷名單通聯結果查詢", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-09", "parentMenuCode": "M50", "menuName": "行銷名單通聯結果查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M50-08", "name": "我的商機名單", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-08", "parentMenuCode": "M50", "menuName": "我的商機名單", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M50-00", "name": "禁止打擾客戶名單", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-00", "parentMenuCode": "M50", "menuName": "禁止打擾客戶名單", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M50", "parentMenuCode": "M5", "menuName": "商機名單行銷維護", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M5", "parentMenuCode": "", "menuName": "行銷管理", "roleName": "理財專員", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M0", "name": "系統管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M02", "name": "代理人", "parentCode": "M0", "order": 0, "leaf": false, "nodes": [{"code": "M02-00", "name": "代理人設定", "parentCode": "M02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M02-00", "parentMenuCode": "M02", "menuName": "代理人設定", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M02-01", "name": "代理人設定紀錄", "parentCode": "M02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M02-01", "parentMenuCode": "M02", "menuName": "代理人設定紀錄", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M02", "parentMenuCode": "M0", "menuName": "代理人", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M0", "parentMenuCode": "", "menuName": "系統管理", "roleName": "理財專員", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M2", "name": "客戶管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M20", "name": "客戶查詢", "parentCode": "M2", "order": 0, "leaf": false, "nodes": [{"code": "M20-03", "name": "人身保險銷售/服務查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-03", "parentMenuCode": "M20", "menuName": "人身保險銷售/服務查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-04", "name": "客戶升降等查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-04", "parentMenuCode": "M20", "menuName": "客戶升降等查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-05", "name": "客戶總覽", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [{"code": "M20-058", "name": "投資績效分析", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [{"code": "M20-0585", "name": "交易紀錄查詢", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0585", "parentMenuCode": "M20-058", "menuName": "交易紀錄查詢", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0583", "name": "投資標的分析", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0583", "parentMenuCode": "M20-058", "menuName": "投資標的分析", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0587", "name": "投資績效分析/資產及報酬率分析", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0587", "parentMenuCode": "M20-058", "menuName": "投資績效分析/資產及報酬率分析", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0586", "name": "實現損益查詢", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0586", "parentMenuCode": "M20-058", "menuName": "實現損益查詢", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-058", "parentMenuCode": "M20-05", "menuName": "投資績效分析", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-056", "name": "服務紀錄", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [{"code": "M20-0560", "name": "一般通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0560", "parentMenuCode": "M20-056", "menuName": "一般通聯", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0562", "name": "行銷專案通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0562", "parentMenuCode": "M20-056", "menuName": "行銷專案通聯", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0561", "name": "事件通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0561", "parentMenuCode": "M20-056", "menuName": "事件通聯", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0563", "name": "客戶回饋紀錄", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0563", "parentMenuCode": "M20-056", "menuName": "客戶回饋紀錄", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-056", "parentMenuCode": "M20-05", "menuName": "服務紀錄", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-059", "name": "客戶投資屬性問卷紀錄", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-059", "parentMenuCode": "M20-05", "menuName": "客戶投資屬性問卷紀錄", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-057", "name": "客戶報告書", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-057", "parentMenuCode": "M20-05", "menuName": "客戶報告書", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-051", "name": "客戶總覽", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-051", "parentMenuCode": "M20-05", "menuName": "客戶總覽", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-052", "name": "基本資料", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [{"code": "M20-0521", "name": "公司基本資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0521", "parentMenuCode": "M20-052", "menuName": "公司基本資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0520", "name": "本行顧客資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0520", "parentMenuCode": "M20-052", "menuName": "本行顧客資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0524", "name": "其他補充資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0524", "parentMenuCode": "M20-052", "menuName": "其他補充資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0523", "name": "重要節日設定", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0523", "parentMenuCode": "M20-052", "menuName": "重要節日設定", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0522", "name": "家庭與親友資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0522", "parentMenuCode": "M20-052", "menuName": "家庭與親友資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-052", "parentMenuCode": "M20-05", "menuName": "基本資料", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-054", "name": "帳戶綜合概要", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-054", "parentMenuCode": "M20-05", "menuName": "帳戶綜合概要", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-055", "name": "帳戶總覽", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-055", "parentMenuCode": "M20-05", "menuName": "帳戶總覽", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-05", "parentMenuCode": "M20", "menuName": "客戶總覽", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-00", "name": "單一條件客戶查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-00", "parentMenuCode": "M20", "menuName": "單一條件客戶查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-01", "name": "綜合條件客戶查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-01", "parentMenuCode": "M20", "menuName": "綜合條件客戶查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20", "parentMenuCode": "M2", "menuName": "客戶查詢", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M21", "name": "客戶歸屬管理", "parentCode": "M2", "order": 0, "leaf": false, "nodes": [{"code": "M21-02", "name": "客戶異動申請", "parentCode": "M21", "order": 0, "leaf": false, "nodes": [], "menuCode": "M21-02", "parentMenuCode": "M21", "menuName": "客戶異動申請", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M21-01", "name": "客戶異動紀錄查詢", "parentCode": "M21", "order": 0, "leaf": false, "nodes": [], "menuCode": "M21-01", "parentMenuCode": "M21", "menuName": "客戶異動紀錄查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M21", "parentMenuCode": "M2", "menuName": "客戶歸屬管理", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M25", "name": "客訴資料管理", "parentCode": "M2", "order": 0, "leaf": false, "nodes": [{"code": "M25-01", "name": "客訴資料查詢", "parentCode": "M25", "order": 0, "leaf": false, "nodes": [], "menuCode": "M25-01", "parentMenuCode": "M25", "menuName": "客訴資料查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M25-00", "name": "客訴資料維護", "parentCode": "M25", "order": 0, "leaf": false, "nodes": [{"code": "M25-000", "name": "客訴資料上傳", "parentCode": "M25-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M25-000", "parentMenuCode": "M25-00", "menuName": "客訴資料上傳", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M25-001", "name": "客訴資料上傳查詢", "parentCode": "M25-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M25-001", "parentMenuCode": "M25-00", "menuName": "客訴資料上傳查詢", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M25-00", "parentMenuCode": "M25", "menuName": "客訴資料維護", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M25", "parentMenuCode": "M2", "menuName": "客訴資料管理", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M23", "name": "顧客歸戶管理", "parentCode": "M2", "order": 0, "leaf": false, "nodes": [{"code": "M23-02", "name": "歸戶查詢", "parentCode": "M23", "order": 0, "leaf": false, "nodes": [], "menuCode": "M23-02", "parentMenuCode": "M23", "menuName": "歸戶查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M23-00", "name": "歸戶設定", "parentCode": "M23", "order": 0, "leaf": false, "nodes": [], "menuCode": "M23-00", "parentMenuCode": "M23", "menuName": "歸戶設定", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M23-01", "name": "歸戶維護", "parentCode": "M23", "order": 0, "leaf": false, "nodes": [], "menuCode": "M23-01", "parentMenuCode": "M23", "menuName": "歸戶維護", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M23", "parentMenuCode": "M2", "menuName": "顧客歸戶管理", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M2", "parentMenuCode": "", "menuName": "客戶管理", "roleName": "理財專員", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M3", "name": "商品管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M30", "name": "商品資料", "parentCode": "M3", "order": 0, "leaf": false, "nodes": [{"code": "M30-00", "name": "商品資料查詢", "parentCode": "M30", "order": 0, "leaf": false, "nodes": [{"code": "M30-007", "name": "人身保險", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-007", "parentMenuCode": "M30-00", "menuName": "人身保險", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-000", "name": "全商品", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-000", "parentMenuCode": "M30-00", "menuName": "全商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-002", "name": "信託-ETF", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-002", "parentMenuCode": "M30-00", "menuName": "信託-ETF", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-004", "name": "信託-海外股票", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-004", "parentMenuCode": "M30-00", "menuName": "信託-海外股票", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-003", "name": "信託-海外債", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-003", "parentMenuCode": "M30-00", "menuName": "信託-海外債", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-001", "name": "信託-基金", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-001", "parentMenuCode": "M30-00", "menuName": "信託-基金", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-005", "name": "信託-境外結構型商品", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-005", "parentMenuCode": "M30-00", "menuName": "信託-境外結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-006", "name": "信託-銀行結構型商品", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-006", "parentMenuCode": "M30-00", "menuName": "信託-銀行結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M30-00", "parentMenuCode": "M30", "menuName": "商品資料查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M30", "parentMenuCode": "M3", "menuName": "商品資料", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M31", "name": "精選推薦商品", "parentCode": "M3", "order": 0, "leaf": false, "nodes": [{"code": "M31-01", "name": "精選推薦商品查詢", "parentCode": "M31", "order": 0, "leaf": false, "nodes": [], "menuCode": "M31-01", "parentMenuCode": "M31", "menuName": "精選推薦商品查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M31", "parentMenuCode": "M3", "menuName": "精選推薦商品", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M3", "parentMenuCode": "", "menuName": "商品管理", "roleName": "理財專員", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M8", "name": "報表管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M80", "name": "業績管理", "parentCode": "M8", "order": 0, "leaf": false, "nodes": [{"code": "M80-01", "name": "各區理財達成率-理專", "parentCode": "M80", "order": 0, "leaf": false, "nodes": [], "menuCode": "M80-01", "parentMenuCode": "M80", "menuName": "各區理財達成率-理專", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M80", "parentMenuCode": "M8", "menuName": "業績管理", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M8", "parentMenuCode": "", "menuName": "報表管理", "roleName": "理財專員", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "timestamp": "2025/04/11", "sqlTracer": [{"data": [{"code": "M40-022", "name": "一般", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-022", "parentMenuCode": "M40-02", "menuName": "一般", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M51", "name": "一般活動行銷維護", "parentCode": "M5", "order": 0, "leaf": false, "nodes": [{"code": "M51-03", "name": "名單下載及出席登錄", "parentCode": "M51", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-03", "parentMenuCode": "M51", "menuName": "名單下載及出席登錄", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M51-02", "name": "客戶活動報名", "parentCode": "M51", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-02", "parentMenuCode": "M51", "menuName": "客戶活動報名", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M51-04", "name": "活動查詢", "parentCode": "M51", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-04", "parentMenuCode": "M51", "menuName": "活動查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M51", "parentMenuCode": "M5", "menuName": "一般活動行銷維護", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0560", "name": "一般通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0560", "parentMenuCode": "M20-056", "menuName": "一般通聯", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M4", "name": "一般管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M40", "name": "文件公告訊息管理", "parentCode": "M4", "order": 0, "leaf": false, "nodes": [{"code": "M40-02", "name": "公告及文件下載", "parentCode": "M40", "order": 0, "leaf": false, "nodes": [{"code": "M40-022", "name": "一般", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-022", "parentMenuCode": "M40-02", "menuName": "一般", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-020", "name": "公告及文件下載", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-020", "parentMenuCode": "M40-02", "menuName": "公告及文件下載", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-024", "name": "強制閱讀", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-024", "parentMenuCode": "M40-02", "menuName": "強制閱讀", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-021", "name": "產品", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-021", "parentMenuCode": "M40-02", "menuName": "產品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-023", "name": "跑馬燈", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-023", "parentMenuCode": "M40-02", "menuName": "跑馬燈", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M40-02", "parentMenuCode": "M40", "menuName": "公告及文件下載", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M40", "parentMenuCode": "M4", "menuName": "文件公告訊息管理", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41", "name": "金融訊息", "parentCode": "M4", "order": 0, "leaf": false, "nodes": [{"code": "M41-02", "name": "行銷活動", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [{"code": "M41-022", "name": "市場研討會", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-022", "parentMenuCode": "M41-02", "menuName": "市場研討會", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-020", "name": "行銷活動", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-020", "parentMenuCode": "M41-02", "menuName": "行銷活動", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-024", "name": "高資產客戶專屬", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-024", "parentMenuCode": "M41-02", "menuName": "高資產客戶專屬", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-023", "name": "專案活動", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-023", "parentMenuCode": "M41-02", "menuName": "專案活動", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-021", "name": "新產品研討會", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-021", "parentMenuCode": "M41-02", "menuName": "新產品研討會", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M41-02", "parentMenuCode": "M41", "menuName": "行銷活動", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-03", "name": "研究報告", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [{"code": "M41-037", "name": "人身保險", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-037", "parentMenuCode": "M41-03", "menuName": "人身保險", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-030", "name": "不分", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-030", "parentMenuCode": "M41-03", "menuName": "不分", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-032", "name": "信託-ETF", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-032", "parentMenuCode": "M41-03", "menuName": "信託-ETF", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-034", "name": "信託-海外股票", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-034", "parentMenuCode": "M41-03", "menuName": "信託-海外股票", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-033", "name": "信託-海外債", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-033", "parentMenuCode": "M41-03", "menuName": "信託-海外債", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-031", "name": "信託-基金", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-031", "parentMenuCode": "M41-03", "menuName": "信託-基金", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-035", "name": "信託-境外結構型商品", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-035", "parentMenuCode": "M41-03", "menuName": "信託-境外結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-036", "name": "信託-銀行結構型商品", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-036", "parentMenuCode": "M41-03", "menuName": "信託-銀行結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-038", "name": "黃金存摺", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-038", "parentMenuCode": "M41-03", "menuName": "黃金存摺", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M41-03", "parentMenuCode": "M41", "menuName": "研究報告", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-01", "name": "產品文件", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [{"code": "M41-016", "name": "人身保險", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-016", "parentMenuCode": "M41-01", "menuName": "人身保險", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-011", "name": "信託-ETF", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-011", "parentMenuCode": "M41-01", "menuName": "信託-ETF", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-013", "name": "信託-海外股票", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-013", "parentMenuCode": "M41-01", "menuName": "信託-海外股票", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-012", "name": "信託-海外債", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-012", "parentMenuCode": "M41-01", "menuName": "信託-海外債", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-010", "name": "信託-基金", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-010", "parentMenuCode": "M41-01", "menuName": "信託-基金", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-014", "name": "信託-境外結構型商品", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-014", "parentMenuCode": "M41-01", "menuName": "信託-境外結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-015", "name": "信託-銀行結構型商品", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-015", "parentMenuCode": "M41-01", "menuName": "信託-銀行結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M41-01", "parentMenuCode": "M41", "menuName": "產品文件", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-04", "name": "發行機構報告", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-04", "parentMenuCode": "M41", "menuName": "發行機構報告", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-05", "name": "新聞事件", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-05", "parentMenuCode": "M41", "menuName": "新聞事件", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M41", "parentMenuCode": "M4", "menuName": "金融訊息", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M43", "name": "證照管理", "parentCode": "M4", "order": 0, "leaf": false, "nodes": [{"code": "M43-01", "name": "證照登錄查詢", "parentCode": "M43", "order": 0, "leaf": false, "nodes": [], "menuCode": "M43-01", "parentMenuCode": "M43", "menuName": "證照登錄查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M43", "parentMenuCode": "M4", "menuName": "證照管理", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M4", "parentMenuCode": "", "menuName": "一般管理", "roleName": "理財專員", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-016", "name": "人身保險", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-016", "parentMenuCode": "M41-01", "menuName": "人身保險", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-037", "name": "人身保險", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-037", "parentMenuCode": "M41-03", "menuName": "人身保險", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-007", "name": "人身保險", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-007", "parentMenuCode": "M30-00", "menuName": "人身保險", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-03", "name": "人身保險銷售/服務查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-03", "parentMenuCode": "M20", "menuName": "人身保險銷售/服務查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M1", "name": "工作管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M10", "name": "行事曆", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [], "menuCode": "M10", "parentMenuCode": "M1", "menuName": "行事曆", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M11", "name": "事件通知", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [{"code": "M11-01", "name": "已完成工作事項", "parentCode": "M11", "order": 0, "leaf": false, "nodes": [], "menuCode": "M11-01", "parentMenuCode": "M11", "menuName": "已完成工作事項", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M11-00", "name": "待辦工作事項", "parentCode": "M11", "order": 0, "leaf": false, "nodes": [], "menuCode": "M11-00", "parentMenuCode": "M11", "menuName": "待辦工作事項", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M11", "parentMenuCode": "M1", "menuName": "事件通知", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M1B", "name": "事件通知TOP10自設", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [], "menuCode": "M1B", "parentMenuCode": "M1", "menuName": "事件通知TOP10自設", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M12", "name": "事件通知查詢", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [], "menuCode": "M12", "parentMenuCode": "M1", "menuName": "事件通知查詢", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M14", "name": "服務紀錄查詢", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [], "menuCode": "M14", "parentMenuCode": "M1", "menuName": "服務紀錄查詢", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M17", "name": "排行榜", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [{"code": "M17-00", "name": "排行榜-AO", "parentCode": "M17", "order": 0, "leaf": false, "nodes": [], "menuCode": "M17-00", "parentMenuCode": "M17", "menuName": "排行榜-AO", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M17", "parentMenuCode": "M1", "menuName": "排行榜", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M1", "parentMenuCode": "", "menuName": "工作管理", "roleName": "理財專員", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M11-01", "name": "已完成工作事項", "parentCode": "M11", "order": 0, "leaf": false, "nodes": [], "menuCode": "M11-01", "parentMenuCode": "M11", "menuName": "已完成工作事項", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-030", "name": "不分", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-030", "parentMenuCode": "M41-03", "menuName": "不分", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0521", "name": "公司基本資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0521", "parentMenuCode": "M20-052", "menuName": "公司基本資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-020", "name": "公告及文件下載", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-020", "parentMenuCode": "M40-02", "menuName": "公告及文件下載", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-02", "name": "公告及文件下載", "parentCode": "M40", "order": 0, "leaf": false, "nodes": [{"code": "M40-022", "name": "一般", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-022", "parentMenuCode": "M40-02", "menuName": "一般", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-020", "name": "公告及文件下載", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-020", "parentMenuCode": "M40-02", "menuName": "公告及文件下載", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-024", "name": "強制閱讀", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-024", "parentMenuCode": "M40-02", "menuName": "強制閱讀", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-021", "name": "產品", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-021", "parentMenuCode": "M40-02", "menuName": "產品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-023", "name": "跑馬燈", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-023", "parentMenuCode": "M40-02", "menuName": "跑馬燈", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M40-02", "parentMenuCode": "M40", "menuName": "公告及文件下載", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40", "name": "文件公告訊息管理", "parentCode": "M4", "order": 0, "leaf": false, "nodes": [{"code": "M40-02", "name": "公告及文件下載", "parentCode": "M40", "order": 0, "leaf": false, "nodes": [{"code": "M40-022", "name": "一般", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-022", "parentMenuCode": "M40-02", "menuName": "一般", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-020", "name": "公告及文件下載", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-020", "parentMenuCode": "M40-02", "menuName": "公告及文件下載", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-024", "name": "強制閱讀", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-024", "parentMenuCode": "M40-02", "menuName": "強制閱讀", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-021", "name": "產品", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-021", "parentMenuCode": "M40-02", "menuName": "產品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-023", "name": "跑馬燈", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-023", "parentMenuCode": "M40-02", "menuName": "跑馬燈", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M40-02", "parentMenuCode": "M40", "menuName": "公告及文件下載", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M40", "parentMenuCode": "M4", "menuName": "文件公告訊息管理", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M02", "name": "代理人", "parentCode": "M0", "order": 0, "leaf": false, "nodes": [{"code": "M02-00", "name": "代理人設定", "parentCode": "M02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M02-00", "parentMenuCode": "M02", "menuName": "代理人設定", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M02-01", "name": "代理人設定紀錄", "parentCode": "M02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M02-01", "parentMenuCode": "M02", "menuName": "代理人設定紀錄", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M02", "parentMenuCode": "M0", "menuName": "代理人", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M02-00", "name": "代理人設定", "parentCode": "M02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M02-00", "parentMenuCode": "M02", "menuName": "代理人設定", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M02-01", "name": "代理人設定紀錄", "parentCode": "M02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M02-01", "parentMenuCode": "M02", "menuName": "代理人設定紀錄", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-022", "name": "市場研討會", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-022", "parentMenuCode": "M41-02", "menuName": "市場研討會", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0520", "name": "本行顧客資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0520", "parentMenuCode": "M20-052", "menuName": "本行顧客資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0585", "name": "交易紀錄查詢", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0585", "parentMenuCode": "M20-058", "menuName": "交易紀錄查詢", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-000", "name": "全商品", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-000", "parentMenuCode": "M30-00", "menuName": "全商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M80-01", "name": "各區理財達成率-理專", "parentCode": "M80", "order": 0, "leaf": false, "nodes": [], "menuCode": "M80-01", "parentMenuCode": "M80", "menuName": "各區理財達成率-理專", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M51-03", "name": "名單下載及出席登錄", "parentCode": "M51", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-03", "parentMenuCode": "M51", "menuName": "名單下載及出席登錄", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M10", "name": "行事曆", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [], "menuCode": "M10", "parentMenuCode": "M1", "menuName": "行事曆", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M50-09", "name": "行銷名單通聯結果查詢", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-09", "parentMenuCode": "M50", "menuName": "行銷名單通聯結果查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-020", "name": "行銷活動", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-020", "parentMenuCode": "M41-02", "menuName": "行銷活動", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-02", "name": "行銷活動", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [{"code": "M41-022", "name": "市場研討會", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-022", "parentMenuCode": "M41-02", "menuName": "市場研討會", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-020", "name": "行銷活動", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-020", "parentMenuCode": "M41-02", "menuName": "行銷活動", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-024", "name": "高資產客戶專屬", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-024", "parentMenuCode": "M41-02", "menuName": "高資產客戶專屬", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-023", "name": "專案活動", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-023", "parentMenuCode": "M41-02", "menuName": "專案活動", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-021", "name": "新產品研討會", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-021", "parentMenuCode": "M41-02", "menuName": "新產品研討會", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M41-02", "parentMenuCode": "M41", "menuName": "行銷活動", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0562", "name": "行銷專案通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0562", "parentMenuCode": "M20-056", "menuName": "行銷專案通聯", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M5", "name": "行銷管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M51", "name": "一般活動行銷維護", "parentCode": "M5", "order": 0, "leaf": false, "nodes": [{"code": "M51-03", "name": "名單下載及出席登錄", "parentCode": "M51", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-03", "parentMenuCode": "M51", "menuName": "名單下載及出席登錄", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M51-02", "name": "客戶活動報名", "parentCode": "M51", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-02", "parentMenuCode": "M51", "menuName": "客戶活動報名", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M51-04", "name": "活動查詢", "parentCode": "M51", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-04", "parentMenuCode": "M51", "menuName": "活動查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M51", "parentMenuCode": "M5", "menuName": "一般活動行銷維護", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M50", "name": "商機名單行銷維護", "parentCode": "M5", "order": 0, "leaf": false, "nodes": [{"code": "M50-09", "name": "行銷名單通聯結果查詢", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-09", "parentMenuCode": "M50", "menuName": "行銷名單通聯結果查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M50-08", "name": "我的商機名單", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-08", "parentMenuCode": "M50", "menuName": "我的商機名單", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M50-00", "name": "禁止打擾客戶名單", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-00", "parentMenuCode": "M50", "menuName": "禁止打擾客戶名單", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M50", "parentMenuCode": "M5", "menuName": "商機名單行銷維護", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M5", "parentMenuCode": "", "menuName": "行銷管理", "roleName": "理財專員", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M50-08", "name": "我的商機名單", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-08", "parentMenuCode": "M50", "menuName": "我的商機名單", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0583", "name": "投資標的分析", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0583", "parentMenuCode": "M20-058", "menuName": "投資標的分析", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-058", "name": "投資績效分析", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [{"code": "M20-0585", "name": "交易紀錄查詢", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0585", "parentMenuCode": "M20-058", "menuName": "交易紀錄查詢", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0583", "name": "投資標的分析", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0583", "parentMenuCode": "M20-058", "menuName": "投資標的分析", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0587", "name": "投資績效分析/資產及報酬率分析", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0587", "parentMenuCode": "M20-058", "menuName": "投資績效分析/資產及報酬率分析", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0586", "name": "實現損益查詢", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0586", "parentMenuCode": "M20-058", "menuName": "實現損益查詢", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-058", "parentMenuCode": "M20-05", "menuName": "投資績效分析", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0587", "name": "投資績效分析/資產及報酬率分析", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0587", "parentMenuCode": "M20-058", "menuName": "投資績效分析/資產及報酬率分析", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M0", "name": "系統管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M02", "name": "代理人", "parentCode": "M0", "order": 0, "leaf": false, "nodes": [{"code": "M02-00", "name": "代理人設定", "parentCode": "M02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M02-00", "parentMenuCode": "M02", "menuName": "代理人設定", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M02-01", "name": "代理人設定紀錄", "parentCode": "M02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M02-01", "parentMenuCode": "M02", "menuName": "代理人設定紀錄", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M02", "parentMenuCode": "M0", "menuName": "代理人", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M0", "parentMenuCode": "", "menuName": "系統管理", "roleName": "理財專員", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M11", "name": "事件通知", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [{"code": "M11-01", "name": "已完成工作事項", "parentCode": "M11", "order": 0, "leaf": false, "nodes": [], "menuCode": "M11-01", "parentMenuCode": "M11", "menuName": "已完成工作事項", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M11-00", "name": "待辦工作事項", "parentCode": "M11", "order": 0, "leaf": false, "nodes": [], "menuCode": "M11-00", "parentMenuCode": "M11", "menuName": "待辦工作事項", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M11", "parentMenuCode": "M1", "menuName": "事件通知", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M1B", "name": "事件通知TOP10自設", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [], "menuCode": "M1B", "parentMenuCode": "M1", "menuName": "事件通知TOP10自設", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M12", "name": "事件通知查詢", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [], "menuCode": "M12", "parentMenuCode": "M1", "menuName": "事件通知查詢", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0561", "name": "事件通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0561", "parentMenuCode": "M20-056", "menuName": "事件通聯", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0524", "name": "其他補充資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0524", "parentMenuCode": "M20-052", "menuName": "其他補充資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-056", "name": "服務紀錄", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [{"code": "M20-0560", "name": "一般通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0560", "parentMenuCode": "M20-056", "menuName": "一般通聯", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0562", "name": "行銷專案通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0562", "parentMenuCode": "M20-056", "menuName": "行銷專案通聯", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0561", "name": "事件通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0561", "parentMenuCode": "M20-056", "menuName": "事件通聯", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0563", "name": "客戶回饋紀錄", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0563", "parentMenuCode": "M20-056", "menuName": "客戶回饋紀錄", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-056", "parentMenuCode": "M20-05", "menuName": "服務紀錄", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M14", "name": "服務紀錄查詢", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [], "menuCode": "M14", "parentMenuCode": "M1", "menuName": "服務紀錄查詢", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41", "name": "金融訊息", "parentCode": "M4", "order": 0, "leaf": false, "nodes": [{"code": "M41-02", "name": "行銷活動", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [{"code": "M41-022", "name": "市場研討會", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-022", "parentMenuCode": "M41-02", "menuName": "市場研討會", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-020", "name": "行銷活動", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-020", "parentMenuCode": "M41-02", "menuName": "行銷活動", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-024", "name": "高資產客戶專屬", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-024", "parentMenuCode": "M41-02", "menuName": "高資產客戶專屬", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-023", "name": "專案活動", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-023", "parentMenuCode": "M41-02", "menuName": "專案活動", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-021", "name": "新產品研討會", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-021", "parentMenuCode": "M41-02", "menuName": "新產品研討會", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M41-02", "parentMenuCode": "M41", "menuName": "行銷活動", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-03", "name": "研究報告", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [{"code": "M41-037", "name": "人身保險", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-037", "parentMenuCode": "M41-03", "menuName": "人身保險", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-030", "name": "不分", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-030", "parentMenuCode": "M41-03", "menuName": "不分", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-032", "name": "信託-ETF", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-032", "parentMenuCode": "M41-03", "menuName": "信託-ETF", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-034", "name": "信託-海外股票", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-034", "parentMenuCode": "M41-03", "menuName": "信託-海外股票", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-033", "name": "信託-海外債", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-033", "parentMenuCode": "M41-03", "menuName": "信託-海外債", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-031", "name": "信託-基金", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-031", "parentMenuCode": "M41-03", "menuName": "信託-基金", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-035", "name": "信託-境外結構型商品", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-035", "parentMenuCode": "M41-03", "menuName": "信託-境外結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-036", "name": "信託-銀行結構型商品", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-036", "parentMenuCode": "M41-03", "menuName": "信託-銀行結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-038", "name": "黃金存摺", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-038", "parentMenuCode": "M41-03", "menuName": "黃金存摺", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M41-03", "parentMenuCode": "M41", "menuName": "研究報告", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-01", "name": "產品文件", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [{"code": "M41-016", "name": "人身保險", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-016", "parentMenuCode": "M41-01", "menuName": "人身保險", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-011", "name": "信託-ETF", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-011", "parentMenuCode": "M41-01", "menuName": "信託-ETF", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-013", "name": "信託-海外股票", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-013", "parentMenuCode": "M41-01", "menuName": "信託-海外股票", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-012", "name": "信託-海外債", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-012", "parentMenuCode": "M41-01", "menuName": "信託-海外債", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-010", "name": "信託-基金", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-010", "parentMenuCode": "M41-01", "menuName": "信託-基金", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-014", "name": "信託-境外結構型商品", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-014", "parentMenuCode": "M41-01", "menuName": "信託-境外結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-015", "name": "信託-銀行結構型商品", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-015", "parentMenuCode": "M41-01", "menuName": "信託-銀行結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M41-01", "parentMenuCode": "M41", "menuName": "產品文件", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-04", "name": "發行機構報告", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-04", "parentMenuCode": "M41", "menuName": "發行機構報告", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-05", "name": "新聞事件", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-05", "parentMenuCode": "M41", "menuName": "新聞事件", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M41", "parentMenuCode": "M4", "menuName": "金融訊息", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-011", "name": "信託-ETF", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-011", "parentMenuCode": "M41-01", "menuName": "信託-ETF", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-032", "name": "信託-ETF", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-032", "parentMenuCode": "M41-03", "menuName": "信託-ETF", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-002", "name": "信託-ETF", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-002", "parentMenuCode": "M30-00", "menuName": "信託-ETF", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-013", "name": "信託-海外股票", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-013", "parentMenuCode": "M41-01", "menuName": "信託-海外股票", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-034", "name": "信託-海外股票", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-034", "parentMenuCode": "M41-03", "menuName": "信託-海外股票", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-004", "name": "信託-海外股票", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-004", "parentMenuCode": "M30-00", "menuName": "信託-海外股票", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-012", "name": "信託-海外債", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-012", "parentMenuCode": "M41-01", "menuName": "信託-海外債", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-033", "name": "信託-海外債", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-033", "parentMenuCode": "M41-03", "menuName": "信託-海外債", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-003", "name": "信託-海外債", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-003", "parentMenuCode": "M30-00", "menuName": "信託-海外債", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-010", "name": "信託-基金", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-010", "parentMenuCode": "M41-01", "menuName": "信託-基金", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-031", "name": "信託-基金", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-031", "parentMenuCode": "M41-03", "menuName": "信託-基金", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-001", "name": "信託-基金", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-001", "parentMenuCode": "M30-00", "menuName": "信託-基金", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-014", "name": "信託-境外結構型商品", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-014", "parentMenuCode": "M41-01", "menuName": "信託-境外結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-035", "name": "信託-境外結構型商品", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-035", "parentMenuCode": "M41-03", "menuName": "信託-境外結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-005", "name": "信託-境外結構型商品", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-005", "parentMenuCode": "M30-00", "menuName": "信託-境外結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-015", "name": "信託-銀行結構型商品", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-015", "parentMenuCode": "M41-01", "menuName": "信託-銀行結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-036", "name": "信託-銀行結構型商品", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-036", "parentMenuCode": "M41-03", "menuName": "信託-銀行結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-006", "name": "信託-銀行結構型商品", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-006", "parentMenuCode": "M30-00", "menuName": "信託-銀行結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-04", "name": "客戶升降等查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-04", "parentMenuCode": "M20", "menuName": "客戶升降等查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0563", "name": "客戶回饋紀錄", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0563", "parentMenuCode": "M20-056", "menuName": "客戶回饋紀錄", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-059", "name": "客戶投資屬性問卷紀錄", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-059", "parentMenuCode": "M20-05", "menuName": "客戶投資屬性問卷紀錄", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20", "name": "客戶查詢", "parentCode": "M2", "order": 0, "leaf": false, "nodes": [{"code": "M20-03", "name": "人身保險銷售/服務查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-03", "parentMenuCode": "M20", "menuName": "人身保險銷售/服務查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-04", "name": "客戶升降等查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-04", "parentMenuCode": "M20", "menuName": "客戶升降等查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-05", "name": "客戶總覽", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [{"code": "M20-058", "name": "投資績效分析", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [{"code": "M20-0585", "name": "交易紀錄查詢", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0585", "parentMenuCode": "M20-058", "menuName": "交易紀錄查詢", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0583", "name": "投資標的分析", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0583", "parentMenuCode": "M20-058", "menuName": "投資標的分析", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0587", "name": "投資績效分析/資產及報酬率分析", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0587", "parentMenuCode": "M20-058", "menuName": "投資績效分析/資產及報酬率分析", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0586", "name": "實現損益查詢", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0586", "parentMenuCode": "M20-058", "menuName": "實現損益查詢", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-058", "parentMenuCode": "M20-05", "menuName": "投資績效分析", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-056", "name": "服務紀錄", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [{"code": "M20-0560", "name": "一般通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0560", "parentMenuCode": "M20-056", "menuName": "一般通聯", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0562", "name": "行銷專案通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0562", "parentMenuCode": "M20-056", "menuName": "行銷專案通聯", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0561", "name": "事件通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0561", "parentMenuCode": "M20-056", "menuName": "事件通聯", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0563", "name": "客戶回饋紀錄", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0563", "parentMenuCode": "M20-056", "menuName": "客戶回饋紀錄", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-056", "parentMenuCode": "M20-05", "menuName": "服務紀錄", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-059", "name": "客戶投資屬性問卷紀錄", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-059", "parentMenuCode": "M20-05", "menuName": "客戶投資屬性問卷紀錄", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-057", "name": "客戶報告書", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-057", "parentMenuCode": "M20-05", "menuName": "客戶報告書", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-051", "name": "客戶總覽", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-051", "parentMenuCode": "M20-05", "menuName": "客戶總覽", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-052", "name": "基本資料", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [{"code": "M20-0521", "name": "公司基本資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0521", "parentMenuCode": "M20-052", "menuName": "公司基本資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0520", "name": "本行顧客資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0520", "parentMenuCode": "M20-052", "menuName": "本行顧客資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0524", "name": "其他補充資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0524", "parentMenuCode": "M20-052", "menuName": "其他補充資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0523", "name": "重要節日設定", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0523", "parentMenuCode": "M20-052", "menuName": "重要節日設定", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0522", "name": "家庭與親友資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0522", "parentMenuCode": "M20-052", "menuName": "家庭與親友資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-052", "parentMenuCode": "M20-05", "menuName": "基本資料", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-054", "name": "帳戶綜合概要", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-054", "parentMenuCode": "M20-05", "menuName": "帳戶綜合概要", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-055", "name": "帳戶總覽", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-055", "parentMenuCode": "M20-05", "menuName": "帳戶總覽", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-05", "parentMenuCode": "M20", "menuName": "客戶總覽", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-00", "name": "單一條件客戶查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-00", "parentMenuCode": "M20", "menuName": "單一條件客戶查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-01", "name": "綜合條件客戶查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-01", "parentMenuCode": "M20", "menuName": "綜合條件客戶查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20", "parentMenuCode": "M2", "menuName": "客戶查詢", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M51-02", "name": "客戶活動報名", "parentCode": "M51", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-02", "parentMenuCode": "M51", "menuName": "客戶活動報名", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M21-02", "name": "客戶異動申請", "parentCode": "M21", "order": 0, "leaf": false, "nodes": [], "menuCode": "M21-02", "parentMenuCode": "M21", "menuName": "客戶異動申請", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M21-01", "name": "客戶異動紀錄查詢", "parentCode": "M21", "order": 0, "leaf": false, "nodes": [], "menuCode": "M21-01", "parentMenuCode": "M21", "menuName": "客戶異動紀錄查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-057", "name": "客戶報告書", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-057", "parentMenuCode": "M20-05", "menuName": "客戶報告書", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M2", "name": "客戶管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M20", "name": "客戶查詢", "parentCode": "M2", "order": 0, "leaf": false, "nodes": [{"code": "M20-03", "name": "人身保險銷售/服務查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-03", "parentMenuCode": "M20", "menuName": "人身保險銷售/服務查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-04", "name": "客戶升降等查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-04", "parentMenuCode": "M20", "menuName": "客戶升降等查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-05", "name": "客戶總覽", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [{"code": "M20-058", "name": "投資績效分析", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [{"code": "M20-0585", "name": "交易紀錄查詢", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0585", "parentMenuCode": "M20-058", "menuName": "交易紀錄查詢", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0583", "name": "投資標的分析", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0583", "parentMenuCode": "M20-058", "menuName": "投資標的分析", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0587", "name": "投資績效分析/資產及報酬率分析", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0587", "parentMenuCode": "M20-058", "menuName": "投資績效分析/資產及報酬率分析", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0586", "name": "實現損益查詢", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0586", "parentMenuCode": "M20-058", "menuName": "實現損益查詢", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-058", "parentMenuCode": "M20-05", "menuName": "投資績效分析", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-056", "name": "服務紀錄", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [{"code": "M20-0560", "name": "一般通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0560", "parentMenuCode": "M20-056", "menuName": "一般通聯", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0562", "name": "行銷專案通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0562", "parentMenuCode": "M20-056", "menuName": "行銷專案通聯", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0561", "name": "事件通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0561", "parentMenuCode": "M20-056", "menuName": "事件通聯", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0563", "name": "客戶回饋紀錄", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0563", "parentMenuCode": "M20-056", "menuName": "客戶回饋紀錄", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-056", "parentMenuCode": "M20-05", "menuName": "服務紀錄", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-059", "name": "客戶投資屬性問卷紀錄", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-059", "parentMenuCode": "M20-05", "menuName": "客戶投資屬性問卷紀錄", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-057", "name": "客戶報告書", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-057", "parentMenuCode": "M20-05", "menuName": "客戶報告書", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-051", "name": "客戶總覽", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-051", "parentMenuCode": "M20-05", "menuName": "客戶總覽", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-052", "name": "基本資料", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [{"code": "M20-0521", "name": "公司基本資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0521", "parentMenuCode": "M20-052", "menuName": "公司基本資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0520", "name": "本行顧客資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0520", "parentMenuCode": "M20-052", "menuName": "本行顧客資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0524", "name": "其他補充資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0524", "parentMenuCode": "M20-052", "menuName": "其他補充資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0523", "name": "重要節日設定", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0523", "parentMenuCode": "M20-052", "menuName": "重要節日設定", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0522", "name": "家庭與親友資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0522", "parentMenuCode": "M20-052", "menuName": "家庭與親友資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-052", "parentMenuCode": "M20-05", "menuName": "基本資料", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-054", "name": "帳戶綜合概要", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-054", "parentMenuCode": "M20-05", "menuName": "帳戶綜合概要", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-055", "name": "帳戶總覽", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-055", "parentMenuCode": "M20-05", "menuName": "帳戶總覽", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-05", "parentMenuCode": "M20", "menuName": "客戶總覽", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-00", "name": "單一條件客戶查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-00", "parentMenuCode": "M20", "menuName": "單一條件客戶查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-01", "name": "綜合條件客戶查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-01", "parentMenuCode": "M20", "menuName": "綜合條件客戶查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20", "parentMenuCode": "M2", "menuName": "客戶查詢", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M21", "name": "客戶歸屬管理", "parentCode": "M2", "order": 0, "leaf": false, "nodes": [{"code": "M21-02", "name": "客戶異動申請", "parentCode": "M21", "order": 0, "leaf": false, "nodes": [], "menuCode": "M21-02", "parentMenuCode": "M21", "menuName": "客戶異動申請", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M21-01", "name": "客戶異動紀錄查詢", "parentCode": "M21", "order": 0, "leaf": false, "nodes": [], "menuCode": "M21-01", "parentMenuCode": "M21", "menuName": "客戶異動紀錄查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M21", "parentMenuCode": "M2", "menuName": "客戶歸屬管理", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M25", "name": "客訴資料管理", "parentCode": "M2", "order": 0, "leaf": false, "nodes": [{"code": "M25-01", "name": "客訴資料查詢", "parentCode": "M25", "order": 0, "leaf": false, "nodes": [], "menuCode": "M25-01", "parentMenuCode": "M25", "menuName": "客訴資料查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M25-00", "name": "客訴資料維護", "parentCode": "M25", "order": 0, "leaf": false, "nodes": [{"code": "M25-000", "name": "客訴資料上傳", "parentCode": "M25-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M25-000", "parentMenuCode": "M25-00", "menuName": "客訴資料上傳", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M25-001", "name": "客訴資料上傳查詢", "parentCode": "M25-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M25-001", "parentMenuCode": "M25-00", "menuName": "客訴資料上傳查詢", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M25-00", "parentMenuCode": "M25", "menuName": "客訴資料維護", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M25", "parentMenuCode": "M2", "menuName": "客訴資料管理", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M23", "name": "顧客歸戶管理", "parentCode": "M2", "order": 0, "leaf": false, "nodes": [{"code": "M23-02", "name": "歸戶查詢", "parentCode": "M23", "order": 0, "leaf": false, "nodes": [], "menuCode": "M23-02", "parentMenuCode": "M23", "menuName": "歸戶查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M23-00", "name": "歸戶設定", "parentCode": "M23", "order": 0, "leaf": false, "nodes": [], "menuCode": "M23-00", "parentMenuCode": "M23", "menuName": "歸戶設定", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M23-01", "name": "歸戶維護", "parentCode": "M23", "order": 0, "leaf": false, "nodes": [], "menuCode": "M23-01", "parentMenuCode": "M23", "menuName": "歸戶維護", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M23", "parentMenuCode": "M2", "menuName": "顧客歸戶管理", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M2", "parentMenuCode": "", "menuName": "客戶管理", "roleName": "理財專員", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-051", "name": "客戶總覽", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-051", "parentMenuCode": "M20-05", "menuName": "客戶總覽", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-05", "name": "客戶總覽", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [{"code": "M20-058", "name": "投資績效分析", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [{"code": "M20-0585", "name": "交易紀錄查詢", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0585", "parentMenuCode": "M20-058", "menuName": "交易紀錄查詢", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0583", "name": "投資標的分析", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0583", "parentMenuCode": "M20-058", "menuName": "投資標的分析", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0587", "name": "投資績效分析/資產及報酬率分析", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0587", "parentMenuCode": "M20-058", "menuName": "投資績效分析/資產及報酬率分析", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0586", "name": "實現損益查詢", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0586", "parentMenuCode": "M20-058", "menuName": "實現損益查詢", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-058", "parentMenuCode": "M20-05", "menuName": "投資績效分析", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-056", "name": "服務紀錄", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [{"code": "M20-0560", "name": "一般通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0560", "parentMenuCode": "M20-056", "menuName": "一般通聯", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0562", "name": "行銷專案通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0562", "parentMenuCode": "M20-056", "menuName": "行銷專案通聯", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0561", "name": "事件通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0561", "parentMenuCode": "M20-056", "menuName": "事件通聯", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0563", "name": "客戶回饋紀錄", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0563", "parentMenuCode": "M20-056", "menuName": "客戶回饋紀錄", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-056", "parentMenuCode": "M20-05", "menuName": "服務紀錄", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-059", "name": "客戶投資屬性問卷紀錄", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-059", "parentMenuCode": "M20-05", "menuName": "客戶投資屬性問卷紀錄", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-057", "name": "客戶報告書", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-057", "parentMenuCode": "M20-05", "menuName": "客戶報告書", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-051", "name": "客戶總覽", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-051", "parentMenuCode": "M20-05", "menuName": "客戶總覽", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-052", "name": "基本資料", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [{"code": "M20-0521", "name": "公司基本資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0521", "parentMenuCode": "M20-052", "menuName": "公司基本資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0520", "name": "本行顧客資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0520", "parentMenuCode": "M20-052", "menuName": "本行顧客資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0524", "name": "其他補充資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0524", "parentMenuCode": "M20-052", "menuName": "其他補充資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0523", "name": "重要節日設定", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0523", "parentMenuCode": "M20-052", "menuName": "重要節日設定", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0522", "name": "家庭與親友資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0522", "parentMenuCode": "M20-052", "menuName": "家庭與親友資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-052", "parentMenuCode": "M20-05", "menuName": "基本資料", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-054", "name": "帳戶綜合概要", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-054", "parentMenuCode": "M20-05", "menuName": "帳戶綜合概要", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-055", "name": "帳戶總覽", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-055", "parentMenuCode": "M20-05", "menuName": "帳戶總覽", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-05", "parentMenuCode": "M20", "menuName": "客戶總覽", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M21", "name": "客戶歸屬管理", "parentCode": "M2", "order": 0, "leaf": false, "nodes": [{"code": "M21-02", "name": "客戶異動申請", "parentCode": "M21", "order": 0, "leaf": false, "nodes": [], "menuCode": "M21-02", "parentMenuCode": "M21", "menuName": "客戶異動申請", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M21-01", "name": "客戶異動紀錄查詢", "parentCode": "M21", "order": 0, "leaf": false, "nodes": [], "menuCode": "M21-01", "parentMenuCode": "M21", "menuName": "客戶異動紀錄查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M21", "parentMenuCode": "M2", "menuName": "客戶歸屬管理", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M25-000", "name": "客訴資料上傳", "parentCode": "M25-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M25-000", "parentMenuCode": "M25-00", "menuName": "客訴資料上傳", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M25-001", "name": "客訴資料上傳查詢", "parentCode": "M25-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M25-001", "parentMenuCode": "M25-00", "menuName": "客訴資料上傳查詢", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M25-01", "name": "客訴資料查詢", "parentCode": "M25", "order": 0, "leaf": false, "nodes": [], "menuCode": "M25-01", "parentMenuCode": "M25", "menuName": "客訴資料查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M25", "name": "客訴資料管理", "parentCode": "M2", "order": 0, "leaf": false, "nodes": [{"code": "M25-01", "name": "客訴資料查詢", "parentCode": "M25", "order": 0, "leaf": false, "nodes": [], "menuCode": "M25-01", "parentMenuCode": "M25", "menuName": "客訴資料查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M25-00", "name": "客訴資料維護", "parentCode": "M25", "order": 0, "leaf": false, "nodes": [{"code": "M25-000", "name": "客訴資料上傳", "parentCode": "M25-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M25-000", "parentMenuCode": "M25-00", "menuName": "客訴資料上傳", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M25-001", "name": "客訴資料上傳查詢", "parentCode": "M25-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M25-001", "parentMenuCode": "M25-00", "menuName": "客訴資料上傳查詢", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M25-00", "parentMenuCode": "M25", "menuName": "客訴資料維護", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M25", "parentMenuCode": "M2", "menuName": "客訴資料管理", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M25-00", "name": "客訴資料維護", "parentCode": "M25", "order": 0, "leaf": false, "nodes": [{"code": "M25-000", "name": "客訴資料上傳", "parentCode": "M25-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M25-000", "parentMenuCode": "M25-00", "menuName": "客訴資料上傳", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M25-001", "name": "客訴資料上傳查詢", "parentCode": "M25-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M25-001", "parentMenuCode": "M25-00", "menuName": "客訴資料上傳查詢", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M25-00", "parentMenuCode": "M25", "menuName": "客訴資料維護", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M11-00", "name": "待辦工作事項", "parentCode": "M11", "order": 0, "leaf": false, "nodes": [], "menuCode": "M11-00", "parentMenuCode": "M11", "menuName": "待辦工作事項", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M51-04", "name": "活動查詢", "parentCode": "M51", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-04", "parentMenuCode": "M51", "menuName": "活動查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-03", "name": "研究報告", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [{"code": "M41-037", "name": "人身保險", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-037", "parentMenuCode": "M41-03", "menuName": "人身保險", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-030", "name": "不分", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-030", "parentMenuCode": "M41-03", "menuName": "不分", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-032", "name": "信託-ETF", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-032", "parentMenuCode": "M41-03", "menuName": "信託-ETF", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-034", "name": "信託-海外股票", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-034", "parentMenuCode": "M41-03", "menuName": "信託-海外股票", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-033", "name": "信託-海外債", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-033", "parentMenuCode": "M41-03", "menuName": "信託-海外債", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-031", "name": "信託-基金", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-031", "parentMenuCode": "M41-03", "menuName": "信託-基金", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-035", "name": "信託-境外結構型商品", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-035", "parentMenuCode": "M41-03", "menuName": "信託-境外結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-036", "name": "信託-銀行結構型商品", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-036", "parentMenuCode": "M41-03", "menuName": "信託-銀行結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-038", "name": "黃金存摺", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-038", "parentMenuCode": "M41-03", "menuName": "黃金存摺", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M41-03", "parentMenuCode": "M41", "menuName": "研究報告", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0523", "name": "重要節日設定", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0523", "parentMenuCode": "M20-052", "menuName": "重要節日設定", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0522", "name": "家庭與親友資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0522", "parentMenuCode": "M20-052", "menuName": "家庭與親友資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-024", "name": "高資產客戶專屬", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-024", "parentMenuCode": "M41-02", "menuName": "高資產客戶專屬", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30", "name": "商品資料", "parentCode": "M3", "order": 0, "leaf": false, "nodes": [{"code": "M30-00", "name": "商品資料查詢", "parentCode": "M30", "order": 0, "leaf": false, "nodes": [{"code": "M30-007", "name": "人身保險", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-007", "parentMenuCode": "M30-00", "menuName": "人身保險", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-000", "name": "全商品", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-000", "parentMenuCode": "M30-00", "menuName": "全商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-002", "name": "信託-ETF", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-002", "parentMenuCode": "M30-00", "menuName": "信託-ETF", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-004", "name": "信託-海外股票", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-004", "parentMenuCode": "M30-00", "menuName": "信託-海外股票", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-003", "name": "信託-海外債", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-003", "parentMenuCode": "M30-00", "menuName": "信託-海外債", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-001", "name": "信託-基金", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-001", "parentMenuCode": "M30-00", "menuName": "信託-基金", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-005", "name": "信託-境外結構型商品", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-005", "parentMenuCode": "M30-00", "menuName": "信託-境外結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-006", "name": "信託-銀行結構型商品", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-006", "parentMenuCode": "M30-00", "menuName": "信託-銀行結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M30-00", "parentMenuCode": "M30", "menuName": "商品資料查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M30", "parentMenuCode": "M3", "menuName": "商品資料", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-00", "name": "商品資料查詢", "parentCode": "M30", "order": 0, "leaf": false, "nodes": [{"code": "M30-007", "name": "人身保險", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-007", "parentMenuCode": "M30-00", "menuName": "人身保險", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-000", "name": "全商品", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-000", "parentMenuCode": "M30-00", "menuName": "全商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-002", "name": "信託-ETF", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-002", "parentMenuCode": "M30-00", "menuName": "信託-ETF", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-004", "name": "信託-海外股票", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-004", "parentMenuCode": "M30-00", "menuName": "信託-海外股票", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-003", "name": "信託-海外債", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-003", "parentMenuCode": "M30-00", "menuName": "信託-海外債", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-001", "name": "信託-基金", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-001", "parentMenuCode": "M30-00", "menuName": "信託-基金", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-005", "name": "信託-境外結構型商品", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-005", "parentMenuCode": "M30-00", "menuName": "信託-境外結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-006", "name": "信託-銀行結構型商品", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-006", "parentMenuCode": "M30-00", "menuName": "信託-銀行結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M30-00", "parentMenuCode": "M30", "menuName": "商品資料查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M3", "name": "商品管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M30", "name": "商品資料", "parentCode": "M3", "order": 0, "leaf": false, "nodes": [{"code": "M30-00", "name": "商品資料查詢", "parentCode": "M30", "order": 0, "leaf": false, "nodes": [{"code": "M30-007", "name": "人身保險", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-007", "parentMenuCode": "M30-00", "menuName": "人身保險", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-000", "name": "全商品", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-000", "parentMenuCode": "M30-00", "menuName": "全商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-002", "name": "信託-ETF", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-002", "parentMenuCode": "M30-00", "menuName": "信託-ETF", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-004", "name": "信託-海外股票", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-004", "parentMenuCode": "M30-00", "menuName": "信託-海外股票", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-003", "name": "信託-海外債", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-003", "parentMenuCode": "M30-00", "menuName": "信託-海外債", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-001", "name": "信託-基金", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-001", "parentMenuCode": "M30-00", "menuName": "信託-基金", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-005", "name": "信託-境外結構型商品", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-005", "parentMenuCode": "M30-00", "menuName": "信託-境外結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-006", "name": "信託-銀行結構型商品", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-006", "parentMenuCode": "M30-00", "menuName": "信託-銀行結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M30-00", "parentMenuCode": "M30", "menuName": "商品資料查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M30", "parentMenuCode": "M3", "menuName": "商品資料", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M31", "name": "精選推薦商品", "parentCode": "M3", "order": 0, "leaf": false, "nodes": [{"code": "M31-01", "name": "精選推薦商品查詢", "parentCode": "M31", "order": 0, "leaf": false, "nodes": [], "menuCode": "M31-01", "parentMenuCode": "M31", "menuName": "精選推薦商品查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M31", "parentMenuCode": "M3", "menuName": "精選推薦商品", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M3", "parentMenuCode": "", "menuName": "商品管理", "roleName": "理財專員", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M50", "name": "商機名單行銷維護", "parentCode": "M5", "order": 0, "leaf": false, "nodes": [{"code": "M50-09", "name": "行銷名單通聯結果查詢", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-09", "parentMenuCode": "M50", "menuName": "行銷名單通聯結果查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M50-08", "name": "我的商機名單", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-08", "parentMenuCode": "M50", "menuName": "我的商機名單", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M50-00", "name": "禁止打擾客戶名單", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-00", "parentMenuCode": "M50", "menuName": "禁止打擾客戶名單", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M50", "parentMenuCode": "M5", "menuName": "商機名單行銷維護", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-052", "name": "基本資料", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [{"code": "M20-0521", "name": "公司基本資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0521", "parentMenuCode": "M20-052", "menuName": "公司基本資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0520", "name": "本行顧客資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0520", "parentMenuCode": "M20-052", "menuName": "本行顧客資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0524", "name": "其他補充資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0524", "parentMenuCode": "M20-052", "menuName": "其他補充資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0523", "name": "重要節日設定", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0523", "parentMenuCode": "M20-052", "menuName": "重要節日設定", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0522", "name": "家庭與親友資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0522", "parentMenuCode": "M20-052", "menuName": "家庭與親友資料", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-052", "parentMenuCode": "M20-05", "menuName": "基本資料", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-023", "name": "專案活動", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-023", "parentMenuCode": "M41-02", "menuName": "專案活動", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-054", "name": "帳戶綜合概要", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-054", "parentMenuCode": "M20-05", "menuName": "帳戶綜合概要", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-055", "name": "帳戶總覽", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-055", "parentMenuCode": "M20-05", "menuName": "帳戶總覽", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-024", "name": "強制閱讀", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-024", "parentMenuCode": "M40-02", "menuName": "強制閱讀", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M17", "name": "排行榜", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [{"code": "M17-00", "name": "排行榜-AO", "parentCode": "M17", "order": 0, "leaf": false, "nodes": [], "menuCode": "M17-00", "parentMenuCode": "M17", "menuName": "排行榜-AO", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M17", "parentMenuCode": "M1", "menuName": "排行榜", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M17-00", "name": "排行榜-AO", "parentCode": "M17", "order": 0, "leaf": false, "nodes": [], "menuCode": "M17-00", "parentMenuCode": "M17", "menuName": "排行榜-AO", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-021", "name": "產品", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-021", "parentMenuCode": "M40-02", "menuName": "產品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-01", "name": "產品文件", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [{"code": "M41-016", "name": "人身保險", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-016", "parentMenuCode": "M41-01", "menuName": "人身保險", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-011", "name": "信託-ETF", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-011", "parentMenuCode": "M41-01", "menuName": "信託-ETF", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-013", "name": "信託-海外股票", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-013", "parentMenuCode": "M41-01", "menuName": "信託-海外股票", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-012", "name": "信託-海外債", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-012", "parentMenuCode": "M41-01", "menuName": "信託-海外債", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-010", "name": "信託-基金", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-010", "parentMenuCode": "M41-01", "menuName": "信託-基金", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-014", "name": "信託-境外結構型商品", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-014", "parentMenuCode": "M41-01", "menuName": "信託-境外結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-015", "name": "信託-銀行結構型商品", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-015", "parentMenuCode": "M41-01", "menuName": "信託-銀行結構型商品", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M41-01", "parentMenuCode": "M41", "menuName": "產品文件", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-00", "name": "單一條件客戶查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-00", "parentMenuCode": "M20", "menuName": "單一條件客戶查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M8", "name": "報表管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M80", "name": "業績管理", "parentCode": "M8", "order": 0, "leaf": false, "nodes": [{"code": "M80-01", "name": "各區理財達成率-理專", "parentCode": "M80", "order": 0, "leaf": false, "nodes": [], "menuCode": "M80-01", "parentMenuCode": "M80", "menuName": "各區理財達成率-理專", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M80", "parentMenuCode": "M8", "menuName": "業績管理", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M8", "parentMenuCode": "", "menuName": "報表管理", "roleName": "理財專員", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-04", "name": "發行機構報告", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-04", "parentMenuCode": "M41", "menuName": "發行機構報告", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-023", "name": "跑馬燈", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-023", "parentMenuCode": "M40-02", "menuName": "跑馬燈", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-038", "name": "黃金存摺", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-038", "parentMenuCode": "M41-03", "menuName": "黃金存摺", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-021", "name": "新產品研討會", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-021", "parentMenuCode": "M41-02", "menuName": "新產品研討會", "roleName": "理財專員", "depths": 4, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-05", "name": "新聞事件", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-05", "parentMenuCode": "M41", "menuName": "新聞事件", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M80", "name": "業績管理", "parentCode": "M8", "order": 0, "leaf": false, "nodes": [{"code": "M80-01", "name": "各區理財達成率-理專", "parentCode": "M80", "order": 0, "leaf": false, "nodes": [], "menuCode": "M80-01", "parentMenuCode": "M80", "menuName": "各區理財達成率-理專", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M80", "parentMenuCode": "M8", "menuName": "業績管理", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M50-00", "name": "禁止打擾客戶名單", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-00", "parentMenuCode": "M50", "menuName": "禁止打擾客戶名單", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-0586", "name": "實現損益查詢", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0586", "parentMenuCode": "M20-058", "menuName": "實現損益查詢", "roleName": "理財專員", "depths": 5, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M31", "name": "精選推薦商品", "parentCode": "M3", "order": 0, "leaf": false, "nodes": [{"code": "M31-01", "name": "精選推薦商品查詢", "parentCode": "M31", "order": 0, "leaf": false, "nodes": [], "menuCode": "M31-01", "parentMenuCode": "M31", "menuName": "精選推薦商品查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M31", "parentMenuCode": "M3", "menuName": "精選推薦商品", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M31-01", "name": "精選推薦商品查詢", "parentCode": "M31", "order": 0, "leaf": false, "nodes": [], "menuCode": "M31-01", "parentMenuCode": "M31", "menuName": "精選推薦商品查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-01", "name": "綜合條件客戶查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-01", "parentMenuCode": "M20", "menuName": "綜合條件客戶查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M23-02", "name": "歸戶查詢", "parentCode": "M23", "order": 0, "leaf": false, "nodes": [], "menuCode": "M23-02", "parentMenuCode": "M23", "menuName": "歸戶查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M23-00", "name": "歸戶設定", "parentCode": "M23", "order": 0, "leaf": false, "nodes": [], "menuCode": "M23-00", "parentMenuCode": "M23", "menuName": "歸戶設定", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M23-01", "name": "歸戶維護", "parentCode": "M23", "order": 0, "leaf": false, "nodes": [], "menuCode": "M23-01", "parentMenuCode": "M23", "menuName": "歸戶維護", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M43-01", "name": "證照登錄查詢", "parentCode": "M43", "order": 0, "leaf": false, "nodes": [], "menuCode": "M43-01", "parentMenuCode": "M43", "menuName": "證照登錄查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M43", "name": "證照管理", "parentCode": "M4", "order": 0, "leaf": false, "nodes": [{"code": "M43-01", "name": "證照登錄查詢", "parentCode": "M43", "order": 0, "leaf": false, "nodes": [], "menuCode": "M43-01", "parentMenuCode": "M43", "menuName": "證照登錄查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M43", "parentMenuCode": "M4", "menuName": "證照管理", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M23", "name": "顧客歸戶管理", "parentCode": "M2", "order": 0, "leaf": false, "nodes": [{"code": "M23-02", "name": "歸戶查詢", "parentCode": "M23", "order": 0, "leaf": false, "nodes": [], "menuCode": "M23-02", "parentMenuCode": "M23", "menuName": "歸戶查詢", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M23-00", "name": "歸戶設定", "parentCode": "M23", "order": 0, "leaf": false, "nodes": [], "menuCode": "M23-00", "parentMenuCode": "M23", "menuName": "歸戶設定", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M23-01", "name": "歸戶維護", "parentCode": "M23", "order": 0, "leaf": false, "nodes": [], "menuCode": "M23-01", "parentMenuCode": "M23", "menuName": "歸戶維護", "roleName": "理財專員", "depths": 3, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M23", "parentMenuCode": "M2", "menuName": "顧客歸戶管理", "roleName": "理財專員", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "sqlInfo": " SELECT ARMM.<PERSON><PERSON><PERSON>_CODE, AR.ROLE_NAME,AM.MENU_NAME, AM.MENU_CODE, AM.ACTIVE_YN, AM.DEPTHS, AM.PARENT_MENU_CODE  FROM ADM_ROLE_MENU_MAP ARMM  INNER JOIN ADM_MENUS AM ON AM.MENU_CODE = ARMM.MENU_CODE  INNER JOIN ADM_ROLES AR ON AR.ROLE_CODE = ARMM.ROLE_CODE  WHERE 1=1  AND ARMM.ROLE_CODE = :roleCode  ORDER BY AM.MENU_NAME, AM.SHOW_ORDER ,class com.bi.pbs.adm.web.model.RoleMenuResp,{roleCode=00}"}]}