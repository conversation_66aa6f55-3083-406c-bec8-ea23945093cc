{"status": 200, "data": [{"userCode": "112790", "posCode": "891_41", "branCode": "891", "buCode": "Z", "posName": "系統管理經辦"}, {"userCode": "112790", "posCode": "891_49", "branCode": "891", "buCode": "Z", "posName": "系統管理幹部"}, {"userCode": "112790", "posCode": "891_98", "branCode": "891", "buCode": "Z", "posName": "系統管理者"}], "timestamp": "2025/04/21", "sqlTracer": [{"data": [{"userCode": "112790", "posCode": "891_41", "branCode": "891", "buCode": "Z", "posName": "系統管理經辦"}, {"userCode": "112790", "posCode": "891_49", "branCode": "891", "buCode": "Z", "posName": "系統管理幹部"}, {"userCode": "112790", "posCode": "891_98", "branCode": "891", "buCode": "Z", "posName": "系統管理者"}], "sqlInfo": " SELECT AUPM.USER_CODE, AUPM.POS_CODE, AUPM.BRAN_CODE, AP.BU_CODE, AP.POS_NAME  FROM ADM_USER_POS_MAP AUPM  INNER JOIN ADM_POSITIONS AP ON AP.POS_CODE = AUPM.POS_CODE  WHERE AUPM.USER_CODE = :userCode ,class com.bi.pbs.adm.web.model.AdmUserPosMapResp,{userCode=112790}"}]}