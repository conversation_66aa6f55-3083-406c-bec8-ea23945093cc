import getVisitLogsApiPageDataApiJson from './mockData/wob/GetVisitLogsApiData.json';
import getWobTdRecVerifiesApiPageDataApiJson from './mockData/wob/GetWobTdRecVerifiesApiData.json';

export function postPersonalTaskApi({ nextRemindDt, nextRemindTime, advNce, advNceDay, advNcePrd, title, content }) {
	return Promise.resolve({ data: null });
}

export function postVisit({ cusCode, nextRemindDt, nextRemindTime, advNce, advNceDay, advNcePrd, visitAprCode, visitPurCode, title, content }) {
	return Promise.resolve({ data: null });
}

export function postConnect({ cusCode, nextRemindDt, nextRemindTime, title, visitAprCode, content, contStatCode, contProcCode, doneYn }) {
	return Promise.resolve({ data: null });
}

export function postMemoryDateApi({ cusCode, dateDt, note, remindYn, remindDays, remindPrd }) {
	return Promise.resolve({ data: null });
}

export function getReuseWordsApi() {
	return Promise.resolve({ data: null });
}

export function postReuseWordsApi({ wordsId, words }) {
	return Promise.resolve({ data: null });
}

export function updateReuseWordsApi(dataArray) {
	return Promise.resolve({ data: null });
}

export function getCalendarTasksApi({ startDate, endDate }) {
	return Promise.resolve({ data: null });
}

export function getTdRecApi({ recCode }) {
	return Promise.resolve({ data: null });
}

export function deleteTdRecApi({ recCode }) {
	return Promise.resolve({ data: null });
}

export function patchTdPersonalRecApi({ tdRec, nextRemindDt, nextRemindTime, title, content, advNce, advNceDay, advNcePrd }) {
	return Promise.resolve({ data: null });
}

export function getAppointmentTdRecApi({ recCode }) {
	return Promise.resolve({ data: null });
}

export function patchAppointmentTdRecApi({
	recCode,
	cusCode,
	nextRemindDt,
	nextRemindTime,
	visitPurCode,
	visitAprCode,
	title,
	advNce,
	advNceDay,
	advNcePrd,
	content,
	doneYn,
	verifyStatusCode
}) {
	return Promise.resolve({ data: null });
}

export function getMemoryCalendarTaskApi({ id }) {
	return Promise.resolve({ data: null });
}

export function deleteMemoryDateApi({ id }) {
	return Promise.resolve({ data: null });
}

export function patchMemoryDateApi({ id, cusCode, dateDt, note, remindDays, remindPrd, remindYn }) {
	return Promise.resolve({ data: null });
}

export function getTdConnRecApi({ recCode }) {
	return Promise.resolve({ data: null });
}

export function patchTdConnRecApi({
	recCode,
	cusCode,
	nextRemindDt,
	nextRemindTime,
	doneYn,
	title,
	content,
	visitAprCode,
	contStatCode,
	contProcCode,
	contProcDesc,
	visitUserName,
	verifyStatusCode,
	giftId,
	giftDesc
}) {
	return Promise.resolve({ data: null });
}

export function postWobReuseWordsApi({ wordsId, words }) {
	return Promise.resolve({ data: null });
}

export function getWobReuseWordsApi() {
	return Promise.resolve({ data: null });
}

export function getVisitLogsApi({ cusCode, strDate, endDate, branCode, userCode, eventType, visitPurCode }, queryString) {
	return Promise.resolve({ data: getVisitLogsApiPageDataApiJson });
}

export function getWobTdRecVerifiesApi(
	{
		minorAreaBranCode,
		allBranCodes,
		branCode,
		allUserCodes,
		userCode,
		buCode,
		createStartDate,
		createEndDate,
		cusName,
		idn,
		verifyStartDate,
		verifyEndDate,
		verifyStatusCodes
	},
	queryString
) {
	return Promise.resolve({ data: getWobTdRecVerifiesApiPageDataApiJson });
}

export function patchWobTdRecVerifiesApi(updateItems) {
	return Promise.resolve({ data: null });
}
