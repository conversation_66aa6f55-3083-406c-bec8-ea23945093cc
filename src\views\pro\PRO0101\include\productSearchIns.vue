<template>
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<!--頁面內容 保險ins start-->
			<div class="tab-nav-tabs my-3">
				<ul class="nav nav-tabs nav-justified">
					<li class="nav-item">
						<a class="nav-link" :class="{ active: activeTab == 'common' }" data-bs-toggle="tab"
							@click="changeTab('common')">一般篩選</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" :class="{ active: activeTab == 'fast' }" data-bs-toggle="tab"
							@click="changeTab('fast')">快速篩選</a>
					</li>
				</ul>

				<div class="tab-content">
					<div class="tab-pane fade show" id="SectionA" :class="{ 'active show': activeTab == 'common' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
								<h4>查詢條件</h4>
								<span class="tx-square-bracket">為必填欄位</span>
							</div>
							<div class="collapse show" id="collapseListGroup1">
								<div class="card-body">
									<vue-form v-slot="{ errors }" ref="searchQueryForm">
										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">險種代碼</label>
												<input class="form-control" id="prod_pro_code" maxlength="20" size="45" type="text"
													v-model="bankProCode" />
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">保險商品名稱</label>
												<input class="form-control" id="prod_pro_name" maxlength="20" size="45" type="text"
													v-model="proName" />
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 保險次分類</label>
												<select class="form-select" id="proTypeCode" name="proTypeCode" v-model="proTypeCode">
													<option value="">全部</option>
													<option v-for="(item, index) in proTypeMenu" :key="index" :value="item.proTypeCode">
														{{ $filters.defaultValue(item.proTypeName, '--') }}
													</option>
												</select>
												<div style="height: 3px">
													<span class="text-danger" v-show="errors.proTypeCode">{{
														$filters.defaultValue(errors.proTypeCode, '--')
													}}</span>
												</div>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 風險等級 </label>
												<select class="form-select" id="riskCode" name="riskCode" v-model="riskCode">
													<option value="">全部</option>
													<option v-for="item in riskMenu" :value="item.riskCode">
														{{ $filters.defaultValue(item.riskName, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 計價幣別</label>
												<select class="selectpicker form-control" id="curMenuIns" multiple title="請選擇幣別"
													v-model="curObjs" data-style="btn-white">
													<option value="">全部</option>
													<option v-for="(item, index) in curOption" :key="index" :value="item.value">
														{{ $filters.defaultValue(item.name, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">保險年期(年)</label>
												<div class="input-group">
													<input type="text" class="form-control" size="5" v-model="insTermDtStart" />
													<div class="input-group-text">~</div>
													<input type="text" class="form-control" size="5" v-model="insTermDtEnd" />
												</div>
											</div>
											<div class="form-group col-12 col-lg-5">
												<label class="form-label"> 保險公司 </label>
												<button type="button" class="btn btn-primary" @click="groupInsCmpModalHandler()">選擇保險公司</button>
												<vue-modal :is-open="isOpenInsCmpModal" :before-close="isOpenInsCmpModal = false">
													<template v-slot:content="props">
														<vue-group-inscmp-modal :close="props.close" ref="groupInsCmpModalRef" id="groupInsCmpModal"
															:issuer-prop="insCmpItem" @selected="selectedInsCmp"></vue-group-inscmp-modal>
													</template>
												</vue-modal>
											</div>
											<div class="form-group col-12 col-lg-7">
												<label class="form-label">商品上架日</label>
												<div class="input-group">
													<input class="form-control" id="prod_chinese_name" maxlength="20" name="prod_chinese_name"
														size="10" type="date" v-model="stdDtStart" />
													<div class="input-group-text">~</div>
													<input class="form-control" id="prod_chinese_name" maxlength="20" name="prod_chinese_name"
														size="10" type="date" v-model="stdDtEnd" />
												</div>
											</div>
										</div>

										<div style="padding-left: 120px; padding-bottom: 15px" v-for="item in insCmpItem">
											<span class="form-check-label"> {{ $filters.defaultValue(item.inscmpName, '--') }}</span>
											<a href="#" @click="deleteInsCmpItem(item.inscmpCode)"><img
													:src="getImgURL('icon', 'i-cancel.png')" /></a>
										</div>

										<div class="form-footer">
											<button class="btn btn-primary" @click.prevent="gotoPage(0)">查詢</button>
										</div>
									</vue-form>
								</div>
							</div>
						</div>
					</div>

					<div class="tab-pane fade" id="SectionB" :class="{ 'active show': activeTab == 'fast' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2">
								<h4>查詢條件</h4>
								<span class="tx-square-bracket">為必填欄位</span>
							</div>
							<div class="collapse show" id="collapseListGroup2">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-12">
											<label class="form-label tx-require"> 篩選條件 </label>
											<div class="form-check-group" v-for="item in insFastMenu" @change="fastChange(item.codeValue)">
												<input class="form-check-input" :id="'fast' + item.codeValue" name="fastCode" v-model="fastCode"
													:value="item.codeValue" type="radio" />
												<label class="form-check-label" :for="'fast' + item.codeValue">{{
													$filters.defaultValue(item.codeName, '--')
												}}</label>
											</div>
										</div>
										<div class="form-group col-12 col-lg-6" id="rangeFixedTrIns" style="display: none">
											<label class="form-label tx-require"> 顯示區間</label>
											<select class="form-select" id="prod_protype_code" v-model="timeRange">
												<option v-for="item in timeRangeMenu" :value="item">
													{{ $filters.defaultValue(item.termName, '--') }}
												</option>
											</select>
										</div>

										<div class="form-group col-12 col-lg-6" id="proPerfTimeTrIns" style="display: none">
											<label class="form-label tx-require">標的績效 </label>

											<select class="form-select" id="vfAstStat_stat_code" v-model="perf">
												<option v-for="item in perfMenu" :value="item.codeValue">
													{{ $filters.defaultValue(item.codeName, '--') }}
												</option>
											</select>
										</div>

										<div class="form-group col-12 col-lg-6" id="maxRowIdTrIns" style="display: none">
											<label class="form-label tx-require">顯示資料筆數</label>
											<select class="form-select" id="maxRowId" v-model="rowNumber">
												<option value="">全部</option>
												<option v-for="item in rowNumerMenu" :value="item.rangeFixed">
													{{ $filters.defaultValue(item.termName, '--') }}
												</option>
											</select>
										</div>
									</div>

									<div class="form-footer">
										<button class="btn btn-primary" @click.prevent="gotoFastPage(0)">查詢</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div id="searchResult" v-if="pageData.content.length > 0">
				<div class="card card-table">
					<div class="card-header">
						<h4>查詢結果</h4>
						<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-bordered text-center">
							<thead>
								<tr>
									<th>保險次分類<a href="#" class="icon-sort" @click="sort('PROTYPE_NAME')"></a></th>
									<th>險種類別</th>
									<th>險種代碼</th>
									<th>商品名稱<a href="#" class="icon-sort" @click="sort('PRO_NAME')"></a></th>
									<th>保險公司<a href="#" class="icon-sort" @click="sort('INSCMP_NAME')"></a></th>
									<th>風險等級</th>
									<th>幣別<a href="#" class="icon-sort" @click="sort('CUR_CODE')"></a></th>
									<th>保險年期</th>
									<th>上架日期</th>
									<th class="text-center" width="120">觀察</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="(item, index) in pageData.content">
									<td data-th="保險次分類">
										<span>{{ $filters.defaultValue(item.proTypeName, '--') }}</span>
									</td>
									<td data-th="險種類別">
										<span>{{ $filters.defaultValue(item.insType, '--') }}</span>
									</td>
									<td data-th="險種代碼">
										<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
									</td>
									<td class="text-start" data-th="商品名稱">
										<span>
											<a class="tx-link" href="#" @click="insModalHandler(item.proCode, item.pfcatCode)">{{
												$filters.defaultValue(item.proName, '--')
											}}</a>
										</span>
									</td>
									<td class="text-end" data-th="保險公司">
										<span>{{ $filters.defaultValue(item.inscmpName, '--') }}</span>
									</td>
									<td class="text-end" data-th="風險等級">
										<span>{{ $filters.defaultValue(item.riskName, '--') }}</span>
									</td>
									<td class="text-end" data-th="計價幣別">
										<span>{{ $filters.defaultValue(item.curCode, '--') }}</span>
									</td>
									<td class="text-end" data-th="保險年期">
										<span>{{ $filters.defaultValue(item.insTerm, '--') }}</span>
									</td>
									<td class="text-end" data-th="上架日期">
										<span>{{ $filters.defaultValue(item.stdDt, '--') }}</span>
									</td>
									<td class="text-center" data-th="觀察">
										<button v-if="activeTab === 'fast' && fastCode === '06'" type="button" class="btn btn-primary"
											title="移除我的最愛" @click="remove(item.proCode)">
											移除最愛
										</button>
										<button v-else type="button" class="btn btn-dark btn-icon" data-bs-toggle="tooltip" title="加入我的最愛"
											@click="favoritesHandler(item.proCode, item.pfcatCode)">
											<i class="bi bi-heart text-danger"></i>
										</button>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="tx-note">
					<ol>
						<li><span>資料日期：</span></li>
						<li><span>商品是否可申購以交易系統為主</span></li>
					</ol>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import pagination from '@/views/components/pagination.vue';
import { Form, Field } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import vueGroupInscmpModal from './groupInsCmpModal.vue';
import { getImgURL } from '@/utils/imgURL.js';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		'vue-pagination': pagination,
		vueModal,
		vueGroupInscmpModal
	},
	props: {
		insModalHandler: Function,
		favoritesHandler: Function,
		curOption: Array,
		riskMenu: Array
	},
	data: function () {
		return {
			activeTab: 'common',
			perf: '',
			insFastMenu: [],
			perfMenu: [],

			bankProCode: null, // 保險險種代號
			proName: null, // 保險商品名稱
			proTypeCode: '', // 保險分類
			riskCode: '', // 風險等級
			curObjs: [], // 計價幣別
			insTermDtStart: null, // 保險年期(年)-起
			insTermDtEnd: null, // 保險年期(年)-迄
			insCmpItem: [], // 保險公司
			stdDtStart: null, // 商品上架日-起
			stdDtEnd: null, // 商品上架日-迄

			fastCode: '03', // 快速 篩選條件
			timeRange: null, // 快速 顯示區間
			rowNumber: null, // 快速 顯示資料筆數

			proTypeMenu: [], // 保險分類

			proCodes: [], //移除我的最愛按鈕、執行績效比較圖
			selectedItems: {}, // 加入比較選項
			timeRangeMenu: [], // 顯示區間
			rowNumerMenu: [], // 顯示資料筆數

			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: ['PRO_CODE'],
				direction: 'ASC'
			},
			isOpenInsCmpModal: false
		};
	},
	watch: {
		selectedItems: {
			handler(newValues) {
				this.proCodes = Object.keys(newValues).filter((proCode) => newValues[proCode]);
			},
			deep: true
		},
		activeTab(newVal, oldVal) {
			var self = this;
			self.fastCode = '03';
			self.fastChange(self.fastCode);
		},
		curObjs(newVal, oldVal) {
			var self = this;
			if (newVal[0] !== oldVal[0]) {
				if (newVal[0] === '') {
					$('#curMenuIns').selectpicker('selectAll');
				} else if (oldVal[0] === '' && newVal[0] !== '') {
					$('#curMenuIns').selectpicker('deselectAll');
				}
			}
		}
	},
	mounted: function () {
		var self = this;
		$('#curMenuIns').selectpicker('refresh');
		self.getProTypeMenu(); // 取得保險分類選項
		self.getInsFastMenu(); // 取得快速篩選條件選項
		self.getTimeRangeMenu(); // 取得顯示區間選項
		self.getRowNumerMenu(); // 取得顯示資料筆數選項
	},
	methods: {
		getImgURL,
		// 條件Tab切換
		changeTab: function (tabName) {
			var self = this;
			self.activeTab = tabName;
			self.pageData = {
				// 清空查詢資料
				content: []
			};
		},
		// 取得保險分類選項
		async getProTypeMenu() {
			const ret = await this.$api.getProTypeListApi({
				pfcatCode: 'INS'
			});
			this.proTypeMenu = ret.data;
		},
		// 取得快速篩選條件選項
		async getInsFastMenu() {
			const ret = await this.$api.getInsFastFilterMenuApi();
			this.insFastMenu = ret.data;
		},
		// 取得顯示區間
		async getTimeRangeMenu() {
			const ret = await this.$api.getTimeRangeMenuApi();
			this.timeRangeMenu = ret.data;
		},
		// 取得顯示資料筆數
		async getRowNumerMenu() {
			const ret = await this.$api.getRowNumerMenuApi();
			this.rowNumerMenu = ret.data;
		},
		fastChange(fastCode) {
			// 快速查詢切換
			var self = this;
			self.timeRange = {}; // 快速 顯示區間
			self.rowNumber = ''; // 快速 顯示資料筆數
			self.perf = '';
			self.pageData = {
				// 清空查詢資料
				content: []
			};
			if (fastCode === '05') {
				// 超人氣
				$('#proPerfTimeTrIns').hide();
				$('#rangeFixedTrIns').show();
				$('#maxRowIdTrIns').show();
				self.timeRange = self.timeRangeMenu[0];
			} else if (fastCode === '07') {
				// 績效排行
				$('#rangeFixedTrIns').hide();
				$('#maxRowIdTrIns').show();
				$('#proPerfTimeTrIns').show();
				self.perf = 'PCTYTD';
			} else if (fastCode === '08') {
				// 最高配息率商品
				$('#maxRowIdTrIns').show();
				$('#rangeFixedTrIns').hide();
				$('#proPerfTimeTrIns').hide();
			} else {
				$('#maxRowIdTrIns').hide();
				$('#rangeFixedTrIns').hide();
				$('#proPerfTimeTrIns').hide();
			}
		},
		// 由查詢結果標題觸發
		sort: function (columnName) {
			var self = this;
			if (this.pageable.sort !== columnName) {
				this.pageable.sort = columnName;
				this.pageable.direction = 'DESC';
			} else {
				this.pageable.direction = this.pageable.direction == 'ASC' ? 'DESC' : 'ASC';
			}

			if (self.activeTab === 'common') {
				this.gotoPage(0);
			} else if (self.activeTab === 'fast') {
				this.gotoFastPage(0);
			}
		},
		gotoPage: function (page) {
			var self = this;
			self.$refs.searchQueryForm.validate().then(function (pass) {
				if (pass.valid) {
					self.pageable.page = page;
					self.getPageData(page);
				}
			});
		},
		async getPageData(_page) {
			const self = this;
			const page = _.isNumber(_page) ? _page : this.pageable.page;
			const queryString = `?page=${page}&size=${this.pageable.size}&sort=${this.pageable.sort},${this.pageable.direction}`;

			const cmpCodes = this.insCmpItem.map((item) => item.inscmpCode);
			const ret = await this.$api.getInsProductsApi(
				{
					bankProCode: self.bankProCode, // 保險險種代號
					proName: self.proName, // 保險商品名稱
					protypeCode: self.proTypeCode, // 保險分類
					riskCode: self.riskCode, // 風險等級
					curCodes: self.curObjs, // 計價幣別
					insTermDtStart: self.insTermDtStart, // 保險年期(年)-起
					insTermDtEnd: self.insTermDtEnd, // 保險年期(年)-迄
					inscmpCodes: cmpCodes, // 保險公司
					stdDtStart: self.stdDtStart, // 商品上架日-起
					stdDtEnd: self.stdDtEnd // 商品上架日-迄
				},
				queryString
			);

			this.pageData = ret.data;
			this.$forceUpdate();
		},
		gotoFastPage: function (page) {
			this.pageable.page = page;
			this.getFastPageData(page);
		},
		async getFastPageData(_page) {
			const page = _.isNumber(_page) ? _page : this.pageable.page;
			const queryString = `?page=${page}&size=${this.pageable.size}&sort=${this.pageable.sort},${this.pageable.direction}`;

			const ret = await this.$api.getInsProductsFilterQueryApi(
				{
					filterCodeValue: this.fastCode,
					timeRangeType: this.timeRange.rangeType,
					timeRangeFixed: this.timeRange.rangeFixed,
					rowNumberFixed: this.rowNumber
				},
				queryString
			);

			this.pageData = ret.data;
		},
		groupInsCmpModalHandler: function () {
			//顯示保險公司 model
			var self = this;
			this.$refs.groupInsCmpModalRef.inscmpPropItem(self.insCmpItem);
			this.isOpenInsCmpModal = true;
		},
		selectedInsCmp(insCmpItem) {
			// 顯示保險公司選擇項目
			var self = this;
			this.isOpenInsCmpModal = false;
			self.insCmpItem = insCmpItem; //取得保險公司資料
		},
		deleteInsCmpItem(inscmpCode) {
			var self = this;
			_.remove(self.insCmpItem, (item) => item.inscmpCode === inscmpCode); // 移除刪除項目
		},
		// 刪除我的最愛
		async remove(proCode) {
			await this.$api.deleteFavoriteApi({
				proCode: proCode
			});
			this.$bi.alert('刪除成功');
			this.checkboxs = [];
			this.proCodes = [];
			this.gotoFastPage(0);
		}
	} // methods end
};
</script>
