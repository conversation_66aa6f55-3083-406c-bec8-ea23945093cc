<template>
	<div class="tab-content">
		<!--頁面內容 信託-海外債bond start-->
		<div class="tab-pane fade show active">
			<div class="tab-nav-tabs my-3">
				<ul class="nav nav-tabs nav-justified">
					<li class="nav-item">
						<a class="nav-link" :class="{ active: activeTab == 'common' }" data-bs-toggle="tab" @click="changeTab('common')">一般篩選</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" :class="{ active: activeTab == 'fast' }" data-bs-toggle="tab" @click="changeTab('fast')">快速篩選</a>
					</li>
				</ul>

				<div class="tab-content">
					<div class="tab-pane fade show" id="SectionA" :class="{ 'active show': activeTab == 'common' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
								<h4>查詢條件</h4>
								<span class="tx-square-bracket">為必填欄位</span>
							</div>
							<div class="collapse show" id="collapseListGroup1">
								<div class="card-body">
									<form>
										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 商品代號 </label>
												<input
													class="form-control"
													id="prod_bank_pro_code"
													maxlength="20"
													v-model="bankProCode"
													size="25"
													type="text"
													value=""
												/>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 商品名稱</label>
												<input
													class="form-control"
													id="prod_pro_name"
													maxlength="20"
													v-model="proName"
													size="45"
													type="text"
													value=""
												/>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 商品類型</label>
												<select
													class="form-select"
													id="proType"
													name="proType"
													title="請選擇類型"
													v-model="proTypeCode"
													data-style="btn-white"
												>
													<option value="">全部</option>
													<option v-for="item in proTypeMenu" :value="item.proTypeCode">
														{{ $filters.defaultValue(item.proTypeName, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 計價幣別</label>
												<select
													class="selectpicker form-control"
													id="curMenuBond"
													multiple
													title="請選擇幣別"
													v-model="curObjs"
													data-style="btn-white"
												>
													<option value="">全部</option>
													<option v-for="(item, index) in curOption" :key="index" :value="item.value">
														{{ $filters.defaultValue(item.name, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> 風險等級 </label>
												<div class="form-check-group">
													<div class="form-check form-check-inline" v-for="(item, index) in riskMenu">
														<input
															type="checkbox"
															class="form-check-input"
															name="riskCodes"
															v-model="riskCodes"
															:id="'riskGrade-' + index"
															:value="item.riskCode"
														/>
														<label :for="'riskGrade-' + index" class="form-check-label">{{ item.riskName }}</label>
													</div>
												</div>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">配息頻率</label>
												<select class="form-select" id="freqUnit" name="freqUnit" v-model="freqUnit" data-style="btn-white">
													<option value="">全部</option>
													<option v-for="item in intFreqUnitMenu" :value="item.codeValue">
														{{ $filters.defaultValue(item.codeName, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">最低申購面額</label>
												<input type="number" class="form-control" v-model="mininvAmt" />
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">累加面額</label>
												<input
													class="form-control"
													id="mininvAccAmt"
													maxlength="20"
													v-model="mininvAccAmt"
													size="25"
													type="number"
												/>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">債券價格</label>
												<input type="text" class="form-control" v-model="price" />
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">發行機構</label>
												<select
													class="form-select"
													id="issuerCode"
													name="issuerCode"
													v-model="issuerCode"
													data-style="btn-white"
												>
													<option value="">全部</option>
													<option v-for="item in proIssuerMenu" :value="item.issuerCode">
														{{ $filters.defaultValue(item.issuerName, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">客戶類別</label>
												<div class="form-check-group">
													<input
														class="form-check-input"
														id="cusBuNan"
														name="targetCusBu"
														v-model="targetCusBu"
														value=""
														type="radio"
													/>
													<label class="form-check-label" for="cusBuNan">全部</label>
												</div>
												<div class="form-check-group" v-for="item in targetCusBuMenu">
													<input
														class="form-check-input"
														:id="'cusBu' + item.codeValue"
														name="targetCusBu"
														v-model="targetCusBu"
														:value="item.codeValue"
														type="radio"
													/>
													<label class="form-check-label" :for="'cusBu' + item.codeValue">{{
														$filters.defaultValue(item.codeName, '--')
													}}</label>
												</div>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">票面利率(%)</label>
												<div class="input-group">
													<input type="number" class="form-control" size="5" v-model="parRateMin" />
													<div class="input-group-text">~</div>
													<input type="number" class="form-control" size="5" v-model="parRateMax" />
												</div>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">限PI銷售</label>
												<div v-for="item in profInvestorMenu" class="form-check form-check-inline">
													<input
														class="form-check-input"
														:id="'profInvestor' + item.codeValue"
														v-model="profInvestorYn"
														type="radio"
														:value="item.codeValue"
														name="fastCode"
													/>
													<label class="form-check-label" :for="'profInvestor' + item.codeValue">{{
														$filters.defaultValue(item.codeName, '--')
													}}</label>
												</div>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">ISINCODE</label>
												<input class="form-control" id="isinCode" maxlength="20" v-model="isinCode" size="45" type="text" />
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">債券分類<br />依發行機構</label>
												<select
													class="form-select"
													id="issuerType"
													name="issuerType"
													v-model="issuerType"
													data-style="btn-white"
												>
													<option value="">全部</option>
													<option v-for="item in issuerTypeMenu" :value="item.codeValue">
														{{ $filters.defaultValue(item.codeName, '--') }}
													</option>
												</select>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">債券分類<br />依投資品質</label>
												<select
													class="form-select"
													id="invQuality"
													name="invQuality"
													v-model="invQuality"
													data-style="btn-white"
												>
													<option value="">全部</option>
													<option v-for="item in invQualityMenu" :value="item.codeValue">
														{{ $filters.defaultValue(item.codeName, '--') }}
													</option>
												</select>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">債券分類<br />依票面利率</label>
												<select
													class="form-select"
													id="bondRateType"
													name="bondRateType"
													v-model="bondRateType"
													data-style="btn-white"
												>
													<option value="">全部</option>
													<option v-for="item in bondRateTypeMenu" :value="item.codeValue">
														{{ $filters.defaultValue(item.codeName, '--') }}
													</option>
												</select>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">債券分類<br />依掛牌地</label>
												<select
													class="form-select"
													id="listingType"
													name="listingType"
													v-model="listingType"
													data-style="btn-white"
												>
													<option value="">全部</option>
													<option v-for="item in listingTypeMenu" :value="item.codeValue">
														{{ $filters.defaultValue(item.codeName, '--') }}
													</option>
												</select>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">債券分類<br />依受償順位</label>
												<select
													class="form-select"
													id="payAllocation"
													name="payAllocation"
													v-model="payAllocation"
													data-style="btn-white"
												>
													<option value="">全部</option>
													<option v-for="item in payAllocationMenu" :value="item.codeValue">
														{{ $filters.defaultValue(item.codeName, '--') }}
													</option>
												</select>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">到期殖利率</label>
												<div class="input-group">
													<input type="number" class="form-control" size="5" v-model="expireYieldMin" />
													<div class="input-group-text">~</div>
													<input type="number" class="form-control" size="5" v-model="expireYieldMax" />
												</div>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">剩餘年限(年)</label>
												<div class="input-group">
													<input type="number" class="form-control" size="5" v-model="periodMin" />
													<div class="input-group-text">~</div>
													<input type="number" class="form-control" size="5" v-model="periodMax" />
												</div>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">保證機構</label>
												<select class="form-select" id="guarCode" name="guarCode" v-model="guarCode" data-style="btn-white">
													<option value="">全部</option>
													<option v-for="item in proGuaranteesMenu" :value="item.guarCode">
														{{ $filters.defaultValue(item.guarName, '--') }}
													</option>
												</select>
											</div>
										</div>
										<!--										<div style="padding-left: 120px;padding-bottom: 15px"-->
										<!--											v-for="item in issuerItem">-->
										<!--											<span class="form-check-label">-->
										<!--												{{$filters.defaultValue(item.issuerName,'--')}}</span>-->
										<!--											<a href="#" @click="deleteIssuerItem(item.issuerCode)"><img-->
										<!--													th:src="@{/images/icon/i-cancel.png}" /></a>-->
										<!--										</div>-->
										<div class="form-footer">
											<button class="btn btn-primary" @click.prevent="gotoPage(0)">查詢</button>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>

					<div class="tab-pane fade" id="SectionB" :class="{ 'active show': activeTab == 'fast' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2">
								<h4>查詢條件</h4>
								<span class="tx-square-bracket">為必填欄位</span>
							</div>
							<div class="collapse show" id="collapseListGroup2">
								<div class="card-body">
									<form>
										<div class="form-row">
											<div class="form-group col-12 col-lg-12">
												<label class="form-label tx-require"> 篩選條件 </label>
												<div class="form-check-group" v-for="item in bondFastMenu" @change="fastChange(item.codeValue)">
													<input
														class="form-check-input"
														:id="'fast' + item.codeValue"
														name="fastCode"
														v-model="fastCode"
														:value="item.codeValue"
														type="radio"
													/>
													<label class="form-check-label" :for="'fast' + item.codeValue">{{
														$filters.defaultValue(item.codeName, '--')
													}}</label>
												</div>
											</div>

											<div class="form-group col-12 col-lg-6" id="rangeFixedTrBond" style="display: none">
												<label class="form-label tx-require"> 顯示區間</label>

												<select class="form-select" id="prod_protype_code" v-model="timeRange">
													<option v-for="item in timeRangeMenu" :value="item">
														{{ $filters.defaultValue(item.termName, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-6" id="proPerfTimeTrBond" style="display: none">
												<label class="form-label tx-require">標的績效 </label>

												<select class="form-select" id="vfAstStat_stat_code" v-model="perf">
													<option v-for="item in perfMenu" :value="item.codeValue">
														{{ $filters.defaultValue(item.codeName, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-6" id="curTrBond" style="display: none">
												<label class="form-label tx-require">幣別 </label>
												<select class="form-select" id="curMenuBondFast" title="請選擇幣別" v-model="curObjsFast">
													<option value="">全部</option>
													<option v-for="(item, index) in curOption" :key="index" :value="item.value">
														{{ $filters.defaultValue(item.name, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-6" id="priceDiffTrBond" style="display: none">
												<label class="form-label tx-require">價差</label>
												<input type="number" class="form-control" size="10" v-model="priceDiffStart" />
												<div class="input-group-text">~</div>
												<input type="number" class="form-control" size="10" v-model="priceDiffEnd" />
											</div>

											<div class="form-group col-12 col-lg-6" id="maxRowIdTrBond" style="display: none">
												<label class="form-label tx-require">顯示資料筆數</label>
												<select class="form-select" id="maxRowId" v-model="rowNumber">
													<option value="">全部</option>
													<option v-for="item in rowNumerMenu" :value="item.rangeFixed">
														{{ $filters.defaultValue(item.termName, '--') }}
													</option>
												</select>
											</div>
										</div>
									</form>

									<div class="form-footer">
										<button class="btn btn-primary" @click.prevent="gotoFastPage(0)">查詢</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div id="result" v-if="pageData.content.length > 0">
				<div class="tab-nav-line">
					<ul
						class="nav nav-line"
						style="background-color: transparent; margin-top: 10px; margin-bottom: 20px; border-bottom: 2px solid #e2e2e2"
					>
						<li class="nav-item"><a href="#pie_chart1" data-bs-toggle="tab" class="nav-link active">基本資料</a></li>
						<li class="nav-item"><a href="#pie_chart2" data-bs-toggle="tab" class="nav-link">市場績效</a></li>
					</ul>
					<div class="tab-content">
						<div role="tabpanel" class="tab-pane active" id="pie_chart1">
							<div class="card card-table">
								<div class="card-header">
									<h4>查詢結果</h4>
									<div style="display: flex">
										<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
										<button type="button" class="btn btn-info ms-2" @click="performancesCompareModelHandler()">績效比較圖</button>
										<vue-modal :is-open="isOpenCompareModal" :before-close="isOpenCompareModal = false">
											<template v-slot:content="props">
												<vue-performances-compare-modal
													:close="props.close"
													ref="performancesCompareModalRef"
													id="performancesCompareModal"
												></vue-performances-compare-modal>
											</template>
										</vue-modal>
									</div>
								</div>
								<div class="table-responsive">
									<table class="table table-RWD table-bordered text-center">
										<thead>
											<tr>
												<th rowspan="2" class="wd-100 text-start">加入比較</th>
												<th rowspan="2">商品代號</th>
												<th rowspan="2">ISIN_CODE</th>
												<th rowspan="2" class="10% text-start">商品中文名稱</th>
												<th rowspan="2">計價幣別</th>
												<th rowspan="2">票面利率</th>
												<th rowspan="2">配息頻率</th>
												<th rowspan="2">配息日[下次]</th>
												<th rowspan="2">風險等級</th>
												<th rowspan="2">最低申購面額<br />累加面額</th>
												<th rowspan="2">銷售對象<br />(是否限PI)</th>
												<th colspan="3">發行機構評等</th>
												<th rowspan="2" class="text-center" width="120">執行</th>
											</tr>
											<tr>
												<th>穆迪</th>
												<th>標普</th>
												<th>惠譽</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="(item, index) in pageData.content">
												<td data-th="加入比較" class="text-start text-center">
													<input
														class="form-check-input text-center"
														v-model="checkboxs[index]"
														:checked="false"
														@click="checkoutPro(item.proCode)"
														:id="'id-' + item.bankProCode"
														type="checkbox"
													/>
													<label class="form-check-label" :for="'id-' + item.bankProCode"></label>
												</td>
												<td data-th="商品代號">
													<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
												</td>
												<td data-th="ISIN_CODE">
													<span>{{ $filters.defaultValue(item.isinCode, '--') }}</span>
												</td>
												<td class="text-start" data-th="商品中文名稱">
													<span>
														<a class="tx-link" @click="bondModalHandler(item.proCode, item.pfcatCode)">{{
															$filters.defaultValue(item.proName, '--')
														}}</a>
													</span>
												</td>
												<td class="text-end" data-th="計價幣別">
													<span>{{ $filters.defaultValue(item.curCode, '--') }}</span>
												</td>
												<td class="text-end" data-th="票面利率">
													<span>{{ $filters.formatPct(item.parRate, '--') }}%</span>
												</td>
												<td class="text-end" data-th="配息頻率">
													<span>{{ $filters.defaultValue(item.intFreqName, '--') }}</span>
												</td>
												<td data-th="配息日">
													<span>{{ $filters.defaultValue(item.nextIntDt, '--') }}</span>
												</td>
												<td data-th="風險等級">
													<span>{{ $filters.defaultValue(item.riskName, '--') }}</span>
												</td>
												<td data-th="最低申購面額">
													<span>
														<!-- class="icon-yes" -->
														<span>{{ $filters.formatNumber(item.mininvAmt, '--') }}</span
														><br />
														{{ $filters.formatNumber(item.mininvAccAmt, '--') }}
													</span>
												</td>
												<td data-th="銷售對象<br>(是否限PI)">
													<span>{{ $filters.defaultValue(item.piYnName, '--') }}</span>
												</td>
												<td data-th="穆迪">
													<span>{{ $filters.defaultValue(item.mrating, '--') }}</span>
												</td>
												<td data-th="標普">
													<span>{{ $filters.defaultValue(item.srating, '--') }}</span>
												</td>
												<td data-th="惠譽">
													<span>{{ $filters.defaultValue(item.frating, '--') }}</span>
												</td>
												<td class="text-center" data-th="執行">
													<button
														v-if="activeTab === 'fast' && fastCode === '06'"
														type="button"
														class="btn btn-primary"
														title="移除我的最愛"
														@click="remove(item.proCode)"
													>
														移除最愛
													</button>
													<button
														v-else
														type="button"
														class="btn btn-dark btn-icon"
														data-bs-toggle="tooltip"
														title="加入我的最愛"
														@click="favoritesHandler(item.proCode, item.pfcatCode)"
													>
														<i class="bi bi-heart text-danger"></i>
													</button>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
						<div role="tabpanel" class="tab-pane" id="pie_chart2">
							<div class="card card-table">
								<div class="card-header">
									<h4>查詢結果</h4>
									<vue-pagination :pageable="pageData" :goto-page="gotoPage"></vue-pagination>
									<button type="button" class="btn btn-info ms-2" @click="performancesCompareModelHandler()">績效比較圖</button>
								</div>
								<div class="table-responsive">
									<table class="table table-RWD table-bordered text-center">
										<thead>
											<tr>
												<th class="wd-100">加入比較</th>
												<th>商品代號</th>
												<th>ISIN_CODE</th>
												<th class="10%">商品中文名稱</th>
												<th>前手息</th>
												<th>參考申購價</th>
												<th>參考贖回價</th>
												<th>到期殖利率</th>
												<th>報價日</th>
												<th>債券到期日</th>
												<th>剩餘年限(年)</th>
												<th>預計通路服務費率</th>
												<th class="text-center" width="120">執行</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="item in pageData.content">
												<td data-th="加入比較" class="text-start text-center">
													<input
														class="form-check-input text-center"
														@click="checkoutPro(item.proCode)"
														:id="'id-' + item.bankProCode"
														type="checkbox"
													/>
													<label class="form-check-label" :for="'id-' + item.bankProCode">{{
														$filters.defaultValue(item.codeName, '--')
													}}</label>
												</td>
												<td data-th="商品代號">
													<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
												</td>
												<td data-th="ISIN_CODE">
													<span>{{ $filters.defaultValue(item.ISIN_CODE, '--') }}</span>
												</td>
												<td class="text-start" data-th="商品中文名稱">
													<span>
														<a class="tx-link" @click="bondModalHandler(item.proCode, item.pfcatCode)">{{
															$filters.defaultValue(item.proName, '--')
														}}</a>
													</span>
												</td>
												<td class="text-end" data-th="前手息">
													<span>{{ $filters.formatNumber(item.preInt, '--') }}</span>
												</td>
												<td class="text-end" data-th="參考申購價">
													<span>{{ $filters.formatNumber(item.bprice, '--') }}</span>
												</td>
												<td class="text-end" data-th="參考贖回價">
													<span>{{ $filters.formatNumber(item.sprice, '--') }}</span>
												</td>
												<td class="text-end" data-th="到期殖利率">
													<span v-if="item.expireYield">{{ $filters.formatPct(item.expireYield) }}%</span>
													<span v-else>--</span>
												</td>
												<td class="text-end" data-th="報價日">
													<span>{{ $filters.defaultValue(item.priceDt, '--') }}</span>
												</td>
												<td class="text-end" data-th="債券到期日">
													<span>{{ $filters.defaultValue(item.expireDt, '--') }}</span>
												</td>
												<td class="text-end" data-th="剩餘年限(年)">
													<span v-if="item.remainDay">{{ $filters.formatNumber(item.remainDay / 365) }}</span>
													<span v-else>--</span>
												</td>
												<td class="text-end" data-th="預計通路服務費率">
													<span v-if="item.channelServiceRate">{{ $filters.formatPct(item.channelServiceRate) }}%</span>
													<span v-else>--</span>
												</td>
												<td class="text-center" data-th="執行">
													<button
														v-if="activeTab === 'fast' && fastCode === '06'"
														type="button"
														class="btn btn-primary"
														title="移除我的最愛"
														@click="remove(item.proCode)"
													>
														移除最愛
													</button>
													<button
														v-else
														type="button"
														class="btn btn-dark btn-icon"
														data-bs-toggle="tooltip"
														title="加入我的最愛"
														@click="favoritesHandler(item.proCode, item.pfcatCode)"
													>
														<i class="bi bi-heart text-danger"></i>
													</button>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
					<div class="tx-note">
						<ol>
							<li><span>資料日期：</span></li>
							<li><span>商品是否可申購以交易系統為主</span></li>
						</ol>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import pagination from '@/views/components/pagination.vue';
import vuePerformancesCompareModal from './performancesCompareModal.vue';
import vueModal from '@/views/components/model.vue';
export default {
	components: {
		'vue-pagination': pagination,
		vuePerformancesCompareModal,
		vueModal
	},
	props: {
		bondModalHandler: Function,
		favoritesHandler: Function,
		curOption: Array,
		riskMenu: Array
	},
	data: function () {
		return {
			timeRangeMenu: [],
			rowNumerMenu: [],
			activeTab: 'common',
			bankProCode: null, // 商品代號
			proName: null, //商品名稱
			proTypeCode: '', //商品類型
			curObjs: [], // 計價幣別 陣列物件
			riskCodes: [], //風險等級
			freqUnit: '', //配息頻率
			mininvAmt: null, // 最低申購面額
			mininvAccAmt: null, //累加面額
			price: null, // 債券價格
			issuerItem: [], // 發行機構
			targetCusBu: null, // 銷售對象
			parRateMin: null, // 票面利率下限
			parRateMax: null, // 票面利率上限
			isinCode: null,
			issuerCode: '', // 發行機構
			guarCode: '', // 保證機構
			profInvestorYn: '', // 限PI銷售
			issuerType: '', // 債券分類－依發行機構
			invQuality: '', // 債券分類－依投資品質
			bondRateType: '', // 債券分類－依票面利率
			listingType: '', // 債券分類－依掛牌地
			payAllocation: '', // 債券分類－依受償順位
			expireYieldMin: self.expireYieldMin, // 到期殖利率下限
			expireYieldMax: self.expireYieldMax, // 到期殖利率上限
			periodMin: self.periodMin, // 剩餘年限(年)下限
			periodMax: self.periodMax, // 剩餘年限(年)上限

			proTypeMenu: [], // 商品類型選單
			intFreqUnitMenu: [], //配息頻率選單
			proIssuerMenu: [], // 發行機構選單
			proGuaranteesMenu: [], // 保證機構選單
			//			bondAccAmtMenu: [], //累加面額選單
			profInvestorMenu: [], // 限PI銷售選項
			issuerTypeMenu: [], // 債券分類－依發行機構選項
			invQualityMenu: [], // 債券分類－依投資品質選項
			bondRateTypeMenu: [], // 債券分類－依票面利率選項
			listingTypeMenu: [], // 債券分類－依掛牌地選項
			payAllocationMenu: [], // 債券分類－依受償順位選項
			bondFastMenu: [], //快速篩選條件
			perfMenu: [], // 標的績效選單,
			targetCusBuMenu: [], // 銷售對象選項
			fastCode: '03', //快速篩選
			timeRange: null, // 快速 顯示區間
			rowNumber: null, // 快速 顯示資料筆數
			curObjsFast: '', // 快速 幣別
			priceDiffStart: null, // 快速 價差起
			priceDiffEnd: null, // 快速 價差迄
			perf: 'PCTYTD', // 標的績效
			proCodes: [], //選擇商品做績效比較圖
			checkboxs: [], // 加入比較選項

			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: ['PRO_CODE'],
				direction: 'ASC'
			},
			isOpenIssuerModal: false,
			isOpenCompareModal: false
		};
	},
	watch: {
		activeTab(newVal, oldVal) {
			var self = this;
			self.fastCode = '03';
			self.fastChange(self.fastCode);
		},
		curObjs(newVal, oldVal) {
			var self = this;
			if (newVal[0] !== oldVal[0]) {
				if (newVal[0] === '') {
					$('#curMenuBond').selectpicker('selectAll');
				} else if (oldVal[0] === '' && newVal[0] !== '') {
					$('#curMenuBond').selectpicker('deselectAll');
				}
			}
		}
	},
	mounted: function () {
		var self = this;
		$('#curMenuBond').selectpicker('refresh');
		self.getProTypeMenu(); //// 商品類型選單
		self.getIntFreqUnitTypeMenu(); // 配息頻率選單
		self.getProIssuerMenu(), // 取得發行機構選單
			self.getProGuaranteesMenu(), // 取得保證機構選單
			self.getProfInvestorMenu(); // 限PI銷售選項
		//		self.getBondAccAmtMenu(); // 取得累加面額選項
		self.getTargetCusBuMenu(); // 取得銷售對象選項
		self.getIssuerTypeMenu(); // 取得債券分類－依發行機構選項
		self.getInvQualityMenu(); // 取得債券分類－依投資品質選項
		self.getBondRateTypeMenu(); // 取得債券分類－依票面利率選項
		self.getListingTypeMenu(); // 取得債券分類－依掛牌地選項
		self.getPayAllocationMenu(); // 取得債券分類－依受償順位選項
		self.getbondFastMenu(); // 取得快速篩選條件選項
		self.getTimeRangeMenu(); // 取得顯示區間選項
		self.getPerfMenu(); // 取得標的績效
		self.getRowNumerMenu(); // 取得顯示資料筆數選項
	},
	methods: {
		// 條件Tab切換
		changeTab: function (tabName) {
			var self = this;
			self.activeTab = tabName;
			self.pageData = {
				// 清空查詢資料
				content: []
			};
		},
		// 商品類型選單
		getProTypeMenu: async function () {
			const res = await this.$api.getProTypeListApi({
				pfcatCode: 'FB'
			});
			this.proTypeMenu = res.data;
		},
		// 取得配息頻率選項
		getIntFreqUnitTypeMenu: async function () {
			const res = await this.$api.getAdmCodeDetail({
				codeType: 'INT_FREQ_UNITTYPE'
			});
			this.intFreqUnitMenu = res.data;
		},
		// 取得累加面額選項
		//		getBondAccAmtMenu: function () {
		//			var self = this;
		//			this.$bi
		//				.ajax({
		//					url: self.config.apiPath + '/adm/codeDetail',
		//					method: 'GET',
		//					data: {
		//						codeType: 'BOND_ACC_AMT'
		//					}
		//				})
		//				.then(function (ret) {
		//					self.bondAccAmtMenu = ret.data;
		//				});
		//		},
		// 取得發行機構選單
		getProIssuerMenu: async function () {
			const res = await this.$api.getBondIssuersApi();
			this.proIssuerMenu = res.data;
		},
		// 取得保證機構選單
		getProGuaranteesMenu: async function () {
			const res = await this.$api.getBondGuaranteesApi();
			this.proGuaranteesMenu = res.data;
		},
		// 取得限PI銷售選項
		getProfInvestorMenu: async function () {
			this.profInvestorMenu = [{ codeValue: '', codeName: '不限' }];
			const res = await this.$api.getAdmCodeDetail({
				codeType: 'SELECT_YN'
			});
			Array.prototype.push.apply(this.profInvestorMenu, res.data);
		},
		//顯示發行機構 model
		groupIssuerModalHandler: function () {
			var self = this;
			this.$refs.groupIssuerModalRef.issuerPropItem(self.issuerItem);
			this.isOpenIssuerModal = true;
		},
		selectedIssuer(issuerItem) {
			// 顯示發行機構選擇項目
			var self = this;
			this.isOpenIssuerModal = false;
			self.issuerItem = issuerItem; //取得發行機構資料
		},
		deleteIssuerItem(issuerCode) {
			var self = this;
			_.remove(self.issuerItem, (item) => item.issuerCode === issuerCode); // 移除刪除項目
		},
		// 取得銷售對象
		async getTargetCusBuMenu() {
			const res = await this.$api.getAdmCodeDetail({
				codeType: 'CUS_BU'
			});
			this.targetCusBuMenu = res.data;
		},
		// 取得債券分類－依發行機構選項
		async getIssuerTypeMenu() {
			const res = await this.$api.getAdmCodeDetail({
				codeType: 'ISSUER_TYPE'
			});
			this.issuerTypeMenu = res.data;
		},
		// 取得債券分類－依投資品質選項
		async getInvQualityMenu() {
			const res = await this.$api.getAdmCodeDetail({
				codeType: 'INV_QUALITY'
			});
			this.invQualityMenu = res.data;
		},
		// 取得債券分類－依票面利率選項
		async getBondRateTypeMenu() {
			const res = await this.$api.getAdmCodeDetail({
				codeType: 'BOND_RATE_TYPE'
			});
			this.bondRateTypeMenu = res.data;
		},
		// 取得債券分類－依掛牌地選項
		async getListingTypeMenu() {
			const res = await this.$api.getAdmCodeDetail({
				codeType: 'LISTING_TYPE'
			});
			this.listingTypeMenu = res.data;
		},
		// 取得債券分類－依受償順位選項
		async getPayAllocationMenu() {
			const res = await this.$api.getAdmCodeDetail({
				codeType: 'PAY_ALLOCATION'
			});
			this.payAllocationMenu = res.data;
		},
		// 取得快速篩選條件選項
		async getbondFastMenu() {
			const res = await this.$api.getBondFastMenuApi();
			this.bondFastMenu = res.data;
		},
		// 取得顯示區間
		async getTimeRangeMenu() {
			const res = await this.$api.getTimeRangeMenuApi();
			this.timeRangeMenu = res.data;
		},
		// 取得標的績效
		async getPerfMenu() {
			const res = await this.$api.getPerfMenuApi();
			this.perfMenu = res.data;
		},
		// 取得顯示資料筆數
		async getRowNumerMenu() {
			const res = await this.$api.getRowNumerMenuApi();
			this.rowNumerMenu = res.data;
		},
		// 快速查詢切換
		fastChange(fastCode) {
			var self = this;
			self.timeRange = {}; // 快速 顯示區間
			self.rowNumber = ''; // 快速 顯示資料筆數
			self.perf = '';
			self.priceDiffStart = null;
			self.priceDiffEnd = null;
			self.pageData = {
				// 清空查詢資料
				content: []
			};
			if (fastCode === '05') {
				// 超人氣
				$('#proPerfTimeTrBond').hide();
				$('#curTrBond').hide();
				$('#priceDiffTrBond').hide();
				$('#rangeFixedTrBond').show();
				$('#maxRowIdTrBond').show();
				self.timeRange = self.timeRangeMenu[0];
			} else if (fastCode === '07') {
				// 績效排行
				$('#rangeFixedTrBond').hide();
				$('#curTrBond').hide();
				$('#priceDiffTrBond').hide();
				$('#maxRowIdTrBond').show();
				$('#proPerfTimeTrBond').show();
				self.perf = 'PCTYTD';
			} else if (fastCode === '08' || fastCode === '09' || fastCode === '12') {
				// 最高配息率商品
				$('#maxRowIdTrBond').show();
				$('#curTrBond').hide();
				$('#priceDiffTrBond').hide();
				$('#rangeFixedTrBond').hide();
				$('#proPerfTimeTrBond').hide();
			} else if (fastCode === '13') {
				// 買賣價差
				$('#curTrBond').show();
				$('#priceDiffTrBond').show();
				$('#maxRowIdTrBond').show();
				$('#rangeFixedTrBond').hide();
				$('#proPerfTimeTrBond').hide();
			} else {
				$('#maxRowIdTrBond').hide();
				$('#curTrBond').hide();
				$('#priceDiffTrBond').hide();
				$('#rangeFixedTrBond').hide();
				$('#proPerfTimeTrBond').hide();
			}
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		async getPageData(page) {
			const pageNum = _.isNumber(page) ? page : this.pageable.page;
			const params = {
				page: pageNum,
				size: this.pageable.size,
				sort: this.pageable.sort + ',' + this.pageable.direction,
				bankProCode: this.bankProCode,
				proName: this.proName,
				proType: this.proTypeCode,
				curCodes: this.curObjs,
				riskCodes: this.riskCodes,
				intFreqUnitType: this.freqUnit,
				buyMin: this.mininvAmt,
				invAccAmt: this.mininvAccAmt,
				price: this.price,
				issuerCodes: [this.issuerCode],
				buCode: this.targetCusBu,
				parRateMin: this.parRateMin,
				parRateMax: this.parRateMax,
				isinCode: this.isinCode,
				profInvestorYn: this.profInvestorYn,
				issuerType: this.issuerType,
				invQuality: this.invQuality,
				bondRateType: this.bondRateType,
				listingType: this.listingType,
				payAllocation: this.payAllocation,
				expireYieldMin: this.expireYieldMin,
				expireYieldMax: this.expireYieldMax,
				periodMin: this.periodMin,
				periodMax: this.periodMax,
				guarCodes: [this.guarCode]
			};

			const res = await this.$api.getBondProductsApi(params);
			this.pageData = res.data;
			this.pageData.content.forEach(() => {
				this.checkboxs.push(false);
			});
		},
		// 加入比較checkbox選項
		checkoutPro: function (proCode) {
			var self = this;
			if (!self.proCodes.includes(proCode)) {
				self.proCodes.push(proCode);
			} else {
				_.remove(self.proCodes, (code) => code === proCode);
			}
		},
		//執行績效比較圖
		performancesCompareModelHandler: function () {
			var self = this;
			if (self.proCodes.length > 0) {
				if (self.proCodes.length > 6) {
					this.$bi.alert('最多加入6筆');
				} else {
					this.$refs.performancesCompareModalRef.comparePropItem(self.proCodes, 'bond');
					this.isOpenCompareModal = true;
				}
			} else {
				this.$bi.alert('至少要勾選一項商品');
			}
		},

		gotoFastPage: function (page) {
			this.pageable.page = page;
			this.getFastPageData(page);
		},
		async getFastPageData(page) {
			if (this.fastCode === '13' && (!$.isNumeric(this.priceDiffStart) || !$.isNumeric(this.priceDiffEnd))) {
				this.$bi.alert('價差為必填');
				return;
			}

			// 设置排序
			if (this.fastCode === '03' || this.fastCode === '06' || this.fastCode === '09') {
				this.pageable.sort = 'BANK_PRO_CODE';
			} else if (this.fastCode === '08') {
				this.pageable.sort = 'INT_RATE';
			} else if (this.fastCode === '08') {
				this.pageable.sort = 'FC_RETURN';
			}

			const pageNum = _.isNumber(page) ? page : this.pageable.page;
			const params = {
				page: pageNum,
				size: this.pageable.size,
				sort: this.pageable.sort + ',' + this.pageable.direction,
				filterCodeValue: this.fastCode,
				timeRangeType: this.timeRange.rangeType,
				timeRangeFixed: this.timeRange.rangeFixed,
				rowNumberFixed: this.rowNumber,
				perfTimeCode: this.perf,
				curCode: this.curObjsFast,
				spreadMin: this.priceDiffStart,
				spreadMax: this.priceDiffEnd
			};

			const res = await this.$api.getBondFastProductsApi(params);
			this.pageData = res.data;
			this.pageData.content.forEach(() => {
				this.checkboxs.push(false);
			});
		},
		// 删除我的最爱
		async remove(proCode) {
			const res = await this.$api.deleteBondFavoriteApi({
				proCode: proCode
			});
			this.$bi.alert('刪除成功');
			this.checkboxs = [];
			this.proCodes = [];
			this.gotoFastPage(0);
		}
	} // methods end
};
</script>
