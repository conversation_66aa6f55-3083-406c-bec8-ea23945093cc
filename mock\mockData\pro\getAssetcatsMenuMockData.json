{"status": 200, "data": [{"assetcatCode": "BOND", "assetcatName": "債券及固定收益", "assetcatEname": "Debt Capital Market", "showOrder": 2, "createBy": "SYSBATCH", "createDt": "2024-09-23T00:00:00", "modifyBy": ""}, {"assetcatCode": "MONEY", "assetcatName": "現金及貨幣市場", "assetcatEname": "Money Market", "showOrder": 1, "createBy": "SYSBATCH", "createDt": "2024-09-23T00:00:00", "modifyBy": ""}, {"assetcatCode": "OTHER", "assetcatName": "其他型資產", "assetcatEname": "Other Assets", "showOrder": 4, "createBy": "SYSBATCH", "createDt": "2024-09-23T00:00:00", "modifyBy": ""}, {"assetcatCode": "STOCK", "assetcatName": "股票市場", "assetcatEname": "Equity Market", "showOrder": 3, "createBy": "SYSBATCH", "createDt": "2024-09-23T00:00:00", "modifyBy": ""}], "timestamp": "2025/06/30", "sqlTracer": [{"data": [{"assetcatCode": "BOND", "assetcatName": "債券及固定收益", "assetcatEname": "Debt Capital Market", "showOrder": 2, "createBy": "SYSBATCH", "createDt": "2024-09-23T00:00:00", "modifyBy": ""}, {"assetcatCode": "MONEY", "assetcatName": "現金及貨幣市場", "assetcatEname": "Money Market", "showOrder": 1, "createBy": "SYSBATCH", "createDt": "2024-09-23T00:00:00", "modifyBy": ""}, {"assetcatCode": "OTHER", "assetcatName": "其他型資產", "assetcatEname": "Other Assets", "showOrder": 4, "createBy": "SYSBATCH", "createDt": "2024-09-23T00:00:00", "modifyBy": ""}, {"assetcatCode": "STOCK", "assetcatName": "股票市場", "assetcatEname": "Equity Market", "showOrder": 3, "createBy": "SYSBATCH", "createDt": "2024-09-23T00:00:00", "modifyBy": ""}], "sqlInfo": "SELECT ASSETCAT_CODE, ASSETCAT_ENAME, ASSETCAT_NAME, CREATE_BY, CREATE_DT, MODIFY_BY, MODIFY_DT, SHOW_ORDER FROM PRO_ASSETCATS ,class com.bi.pbs.pro.model.ProAssetcats,[Ljava.lang.Object;@7383c4a5"}]}