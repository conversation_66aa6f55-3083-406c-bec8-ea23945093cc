<template>
	<div>
		<div role="tabpanel" class="tab-pane fade show active">
			<div class="card card-form-collapse">
				<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formsearch1">
					<h4>查詢條件</h4>
					<span class="tx-square-bracket">為必填欄位</span>
				</div>

				<div class="card-body collapse show" id="formsearch1">
					<form>
						<div class="form-row">
							<div class="form-group col-lg-6">
								<label class="form-label">選擇分行(單位)</label>
								<div class="input-group">
									<input type="text" id="branNameId" name="branName" class="form-control" readonly="true" v-model="branName" />
									<button class="btn btn-info" type="button" @click="openBranchModal">選擇分行(單位)</button>
									<button class="btn btn-white" type="button" @click="clearForm()">清空</button>
								</div>
							</div>
							<div class="form-group col-lg-6">
								<label class="form-label">使用者姓名</label>
								<input name="userName" type="text" class="form-control" size="20" v-model="userName" />
							</div>
						</div>
						<div class="form-row">
							<div class="form-group col-lg-6">
								<label class="form-label">使用者代碼</label>
								<input name="userCode" @blur="handleUserCodeBlur" type="text" class="form-control" size="20" v-model="userCode" />
							</div>
							<div class="form-group col-lg-6">
								<label class="form-label">系統角色</label>
								<select name="roleCode" class="form-select" v-model="roleCode">
									<option value="">全部</option>
									<option v-for="roleData in roleMenu" :value="roleData.roleCode">{{ roleData.roleName }}</option>
								</select>
							</div>
						</div>
						<div class="form-footer">
							<button class="btn btn-primary" type="button" @click="getPageData(0)">查詢</button>
							<button class="btn btn-primary" type="button" @click="exportExcel()">下載Excel</button>
						</div>
					</form>
				</div>
			</div>

			<div id="searchResult">
				<div class="card card-table">
					<div class="card-header">
						<h4>查詢結果</h4>
						<pagination :pageable="pageData" :goto-page="gotoPage"></pagination>
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-hover table-bordered">
							<thead>
								<tr>
									<th>分行(單位)名稱</th>
									<th>使用者</th>
									<th>職位及職稱</th>
									<th>業務類別</th>
									<th>提交日期</th>
									<th>提交人員</th>
									<th>審核日期</th>
									<th>審核人員</th>
									<th>審核狀態</th>
									<th>角色設定</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="item in pageData.content" :key="item.id">
									<td data-th="分行(單位)名稱">{{ item.branCode }} {{ item.branName }}</td>
									<td data-th="使用者">{{ item.userCode }} {{ item.userName }}</td>
									<td data-th="職位及職稱">{{ item.titleName }}</td>
									<td data-th="業務類別">{{ item.jobName }}</td>
									<td data-th="提交日期">{{ item.createDt }}</td>
									<td data-th="提交人員">{{ item.createBy }} {{ item.createUserName }}</td>
									<td data-th="審核日期">{{ item.modifyDt }}</td>
									<td data-th="審核人員">{{ item.modifyBy }} {{ item.modifyUserName }}</td>

									<td v-if="item.status == 'P'">待覆核</td>
									<td v-if="item.status == 'R'">
										<a
											href="#"
											class="tx-link tx-danger"
											data-bs-toggle="modal"
											data-bs-target="#alertModal"
											@click.prevent="showRejectContent(item.reason)"
											>退回修改</a
										>
									</td>
									<td v-if="item.status == 'A'">覆核同意</td>
									<td v-if="item.status == null"></td>

									<td data-th="角色設定" class="text-center">
										<button
											v-if="item.status == 'P'"
											type="button"
											class="btn btn-search btn-icon"
											@click="doViewPosEvents(item.eventId)"
										>
											<i class="bi bi-search"></i>
										</button>
										<a data-bs-toggle="tooltip" href="#" class="table-icon">
											<button
												v-if="item.status != 'P'"
												type="button"
												data-bs-toggle="tooltip"
												title="編輯"
												class="btn btn-info btn-icon"
												@click.prevent="setEditAccountToParent(item)"
											>
												<i class="fa-solid fa-pen"></i>
											</button>
										</a>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<p class="tx-note">職稱、業務類別資料顯示人資系統中維護的資訊</p>
			</div>
		</div>
		<!--Tab內容 end-->

		<!--branchModal-->
		<modal ref="branchModal" :before-close="closeBranchModal">
			<template v-slot:content="props">
				<div class="modal-dialog modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title" id="branchModal">選擇分行(單位)</h4>
							<button type="button" class="btn-close" @click="props.close()" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<table class="table table-bordered">
								<tbody>
									<tr>
										<td>
											<button type="button" class="btn btn-info btn-glow" @click="expandBtn">全部展開</button>
											<button type="button" class="btn btn-info btn-glow" @click="collapsedBtn">全部收合</button>
										</td>
									</tr>
									<tr>
										<td>
											<bi-tree v-if="treeData.data" ref="tree" :treeData="treeData"></bi-tree>
											<div>
												<div id="treeview-noborder"></div>
											</div>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
						<div class="modal-footer">
							<button @click="props.close()" type="button" class="btn btn-white">關閉視窗</button>
						</div>
					</div>
				</div>
			</template>
		</modal>

		<!-- Detail Modal -->
		<modal ref="detailModal" :before-close="closeDetailModal">
			<template v-slot:content="props">
				<div class="modal-dialog modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title" id="view">申請明細</h4>
							<button type="button" class="btn-close" @click="props.close()" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<table class="table table-bordered align-middle">
								<caption>
									角色設定預覽
								</caption>
								<tr>
									<th class="th-info">角色</th>
									<th class="th-info text-center">帳號可用性</th>
								</tr>
								<tr>
									<td>
										<ol class="mb-0">
											<li v-for="eventDetail in eventDetails">{{ eventDetail.branName }} > {{ eventDetail.roleName }}</li>
										</ol>
									</td>
									<td class="text-center">
										<span class="icon tx-success" v-if="accountAvailability == 'Y'">
											<!--										<i class="bi bi-check-circle"></i>-->
											狀態:啟用
										</span>
										<span class="icon tx-danger" v-if="accountAvailability == 'N'">
											<!--										<i class="bi bi-check-circle"></i>-->
											狀態:停用
										</span>
									</td>
								</tr>
							</table>
						</div>
						<div class="modal-footer">
							<button @click="props.close()" type="button" class="btn btn-white">關閉視窗</button>
						</div>
					</div>
				</div>
			</template>
		</modal>

		<!--reject Modal-->
		<modal ref="rejectModal" :before-close="closeAlertModal">
			<template v-slot:content="props">
				<div class="modal-dialog modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-body pt-5">
							<div class="icon-alert"></div>
							<input
								type="text"
								id="recipient-name"
								disabled
								class="form-control form-control-plaintext tx-20 tx-danger text-center"
								v-model="rejectMsg"
							/>
						</div>
						<div class="modal-footer">
							<button class="btn btn-primary btn-glow" @click.prevent="props.close()">確認</button>
						</div>
					</div>
				</div>
			</template>
		</modal>
	</div>
</template>

<script>
import pagination from '@/views/components/pagination.vue';
import modal from '@/views/components/model.vue';
import biTree from '@/views/components/biTree.vue';
import _ from 'lodash';

import { Tooltip } from 'bootstrap';

export default {
	components: {
		pagination,
		modal,
		biTree
	},
	// mixins: [userCodeComplement],
	props: {
		setEditAccount: Function
	},
	data: function () {
		return {
			//Api 用參數
			branCode: null,
			branName: null,
			roleCode: null,
			userCode: null,
			userName: null,

			editAccount: {},
			eventDetails: [],
			accountAvailability: 'N',
			//下拉選單
			branMenu: [],
			roleMenu: [],

			//主要顯示資料
			pageData: {},
			pageable: {
				page: 0,
				size: 20,
				sort: 'BRAN_CODE',
				direction: 'DESC'
			},
			rejectMsg: null,

			treeData: {}
		};
	},
	mounted: function () {
		this.getBranMenu();
		this.getRoleMenu();
	},
	methods: {
		openBranchModal() {
			this.$refs.branchModal.open();
		},
		closeBranchModal() {
			this.$refs.branchModal.close();
		},
		openDetailModal() {
			this.$refs.detailModal.open();
		},
		closeDetailModal() {
			this.$refs.detailModal.close();
		},
		getBranMenu: async function () {
			let ret = await this.$api.getBranMenuApi();
			this.branMenu = ret.data;
			this.branMenu = this.genTreeLabel(this.branMenu);

			this.treeData = {
				data: this.branMenu,
				tailLinks: [
					{
						type: 'anchor',
						onClick: (id, node) => {
							const name = node.querySelector('.bi-tree-text')?.textContent || '';
							const [buCode, branCode] = id.split('_');
							this.buCode = buCode;
							this.branCode = branCode;
							this.branName = name;
							this.closeBranchModal();
						},
						text: '選擇',
						toApply: true
					}
				]
			};
		},
		genTreeLabel: function (data) {
			data.forEach((item) => {
				item.text = item.branName;
				if (!this.$_.isEmpty(item.nodes)) {
					this.genTreeLabel(item.nodes);
				}
			});

			return data;
		},
		getRoleMenu: async function () {
			let ret = await this.$api.getRoleMenuApi();
			this.roleMenu = ret.data;
		},
		expandBtn: function () {
			this.$refs.tree.expandAllNodes(true);
		},
		collapsedBtn: function () {
			this.$refs.tree.expandAllNodes(false);
		},
		handleUserCodeBlur: function () {
			this.userCode = this.complementUserCode(this.userCode);
		},
		clearForm: function () {
			this.branCode = null;
			this.branName = null;
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (page) {
			this.pageable.page = _.isNumber(page) ? page : this.pageable.page;
			let ret = await this.$api.getSysUserApi(this.branCode, this.roleCode, this.userCode, this.userName, this.pageable);
			this.pageData = ret.data;

			// 確保在 Vue 完成渲染後再初始化 tooltip
			this.$nextTick(() => {
				const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
				tooltipTriggerList.forEach((el) => {
					new Tooltip(el);
				});
			});
		},
		exportExcel: async function () {
			const dateStr = this.$moment().format('YYYYMMDD');
			const fileName = '系統使用者查詢_' + dateStr + '.xlsx'; // 加上副檔名

			let response = await this.$api.getExportExcelApi(this.branCode, this.roleCode, this.userCode, this.userName);

			const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
			const link = document.createElement('a');
			const downloadUrl = URL.createObjectURL(blob);
			link.href = downloadUrl;
			link.download = fileName;
			document.body.appendChild(link);
			link.click();
			link.remove();
			setTimeout(() => URL.revokeObjectURL(downloadUrl), 500);
		},
		showRejectContent: function (reason) {
			this.rejectMsg = reason;
			this.$refs.rejectModal.open();
		},
		closeAlertModal: function () {
			this.$refs.rejectModal.close();
		},
		setEditAccountToParent: function (item) {
			this.editAccount.tabCode = 'M00-041';
			this.editAccount.userCode = item.userCode;
			this.setEditAccount(this.editAccount);
		},
		doViewPosEvents: async function (eventId) {
			let ret = this.$api.getUserPosEventApi(eventId);
			if (ret.data) {
				this.eventDetails = ret.data;
				this.accountAvailability = ret.data[0].startFlag;
				this.openDetailModal();
			} else {
				this.eventDetails = [];
				this.accountAvailability = 'N';
			}
		}
	}
};
</script>
