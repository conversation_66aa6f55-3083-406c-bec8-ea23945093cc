<template>
	<!-- Modal 3 結構型商品-->
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">結構型商品</h4>
				<button type="button" class="btn-close" @click.prevent="close()" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="card card-prointro shadow-none mb-3">
					<div class="pro_left">
						<div class="pro_name">
							<div class="avatar avatar-md avatar-oversea"></div>
							<h4>
								<span>商品名稱</span>
								<br />{{ $filters.defaultValue(proInfo.proName, '--') }} <br /><span class="tx-black">{{
									$filters.defaultValue(proInfo.proEName, '--')
								}}</span>
							</h4>
						</div>
						<h4 class="pro_value">
							<span>淨值日期與參考淨值</span>
							<br />{{ $filters.formatNumber(proInfo.aprice, '0,0.00' || '--') }} <br /><span>{{
								$filters.formatDate(proInfo.priceDt, '--')
							}}</span>
						</h4>
					</div>
					<div class="pro_class">
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品代碼</span> <br />{{ $filters.defaultValue(proInfo.bankProCode, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>資產類別</span> <br />{{ $filters.defaultValue(proInfo.assetcatName, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品主類</span> <br />{{ $filters.defaultValue(proInfo.pfcatName, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品次類</span> <br />{{ $filters.defaultValue(proInfo.proTypeName, '--') }}</h6>
						</div>
					</div>
				</div>
				<div class="tab-nav-pills">
					<ul class="nav nav-pills justify-content-center" role="tablist">
						<li class="nav-item"><a class="nav-link active" href="#Sectionover1" data-bs-toggle="pill">商品基本資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionover2" data-bs-toggle="pill">商品共同資料</a></li>
						<li class="nav-item"><a class="nav-link" href="#Sectionover3" data-bs-toggle="pill">商品附加資料</a></li>
						<li class="nav-item"><a class="nav-link datainfo" href="#Sectionover4" data-bs-toggle="pill">淨值分析</a></li>
						<li class="nav-item"><a class="nav-link datainfo" href="#Sectionover5" data-bs-toggle="pill">績效表現</a></li>
					</ul>

					<div class="tab-content">
						<div class="tab-pane show active" id="Sectionover1">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>商品資訊</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>風險等級</th>
											<td class="wd-30p">{{ $filters.defaultValue(proInfo.spInfo.riskName, '--') }}</td>
											<th>計價幣別</th>
											<td class="wd-30p">{{ $filters.defaultValue(proInfo.spInfo.curCode, '--') }}</td>
										</tr>
										<tr>
											<th>商品種類</th>
											<td class="wd-30p">{{ $filters.defaultValue(proInfo.spInfo.proTypeName, '--') }}</td>
											<th>發行日期</th>
											<td class="wd-30p">{{ $filters.defaultValue(proInfo.spInfo.issueDt, '--') }}</td>
										</tr>
										<tr>
											<th>到期日</th>
											<td class="wd-30p">{{ $filters.defaultValue($filters.formatDate(proInfo.spInfo.expireDt), '--') }}</td>
											<th>通路報酬</th>
											<td class="wd-30p">{{ $filters.defaultValue(proInfo.spInfo.channelServiceRate, '--') }}%</td>
										</tr>
										<tr>
											<th>保本與否</th>
											<td class="wd-30p">{{ proInfo.principalGuarYn === 'Y' ? '100%' : '不保本' }}</td>
										</tr>
										<tr>
											<th>發行機構名稱</th>
											<td class="wd-30p">{{ $filters.defaultValue(proInfo.spInfo.issueName, '--') }}</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>連結標的</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<thead>
										<tr>
											<th>連結標的</th>
											<th>期初進場價</th>
											<th>轉換價</th>
											<th>下限價</th>
											<th>提前出場價</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="item in proInfo.spInfo.targetList">
											<td class="wd-20p">{{ $filters.defaultValue(item.isinCode, '--') }}</td>
											<td class="wd-20p">{{ item.entryPrice }}</td>
											<td class="wd-20p">{{ item.convPrice }}</td>
											<td class="wd-20p">{{ item.kiPrice }}</td>
											<td class="wd-20p">{{ item.koPrice }}</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>投資金額限制</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>台幣最低投資金額</th>
											<td class="wd-30p">
												{{
													$filters.defaultValue(
														proInfo.spInfo?.mininvLcAmt ? $filters.formatNumber(proInfo.spInfo.mininvLcAmt) : '--',
														'--'
													)
												}}
											</td>
											<th>台幣累加面額</th>
											<td class="wd-30p">
												{{
													$filters.defaultValue(
														proInfo.spInfo?.mininvLcAccAmt ? $filters.formatNumber(proInfo.spInfo.mininvLcAccAmt) : '--',
														'--'
													)
												}}
											</td>
										</tr>
										<tr>
											<th>外幣最低投資金額</th>
											<td>
												{{
													$filters.defaultValue(
														proInfo.spInfo?.mininvFcAmt ? $filters.formatNumber(proInfo.spInfo.mininvFcAmt) : '--',
														'--'
													)
												}}
											</td>
											<th>外幣累加面額</th>
											<td>
												{{
													$filters.defaultValue(
														proInfo.spInfo?.mininvFcAccAmt ? $filters.formatNumber(proInfo.spInfo.mininvFcAccAmt) : '--',
														'--'
													)
												}}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionover2">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>銷售相關資料</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>銷售地區</th>
											<td class="wd-30p" v-if="proInfo.allYn == 'Y'">全行</td>
											<td class="wd-30p" v-else>--</td>
											<th><span>銷售對象</span></th>
											<td class="wd-30p">{{ $filters.defaultValue(proInfo.targetCusBu, '--') }}</td>
										</tr>
										<tr>
											<th>是否開放申購</th>
											<td>{{ $filters.defaultValue(proInfo.buyYn, '--') }}</td>
											<th>是否開放贖回</th>
											<td>{{ $filters.defaultValue(proInfo.sellYn, '--') }}</td>
										</tr>
										<tr>
											<th><span>波動類型</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.volatilityType, '--') }}</span>
											</td>
											<th><span>配息頻率</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.intFreqUnitype, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>理財需求</span></th>
											<td colspan="3">
												<div class="form-check form-check-inline" v-for="item in finReqCodeMenu">
													<input
														class="form-check-input"
														name="finReqCodes"
														disabled
														id="c1"
														v-model="finReqCodes"
														:value="item.codeValue"
														type="checkbox"
													/>
													<label class="form-check-label" for="c1">{{ $filters.defaultValue(item.codeName, '--') }}</label>
												</div>
											</td>
										</tr>
										<tr>
											<th>商品標籤</th>
											<td disabled="disabled" colspan="3">
												{{ $filters.defaultValue(proInfo.selprocatNames, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionover3">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>其他設定</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>商品投資標的</span></th>
											<td class="wd-80p">
												<span>{{ $filters.defaultValue(proInfo.sectorName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>商品投資地區</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.geoFocusName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>比較基準設定</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.benchmarkName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>備註</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.memo, '--') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>商品說明書</th>
											<td class="wd-80p">
												<a v-if="proFileA && proFileA.url" :href="proFileA.url" target="_blank">{{
													$filters.defaultValue(proFileA.url, '--')
												}}</a
												><br v-if="proFileA && proFileA.url" />
												<a v-if="proFileA" class="tx-link" href="#" @click="downloadFile(proFileA)">{{
													$filters.defaultValue(proFileA.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>投資人須知</th>
											<td class="wd-80p">
												<a v-if="proFileD && proFileD.url" :href="proFileD.url" target="_blank">{{
													$filters.defaultValue(proFileD.url, '--')
												}}</a
												><br v-if="proFileD && proFileD.url" />
												<a v-if="proFileD" class="tx-link" href="#" @click="downloadFile(proFileD)">{{
													$filters.defaultValue(proFileD.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>DM</th>
											<td class="wd-80p">
												<a v-if="proFileF && proFileF.url" :href="proFileF.url" target="_blank">{{
													$filters.defaultValue(proFileF.url, '--')
												}}</a
												><br v-if="proFileF && proFileF.url" />
												<a v-if="proFileF" class="tx-link" href="#" @click="downloadFile(proFileF)">{{
													$filters.defaultValue(proFileF.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>其他</th>
											<td class="wd-80p">
												<a v-if="proFileG && proFileG.url" :href="proFileG.url" target="_blank">{{
													$filters.defaultValue(proFileG.url, '--')
												}}</a
												><br v-if="proFileG && proFileG.url" />
												<a v-if="proFileG" class="tx-link" href="#" @click="downloadFile(proFileG)">{{
													$filters.defaultValue(proFileG.showName, '--')
												}}</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>其他相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tr>
										<td>
											<span v-for="(item, index) in otherFileList">
												<a
													v-if="index === otherFileList.length - 1"
													v-show="item.show"
													href="#"
													class="tx-link"
													@click="downloadOtherFile(item.docFileId)"
													>{{ $filters.defaultValue(item.showName, '--') }}</a
												>
												<a v-else href="#" class="tx-link" v-show="item.show" @click="downloadOtherFile(item.docFileId)"
													>{{ $filters.defaultValue(item.showName, '--') }}、</a
												>
											</span>
										</td>
									</tr>
								</table>
							</div>
							<div class="tx-note">其他相關附件為文件管理內所上傳與本商品相關的文件。</div>
						</div>

						<div class="tab-pane fade" id="Sectionover4">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>淨值分析</h4>
								</div>
								<table class="table table-RWD table-bordered text-end">
									<thead>
										<tr>
											<th class="wd-10p text-start">項目</th>
											<th class="wd-30p">淨值</th>
											<th class="wd-30p">最高淨值(年)</th>
											<th class="wd-30p">最低淨值(年)</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td class="text-start" data-th="項目">淨值</td>
											<td class="text-end" data-th="價格">
												<span
													>{{ $filters.defaultValue(spPriceAna.aprice, '--') }}({{
														$filters.defaultValue(spPriceAna.priceDt, '--')
													}})</span
												>
											</td>
											<td class="text-end" data-th="最高價格(年)">
												<span
													>{{ $filters.defaultValue(spPriceAna.maxAprice, '--') }}({{
														$filters.defaultValue(spPriceAna.maxPriceDt, '--')
													}})</span
												>
											</td>
											<td class="text-end" data-th="最低價格(年)">
												<span
													>{{ $filters.defaultValue(spPriceAna.minAprice, '--') }}({{
														$filters.defaultValue(spPriceAna.minPriceDt, '--')
													}})</span
												>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="text-center">
									<div class="col-12">
										<div class="card-header">
											<h4>歷史價格走勢圖</h4>
										</div>
										<br />
										<vue-net-chart
											ref="spNetChartRef"
											:chart-id="chartId"
											:pro-code="proInfo.proCode"
											:pro-price-range-menu="proPriceRangeMenu"
										></vue-net-chart>
										<div class="btn-group btn-group-sm mb-4" role="group">
											<template v-for="item in proPriceRangeMenu">
												<input
													type="radio"
													class="btn-check"
													name="time"
													:id="'spNetPeriod' + item.termValue"
													:checked="item.termValue == '4'"
													@click="getSpNets(proInfo.proCode, item.rangeType, item.rangeFixed)"
												/>
												<label class="btn btn-outline-secondary" :for="'spNetPeriod' + item.termValue">{{
													$filters.defaultValue(item.termName, '--')
												}}</label>
											</template>
										</div>
									</div>
								</div>

								<div class="caption">近30日淨值</div>
								<table class="table table-RWD table-bordered text-center">
									<thead>
										<tr>
											<th>日期</th>
											<th class="text-end">參考淨值</th>
											<th>日期</th>
											<th class="text-end">參考淨值</th>
											<th>日期</th>
											<th class="text-end">參考淨值</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="(item, index) in spPriceHist">
											<td data-th="日期">{{ $filters.defaultValue(item.priceDt1, '--') }}</td>
											<td class="text-end" data-th="參考淨值">
												<span>{{ $filters.defaultValue(item.aprice1, '--') }}</span>
											</td>
											<td data-th="日期">{{ $filters.defaultValue(item.priceDt2, '--') }}</td>
											<td class="text-end" data-th="參考淨值">
												<span>{{ $filters.defaultValue(item.aprice2, '--') }}</span>
											</td>
											<td data-th="日期">{{ $filters.defaultValue(item.priceDt3, '--') }}</td>
											<td class="text-end" data-th="參考淨值">
												<span>{{ $filters.defaultValue(item.aprice3, '--') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Sectionover5">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>績效分析</h4>
								</div>
								<div class="card-body">
									<div class="row">
										<div class="col-sm-2">
											<label class="tc-blue">選擇商品：</label>
										</div>
										<div class="col-sm-8">
											<div class="input-group">
												<select class="form-select" v-model="proCode">
													<option selected value>--</option>
													<option v-for="item in productList" :value="item.proCode">
														{{ $filters.defaultValue(item.proName, '--') }}
														{{ $filters.defaultValue(item.proCode, '--') }}
													</option>
												</select>
											</div>
										</div>
										<div class="col-sm-2">
											<p><input class="btn btn-primary text-alignRight" type="button" value="加入" @click="addPro()" /></p>
										</div>
									</div>

									<div class="caption">已加入商品</div>
									<div class="table-responsive mb-3">
										<table class="table table-bordered">
											<thead>
												<tr>
													<th>商品名稱</th>
													<th class="text-end">一年報酬率</th>
													<th class="text-center">動作</th>
												</tr>
											</thead>
											<tbody>
												<tr v-for="item in observedProList">
													<td>
														{{ $filters.defaultValue(item.proName, '--') }}{{ $filters.defaultValue(item.proCode, '--') }}
													</td>
													<td class="text-end">{{ $filters.defaultValue($filters.formatPct(item.fcTdReturn), '--') }}%</td>
													<td class="text-center">
														<button
															type="button"
															class="btn btn-danger btn-icon"
															data-bs-toggle="tooltip"
															title="刪除"
															@click="deletePro(item.proCode)"
														>
															<i class="fa-solid fa-trash"></i>
														</button>
													</td>
												</tr>
											</tbody>
										</table>
									</div>

									<div class="text-center">
										<!-- 績效分析圖表  -->
										<vue-performances-chart ref="spPerformancesChartRef" :chart-id="performancesId"></vue-performances-chart>
										<div class="btn-group btn-group-sm mb-4" role="group">
											<template v-for="item in proPriceRangeMenu">
												<input
													type="radio"
													class="btn-check"
													name="time"
													:id="'performancesPeriod' + item.termValue"
													:checked="item.termValue == '4' ? true : false"
												/>
												<label
													class="btn btn-outline-secondary"
													:for="'performancesPeriod' + item.termValue"
													@click.prevent="getSpPerformances(proCodes, item.rangeType, item.rangeFixed)"
													>{{ $filters.defaultValue(item.termName, '--') }}</label
												>
											</template>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button @click.prevent="close()" type="button" class="btn btn-white">關閉視窗</button>
			</div>
		</div>
	</div>
	<!-- Modal 3 End -->
</template>
<script>
import _ from 'lodash';
import moment from 'moment';
import vueModal from '@/views/components/model.vue';
import vuePerformancesChart from './performancesChart.vue';
import vueNetChart from './netChart.vue';
export default {
	components: {
		vueModal,
		vuePerformancesChart,
		vueNetChart
	},
	props: {
		downloadFile: Function,
		downloadOtherFile: Function,
		finReqCodeMenu: Array,
		proPriceRangeMenu: Array,
		close: Function
	},
	data: function () {
		const now = moment();
		return {
			proInfo: {
				fundInfo: {},
				etfInfo: {},
				bondInfo: {},
				spInfo: {},
				insInfo: {},
				dciInfo: {},
				secInfo: {}
			},
			chartsData: [], // 績效分析  績效所得圖表資料
			proCodes: [], // 績效分析 已加入商品
			productList: [], // 績效分析 加入商品清單
			observedProList: [], // 績效分析 已加入商品顯示報酬率清單
			proCode: null, // 績效分析 選擇商品

			finReqCodes: [],
			proFileA: {},
			proFileD: {},
			proFileF: {},
			proFileG: {},
			otherFileList: [],
			spPriceAna: {},
			spPriceHist: [],
			chartId: 'spNetChartId',
			performancesId: 'spPerformancesChartId'
		};
	},
	watch: {},
	created() {},
	mounted: function () {},
	methods: {
		getProInfo: async function (bankProCode, pfcatCode) {
			const self = this;
			self.proCodes = [];
			self.proCodes.push(bankProCode); // 預設加入的商品

			const ret = await this.$api.getProductInfoApi({
				proCode: bankProCode,
				pfcatCode: pfcatCode
			});

			if (_.isNil(ret.data)) {
				ret.data = {};
				this.$bi.alert('資料不存在');
				return;
			}
			if (_.isNil(ret.data.spInfo)) {
				ret.data.spInfo = {};
			}

			self.proInfo = ret.data;

			// 商品共用資料
			if (!_.isNil(self.proInfo.spInfo)) {
				self.proInfo.spInfo.principalGuarYn = self.proInfo.spInfo.principalGuarYn === 'Y' ? '100%' : '不保本';
			}

			if (!_.isUndefined(self.proInfo.targetCusBu)) {
				const targetCusBuList = await this.$api.getAdmCodeDetail({
					codeType: 'CUS_BU'
				});
				const targetCusBuObjs = _.filter(targetCusBuList.data, {
					codeValue: self.proInfo.targetCusBu
				});
				self.proInfo.targetCusBu = targetCusBuObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.buyYn) || !_.isUndefined(self.proInfo.sellYn)) {
				const selectYnList = await this.$api.getAdmCodeDetail({
					codeType: 'SELECT_YN'
				});

				if (!_.isUndefined(self.proInfo.buyYn)) {
					const buyYnObjs = _.filter(selectYnList.data, {
						codeValue: self.proInfo.buyYn
					});
					self.proInfo.buyYn = buyYnObjs[0].codeName;
				}

				if (!_.isUndefined(self.proInfo.sellYn)) {
					const sellYnObjs = _.filter(selectYnList.data, {
						codeValue: self.proInfo.sellYn
					});
					self.proInfo.sellYn = sellYnObjs[0].codeName;
				}
			}

			if (!_.isUndefined(self.proInfo.volatilityType)) {
				const volatilityTypeList = await this.$api.getAdmCodeDetail({
					codeType: 'VOLATILITY_TYPE'
				});
				const volatilityTypeObjs = _.filter(volatilityTypeList.data, {
					codeValue: self.proInfo.volatilityType
				});
				self.proInfo.volatilityType = volatilityTypeObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.intFreqUnitype)) {
				const intFreqUnitypeList = await this.$api.getAdmCodeDetail({
					codeType: 'INT_FREQ_UNITTYPE'
				});
				const intFreqUnitypeObjs = _.filter(intFreqUnitypeList.data, {
					codeValue: self.proInfo.intFreqUnitype
				});
				self.proInfo.intFreqUnitype = intFreqUnitypeObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.selprocatNames)) {
				self.proInfo.selprocatNames = self.proInfo.selprocatNames.replaceAll(',', '、');
			}

			if (!_.isUndefined(self.proInfo.finReqCode)) {
				self.finReqCodes = self.proInfo.finReqCode.split(',');
			}

			// 商品附加資料
			const ret2 = await this.$api.getProductsCommInfo({
				proCode: bankProCode,
				pfcatCode: pfcatCode
			});
			if (!_.isNil(ret2.data)) {
				if (ret2.data.proDocs) {
					self.otherFileList = ret2.data.proDocs; // 其他相關附件
					self.otherFileList.forEach(function (item) {
						// 其他相關附件 檔案顯示時間範圍
						item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
					});
				}

				var proFileList = ret2.data.proFiles;
				if (!_.isNil(proFileList)) {
					self.proFileA = proFileList.filter((proFile) => proFile.fileType === 'A')[0];
					self.proFileD = proFileList.filter((proFile) => proFile.fileType === 'D')[0];
					self.proFileF = proFileList.filter((proFile) => proFile.fileType === 'F')[0];
					self.proFileG = proFileList.filter((proFile) => proFile.fileType === 'G')[0];
				}
			}
			this.$forceUpdate();
			self.observedPro(); // 績效分析 取得 已加入商品清單
			self.productMenu();
		},
		// 商品資訊/價格分析及近30日價格
		async getSpPriceAna(proCode) {
			const proCodeArray = [proCode];
			const ret = await this.$api.getPriceAnaApi({
				proCodes: proCodeArray
			});

			if (!_.isNil(ret.data)) {
				this.spPriceAna = ret.data;
				if (!_.isNil(ret.data.priceHist)) {
					const orgPriceHis = ret.data.priceHist;
					const newPriceHis = [];
					orgPriceHis.forEach(function (item, index) {
						if (index % 3 == 0) {
							if (index + 2 < orgPriceHis.length) {
								const pricHisObj = {
									priceDt1: orgPriceHis[index].priceDt,
									aprice1: Number.parseFloat(orgPriceHis[index].aprice),
									priceDt2: orgPriceHis[index + 1].priceDt,
									aprice2: Number.parseFloat(orgPriceHis[index + 1].aprice),
									priceDt3: orgPriceHis[index + 2].priceDt,
									aprice3: Number.parseFloat(orgPriceHis[index + 2].aprice)
								};
								newPriceHis.push(pricHisObj);
							} else if (index + 1 < orgPriceHis.length) {
								const pricHisObj = {
									priceDt1: orgPriceHis[index].priceDt,
									aprice1: Number.parseFloat(orgPriceHis[index].aprice),
									priceDt2: orgPriceHis[index + 1].priceDt,
									aprice2: Number.parseFloat(orgPriceHis[index + 1].aprice),
									priceDt3: null,
									aprice3: null
								};
								newPriceHis.push(pricHisObj);
							} else {
								const pricHisObj = {
									priceDt1: orgPriceHis[index].priceDt,
									aprice1: Number.parseFloat(orgPriceHis[index].aprice),
									priceDt2: null,
									aprice2: null,
									priceDt3: null,
									aprice3: null
								};
								newPriceHis.push(pricHisObj);
							}
						}
					});
					this.spPriceHist = newPriceHis;
				}
			}
		},
		getSpNets: function (proCode, rangeType, rangeFixed) {
			var self = this;
			self.$refs.spNetChartRef.getNets(proCode, rangeType, rangeFixed);
		},
		//績效分析 圖表
		async getSpPerformances(proCodes, rangeType, rangeFixed) {
			const ret = await this.$api.getPerformanceRunChartApi({
				proCodes,
				freqType: rangeType,
				freqFixed: rangeFixed
			});

			if (!_.isEmpty(ret.data.datas)) {
				for (let i = 0; i < ret.data.length; i++) {
					ret.data[i].datas.forEach((e) => {
						e.value = e.returnFc;
						e.date = new Date(e.returnDt).getTime();
					});
				}
				this.chartsData = ret.data;
				this.$refs.spPerformancesChartRef.initChart(this.chartsData);
			}
		},
		// 績效分析 已加入商品清單
		async observedPro() {
			const ret = await this.$api.getObservedProductsApi({
				proCodes: this.proCodes
			});
			this.observedProList = ret.data;
		},
		// 績效分析 選擇商品下拉
		async productMenu() {
			const ret = await this.$api.getProductByPfcatCodeApi({
				pfcatCode: 'sp'
			});
			this.productList = ret.data;
		},
		// 績效分析 選擇商品 加入按鈕
		addPro() {
			var self = this;
			let pk = null;
			pk = _.find(self.proCodes, function (item) {
				return item == self.proCode;
			});
			if (self.proCode != null && pk == null) {
				self.proCodes.push(self.proCode); // 加入選擇商品
				self.observedPro(); // 績效分析 取得 已加入商品清單
				self.getSpPerformances(self.proCodes, 'Y', -1.0); // 商品資訊/績效分析圖表
			} else if (self.proCode != null && pk != null) {
				thi.$bi.alert('此商品已加入');
			} else {
				thi.$bi.alert('請選擇商品');
			}
		},
		// 績效分析 已加入商品 刪除按鈕
		deletePro(proCode) {
			var self = this;
			if (self.proCodes.length > 1) {
				let index = self.proCodes.indexOf(proCode); // 找出要移除的index
				self.proCodes.splice(index, 1); // 移除加入的商品(要插入或刪除的索引位置, 要刪除的元素數量)
				self.observedPro(); // 績效分析 取得 已加入商品清單
				self.getSpPerformances(self.proCodes, 'Y', -1.0); // 商品資訊/績效分析圖表
			} else {
				thi.$bi.alert('至少要有一項商品');
			}
		}
	} // methods end
};
</script>
