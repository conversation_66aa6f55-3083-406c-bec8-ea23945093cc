<template>
	<div class="filemgr-sidebar heighter-cusblock">
		<div class="filemgr-sidebar-header" v-if="customer">
			<div class="text-center">
				<a href="../../CIF/Overview/clientOverview.htm"> <img src="../../../images/avatar/sidemenu-avatar.png" alt="" /></a>
				<h5 class="m-0">{{ customer.cusName }}<span class="cart-container"></span></h5>
				<h6 class="text-secondary">{{ customer.age }}歲 ({{ customer.pbStatusName }})</h6>
			</div>
		</div>
		<div class="filemgr-sidebar-body">
			<div id="sidebarMenu" class="p-3">
				<div id="sidebarMenu" class="px-3">
					<ul class="sidebar-nav">
						<li class="nav-item" v-for="item in summariesMenu">
							<a class="nav-link cif001" @click="menuHandler(item.menuCode)">{{ item.menuName }}</a>
						</li>
					</ul>
				</div>

				<!-- 快捷功能按鈕 -->
				<div class="btn-fast-block" v-if="hasAuth">
					<h6>快捷功能按鈕</h6>
					<a href="../../CIF/Service/cusRecord.htm" class="btn btn-quick-icon cifqic001">
						<i class="quick-icon fa-solid fa-address-card"></i><br />
						<p>聯繫紀錄</p>
					</a>
					<a href="../../CIF/Service/cusMemo.htm" class="btn btn-quick-icon cifqic002">
						<i class="quick-icon fas fa-calendar"></i><br />
						<p>約訪行程</p>
					</a>
					<a href="../../CIF/Service/cusAllEvent.htm" class="btn btn-quick-icon cifqic003">
						<i class="quick-icon fas fa-flag"></i><br />
						<p>事件處理</p>
					</a>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		cusCode: String,
		pageCode: Number,
		hasAuth: Boolean,
		customer: Object,
		setMenuCode: Function
	},
	data: function () {
		return {
			summariesMenu: []
		};
	},
	watch: {
		hasAuth: function (newVal, oldVal) {
			var self = this;
			self.checkRoleMenus();
		}
	},
	mounted: function () {
		var self = this;
		self.checkRoleMenus();
	},
	methods: {
		checkRoleMenus: async function () {
			var self = this;
			const ret = await self.$api.getCusSummariesMenuApi({
				pbStatus: self.customer.pbStatus
			});
			if (ret.data) {
				self.summariesMenu = ret.data;
			}
		},
		menuHandler: function (menuCode) {
			var self = this;
			self.setMenuCode(menuCode);
		}
	}
};
</script>
