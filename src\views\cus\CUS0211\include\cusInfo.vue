<template>
	<div>
		<div class="container-fluid">
			<vue-bi-tabs :menu-code="'M20-052'">
				<template #default="{ id }">
					<div>
						<component :is="id" :cus-code="cusCode" :set-cus-code="setCusCode"></component>
					</div>
				</template>
			</vue-bi-tabs>
		</div>
		<!-- Modal start -->
		<vue-modal :is-open="isOpenModal" :before-close="closeModal">
			<template v-slot:content="props">
				<div class="modal-dialog modal-xl modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title" id="myModal1">身心障礙類別向度說明</h4>
							<button type="button" class="btn-close" @click.prevent="props.close()" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<div class="card card-table mb-3">
								<div class="card-header">
									<h4>身心障礙類別向度說明</h4>
								</div>
								<table class="table table-RWD table-bordered">
									<thead>
										<tr>
											<th>類別</th>
											<th>鑑定向度</th>
											<th>對應中文</th>
										</tr>
									</thead>
									<tbody>
										<template v-for="group in disabledDimensionasGroupedData" :key="group.codeValue">
											<tr v-for="(item, index) in group.data" :key="index">
												<td data-th="類別" :rowspan="group.data.length" v-if="index == 0">{{ item.codeName }}</td>
												<td data-th="鑑定向度">{{ item.disabledCode }}</td>
												<td data-th="對應中文">{{ item.disabledName }}</td>
											</tr>
										</template>
									</tbody>
								</table>
							</div>
						</div>
						<div class="modal-footer" id="disabledDimensionnalClaModalFooter">
							<button @click.prevent="props.close()" type="button" class="btn btn-white">關閉視窗</button>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
	</div>
	<!-- Modal  End -->
</template>
<script>
import vueBiTabs from '@/views/components/biTabs.vue';
import vueCusBasicInfo from './cusBasicInfo.vue';
import vueCusCompany from './cusCompany.vue';
import vueCusHomeFriend from './cusHomeFriend.vue';
import vueCusImportantHoliday from '../../CUS0217/include/importantHoliday.vue';
import vueCusOtherInfo from './cusOtherInfo.vue';
export default {
	components: {
		vueBiTabs,
		vueCusBasicInfo,
		vueCusCompany,
		vueCusHomeFriend,
		vueCusImportantHoliday,
		vueCusOtherInfo
	},
	props: {
		cusCode: String,
		roleCode: String,
		userCode: String,
		hasAuth: Boolean,
		customer: Object,
		setCusCode: Function
	},
	data: function () {
		return {
			isOpenModal: null,
			tabCode: 1,
			tabs: [
				{ tabCode: 1, label: '本行顧客資料' },
				{ tabCode: 2, label: '公司基本資料' },
				{ tabCode: 3, label: '家庭與親友資料' },
				{ tabCode: 4, label: '重要節日設定' }
			],
			disabledDimensionas: []
		};
	},
	computed: {
		disabledDimensionasGroupedData: function () {
			var dataList = this.disabledDimensionas;
			var groupedData = dataList.reduce(function (acc, curr) {
				var codeValue = curr.codeValue;
				var data = Object.assign({}, curr);
				delete data.cusCode;
				if (!acc[codeValue]) {
					acc[codeValue] = { cusCode: codeValue, data: [] };
				}
				acc[codeValue].data.push(data);
				return acc;
			}, {});
			return groupedData;
		}
	},
	mounted: function () {
		var self = this;
		// self.getDisabledDimensiona();
	},
	methods: {
		changeTab: function (tabCode) {
			var self = this;
			if (self.hasAuth) {
				if (self.customer.secretYn == 'Y') {
					self.$bi.alert('該顧客為本行秘密戶。');
				} else if (self.customer.employeesYn == 'Y') {
					self.$bi.alert('該顧客為本行行員。');
				} else {
					self.tabCode = tabCode;
					for (var i = 0; i < self.tabs.length; i++) {
						var tab = self.tabs[i];
						if (tab.tabCode == tabCode) {
							self.title = tab.label;
						}
					}
				}
			} else {
				self.$bi.alert('顧客' + self.customer.cusName + '非屬您歸屬私銀中心/組轄下顧客。');
			}
		},
		initPageDatas: function (cusCode) {
			var self = this;
			if (this.$refs.basicInfo) {
				this.$refs.basicInfo.initPageDatas(cusCode);
			}
		},
		getDisabledDimensiona: async function () {
			var self = this;
			const ret = await self.$api.getDisabledDimensionsApi({
				cusCode: self.cusCode
			});
			self.disabledDimensionas = ret.data;
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		openModal: function () {
			this.isOpenModal = true;
		}
	}
};
</script>
