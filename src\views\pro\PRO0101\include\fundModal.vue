<template>
	<!-- Modal 1 基金 -->
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">信託-基金</h4>
				<button type="button" class="btn-close" @click.prevent="close()" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="card card-prointro shadow-none mb-3">
					<div class="pro_left">
						<div class="pro_name">
							<div class="avatar avatar-md avatar-fund"></div>
							<h4>
								<span>商品名稱</span> <br />{{ $filters.defaultValue(proInfo.proName, '--') }} <br /><span class="tx-black">{{
									$filters.defaultValue(proInfo.proEName, '--')
								}}</span>
							</h4>
						</div>
						<h4 class="pro_value">
							<span>最新淨值</span>
							<br />{{ $filters.formatNumber(proInfo.aprice, '0,0.00' || '--') }} <br /><span>{{
								$filters.formatDate(proInfo.priceDt, '--')
							}}</span>
						</h4>
					</div>
					<div class="pro_class">
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品代碼</span> <br />{{ $filters.defaultValue(proInfo.bankProCode, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6>
								<span>資產類別 <br /></span>{{ $filters.defaultValue(proInfo.assetcatName, '--') }}
							</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品主類</span> <br />{{ $filters.defaultValue(proInfo.pfcatName, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill"></i>
							</div>
							<h6><span>商品次類</span><br />{{ $filters.defaultValue(proInfo.proTypeName, '--') }}</h6>
						</div>
					</div>
				</div>
				<div class="tab-nav-pills">
					<ul class="nav nav-pills justify-content-center" role="tablist">
						<li class="nav-item">
							<a
								class="nav-link"
								href="#Section1"
								data-bs-toggle="pill"
								:class="{ active: activeTab == 'section1' }"
								@click="changeTab('section1')"
								>商品基本資料</a
							>
						</li>
						<li class="nav-item">
							<a
								class="nav-link"
								href="#Section2"
								data-bs-toggle="pill"
								:class="{ active: activeTab == 'section2' }"
								@click="changeTab('section2')"
								>商品共同資料</a
							>
						</li>
						<li class="nav-item">
							<a
								class="nav-link"
								href="#Section3"
								data-bs-toggle="pill"
								:class="{ active: activeTab == 'section3' }"
								@click="changeTab('section3')"
								>商品附加資料</a
							>
						</li>
						<li class="nav-item">
							<a
								class="nav-link datainfo"
								href="#Section4"
								data-bs-toggle="pill"
								v-if="proInfo.fundInfo.lipperId"
								:class="{ active: activeTab == 'section4' }"
								@click="changeTab('section4')"
								>基金資料</a
							>
						</li>
					</ul>

					<div class="tab-content">
						<div class="tab-pane show" id="Section1" :class="{ 'active show': activeTab == 'section1' }">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>基金商品資訊</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th class="wd-20p">基金公司名稱</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo && proInfo.fundInfo.compName, '--') }}
											</td>
											<th class="wd-20p">Lipper 全球分類</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo && proInfo.fundInfo.lipperTypeName, '--') }}
											</td>
										</tr>
										<tr>
											<th>基金類型</th>
											<td colspan="3">
												{{ $filters.defaultValue(proInfo && proInfo.fundInfo.proTypeName, '--') }}
											</td>
										</tr>
										<tr>
											<th>國內基金</th>
											<td>{{ $filters.defaultValue(proInfo.fundInfo.localYn, '--') }}</td>
											<th>基金狀態</th>
											<td>{{ $filters.defaultValue(proInfo.fundInfo.status, '--') }}</td>
										</tr>
										<tr>
											<th>百元基金</th>
											<td>{{ $filters.defaultValue(proInfo.fundInfo.hundredYn, '--') }}</td>

											<th>後收型基金</th>
											<td>{{ $filters.defaultValue(proInfo.fundInfo.backEndLoadYn, '--') }}</td>
										</tr>
										<tr>
											<th>商品風險等級</th>
											<td>
												{{ $filters.defaultValue(proInfo && proInfo.fundInfo.riskName, '--') }}
											</td>
											<th>計價幣別</th>
											<td>
												{{ $filters.defaultValue(proInfo && proInfo.fundInfo.curCode, '--') }}
											</td>
										</tr>
										<tr>
											<th>Lipper Code</th>
											<td>
												{{ $filters.defaultValue(proInfo && proInfo.fundInfo.lipperId, '--') }}
											</td>
											<th>國際代碼</th>
											<td>
												{{ $filters.defaultValue(proInfo && proInfo.fundInfo.isinCode, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>投資金額限制</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th class="wd-20p">台幣單筆最低投資金額</th>
											<td class="wd-30p">
												{{ $filters.formatNumber(proInfo.fundInfo.mininvLcAmt, '0,0.00' || '--') }}
											</td>
											<th class="wd-20p">台幣定時最低投資金額</th>
											<td class="wd-30p">
												{{ $filters.formatNumber(proInfo.fundInfo.mininvLcAmtp, '0,0.00' || '--') }}
											</td>
										</tr>
										<tr>
											<th>外幣單筆最低投資金額</th>
											<td>
												{{ $filters.formatNumber(proInfo.fundInfo.mininvFcAmt, '0,0.00' || '--') }}
											</td>
											<th>外幣定時最低投資金額</th>
											<td>
												{{ $filters.formatNumber(proInfo.fundInfo.mininvFcAmtp, '0,0.00' || '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Section2" :class="{ 'active show': activeTab == 'section2' }">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>銷售相關資料</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th width="20%">銷售地區</th>
											<td width="30%" v-if="proInfo.allYn == 'Y'">全行</td>
											<td width="30%" v-else>--</td>
											<th width="20%">限PI申購</th>
											<td width="30%">
												{{ $filters.defaultValue(proInfo.profInvestorYn, '--') }}
											</td>
										</tr>
										<tr>
											<th width="20%">銷售對象</th>
											<td width="30%" colspan="3">
												{{ $filters.defaultValue(proInfo.targetCusBu, '--') }}
											</td>
										</tr>
										<tr>
											<th>是否開放申購</th>
											<td>{{ $filters.defaultValue(proInfo.buyYn, '--') }}</td>
											<th>是否開放贖回</th>
											<td>{{ $filters.defaultValue(proInfo.sellYn, '--') }}</td>
										</tr>
										<tr>
											<th><span>波動類型</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.volatilityType, '--') }}</span>
											</td>
											<th><span>配息頻率</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.intFreqUnitype, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th>保本要求</th>
											<td colspan="3">{{ $filters.defaultValue(proInfo.principalGuarYn, '--') }}</td>
										</tr>
										<tr>
											<th><span>理財需求</span></th>
											<td colspan="3">
												<div class="form-check form-check-inline" v-for="item in finReqCodeMenu">
													<input
														class="form-check-input"
														name="finReqCodes"
														disabled
														id="c1"
														v-model="finReqCodes"
														:value="item.codeValue"
														type="checkbox"
													/>
													<label class="form-check-label" for="c1">{{ $filters.defaultValue(item.codeName, '--') }}</label>
												</div>
											</td>
										</tr>
										<tr>
											<th>商品標籤</th>
											<td disabled="disabled" colspan="3">
												{{ $filters.defaultValue(proInfo.selprocatNames, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="tab-pane fade" id="Section3" :class="{ 'active show': activeTab == 'section3' }">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>其他設定</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>商品投資標的</span></th>
											<td class="wd-80p">
												<span>{{ $filters.defaultValue(proInfo.sectorName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>商品投資地區</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.geoFocusName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>比較基準設定</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.benchmarkName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>備註</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.memo, '--') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>公開說明書</th>
											<td class="wd-80p">
												<a v-if="proFileB && proFileB.url" :href="proFileB.url" target="_blank">{{ proFileB.url }}</a
												><br v-if="proFileB && proFileB.url" />
												<a v-else>--</a>
												<a v-if="proFileB" class="tx-link" href="#" @click="downloadFile(proFileB)">{{
													$filters.defaultValue(proFileB.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>投資人須知</th>
											<td class="wd-80p">
												<a v-if="proFileD && proFileD.url" :href="proFileD.url" target="_blank">{{ proFileD.url }}</a
												><br v-if="proFileD && proFileD.url" />
												<a v-else>--</a>
												<a v-if="proFileD" class="tx-link" href="#" @click="downloadFile(proFileD)">{{
													$filters.defaultValue(proFileD.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>基金月報</th>
											<td class="wd-80p">
												<a v-if="proFileE && proFileE.url" :href="proFileE.url" target="_blank">{{ proFileE.url }}</a
												><br v-if="proFileE && proFileE.url" />
												<a v-else>--</a>
												<a v-if="proFileE" class="tx-link" href="#" @click="downloadFile(proFileE)">{{
													$filters.defaultValue(proFileE.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>DM</th>
											<td class="wd-80p">
												<a v-if="proFileF && proFileF.url" :href="proFileF.url" target="_blank">{{ proFileF.url }}</a
												><br v-if="proFileF && proFileF.url" /><a v-else>--</a>
												<a v-if="proFileF" class="tx-link" href="#" @click="downloadFile(proFileF)">{{
													$filters.defaultValue(proFileF.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>其他</th>
											<td class="wd-80p">
												<a v-if="proFileG && proFileG.url" :href="proFileG.url" target="_blank">{{ proFileG.url }}</a
												><br v-if="proFileG && proFileG.url" /><a v-else>--</a>
												<a v-if="proFileG" class="tx-link" href="#" @click="downloadFile(proFileG)">{{
													$filters.defaultValue(proFileG.showName, '--')
												}}</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>其他相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<td>
												<span v-for="(item, index) in otherFileList">
													<a
														v-if="index === otherFileList.length - 1"
														v-show="item.show"
														href="#"
														class="tx-link"
														@click="downloadOtherFile(item.docFileId)"
														>{{ $filters.defaultValue(item.showName, '--') }}</a
													>
													<a v-else href="#" class="tx-link" v-show="item.show" @click="downloadOtherFile(item.docFileId)"
														>{{ $filters.defaultValue(item.showName, '--') }}、</a
													>
												</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="tx-note">其他相關附件為文件管理內所上傳與本商品相關的文件。</div>
						</div>

						<!-- vue2 fund -->
						<div class="tab-pane fade" id="Section4" v-if="proInfo.fundInfo.lipperId" :class="{ 'active show': activeTab == 'section4' }">
							<div class="container">
								<div class="card m-t-30">
									<div class="card-header bg-white">
										<div class="card-title">
											{{ $filters.defaultValue(fundInfo && fundInfo.fundEnName, '--') }}
										</div>
										<p>
											<b>基金英文名稱</b>&nbsp;{{ $filters.defaultValue(fundInfo && fundInfo.fundEnName, '--') }}
											<span class="hint-text"> ｜ </span>
											<b>{{ $filters.defaultValue(fundInfo && fundInfo.investmentTypeName, '--') }}</b>
											<span class="hint-text"> ｜ </span>
											<b>理柏代碼</b>&nbsp;{{ $filters.defaultValue(fundInfo && fundInfo.fundCode, '--') }}
											<span class="hint-text"> ｜ </span> <b><a href="#DD" class="link">理柏總回報</a></b>
											<vue-fund-lipper-score :lipper-scores="lipperScores" score-code="TOTRETOV"></vue-fund-lipper-score>
											<br />
											<span class="hint-text"></span> <b>基金經理基準</b>&nbsp;
											<span
												href="#"
												class="text-info p-t-5"
												style="cursor: pointer"
												v-if="fundInfo && _.getRealAssetCode(fundInfo.managerBmCode) && fundInfo.managerBmName"
											>
												{{ $filters.defaultValue(fundInfo.managerBmName, '--') }}
											</span>
											<span v-else>N/A</span>
											<span class="hint-text"> ｜ </span> <b>技術標識基準</b>&nbsp;
											<span
												href="#"
												class="text-info p-t-5"
												style="cursor: pointer"
												v-if="fundInfo && _.getRealAssetCode(fundInfo.analysisBmCode) && fundInfo.analysisBmName"
											>
												{{ $filters.defaultValue(fundInfo.analysisBmName, '--') }}
											</span>
											<span v-else>N/A</span>
										</p>
									</div>
									<div class="card-body" id="fund-basic">
										<div class="row justify-content-between text-center m-t-20">
											<div class="col-6 col-md-4 col-lg-2">
												<h4 class="font-xl no-margin">
													<span v-html="formattedValue ? formattedValue : '--'"></span>
													<small class="font-xs">%</small>
												</h4>
												<p class="small no-margin">近一年累積報酬</p>
											</div>
											<div class="col-6 col-md-4 col-lg-2">
												<h4 class="font-xl no-margin">
													<span>
														{{ getTech(techs, 'ASHP1Y') }}
													</span>
												</h4>
												<p class="small no-margin">＊ 年化Sharpe Ratio</p>
											</div>
											<div class="col-6 col-md-4 col-lg-2">
												<h4 class="font-xl no-margin">
													<span>
														{{ getTech(techs, 'AALP1Y') }}
													</span>
													<small class="font-xs">%</small>
												</h4>
												<p class="small no-margin">＊ 年化Alpha</p>
											</div>
											<div class="col-6 col-md-4 col-lg-2">
												<h4 class="font-xl no-margin">
													<span style="color: #000000">
														{{ getTech(techs, 'ASTD1Y') }}
													</span>
													<small class="font-xs">%</small>
												</h4>
												<p class="small no-margin">＊ 年化標準差</p>
											</div>
											<div class="col-6 col-md-4 col-lg-2">
												<h4 class="font-xl no-margin">
													<span>
														{{ getTech(techs, 'BET1Y') }}
													</span>
												</h4>
												<p class="small no-margin">＊ Beta</p>
											</div>
											<div class="col-6 col-md-4 col-lg-2">
												<h4 class="font-xl no-margin">
													<span>
														{{ getTech(techs, 'RRK1Y') }}
													</span>
												</h4>
												<p class="small no-margin">＊ Return/Risk</p>
											</div>
										</div>
										<p class="small mt-3 mb-0 text-right">(＊以近一年數據計算)</p>
										<div class="row">
											<!--左側-->
											<div class="col-md-12 col-lg-6 col-left">
												<div class="row b-a b-grey padding-10">
													<div class="col-4 text-center bg-contrast-lower padding-10">
														<p class="small no-margin">基金淨值</p>
														<h4 class="font-lg no-margin">
															{{
																$filters.defaultValue(
																	fundInfo && $filters.formatNum(fundInfo.priceLc, '0,0.[0000]'),
																	'--'
																)
															}}
														</h4>
														<p class="no-margin small hint-text">
															（{{ $filters.defaultValue(fundInfo && $filters.formatDate(fundInfo.dataDate), '--') }}）
														</p>
													</div>
													<div class="col-4 text-center bg-contrast-lower padding-10">
														<p class="small no-margin">漲跌幅</p>
														<h4 class="font-lg no-margin">
															<span
																v-html="
																	$filters.defaultValue(
																		fundInfo && $filters.formatFlucWithView(fundInfo.priceFlucLc),
																		'--'
																	)
																"
															></span>
														</h4>
														<p>
															(<span
																v-html="
																	$filters.defaultValue(
																		fundInfo && $filters.formatFlucWithView(fundInfo.priceFlucLcRate, '%'),
																		'--'
																	)
																"
															>
															</span
															>)
														</p>
													</div>
													<div class="col-4 text-center bg-contrast-lower padding-10">
														<p class="small no-margin">基金規模</p>
														<h4 class="font-lg no-margin">
															{{
																$filters.defaultValue(
																	fundInfo && $filters.formatTwMillionUnit(fundInfo.tnaValueLc),
																	'--'
																)
															}}
															<small class="font-xs"
																>({{ fundInfo && $filters.defaultValue(fundInfo.launchCurrencyName, '--') }})</small
															>
														</h4>
														<p class="no-margin small hint-text">
															與上月比<span
																v-html="$filters.defaultValue($filters.formatFlucWithView(tnaValueRate, '%'), '--')"
															></span>
														</p>
													</div>
												</div>
												<div class="row m-t-10">
													<div class="col-12">
														<table class="table table-bordered">
															<tbody>
																<tr>
																	<th width="20%">基金特性</th>
																	<td width="80%">
																		{{ $filters.defaultValue(fundInfo && fundInfo.text, '--') }}
																	</td>
																</tr>
																<tr>
																	<th>同Lipper global分類其他基金</th>
																	<td>
																		<div class="table-scroll ht-100" style="white-space: unset">
																			<vue-fund-same-global-class-fund
																				v-if="fundInfo && fundInfo.fundCode"
																				:fund-code="fundInfo.fundCode"
																				:global-class-code="fundInfo.globalClassCode"
																			></vue-fund-same-global-class-fund>
																		</div>
																	</td>
																</tr>
															</tbody>
														</table>
													</div>
												</div>
												<hr />
												<div class="row m-t-10">
													<vue-fund-tech v-if="showColumnChart" :techs="techs"></vue-fund-tech>
												</div>
												<div class="row m-t-20">
													<vue-fund-price-history
														v-if="fundInfo && fundInfo.fundCode"
														:fund-code="fundInfo.fundCode"
													></vue-fund-price-history>
												</div>
											</div>
											<!--右側-->
											<div class="col-md-12 col-lg-6 col-right">
												<table width="100%" class="table table-bordered">
													<tbody>
														<tr>
															<th>基金發行日</th>
															<td>
																{{
																	$filters.defaultValue(fundInfo && $filters.formatDate(fundInfo.launchDate), '--')
																}}
															</td>
														</tr>
														<tr>
															<th>註冊地</th>
															<td>
																{{ $filters.defaultValue(fundInfo && fundInfo.domicileName, '--') }}
															</td>
														</tr>
														<tr>
															<th>現行計價幣別</th>
															<td>
																{{ $filters.defaultValue(fundInfo && fundInfo.launchCurrencyName, '--') }}
															</td>
														</tr>
														<tr>
															<th>投資標的:Lipper全球分類</th>
															<td>
																{{ $filters.defaultValue(fundInfo && fundInfo.globalClassName, '--') }}
															</td>
														</tr>
														<tr>
															<th>投資標的:Lipper區域分類</th>
															<td>
																{{ $filters.defaultValue(fundInfo && fundInfo.localClassName, '--') }}
															</td>
														</tr>
														<tr>
															<th>投資地區</th>
															<td>
																{{ $filters.defaultValue(fundInfo && fundInfo.geoFocusName, '--') }}
															</td>
														</tr>
														<tr>
															<th>傘型基金</th>
															<td>{{ fundInfo && fundInfo.umbrellaFundName ? fundInfo.umbrellaFundName : '--' }}</td>
														</tr>
													</tbody>
												</table>
												<vue-fund-fund-code
													v-if="fundInfo && fundInfo.fundCode"
													:fund-code="fundInfo.fundCode"
												></vue-fund-fund-code>
												<div class="row" v-show="fundInfo && fundInfo.divsPerYear > 0">
													<h4>配息</h4>
													<table class="table table-bordered">
														<tbody>
															<tr>
																<th width="45%">每年配息次數</th>
																<td width="55%" class="text-right">
																	{{ $filters.formatNumber(fundInfo && fundInfo.divsPerYear, '--') }}
																</td>
															</tr>
															<tr>
																<th>至上一個月底淨值(原幣)</th>
																<td class="text-right">
																	{{
																		$filters.defaultValue(
																			fundInfo && $filters.formatNum(preMonthEndPrice, '0,0.00'),
																			'--'
																		)
																	}}
																</td>
															</tr>
															<tr>
																<th>年初至近一個月底，投入10,000所得之收益</th>
																<td class="text-right">
																	{{
																		$filters.defaultValue(
																			fundInfo && $filters.formatNum(fundInfo.incomeYtm, '0,0.00'),
																			'--'
																		)
																	}}
																</td>
															</tr>
														</tbody>
													</table>
												</div>
												<h4>保管機構</h4>
												<table class="table table-bordered">
													<tbody>
														<tr>
															<th width="45%">公司簡稱</th>
															<td width="55%">
																{{
																	$filters.defaultValue(
																		getCompanyField(companies, 'CUST', 'companyShortName'),
																		'--'
																	)
																}}
															</td>
														</tr>
														<tr>
															<th>電話</th>
															<td>
																{{ $filters.defaultValue(getCompanyField(companies, 'CUST', 'telephone'), '--') }}
															</td>
														</tr>
														<tr>
															<th>傳真</th>
															<td>
																{{ $filters.defaultValue(getCompanyField(companies, 'CUST', 'fax'), '--') }}
															</td>
														</tr>
														<tr>
															<th>Email</th>
															<td>
																{{ $filters.defaultValue(getCompanyField(companies, 'CUST', 'email'), '--') }}
															</td>
														</tr>
														<tr>
															<th>網址</th>
															<td>
																{{ $filters.defaultValue(getCompanyField(companies, 'CUST', 'website'), '--') }}
															</td>
														</tr>
														<tr>
															<th>旗下基金</th>
															<td>
																<div class="table-scroll ht-100" style="white-space: unset">
																	<vue-fund-same-company-fund
																		v-if="fundInfo && fundInfo.fundCode"
																		:fund-code="fundInfo.fundCode"
																	>
																	</vue-fund-same-company-fund>
																</div>
															</td>
														</tr>
													</tbody>
												</table>
												<h4>基金公司</h4>
												<table class="table table-bordered">
													<tbody>
														<tr>
															<th width="45%">公司名稱</th>
															<td width="55%">
																{{ $filters.defaultValue(getCompanyField(companies, 'PMGR', 'companyName'), '--') }}
															</td>
														</tr>
														<tr>
															<th>公司簡稱</th>
															<td>
																{{
																	$filters.defaultValue(
																		getCompanyField(companies, 'PMGR', 'companyShortName'),
																		'--'
																	)
																}}
															</td>
														</tr>
														<tr>
															<th>電話</th>
															<td>
																{{ $filters.defaultValue(getCompanyField(companies, 'PMGR', 'telephone'), '--') }}
															</td>
														</tr>
														<tr>
															<th>傳真</th>
															<td>
																{{ $filters.defaultValue(getCompanyField(companies, 'PMGR', 'fax'), '--') }}
															</td>
														</tr>
														<tr>
															<th>Email</th>
															<td>
																{{ $filters.defaultValue(getCompanyField(companies, 'PMGR', 'email'), '--') }}
															</td>
														</tr>
														<tr>
															<th>網址</th>
															<td>
																{{ $filters.defaultValue(getCompanyField(companies, 'PMGR', 'website'), '--') }}
															</td>
														</tr>
													</tbody>
												</table>
											</div>
										</div>
										<div class="row">
											<vue-fund-risk-pct
												v-if="fundInfo"
												ref="riskPct"
												:fund-code="fundInfo.fundCode"
												:global-class-code="fundInfo.globalClassCode"
											></vue-fund-risk-pct>
											<div class="col-sm-4 col-right">
												<table class="table table-bordered four-grid">
													<tbody>
														<tr>
															<td
																class="text-center text-complete"
																:class="{ 'bg-complete text-white': clsActive2X2('A') }"
															>
																佳
																<p class="font-xs">(象限二)</p>
															</td>
															<td
																class="text-center text-success"
																:class="{ 'bg-success text-white': clsActive2X2('B') }"
															>
																合理
																<p class="font-xs">(象限一)</p>
															</td>
														</tr>
														<tr>
															<td
																class="text-center text-success"
																:class="{ 'bg-success text-white': clsActive2X2('C') }"
															>
																合理
																<p class="font-xs">(象限三)</p>
															</td>
															<td
																class="text-center text-danger"
																:class="{ 'bg-danger text-white': clsActive2X2('D') }"
															>
																欠佳
																<p class="font-xs">(象限四)</p>
															</td>
														</tr>
													</tbody>
												</table>
												<p class="m-t-5">備註：</p>
												<ul class="note">
													<li>
														<p class="text-success tx-title">(象限一)</p>
														<p>
															此象限內之基金，其年化標準差(風險值)與年化報酬率，相較於同類型之其他基金均為較高，故承受較高風險可合理得到較高報酬。
														</p>
													</li>
													<li>
														<p class="text-complete tx-title">(象限二)</p>
														<p>此象限內之基金，相較於同類型之其他基金：</p>
														<ul>
															<li>年化標準差(風險值)較低</li>
															<li>年化報酬率較高</li>
														</ul>
														<p>故單位標準差(風險值)可獲得較高之報酬率。</p>
													</li>
													<li>
														<p class="text-success tx-title">(象限三)</p>
														<p>
															此象限內之基金，其年化標準差(風險值)與年化報酬率，相較於同類型之其他基金均為較低，故承受較低風險可合理得到較低報酬。
														</p>
													</li>
													<li>
														<p class="text-danger tx-title">(象限四)</p>
														<p>此象限內之基金，相較於同類型之其他基金：</p>
														<ul>
															<li>年化標準差(風險值)較高</li>
															<li>年化報酬率較低</li>
														</ul>
														<p>故單位標準差(風險值)可獲得較低之報酬率。</p>
													</li>
												</ul>
											</div>
										</div>
										<div class="row" v-show="fundInfo && fundInfo.divsPerYear > 0">
											<vue-fund-dividend-history
												v-if="fundInfo && fundInfo.fundCode"
												:fund-code="fundInfo.fundCode"
												:local-currency-name="fundInfo.localCurrencyName"
											></vue-fund-dividend-history>
										</div>
										<hr />
										<div class="row" v-show="fundInfo">
											<vue-fund-fund-size-compare
												v-if="fundInfo"
												:tech-currency-code="cookieCurrency"
												:fund-info="fundInfo"
											></vue-fund-fund-size-compare>
										</div>
										<div class="row">
											<vue-fund-perf-compare
												v-if="fundInfo"
												:tech-currency-code="cookieCurrency"
												:fund-info="fundInfo"
												:techs="techs"
											></vue-fund-perf-compare>
										</div>
										<div class="row m-t-20">
											<vue-fund-fluctuation-rate
												:tech-currency-code="cookieCurrency"
												:fund-info="fundInfo"
											></vue-fund-fluctuation-rate>
										</div>
										<div class="row m-t-20">
											<vue-fund-holding v-if="fundInfo && fundInfo.fundCode" :fund-code="fundInfo.fundCode"></vue-fund-holding>
										</div>
										<div class="row m-t-20">
											<div class="col-12">
												<h4>其他技術指標</h4>
												<vue-fund-other-tech
													v-if="showColumnChart"
													:fund-info="fundInfo"
													:techs="techs"
													:bm-Techs="bmTechs"
													:bm-name="bmName"
												></vue-fund-other-tech>
												<vue-fund-other-tech-acr
													v-if="showColumnChart"
													:fund-info="fundInfo"
													:twd-techs="techs"
													:twd-bm-techs="bmTechs"
													:bm-name="bmName"
												></vue-fund-other-tech-acr>
											</div>
										</div>
										<div class="row m-t-20">
											<div class="col-12">
												<h4>理柏排名</h4>
												<table class="table table-bordered m-t-15">
													<thead>
														<tr>
															<th></th>
															<th class="text-center">總回報</th>
															<th class="text-center">保本</th>
															<th class="text-center">穩定</th>
														</tr>
													</thead>
													<tbody>
														<tr>
															<td class="text-center">ALL</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="TOTRETOV"
																></vue-fund-lipper-score>
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="CAPPRESOV"
																></vue-fund-lipper-score>
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="CONSRETOV"
																></vue-fund-lipper-score>
															</td>
														</tr>
														<tr>
															<td class="text-center">3年</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="TOTRET3YR"
																></vue-fund-lipper-score>
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="CAPPRES3YR"
																></vue-fund-lipper-score>
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="CONSRET3YR"
																></vue-fund-lipper-score>
															</td>
														</tr>
														<tr>
															<td class="text-center">5年</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="TOTRET5YR"
																></vue-fund-lipper-score>
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="CAPPRES5YR"
																></vue-fund-lipper-score>
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="CONSRET5YR"
																></vue-fund-lipper-score>
															</td>
														</tr>
														<tr>
															<td class="text-center">10年</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="TOTRET10YR"
																></vue-fund-lipper-score>
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="CAPPRES10YR"
																></vue-fund-lipper-score>
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="CONSRET10YR"
																></vue-fund-lipper-score>
															</td>
														</tr>
													</tbody>
												</table>
												<p class="text-center m-t-10">
													<!--                            <img th:src="@{/image/fund/lipper-rank.png}" width="250px"/>-->
												</p>
												<h4 class="text-color">理柏說明</h4>
												<p id="DD" class="muted">
													1. Lipper Leaders 評級系統中的基金排名每月更新，分別按照 三年、五年、十年和整體的表現。<br />
													根據Lipper Leaders 的四個評估標準，計算每只基金的等權平均數，繼而將同類基金以 百分位數排名。
													每個評估標準如下： 領先的20%評級為5，並被授予Lipper Leaders 的稱號 之後的20%評級為4
													中間的20%評級為3 再之後的20%評級為2 最後的20%評級為1
												</p>
												<p class="muted">
													2. <b>『理柏基金評級』</b><br />
													<b class="text-color">總回報</b>
													<br />總回報評級能夠識別出與同類基金相比之下，總回報（來自股息、利息以及資本增值的收入）表現更佳的基金。
													總回報評級或許最適合那些不注重風險因素，只重視歷史回報的投資人。若只參考這個評估標準，可能不適合希望避免下跌風險的投資人。對於追求低風險的投資人而言，可將總回報評級與保本能力評級和/或穩定回報評級一併考慮，作出可權衡風險和回報的決定。
													<br />

													<b class="text-color">穩定回報</b><br />
													穩定回報評級能夠識別出與同類基金相比，回報更為穩定而風險調整收益更高的基金。穩定回報評級中評級較高的基金，可能最適合那些看重逐年表現相對同類基金更為穩定的投資人。
													投資人需注意的是，某些類別的基金本身具有高波動性，即使在穩定回報評級中獲得Lipper Leaders
													的稱號，也未必適合追求短期標的或風險承受度較低的投資人。<br />

													<b class="text-color">保本能力</b> <br />
													保本能力評級能夠區別出與同資產類別基金相比，在各個資產類別上保本能力更強的基金。
													選取保本能力評級較高的基金，或許有助於將下跌風險最小化。投資人應注意的是，從過去的歷史資料來看，相對於混合型或固定收益型基金，股票型基金的波動性較高，所以即使在保本能力評級中獲得Lipper
													Leaders的稱號，也未必適合追求短期目標或風險承受度較低的投資人。
												</p>
												<p class="muted">
													3. 免責聲明：<br />
													Lipper Leaders fund ratings do not constitute and are not intended to constitute investment advice
													or an offer to sell or the solicitation of an offer to buy any security of any entity in any
													jurisdiction. As a result, you should not make an investment decision on the basis of this
													information. Rather, you should use the Lipper ratings for informational purposes only. Certain
													information provided by Lipper may relate to securities that may not be offered, sold or delivered
													within the United States (or any State thereof) or to, or for the account or benefit of, United
													States citizens. Lipper is not responsible for the accuracy, reliability or completeness of the
													information that you obtain from Lipper. In addition, Lipper will not be liable for any loss or
													damage resulting from information obtained from Lipper or any of its affiliates.
												</p>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button @click.prevent="close()" type="button" class="btn btn-white">關閉視窗</button>
			</div>
		</div>
	</div>
	<!-- Modal 1 End -->
</template>
<script>
import moment from 'moment';
import _ from 'lodash';

import vueFundLipperScore from './fundLipperScore.vue';
import vueFundSameCompanyFund from './fundSameCompanyFund.vue';
import vueFundSameGlobalClassFund from './fundSameGlobalClassFund.vue';
import vueFundPriceHistory from './fundPriceHistory.vue';
import vueFundRiskPct from './fundRiskPct.vue';
import vueFundFundCode from './fundFundCode.vue';
import vueFundDividendHistory from './fundDividendHistory.vue';
import vueFundFundSizeCompare from './fundFundSizeCompare.vue';
import vueFundPerfCompare from './fundPerfCompare.vue';
import vueFundFluctuationRate from './fluctuationRate.vue';
import vueFundTech from './fundTech.vue';
import vueFundOtherTech from './fundOtherTech.vue';
import vueFundOtherTechAcr from './fundOtherTechAcr.vue';
import vueFundHolding from './fundHolding.vue';

export default {
	components: {
		vueFundLipperScore,
		vueFundSameCompanyFund,
		vueFundSameGlobalClassFund,
		vueFundPriceHistory,
		vueFundRiskPct,
		vueFundFundCode,
		vueFundDividendHistory,
		vueFundFundSizeCompare,
		vueFundPerfCompare,
		vueFundFluctuationRate,
		vueFundTech,
		vueFundOtherTech,
		vueFundOtherTechAcr,
		vueFundHolding
	},
	props: {
		downloadFile: Function,
		downloadOtherFile: Function,
		finReqCodeMenu: Array,
		close: Function
	},
	data: function () {
		const now = moment();
		return {
			activeTab: 'section1',
			proInfo: {
				fundInfo: {},
				etfInfo: {},
				bondInfo: {},
				spInfo: {},
				insInfo: {},
				dciInfo: {},
				secInfo: {}
			},
			finReqCodes: [],
			proFileB: {},
			proFileD: {},
			proFileE: {},
			proFileF: {},
			proFileG: {},
			otherFileList: [],
			// vue2 fund
			fundInfo: null,
			companies: [],
			lipperScores: [],
			fundSizes: [],
			techs: null,
			bmTechs: null,
			techsOther: [],
			bmName: null,
			preMonthEndPrice: null,
			cookieCurrency: 'TWD', // TODO 測試資料
			fundColumnChartData: []
		};
	},
	watch: {},
	computed: {
		// vue2 fund
		tnaValueRate: function () {
			if (!this.fundSizes || this.fundSizes.length !== 2) return;
			var fundSizes = _.orderBy(this.fundSizes, ['tnaDate'], ['desc']);
			return ((fundSizes[0].tnaValue - fundSizes[1].tnaValue) / fundSizes[0].tnaValue) * 100;
		},
		showColumnChart: function () {
			if (this.techs && this.bmTechs && this.bmName) {
				return true;
			} else {
				return false;
			}
		},
		formattedValue() {
			const value = this.$filters.formatFlucWithView(this.getTech(this.techs, 'PCT1Y'));
			return this.$filters.defaultValue(value, '--');
		}
	},
	mounted: function () {
		var self = this;
		self.activeTab = 'section1';
		self.fundInfo = {};
	},
	methods: {
		// 條件Tab切換
		changeTab: function (tabName) {
			var self = this;
			self.activeTab = tabName;
		},
		getProInfo: async function (proCode, pfcatCode) {
			var self = this;
			const res = await this.$api.getProductInfoApi({
				proCode: proCode,
				pfcatCode: pfcatCode
			});
			if (_.isNil(res.data)) {
				res.data = {};
				this.$bi.alert('資料不存在');
				return;
			}
			if (_.isNil(res.data.fundInfo)) {
				res.data.fundInfo = {};
			}
			// Object.assign(self.proInfo, res.data);

			self.proInfo = res.data;

			var selectYnList = [];

			this.$api
				.getAdmCodeDetail({
					codeType: 'SELECT_YN'
				})
				.then(function (ret) {
					selectYnList = ret.data;

					if (!_.isEmpty(selectYnList)) {
						if (!_.isUndefined(self.proInfo.fundInfo.localYn)) {
							var localYnObjs = _.filter(selectYnList, {
								codeValue: self.proInfo.fundInfo.localYn
							});
							self.proInfo.fundInfo.localYn = localYnObjs[0].codeName;
						}

						if (!_.isUndefined(self.proInfo.fundInfo.hundredYn)) {
							var hundredYnObjs = _.filter(selectYnList, {
								codeValue: self.proInfo.fundInfo.hundredYn
							});
							self.proInfo.fundInfo.hundredYn = hundredYnObjs[0].codeName;
						}

						if (!_.isUndefined(self.proInfo.fundInfo.backEndLoadYn)) {
							var backEndLoadYnObjs = _.filter(selectYnList, {
								codeValue: self.proInfo.fundInfo.backEndLoadYn
							});
							self.proInfo.fundInfo.backEndLoadYn = backEndLoadYnObjs[0].codeName;
						}

						if (!_.isUndefined(self.proInfo.buyYn)) {
							var buyYnObjs = _.filter(selectYnList, {
								codeValue: self.proInfo.buyYn
							});
							self.proInfo.buyYn = buyYnObjs[0].codeName;
						}

						if (!_.isUndefined(self.proInfo.sellYn)) {
							var sellYnObjs = _.filter(selectYnList, {
								codeValue: self.proInfo.sellYn
							});
							self.proInfo.sellYn = sellYnObjs[0].codeName;
						}

						if (!_.isUndefined(self.proInfo.profInvestorYn)) {
							var profInvestorYnObjs = _.filter(selectYnList, {
								codeValue: self.proInfo.profInvestorYn
							});
							self.proInfo.profInvestorYn = profInvestorYnObjs[0].codeName;
						}
					}
				});

			if (!_.isUndefined(self.proInfo.fundInfo.status)) {
				var fundStatusList = [];
				this.$api
					.getAdmCodeDetail({
						codeType: 'FUND_STATUS'
					})
					.then(function (ret) {
						fundStatusList = ret.data;
						var statusObjs = _.filter(fundStatusList, {
							codeValue: self.proInfo.fundInfo.status
						});
						self.proInfo.fundInfo.status = statusObjs[0] && statusObjs[0].codeName;
					});
			}

			if (!_.isUndefined(self.proInfo.targetCusBu)) {
				var targetCusBuList = [];
				this.$api
					.getAdmCodeDetail({
						codeType: 'CUS_BU'
					})
					.then(function (ret) {
						targetCusBuList = ret.data;
						var targetCusBuObjs = _.filter(targetCusBuList, {
							codeValue: self.proInfo.targetCusBu
						});
						self.proInfo.targetCusBu = targetCusBuObjs[0].codeName;
					});
			}

			if (!_.isUndefined(self.proInfo.volatilityType)) {
				var volatilityTypeList = [];
				this.$api
					.getAdmCodeDetail({
						codeType: 'VOLATILITY_TYPE'
					})
					.then(function (ret) {
						volatilityTypeList = ret.data;
						var volatilityTypeObjs = _.filter(volatilityTypeList, {
							codeValue: self.proInfo.volatilityType
						});
						self.proInfo.volatilityType = volatilityTypeObjs[0].codeName;
					});
			}

			if (!_.isUndefined(self.proInfo.intFreqUnitype)) {
				var intFreqUnitypeList = [];
				this.$api
					.getAdmCodeDetail({
						codeType: 'INT_FREQ_UNITTYPE'
					})
					.then(function (ret) {
						intFreqUnitypeList = ret.data;
						var intFreqUnitypeObjs = _.filter(intFreqUnitypeList, {
							codeValue: self.proInfo.intFreqUnitype
						});
						self.proInfo.intFreqUnitype = intFreqUnitypeObjs[0].codeName;
					});
			}

			if (!_.isUndefined(self.proInfo.principalGuarYn)) {
				var principalGuarYnList = [];
				this.$api
					.getAdmCodeDetail({
						codeType: 'GUAR_YN'
					})
					.then(function (ret) {
						principalGuarYnList = ret.data;
						var principalGuarYnObjs = _.filter(principalGuarYnList, {
							codeValue: self.proInfo.principalGuarYn
						});
						self.proInfo.principalGuarYn = principalGuarYnObjs[0].codeName;
					});
			}

			if (!_.isUndefined(self.proInfo.selprocatNames)) {
				var selprocatNames = self.proInfo.selprocatNames.replaceAll(',', '、');
				self.proInfo.selprocatNames = selprocatNames;
			}

			if (!_.isUndefined(self.proInfo.finReqCode)) {
				self.finReqCodes = self.proInfo.finReqCode.split(',');
			}

			// 商品附加資料
			this.$api
				.getProductsCommInfo({
					proCode: proCode,
					pfcatCode: pfcatCode
				})
				.then(function (ret) {
					if (!_.isNil(ret.data)) {
						if (ret.data.proDocs) {
							self.otherFileList = ret.data.proDocs; // 其他相關附件
							self.otherFileList.forEach(function (item) {
								// 其他相關附件 檔案顯示時間範圍
								item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
							});
						}

						var proFileList = ret.data.proFiles;
						if (!_.isNil(proFileList)) {
							self.proFileB = proFileList.filter((proFile) => proFile.fileType === 'B')[0];
							self.proFileD = proFileList.filter((proFile) => proFile.fileType === 'D')[0];
							self.proFileE = proFileList.filter((proFile) => proFile.fileType === 'E')[0];
							self.proFileF = proFileList.filter((proFile) => proFile.fileType === 'F')[0];
							self.proFileG = proFileList.filter((proFile) => proFile.fileType === 'G')[0];
						}
					}
				});

			// vue2 fund
			if (self.proInfo.fundInfo.lipperId) {
				await self.getFundInfo(self.proInfo.fundInfo.lipperId);
				await self.getTechs(self.proInfo.fundInfo.lipperId);
				await self.getCompanies(self.proInfo.fundInfo.lipperId);
				await self.getLipperScore(self.proInfo.fundInfo.lipperId);
				await self.getFundInfoFundSizeLatest2(self.proInfo.fundInfo.lipperId);
				await self.getPreMonthEndPrice(self.proInfo.fundInfo.lipperId);
			}
			this.$forceUpdate();
		},
		// vue2 fund
		getFundInfo: async function (proCode) {
			var self = this;
			const res = await this.$api.getFundInfo({
				proCode: proCode
			});
			self.fundInfo = res.data;
			self.getBmTechs();
		},
		getCompanies: function (proCode) {
			var self = this;
			return this.$api
				.getFundCompany({
					proCode: proCode
				})
				.then(function (ret) {
					self.companies = ret.data;
				});
		},
		getLipperScore: function (proCode) {
			var self = this;
			return this.$api
				.getLipperScoreApi({
					proCode: proCode
				})
				.then(function (ret) {
					self.lipperScores = ret.data;
				});
		},
		getFundInfoFundSizeLatest2: function (proCode) {
			var self = this;
			return this.$api
				.getFundInfoFundSizeLatest2Api({
					proCode: proCode
				})
				.then(function (ret) {
					self.fundSizes = ret.data;
				});
		},
		getTechs: async function (proCode) {
			var self = this;
			const res = await this.$api.getTechsApi({
				proCode: proCode,
				techCurrencyCode: self.cookieCurrency
			});
			if (!_.isNil(res.data)) {
				self.techs = res.data;
			}
		},
		getBmTechs: async function () {
			var self = this;
			var managerBmCode = self.fundInfo.managerBmCode;
			var analysisBmCode = self.fundInfo.analysisBmCode;
			var bmCode;

			if (managerBmCode && managerBmCode != '11000006' && managerBmCode != '11000000') {
				self.bmName = self.fundInfo.managerBmName;
				bmCode = managerBmCode;
			} else if (analysisBmCode) {
				self.bmName = self.fundInfo.analysisBmName;
				bmCode = analysisBmCode;
			}

			const res = await this.$api.getTechsApi({
				proCode: bmCode,
				techCurrencyCode: self.cookieCurrency
			});
			self.bmTechs = res.data;
		},
		getPreMonthEndPrice: async function (proCode) {
			var self = this;
			const ret = this.$api.getPreMonthEndPriceApi({
				proCode: proCode,
				beginDate: moment().subtract(1, 'months').startOf('month').format('YYYY/MM/DD'),
				endDate: moment().subtract(1, 'months').endOf('month').format('YYYY/MM/DD')
			});
			var preMonthEndPrice = _.orderBy(
				_.filter(ret.data, function (item) {
					return item.priceLc > 0;
				}),
				['dataDate'],
				['desc']
			);
			self.preMonthEndPrice = preMonthEndPrice && preMonthEndPrice[0] ? preMonthEndPrice[0].priceLc : null;
		},
		clsActive2X2: function (median) {
			var self = this;
			if (self.$refs.riskPct) {
				var pointFund = self.$refs.riskPct.pointFund;
				if (pointFund && pointFund.median === median && self.$refs.riskPct.fund2x2Perfs.length > 1) {
					return true;
				}
			}
			return false;
		},
		getTech: function (techs, statCode) {
			var tech = _.find(techs, { statCode: statCode });
			return tech ? tech.dvalue : null;
		},
		getBmTech: function (statCode) {
			var tech = _.find(this.techsOther, { statCode: statCode });
			return tech ? tech.dvalue : null;
		},
		getCompanyField: function (value, companyRoleCode, field) {
			var company = _.find(value, { companyRoleCode: companyRoleCode });
			if (company && company[field]) {
				return company[field];
			} else {
				return '--';
			}
		}
	} // methods end
};
</script>
