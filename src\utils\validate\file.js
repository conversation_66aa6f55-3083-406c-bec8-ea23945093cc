import { defineRule } from 'vee-validate';
import numeral from 'numeral';
import { _ } from 'lodash';

defineRule('mbSize', function (value, params, config) {
	var maxSize = params[0];
	if (!value || !value.size || !numeral(maxSize).value()) {
		return false;
	}
	if (value.size > 1024 * 1024 * numeral(maxSize).value()) {
		return config.field + '的檔案須小於' + maxSize + 'MB';
	}
	return true;
});

defineRule('validateName', function (value, params, config) {
	var symbols = params;
	if (_.some(symbols, (symbol) => _.includes(value.name, symbol))) {
		return config.field + '檔案名稱不可以包含' + _.join(symbols, ' ');
	}
	return true;
});

defineRule('required_file', function (value, params, config) {
	if (!value) {
		return config.field + ' 請選擇有效檔案';
	}
	return true;
});

defineRule('extMgt', function (value, params, config) {
	var validExts = params;
	if (!value || !value.name) {
		return false;
	}
	var fileType = value.name.split('.').pop().toLowerCase();
	if (!validExts.includes(fileType)) {
		return config.field + ' 僅支援檔案類型: ' + validExts.join(', ');
	}
	return true;
});

defineRule('isOverSize', function (value, params, config) {
	var size = params.fieldCnt;
	var maxFileCount = params.maxFileCount;
	if (size >= maxFileCount) {
		return config.field + ' 一個文件公告最多只能傳' + maxFileCount + '個檔案 ';
	}
	return true;
});
