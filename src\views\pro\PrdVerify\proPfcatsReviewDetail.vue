<template>
	<!-- Modal 1-->
	<vue-modal :is-open="isOpenMyModal" :before-close="closeMyModal()">
		<template v-slot:content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">精選推薦商品</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()"><i class="bi bi-arrows-fullscreen"></i></button>
						<button type="button" class="btn-close" @click.prevent="props.close()" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="tx-title">精選商品類型</div>
						<table class="table table-RWD table-bordered table-horizontal-RWD">
							<tbody>
								<tr>
									<th class="wd-20p">商品主類</th>
									<td class="wd-80p">{{ previewItem.pfcatName }}</td>
								</tr>
								<tr>
									<th>精選商品類別</th>
									<td>{{ previewItem.selprocatName }}</td>
								</tr>
								<tr>
									<th>精選商品套裝名稱</th>
									<td>{{ previewItem.selproName }}</td>
								</tr>
								<tr>
									<th>上架日期</th>
									<td>{{ previewItem.startDate }}~ {{ previewItem.endDate }}</td>
								</tr>
							</tbody>
						</table>
						<div class="tx-title">商品列表</div>
						<div class="table-responsive">
							<table class="table table-RWD table-hover table-bordered">
								<thead>
									<tr>
										<th>商品代碼</th>
										<th>商品名稱</th>
										<th>發行機構</th>
										<th>商品風險等級</th>
										<th>商品主類</th>
										<th>商品次分類</th>
										<th>非主級別</th>
										<th>異動狀態</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="item in previewItem.proSelectedMaps">
										<td>{{ item.bankProCode }}</td>
										<td>
											<span>
												<a
													v-if="previewItem.pfcatCode === 'FUND'"
													class="tx-link"
													href="#"
													@click="fundModalHandler(item.proCode, previewItem.pfcatCode)"
													>{{ $filters.defaultValue(item.proName, '--') }}</a
												>
												<a
													v-else-if="previewItem.pfcatCode === 'ETF'"
													class="tx-link"
													href="#"
													@click="etfModalHandler(item.proCode, previewItem.pfcatCode)"
													>{{ $filters.defaultValue(item.proName, '--') }}</a
												>
												<a
													v-else-if="previewItem.pfcatCode === 'FB'"
													class="tx-link"
													href="#"
													@click="bondModalHandler(item.proCode, previewItem.pfcatCode)"
													>{{ $filters.defaultValue(item.proName, '--') }}</a
												>
												<a
													v-else-if="previewItem.pfcatCode === 'SP'"
													class="tx-link"
													href="#"
													@click="spModalHandler(item.proCode, previewItem.pfcatCode)"
													>{{ $filters.defaultValue(item.proName, '--') }}</a
												>
												<a
													v-else-if="previewItem.pfcatCode === 'INS'"
													class="tx-link"
													href="#"
													@click="insModalHandler(item.proCode, previewItem.pfcatCode)"
													>{{ $filters.defaultValue(item.proName, '--') }}</a
												>
												<a
													v-else-if="previewItem.pfcatCode === 'DCI'"
													class="tx-link"
													href="#"
													@click="dciModalHandler(item.proCode, previewItem.pfcatCode)"
													>{{ $filters.defaultValue(item.proName, '--') }}</a
												>
												<a
													v-else-if="previewItem.pfcatCode === 'SEC'"
													class="tx-link"
													href="#"
													@click="secModalHandler(item.proCode, previewItem.pfcatCode)"
													>{{ $filters.defaultValue(item.proName, '--') }}</a
												>
												<a
													v-else-if="previewItem.pfcatCode === 'PFD'"
													class="tx-link"
													href="#"
													@click="pfdModalHandler(item.proCode, previewItem.pfcatCode)"
													>{{ $filters.defaultValue(item.proName, '--') }}</a
												>
												<span v-else>{{ $filters.defaultValue(item.proName, '--') }}</span>
											</span>
										</td>
										<td>{{ item.issuerName }}</td>
										<td>{{ item.riskName }}</td>
										<td>{{ previewItem.pfcatName }}</td>
										<td>{{ item.proTypeName }}</td>
										<td class="text-center">
											<input class="form-check-input text-center" type="checkbox" disabled :checked="item.mainLevelYn == 'Y'" />
										</td>
										<td>
											{{ previewItem.actionName }}
										</td>
									</tr>
								</tbody>
							</table>
						</div>

						<div class="modal-footer" id="modalFooterId">
							<input class="btn btn-white" type="button" value="關閉" @click.prevent="props.close()" />
						</div>
					</div>
				</div>
				<vue-modal :is-open="isOpenModal['fund']" :before-close="closeModal('fund')">
					<template v-slot:content="props">
						<vue-fund-modal
							ref="fundModalRef"
							:is-open-fund-modal="isOpenModal['fund']"
							:fin-req-code-menu="finReqCodeMenu"
							:download-file="downloadFile"
							:download-other-file="downloadOtherFile"
							:close="props.close"
						></vue-fund-modal>
					</template>
				</vue-modal>
				<vue-modal :is-open="isOpenModal['etf']" :before-close="closeModal('etf')">
					<template v-slot:content="props">
						<vue-etf-modal
							ref="etfModalRef"
							:is-open-etf-modal="isOpenModal['etf']"
							:fin-req-code-menu="finReqCodeMenu"
							:pro-price-range-menu="proPriceRangeMenu"
							:download-file="downloadFile"
							:download-other-file="downloadOtherFile"
							:close="props.close"
						></vue-etf-modal>
					</template>
				</vue-modal>
				<vue-modal :is-open="isOpenModal['bond']" :before-close="closeModal('bond')">
					<template v-slot:content="props">
						<vue-bond-modal
							ref="bondModalRef"
							:is-open-bond-modal="isOpenModal['bond']"
							:fin-req-code-menu="finReqCodeMenu"
							:pro-price-range-menu="proPriceRangeMenu"
							:download-file="downloadFile"
							:download-other-file="downloadOtherFile"
							:close="props.close"
						></vue-bond-modal>
					</template>
				</vue-modal>
				<vue-modal :is-open="isOpenModal['pfd']" :before-close="closeModal('pfd')">
					<template v-slot:content="props">
						<vue-pfd-modal
							ref="pfdModalRef"
							:is-open-pfd-modal="isOpenModal['pfd']"
							:fin-req-code-menu="finReqCodeMenu"
							:close="props.close"
						></vue-pfd-modal>
					</template>
				</vue-modal>
				<vue-modal :is-open="isOpenModal['sp']" :before-close="closeModal('sp')">
					<template v-slot:content="props">
						<vue-sp-modal
							ref="spModalRef"
							:is-open-structured-product-modal="isOpenModal['sp']"
							:fin-req-code-menu="finReqCodeMenu"
							:pro-price-range-menu="proPriceRangeMenu"
							:download-file="downloadFile"
							:download-other-file="downloadOtherFile"
							:close="props.close"
						></vue-sp-modal>
					</template>
				</vue-modal>
				<vue-modal :is-open="isOpenModal['ins']" :before-close="closeModal('ins')">
					<template v-slot:content="props">
						<vue-ins-modal
							ref="insModalRef"
							:is-open-ins-modal="isOpenModal['ins']"
							:fin-req-code-menu="finReqCodeMenu"
							:download-file="downloadFile"
							:download-other-file="downloadOtherFile"
							:close="props.close"
						></vue-ins-modal>
					</template>
				</vue-modal>
				<vue-modal :is-open="isOpenModal['dci']" :before-close="closeModal('dci')">
					<template v-slot:content="props">
						<vue-dci-modal
							ref="dciModalRef"
							:is-open-dci-modal="isOpenModal['dci']"
							:fin-req-code-menu="finReqCodeMenu"
							:pro-price-range-menu="proPriceRangeMenu"
							:download-file="downloadFile"
							:download-other-file="downloadOtherFile"
							:close="props.close"
						></vue-dci-modal>
					</template>
				</vue-modal>
				<vue-modal :is-open="isOpenModal['sec']" :before-close="closeModal('sec')">
					<template v-slot:content="props">
						<vue-sec-modal
							ref="secModalRef"
							:is-open-sec-modal="isOpenModal['sec']"
							:fin-req-code-menu="finReqCodeMenu"
							:pro-price-range-menu="proPriceRangeMenu"
							:download-file="downloadFile"
							:download-other-file="downloadOtherFile"
							:close="props.close"
						></vue-sec-modal>
					</template>
				</vue-modal>
			</div>
		</template>
	</vue-modal>
</template>
<script>
import vueModal from '@/views/components/model.vue';
import vueFundModal from '@/views/pro/PRO0101/include/fundModal.vue';
import vueEtfModal from '@/views/pro/PRO0101/include/etfModal.vue';
import vueBondModal from '@/views/pro/PRO0101/include/bondModal.vue';
import vuePfdModal from '@/views/pro/PRO0101/include/pfdModal.vue';
import vueSpModal from '@/views/pro/PRO0101/include/spModal.vue';
import vueInsModal from '@/views/pro/PRO0101/include/insModal.vue';
import vueDciModal from '@/views/pro/PRO0101/include/dciModal.vue';
import vueSecModal from '@/views/pro/PRO0101/include/secModal.vue';

export default {
	components: {
		vueModal,
		vueFundModal,
		vueEtfModal,
		vueBondModal,
		vuePfdModal,
		vueSpModal,
		vueInsModal,
		vueDciModal,
		vueSecModal
	},
	props: {},
	data: function () {
		return {
			proPriceRangeMenu: undefined,
			finReqCodeMenu: undefined,
			previewItem: {},
			isOpenMyModal: false,
			isOpenModal: {
				fund: false,
				etf: false,
				bond: false,
				pfd: false,
				sp: false,
				ins: false,
				dci: false,
				sec: false
			},
			// 定義動態class
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',
			buttonClass: 'btn-expand'
		};
	},
	methods: {
		getDetail: function (eventId) {
			var self = this;
			if (_.isBlank(eventId)) {
				return;
			}
			self.$api
				.getProSelected({
					selproId: null,
					eventId: eventId
				})
				.then(function (ret) {
					self.previewItem = ret.data;
					self.isOpenMyModal = true;
				});
		},
		closeMyModal: function () {
			this.isOpenMyModal = false;
		},
		closeModal: function (type) {
			this.isOpenModal[type] = false;
		},
		fundModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.$refs.fundModalRef.getProInfo(proCode, pfcatCode);
			self.isOpenModal.fund = true;
		},
		etfModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.$refs.etfModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.etfModalRef.getEtfDetail(proCode); // 商品資訊/基金資料
			self.$refs.etfModalRef.getEtfStockHold(proCode); // 商品資訊/ETF持股
			self.$refs.etfModalRef.getEtfPrice(proCode); // 商品資訊/價格分析資料
			self.$refs.etfModalRef.getPricesChartData(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.etfModalRef.getEtfProfileNameMenu(); // 商品資訊/績效分析
			self.$refs.etfModalRef.getEtfProfileBenchmarksMenu(); // 商品資訊/績效分析
			self.isOpenModal.etf = true;
		},
		bondModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.$refs.bondModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.bondModalRef.getBondPriceAna(proCode);
			self.$refs.bondModalRef.getPricesChartData(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.bondModalRef.getBondPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.bond = true;
		},
		pfdModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.$refs.pfdModalRef.getProInfo(proCode, pfcatCode);
			//			self.$refs.pfdModalRef.getEtfDetail(proCode); // 商品資訊/基金資料
			self.isOpenModal.pfd = true;
		},
		spModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.$refs.spModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.spModalRef.getSpPriceAna(proCode); // 商品資訊/淨值分析資料
			self.$refs.spModalRef.getSpNets(proCode, 'Y', -1.0); // 商品資訊/淨值分析圖表
			self.$refs.spModalRef.getSpPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.sp = true;
		},
		insModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.$refs.insModalRef.getProInfo(proCode, pfcatCode);
			self.isOpenModal.ins = true;
		},
		dciModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.$refs.dciModalRef.getProInfo(proCode, pfcatCode); //商品基本資料
			self.$refs.dciModalRef.getDciPriceAna(proCode); // 商品資訊/價格分析資料
			self.$refs.dciModalRef.getDciNets(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.dciModalRef.getDciPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.dci = true;
		},
		secModalHandler: function (proCode, pfcatCode) {
			var self = this;
			self.$refs.secModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.secModalRef.getSecPriceAna(proCode); // 商品資訊/淨值分析資料
			self.$refs.secModalRef.getSecNets(proCode, 'Y', -1.0); // 商品資訊/淨值分析圖表
			self.$refs.secModalRef.getSecPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.sec = true;
		},
		changeModalSize: function () {
			var self = this;
			if (self.modalClass === 'modal-dialog modal-dialog-centered modal-lg') {
				self.modalClass = 'modal-dialog modal-dialog-centered modal-lg fullscreen';
				self.buttonClass = 'btn-expand mini';
			} else {
				self.modalClass = 'modal-dialog modal-dialog-centered modal-lg';
				self.buttonClass = 'btn-expand';
			}
		},
		downloadFile: function (proFile) {
			var self = this;
			var proFileId = proFile.proFileId;
			var fileName = proFile.showName;
			self.$api
				.downloadProFileApi({
					proFileId: proFileId
				})
				.then(function (data) {
					var link = document.createElement('a');
					var url = URL.createObjectURL(data);
					link.download = fileName;
					link.href = url;
					document.body.appendChild(link);
					link.click();
					link.remove();
					setTimeout(() => URL.revokeObjectURL(url), 1000);
				});
		},
		downloadOtherFile: function (fileId) {
			var self = this;
			self.$api.downloadGenOtherFileApi({ fileId: fileId });
		}
	}
};
</script>
