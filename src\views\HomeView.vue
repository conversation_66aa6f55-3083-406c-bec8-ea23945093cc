<template>
	<!-- {{ $t('eventNotification') }} -->
	<div class="container-fluid">
		<div class="content ht-100v pd-0">
			<content-header></content-header>
			<aside-menu></aside-menu>
			<div class="content content-fixed content-body">
				<RouterView />
				<!-- 展開所有節點 -->
				<button @click="expandAllNodes">全部展開</button>
				<!-- 收合所有節點 -->
				<button @click="collapseAllNodes">全部收合</button>
				<bi-tree ref="tree" :treeData="treeData" :expandOnLoad="false" :generateCheckbox="true"></bi-tree>
			</div>
		</div>
	</div>
</template>

<script>
import contentHeader from './components/contentHeader.vue';
import asideMenu from './components/asideMenu.vue';
import biTree from './components/biTree.vue';

export default {
	components: {
		contentHeader,
		asideMenu,
		biTree
	},
	data() {
		return {
			treeData: {}
			// expandAll: false  // 控制是否展開所有節點
		};
	},
	methods: {
		expandAllNodes() {
			this.$refs.tree.expandAllNodes(true);
			// this.expandAll = true;
			// this.updateAllNodesExpanded(true); // 展開所有節點
		},
		collapseAllNodes() {
			this.$refs.tree.expandAllNodes(false);
			// this.expandAll = false;
			// this.updateAllNodesExpanded(false); // 收合所有節點
		}
		// updateAllNodesExpanded(expanded) {
		// 	this.treeData.forEach(node => {
		// 		node.expanded = expanded;
		// 		if (node.nodes && node.nodes.length) {
		// 			this.updateAllNodesExpandedForChildren(node.nodes, expanded);
		// 		}
		// 	});
		// },
		// updateAllNodesExpandedForChildren(nodes, expanded) {
		// 	nodes.forEach(node => {
		// 		node.expanded = expanded;
		// 		if (node.nodes && node.nodes.length) {
		// 			this.updateAllNodesExpandedForChildren(node.nodes, expanded);
		// 		}
		// 	});
		// }
	}
};
</script>
