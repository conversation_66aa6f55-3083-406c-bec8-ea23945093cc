<template>
	<vue-modal :is-open="isOpenModal" :before-close="closeModal">
		<template v-slot:content="props">
			<div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">重點顧客設定</h4>
						<button type="button" class="btn-close" @click="props.close()" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="card card-form">
							<vue-form v-slot="{ errors, validate }" ref="favCustomerModal">
								<div class="card-header">
									<h4>請輸入下列資料</h4>
									<span class="tx-square-bracket">為必填欄位</span>
								</div>
								<div class="card-body">
									<div class="row g-3 align-items-end">
										<div class="col-lg-6">
											<label class="form-label tx-require">自訂群組名稱</label>
											<vue-field
												name="groupName"
												class="form-control"
												id="txtGroupName"
												type="text"
												size="30"
												v-model="groupName"
												rules="required"
												:class="{ 'is-invalid': errors.groupName }"
												label="群組名稱"
											>
											</vue-field>
										</div>
										<div class="col-lg-3">
											<button class="btn btn-primary btn-glow btn-save" id="btnInsGroupName" @click="insertGroup()">
												儲存
											</button>
										</div>
										<div class="col-10" style="height: 3px">
											<span class="text-danger" v-show="errors.groupName">{{ errors.groupName }}</span>
										</div>
									</div>
								</div>
							</vue-form>
						</div>

						<div id="searchRusult" v-if="doUpdateCode == null">
							<div class="card card-table">
								<div class="card-header">
									<h4>重點顧客群組</h4>
								</div>
								<div class="table-responsive">
									<table class="table table-RWD table-hover">
										<thead>
											<tr>
												<th width="50%">自訂群組名稱</th>
												<th width="50%" class="text-end">執行</th>
											</tr>
										</thead>
										<tbody id="wrapperList">
											<tr v-for="cusGroupData in cusGroups">
												<td id="grpName1" data-th="自訂群組名稱">{{ cusGroupData.groupName }}</td>
												<td data-th="執行" class="text-end">
													<button
														type="button"
														class="btn btn-info btn-glow btn-icon"
														data-bs-toggle="tooltip"
														data-bs-original-title="編輯"
														@click="doUpdateGroup(cusGroupData)"
													>
														<i class="bi bi-pen"></i>
													</button>
													<button
														type="button"
														class="btn btn-danger btn-glow btn-icon"
														data-bs-toggle="tooltip"
														data-bs-original-title="刪除"
														@click="deleteGroup(cusGroupData.groupCode)"
													>
														<i class="bi bi-trash"></i>
													</button>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
							<div class="tx-note">重點顧客設定功能提供PBC建立自己的顧客群組，方便PBC在搜尋時能快速搜尋到目標顧客。</div>
							<div class="modal-footer">
								<button type="button" class="btn-close" @click="props.close()" aria-label="Close"></button>
							</div>
						</div>

						<vue-fav-cus-setup
							title="編輯重點顧客"
							:group-name="groupName"
							:do-update-code="doUpdateCode"
							:refresh-update-code="refreshUpdateCode"
						></vue-fav-cus-setup>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
</template>
<script>
import { Field, Form } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import vueFavCusSetup from './favCusSetup.vue';
export default {
	props: {
		refreshFun: Function // 外部程式刷新功能
	},
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueModal,
		vueFavCusSetup
	},
	data: function () {
		return {
			isOpenModal: true,
			//API 用參數
			groupName: null,

			graCode: null,
			idn: null,
			selectCusCodes: [],

			//畫面邏輯用參數
			doUpdateCode: null,
			queryCusCodes: [],
			//下拉選單
			aumMenu: [],
			//畫面顯示用參數
			cusGroups: [],

			//主要顯示資料
			queryCus: [],
			selectCus: [],
			pageable: {
				page: 0,
				size: 5000,
				sort: 'CUS_CODE',
				direction: 'ASC'
			}
		};
	},
	mounted: function () {
		var self = this;
		self.getCusGroup();
	},
	methods: {
		getCusGroup: async function () {
			var self = this;
			const ret = await self.$api.getCusGroupMenuApi();
			self.cusGroups = ret.data;
		},
		insertGroup: async function () {
			var self = this;
			self.$refs.favCustomerModal.validate().then(async function (pass) {
				if (pass.valid) {
					const ret = await self.$api.postGroupApi({ groupName: self.groupName });
					self.$bi.alert('新增成功');
					self.getCusGroup();
				}
			});
		},
		doUpdateGroup: function (cusGroupData) {
			var self = this;
			self.doUpdateCode = cusGroupData.groupCode;
			self.groupName = cusGroupData.groupName;
		},
		deleteGroup: function (groupCode) {
			var self = this;
			self.$bi.confirm('確定要刪除此筆資料嗎?', {
				event: {
					confirmOk: async function () {
						const ret = await self.$api.deleteGroupApi({
							groupCode: groupCode
						});
						self.$bi.alert('刪除成功');
						self.getCusGroup();
					}
				}
			});
		},
		refreshUpdateCode: function (doUpdateCode) {
			var self = this;
			self.doUpdateCode = doUpdateCode;
		},
		refreshOutside: function () {
			var self = this;
			if (self.refreshFun) {
				self.refreshFun();
			}
		},
		closeModal: function () {
			this.isOpenModal = false;
		}
	}
};
</script>
