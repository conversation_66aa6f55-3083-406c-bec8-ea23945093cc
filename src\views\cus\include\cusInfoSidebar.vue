<template>
	<div class="filemgr-sidebar heighter-cusblock">
		<div class="filemgr-sidebar-header" v-if="customer">
			<div class="d-flex align-items-center">
				<div class="avatar avatar-md me-1">
					<img :src="getImgURL('avatar', 'man-2.png')" class="rounded-circle bg-info" v-if="customer.gender == 'M'" />
					<img :src="getImgURL('avatar', 'man-3.png')" class="rounded-circle bg-info" v-if="customer.gender == 'F'" />
				</div>
				<div>
					<h6 class="mb-0">顧客：{{ customer.cusName }}</h6>
					<p class="tx-13 tx-color-03 mb-0">年齡：{{ customer.age }} 歲</p>
				</div>
			</div>
			<ul class="list-info">
				<li>
					歸屬PBC：<span v-if="customer.userName">{{ customer.userName }}</span>
				</li>
			</ul>
		</div>
		<div class="filemgr-sidebar-body">
			<div id="sidebarMenu" class="p-3">
				<ul class="sidebar-nav">
					<li class="nav-item show">
						<a href="#" class="nav-link with-sub"><i></i>顧客總覽</a>
						<nav class="nav">
							<a class="nav-link" :href="config.contextPath + '/cus/cusInfo?cusCode=' + _.convertUrlSpecChar(cusCode)"
								:class="{ active: pageCode == 1 }" v-if="isShowCusInfo">{{ cusInfoMenuName }}</a>
							<a class="nav-link"
								:href="config.contextPath + '/cus/clientOverview?cusCode=' + _.convertUrlSpecChar(cusCode)"
								:class="{ active: pageCode == 2 }" v-if="isShowClientOverview">{{ clientOverviewMenuName }}</a>
							<a class="nav-link"
								:href="config.contextPath + '/cus/assetsDetail?cusCode=' + _.convertUrlSpecChar(cusCode)"
								:class="{ active: pageCode == 3 }" v-if="isShowAssetsDetail">{{ assetsDetailMenuName }}</a>
							<a class="nav-link"
								:href="config.contextPath + '/cus/investAnalysis?cusCode=' + _.convertUrlSpecChar(cusCode)"
								:class="{ active: pageCode == 4 }" v-if="isShowInvestAnalysis">{{ investAnalysisMenuName }}</a>
							<a class="nav-link"
								:href="config.contextPath + '/cus/clientServiceRec?cusCode=' + _.convertUrlSpecChar(cusCode)"
								:class="{ active: pageCode == 5 }" v-if="isShowClientServiceRec">{{ clientServiceRecMenuName }}</a>
							<a class="nav-link"
								:href="config.contextPath + '/cus/clientPriceAlert?cusCode=' + _.convertUrlSpecChar(cusCode)"
								:class="{ active: pageCode == 7 }" v-if="isShowClientPriceAlert">{{ clientPriceAlertMenuName }}</a>
							<a class="nav-link"
								:href="config.contextPath + '/cus/bankstatement?cusCode=' + _.convertUrlSpecChar(cusCode)"
								:class="{ active: pageCode == 9 }" v-if="isShowBankstatement">{{ bankstatementMenuName }}</a>
						</nav>
					</li>
				</ul>
			</div>
		</div>
		<vue-fav-customer-model></vue-fav-customer-model>
	</div>
</template>
<script>
import _ from 'lodash';
import vueFavCustomerModel from '../CUS0201/include/favCustomerModal.vue';
import { getImgURL } from '@/utils/imgURL.js';
export default {
	components: {
		vueFavCustomerModel
	},
	props: {
		cusCode: String,
		pageCode: Number,
		setAuth: Function,
		setCustomer: Function
	},
	data: function () {
		return {
			isHideMenu: false,
			path: null,
			//畫面判斷用邏輯
			customer: {
				cusName: null,
				idnEntityType: null
			},
			isShowCusInfo: false,
			isShowClientOverview: false,
			isShowAssetsDetail: false,
			isShowInvestAnalysis: false,
			isShowClientServiceRec: false,
			isShowClientPriceAlert: false,
			isShowBankstatement: false,

			cusInfoMenuName: '',
			clientOverviewMenuName: '',
			assetsDetailMenuName: '',
			investAnalysisMenuName: '',
			clientServiceRecMenuName: '',
			clientPriceAlertMenuName: '',
			bankstatementMenuName: ''
		};
	},
	mounted: function () {
		var self = this;
		self.checkAuth();
		self.checkRoleMenus();
	},
	methods: {
		getImgURL,
		checkAuth: async function () {
			var self = this;
			const ret = await self.$api.getCustomersApi({
				cusCode: self.cusCode
			});
			if (!ret.data || ret.data.length == 0) {
				self.setAuth(false);
				self.$bi.confirm('無此顧客或該顧客非屬您歸屬私銀中心/組轄下顧客。', {
					event: {
						confirmOk: function () {
							location.href = self.config.contextPath + '/';
						}
					},
					button: {
						confirmOk: '確定'
					}
				});
			} else {
				self.setAuth(true);
				self.customer = ret.data[0];
				if (self.setCustomer) {
					self.setCustomer(self.customer);
				}
			}
		},
		checkRoleMenus: async function () {
			var self = this;
			const ret = await self.$api.getUserRoleMenuApi();
			if (ret.data) {
				ret.data.forEach(function (item) {
					switch (item.menuCode) {
						case 'M21-12':
						case 'MB2-12':
							//基本資料
							self.isShowCusInfo = true;
							self.cusInfoMenuName = item.menuName;
							break;
						case 'M21-11':
						case 'MB2-11':
							//帳戶總覽
							self.isShowClientOverview = true;
							self.clientOverviewMenuName = item.menuName;
							break;
						case 'M21-14':
						case 'MB2-14':
							//帳戶明細
							self.isShowAssetsDetail = true;
							self.assetsDetailMenuName = item.menuName;
							break;
						case 'M21-13':
						case 'MB2-13':
							//投資績效分析
							self.isShowInvestAnalysis = true;
							self.investAnalysisMenuName = item.menuName;
							break;
						case 'M21-15':
						case 'MB2-15':
							//服務紀錄
							self.isShowClientServiceRec = true;
							self.clientServiceRecMenuName = item.menuName;
							break;
						case 'M21-17':
						case 'MB2-17':
							//報酬率警示設定
							self.isShowClientPriceAlert = true;
							self.clientPriceAlertMenuName = item.menuName;
							break;
						case 'M21-16':
						case 'MB2-16':
							//顧客報告書
							self.isShowBankstatement = true;
							self.bankstatementMenuName = item.menuName;
							break;
					}
				});
			}
		}
	}
};
</script>
